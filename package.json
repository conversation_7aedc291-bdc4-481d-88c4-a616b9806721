{"name": "<PERSON><PERSON>i", "version": "0.1.0", "private": true, "scripts": {"dev": "vue-cli-service serve", "prod": "vue-cli-service serve --mode production", "lint": "vue-cli-service lint", "build:prod": "vue-cli-service build", "build:stage": "vue-cli-service build --mode development"}, "dependencies": {"@chenfengyuan/vue-qrcode": "^1.0.2", "@vant/area-data": "^1.4.1", "ali-oss": "^6.15.2", "alife-logger": "^1.8.30", "amfe-flexible": "^2.2.1", "animate.css": "^4.1.1", "axios": "^0.21.1", "bignumber.js": "^9.0.1", "canvas-confetti": "^1.9.2", "core-js": "^3.6.5", "crypto": "^1.0.1", "dayjs": "^1.10.4", "exif-js": "^2.3.0", "jsencrypt": "^3.3.2", "jsrsasign": "^10.5.10", "lib-flexible": "^0.3.2", "mockjs": "^1.1.0", "number-precision": "^1.5.0", "postcss-pxtorem": "^5.1.1", "process": "^0.11.10", "qs": "^6.10.1", "sass": "1.77.6", "sass-loader": "^10.0.2", "style-loader": "^1.2.1", "vant": "^2.12.19", "vue": "^2.6.11", "vue-clipboard2": "^0.3.1", "vue-cropper": "^0.5.6", "vue-router": "^3.2.0", "vuex": "^3.4.0"}, "devDependencies": {"@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-eslint": "~4.5.0", "@vue/cli-plugin-router": "^4.5.13", "@vue/cli-plugin-vuex": "^4.5.13", "@vue/cli-service": "~4.5.0", "babel-eslint": "^10.1.0", "eslint": "^6.7.2", "eslint-plugin-vue": "^6.2.2", "vue-template-compiler": "^2.6.11"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "eslint:recommended"], "parserOptions": {"parser": "babel-es<PERSON>"}, "rules": {}}, "browserslist": ["> 1%", "last 2 versions", "not dead"], "description": "```\r npm install\r ```", "main": ".eslintrc.js", "repository": {"type": "git", "url": "*********************:6062c6c35b9520fa3cfe7cb3/nicetime-web/newUser.git"}, "keywords": [], "author": "", "license": "ISC"}