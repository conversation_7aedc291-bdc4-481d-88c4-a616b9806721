<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2022-09-07 14:41:23
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-10-27 15:51:10
-->
<template>
  <div class="privacy_show">
    <!-- 顶部弹出 -->
    <div v-if="privacyTopShow" class="privacy_show_top">
      <div class="privacy_show_top_box">
        <div>
          <van-icon name="location-o" class="icon" size="30" />
        </div>
        <div>
          <div>位置权限使用说明</div>
          <div>根据您的位置信息为您推荐周边的生活服务</div>
        </div>
      </div>
    </div>
    <div v-if="privacyShow" class="privacy_show_top">
      <div class="privacy_show_top_box">
        <div>
          <van-icon name="info-o" class="icon" size="30" />
        </div>
        <div>
          <div>拍摄与存储权限使用说明</div>
          <div>帮您实现扫码二维码、上传信息认证或拍照发表评价等功能</div>
        </div>
      </div>
    </div>

    <!-- 中间弹出 -->
    <van-overlay :show="privacyShow" z-index="1003">
      <div class="wrapper" @click.stop>
        <div class="block">
          <div class="title">{{ title }}</div>
          <div class="content">
            {{ content }}
          </div>
          <div class="btn">
            <div class="btn-close" @click="cancel">{{ cancelName }}</div>
            <div class="btn-sure" @click="confirm">{{ confirmName }}</div>
          </div>
        </div>
      </div>
    </van-overlay>
  </div>
</template>

<script>
export default {
  props: {
    privacyTopShow: {
      type: Boolean,
      default: false
    },
    privacy2TopShow: {
      type: Boolean,
      default: false
    },
    privacyShow: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: '提示'
    },
    content: {
      type: String,
      default: '提示'
    },
    cancelName: {
      type: String,
      default: '取消'
    },
    confirmName: {
      type: String,
      default: '确认'
    }
  },
  data() {
    return {}
  },
  methods: {
    cancel() {
      this.$emit('cancel')
    },
    confirm() {
      this.$emit('confirm')
    }
  }
}
</script>

<style scoped lang="scss">
.privacy_show {
  .privacy_show_top{
    width: 100%;
    // height: 150px;
    margin: 0 auto;
    position: fixed;
    top: 60px;
    z-index: 5;
    .privacy_show_top_box{
      width: 90%;
      height: 150px;
      margin: 0 auto;
      border-radius: 12px;
      background-color: #fff;
      font-size: 28px;
      padding: 30px;
      display: flex;
      .icon{
        position: relative;
        top: 15px;
        margin-right: 20px;
      }
    }
  }
  .wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
  }
  .block {
    width: 602px;
    min-height: 350px;
    background: #ffffff;
    border-radius: 30px;
    .title {
      text-align: center;
      height: 128px;
      line-height: 128px;
      font-size: 40px;
      font-family: PingFangSC-Medium;
      color: #222222;
    }
    .content {
      width: 514px;
      min-height: 70px;
      font-size: 30px;
      color: #666666;
      margin: 0 auto;
      .b {
        color: #222222;
        font-family: PingFangSC-Medium;
      }
    }
    .btn {
      width: 500px;
      margin: 0 auto;
      margin-top: 45px;
      margin-bottom: 20px;
      display: flex;
      justify-content: space-between;
      div {
        text-align: center;
        margin: 0 auto;
      }
      .btn-close {
        width: 330px;
        height: 75px;
        line-height: 75px;
        font-size: 30px;
        color: #909090;
      }
      .btn-sure {
        width: 442px;
        height: 75px;
        line-height: 75px;
        border-radius: 12px;
        background: #39cf3f;
        color: #ffffff;
        // margin-top: 39px;
        font-family: PingFangSC-Medium;
        font-size: 30px;
      }
    }
  }
}
</style>
