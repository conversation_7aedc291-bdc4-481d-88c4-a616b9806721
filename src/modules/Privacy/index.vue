<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2022-09-07 10:20:06
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-10-13 10:47:29
-->
<template>
  <div class="privacy">
    <!-- 头部 -->
    <NavHeight bgc="#f5f5f5" />
    <div class="privacy_content">
      <h3>温馨提示</h3>
      <div>亲爱的用户，感谢您使用点滴！</div>
      <div>
        我们依据相关法律制定了<span
          class="privacy_y"
          @click="goUseragt"
        >《用户协议》</span>和<span class="privacy_y" @click="goPrivacy">《隐私政策》</span>，请您在点击同意之前仔细阅读并充分理解相关条款，其中的重点条款已为您标注，介绍我们的个人信息使用情况，平台资质及其他服务规则，为方便您了解自己的权利。
      </div>
      <div>
        为了向您提供更好的服务，我们需要获取以下权限和信息，系统将以弹窗提示征求您的授权
      </div>
      <h4>
        <van-icon name="location-o" class="icon" />
        地理位置
      </h4>
      <div class="privacy_l">
        经授权，我们会收集您的地理位置信息，以便为您推荐周边的生活服务等。请注意，在部分业务场景下地理位置是必要信息，拒绝授权可能影响正常使用。
      </div>
      <h4>
        <van-icon name="desktop-o" class="icon" />
        相机
      </h4>
      <div class="privacy_l">
        经授权，我们会在您使用扫码、拍照等对应服务时使用您的相机功能。
      </div>
      <h4>
        <van-icon name="photo-o" class="icon" />
        相册
      </h4>
      <div class="privacy_l">
        经授权，我们会在您使用照片及视频上传、保存等对应服务时手机您的相册信息。
      </div>
      <h4>
        <van-icon name="orders-o" class="icon" />
        设备信息
      </h4>
      <div class="privacy_l">
        经授权，我们会收集您的设备信息以保障系统运行和您的账号、交易安全。
      </div>
      <div>
        我们将通过<span class="privacy_y" @click="goUseragt">《用户协议》</span>向您说明：
      </div>
      <div>
        1.
        为了您可以更好的享受周边的吃喝玩乐等本地服务，我们会根据您的授权内容，收集和使用对应的必要信息（例如您的联系电话、位置信息、配送地址等）。
      </div>
      <div>
        2.
        您可以对上述信息进行访问、更正、删除以及注销账户，我们也将提供专门的个人信息保护联系方式。
      </div>
      <div>
        3.
        未经您的授权同意，我们不会将您的个人信息共享给第三方或用户您未授权的其他用途。
      </div>
    </div>
    <div style="height:150px" />
    <div class="privacy_bottom">
      <div class="privacy_sure" @click="confirm">同意</div>
      <div class="privacy_no" @click="privacyShow = true">不同意</div>
      <div :style="'height: '+this.$store.getters.getNavigationBarHeight + 'px'" />
      <!-- <div :style="'height: '+25 + 'px'" /> -->
    </div>

    <!-- ---------------- -->
    <van-overlay :show="privacyShow" z-index="1003">
      <div class="wrapper" @click.stop>
        <div class="block">
          <div class="title">点滴需要您同意本隐私协议</div>
          <div class="content">
            <div>
              如果不同意<span
                class="privacy_y"
                @click="goPrivacy"
              >《隐私协议》</span>，您只能浏览部分吃喝玩乐内容
            </div>
          </div>
          <div class="btn">
            <div class="btn-close" @click="cancel">仍不同意</div>
            <div class="btn-sure" @click="confirm">同意并继续</div>
          </div>
        </div>
      </div>
    </van-overlay>

  </div>
</template>

<script>
export default {
  data() {
    return {
      privacyShow: false
    }
  },
  created() {},
  mounted() {
    // 回到顶部
    document.body.scrollTop = 0
    document.documentElement.scrollTop = 0
  },
  methods: {
    confirm() {
      this.privacyShow = false
      this.$store.commit('setIsPrivacy', 1)
      this.$emit('confirm')

      AlipayJSBridge.call('UmAgreementIsSubmit', {
        isSubmit: '1'
      }, function(result) {
        console.log('友盟初始化权限' + result)
      })
    },
    cancel() {
      this.privacyShow = false
      this.$store.commit('setIsPrivacy', 2)
      this.$emit('cancel')
    },
    // 隐私协议
    goUseragt() {
      this.$router.push('/useragt')
    },
    goPrivacy() {
      this.$router.push('/privacy')
    }
  }
}
</script>

<style scoped lang="scss">
.privacy {
  .privacy_content {
    padding: 30px;
    font-size: 28px;
    font-weight: 500;
    h3 {
      text-align: center;
      height: 80px;
      line-height: 80px;
      font-size: 38px;
      margin-bottom: 20px;
    }
    h4{
      .icon{
        position: relative;
        top: 3px;
      }
    }
    div {
      margin-bottom: 20px;
    }
    .privacy_l {
      color: #7f7f7f;
    }
  }

  .privacy_bottom {
    width: 100%;
    min-height: 200px;
    background: #f5f5f5;
    padding: 0;
    position: fixed;
    bottom: 0;
    overflow: hidden;
    .privacy_sure {
      width: 92%;
      height: 80px;
      line-height: 80px;
      text-align: center;
      background-color: #39cf3f;
      border-radius: 12px;
      color: #fff;
      font-family: PingFangSC-Medium;
      font-size: 30px;
      margin: 0 auto;
      margin-top: 20px;
    }
    .privacy_no {
      width: 92%;
      text-align: center;
      height: 80px;
      line-height: 80px;
      font-size: 30px;
      text-decoration: underline;
      color: #7f7f7f;
      margin: 0 auto;
    }
  }
  .privacy_y {
    color: #39cf3f;
    margin-left: 3px;
    margin-right: 3px;
  }
  .wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
  }
  .block {
    width: 602px;
    height: 460px;
    background: #ffffff;
    border-radius: 36px;
    .title {
      text-align: center;
      height: 128px;
      line-height: 128px;
      font-size: 40px;
      font-family: PingFangSC-Medium;
      color: #222222;
    }
    .content {
      width: 514px;
      height: 70px;
      font-size: 30px;
      color: #666666;
      margin: 0 auto;
      .b {
        color: #222222;
        font-family: PingFangSC-Medium;
      }
    }
    .btn {
      width: 500px;
      margin: 0 auto;
      margin-top: 45px;
      div {
        text-align: center;
        margin: 0 auto;
      }
      .btn-close {
        width: 330px;
        height: 60px;
        line-height: 60px;
        font-size: 30px;
        color: #909090;
        border-bottom: 1px solid #cfcfcf;
      }
      .btn-sure {
        width: 442px;
        height: 75px;
        line-height: 75px;
        border-radius: 12px;
        background: #39cf3f;
        color: #ffffff;
        margin-top: 39px;
        font-family: PingFangSC-Medium;
        font-size: 30px;
      }
    }
  }
}
</style>
