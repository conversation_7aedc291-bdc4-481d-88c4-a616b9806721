/*
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2022-08-02 14:57:15
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-09-22 14:37:41
 */
import router from '@/router'
import { Toast, Dialog } from 'vant'
import { payLoading } from '@/api/takeout'

var u = navigator.userAgent
var isiOS = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/) // ios终端

class DPay {
  getPay(type, payData, scene, orderNo) {
    let self = this
    // 微信支付
    if (type == 3) {
      AlipayJSBridge.call('HsbFun', { orderNo: payData }, function(result) {
        if (result.payResult === 'success') {
          Toast('支付成功')
          self.jump(orderNo, scene)
        } else {
          Toast('支付失败')
        }
      })
    }

    // 支付宝支付
    if (type == 2) {
      console.log('支付宝支付')

      let self = this
      // let packageName = isiOS ? 'alipay://' : 'com.eg.android.AlipayGphone'
      // AlipayJSBridge.call('IsAvailable', { packageName: packageName }, function(result) {
      //   if (result.available == true) {
      //     if (this.$store.getters.getRegionId == 7) {
      //       let url = 'alipays://platformapi/startapp?saId=10000007&qrcode=' + `https://dshop.diandiandidi.top/h5/alipay.html?orderId=${orderNo}`
      //       window.location.href = url
      //       setTimeout(() => {
      //         self.confirm(orderNo, scene)
      //       }, 2000)
      //       return
      //     }
      //     let url = 'alipays://platformapi/startapp?saId=10000007&qrcode=' + payData
      //     window.location.href = url
      //     setTimeout(() => {
      //       self.confirm(orderNo, scene)
      //     }, 2000)
      //   } else {
      //     Toast('未安装支付宝')
      //   }
      // })

      if (localStorage.getItem('regionId') == 7) {
        let url = 'alipays://platformapi/startapp?saId=10000007&qrcode=' + `https://dshop.diandiandidi.top/h5/alipay.html?orderId=${orderNo}`
        window.location.href = url
        setTimeout(() => {
          self.confirm(orderNo, scene)
        }, 2000)
        return
      }
      let url = 'alipays://platformapi/startapp?saId=10000007&qrcode=' + payData
      console.log(url)

      window.location.href = url
      setTimeout(() => {
        self.confirm(orderNo, scene)
      }, 2000)
    }

    // 云闪付
    if (type == 9) {
      let self = this
      let packageName = isiOS ? 'upwallet://' : 'com.unionpay'
      AlipayJSBridge.call('IsAvailable', { packageName: packageName }, function(result) {
        if (result.available == true) {
          let url = 'upwallet://html/' + payData.slice(8)
          window.location.href = url
          setTimeout(() => {
            self.confirm(orderNo, scene)
          }, 2000)
        } else {
          Toast('未安装云闪付')
          if (isiOS) {
            window.location.href = 'https://wallet.95516.com/s/wl/webV3/activity/yhtzbtoc/html/snsIndex.html?r=6f040ef12df584d1a98ee25aefacf97c&code='
          } else {
            AlipayJSBridge.call('OpenUrlForAPP', { openurl: 'https://wallet.95516.com/s/wl/webV3/activity/yhtzbtoc/html/snsIndex.html?r=6f040ef12df584d1a98ee25aefacf97c&code=' }, function(result) {})
          }
        }
      })
    }

    if (type == 11) {
      let self = this
      setTimeout(() => {
        self.confirm(orderNo, scene)
      }, 3000)
      AlipayJSBridge.call('startABC', { openurl: payData }, function(result) {
        console.log(result)
      })
    }
  }

  // 支付确认
  confirm(orderNo, scene) {
    let self = this
    function checkPay() {
      payLoading(orderNo).then(function(res) {
        if (res.status == 200) {
          if (res.data == true) {
            Toast('支付成功')
            self.jump(orderNo, scene)
          } else {
            Toast('支付失败')
          }
        }
      })
    }
    Dialog.confirm({ title: '是否支付完成？', message: '' }).then(() => {
      checkPay()
    }).catch(() => {
      checkPay()
    })
  }

  /**
   * scene: 场景
   * 0: 进入订单列表
   * 1: 进入订单详情
   * 2: 其他不跳转
   * 3: 商城订单
   * 4: 套餐订单详情
   */
  jump(orderNo, scene) {
    if (scene == 0) {
      router.push({
        name: 'Order'
      })
    } else if (scene == 4) {
      router.push({
        name: 'SetMealorderList'
      })
    } else {
      router.push({
        name: 'OrderDetail',
        query: {
          orderNo: orderNo,
          type: 7,
          from: 1
        }
      })
    }
  }
}

export default new DPay()
