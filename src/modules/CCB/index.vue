<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2022-11-15 17:35:01
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-12-08 13:56:03
-->
<template>
  <div>
    <div class="banner" @click="onCCB">
      <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/bank/ccbank.jpg" alt="">
    </div>
    <van-popup v-model="show" round position="bottom" :style="{ height: '30%' }">
      <div class="ccb_box">
        <div class="ccb_title">
          <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/bank/ccblogo.png" alt="">
          约惠浙里
        </div>
        <div class="ccb_privacy">
          <div class="ccb_privacy_name">申请获取以下权限</div>
          <div class="ccb_privacy_mag">手机号码</div>
        </div>
        <div class="ccb_radio">
          <van-checkbox v-model="checked" style="width: 130px;">
            我已阅读并同意
          </van-checkbox>
          <span class="link" @click="privacyShow = true">《用户授权协议》</span>
        </div>
        <div class="ccb_btn">
          <van-button class="btn" type="default" @click="show = false">拒 绝</van-button>
          <van-button class="btn" type="primary" @click="isAvailable">同 意</van-button>
        </div>
      </div>
    </van-popup>

    <van-popup v-model="privacyShow" position="center" closeable>
      <div class="privacy_mag">
        《用户授权协议》（以下简称“本协议”)是点滴 （以
        下简称“我们”） 与用户（以下简称“您”）所订立的有效合
        约。请您先仔细阅读本协议内容。如您对本协议内容或页面
        提示信息有疑问，请勿进行下一步操作。如您通过页面点击
        或我们认可的其他方式确认本协议即表示您已同意本协议。
        1、为了便于您使用第三方的服务，您同意我们将您的用户
        标识(含用户编号和手机号码)及页面提示的相关信息传递给
        第三方。页面提示上会展示具体授权对象以及授权信息类
        型，您的信息将通过加密通道传递给第三方。我们会要求第
        三方严格遵守相关法律法规与监管要求，依法使用您的信
        息，并应对您的信息保密。点击授权之后，授权关系长期有
        效，直至您主动解除。
        2、上述第三方服务由该第三方独立运营并独立承担全部责
        任。因第三方服务或其使用您的信息而产生的纠纷，或第三
        方服务违反相关法律法规或协议约定，或您在使用第三方服
        务过程中遭受损失的，请您和第三方协商解决。
        3、如我们对本协议进行变更，我们将通过公告或客户端消
        息等方式予以通知，该等变更自通知载明的生效时间开始生
        效。若您无法同意变更修改后的协议内容，您有权停止使用
        相关服务；双方协商一致的，也可另行变更相关服务和对应
        协议内容。
        4、本协议之效力、解释、变更、执行与争议解决均适用中
        华人民共和国法律。因本协议产生的争议，均应依照中华人
        民共和国法律予以处理．并由被告住所地人民法院管辖
      </div>
    </van-popup>
  </div>

</template>

<script>
import { ccbIncrTraffic } from '@/api/bank'
export default {
  data() {
    return {
      privacyShow: false,
      show: false,
      checked: false
    }
  },
  watch: {
    show(val) {
      if (val == false) {
        this.checked = false
      }
    }
  },
  methods: {
    onCCB() {
      if (this.$store.getters.getUserId == null) {
        this.$toast('请登录')
        this.$router.push({ name: 'wxLogin2' })
        return
      }
      this.show = true
    },
    isAvailable() {
      if (this.checked == false) {
        this.$toast('请先同意相关协议')
        return
      }
      var self = this
      var u = navigator.userAgent
      var isiOS = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/) // ios终端
      let packageName = isiOS ? 'ccbapp://outlink' : 'com.chinamworld.main'
      if (isiOS) {
        self.openapp()
        return
      }

      AlipayJSBridge.call('IsAvailable', { packageName: packageName }, function(result) {
        console.log('packageName')
        console.log(result)
        if (result.available == true) {
          console.log('okokok')
          self.openapp()
        } else {
          self.$toast('未安装建设银行APP')
          if (isiOS) {
            console.log('nonono')
            window.location.href = 'http://m.ccb.com/cn/mobilev3/home/<USER>/download.html'
          } else {
            AlipayJSBridge.call('OpenUrlForAPP', { openurl: 'http://m.ccb.com/cn/mobilev3/home/<USER>/download.html' }, function(result) {})
          }
        }
      })
    },
    openapp() {
      var u = navigator.userAgent
      var isiOS = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/) // ios终端

      ccbIncrTraffic(isiOS ? 'iOS' : 'Android').then(res => {
        this.show = false
        console.log(res)
        let packageName = 'ccbapp://'
        let openurl = packageName + `outlink?link_id=${res.data.linkId}&crypt_type=1&cryptParam=${res.data.cryptParam}&back_info=back_url%3Ddiandi%3A%2F%2F%26back_icon%3Dhttps%3A%2F%2Fdiandi-app.oss-cn-hangzhou.aliyuncs.com%2Flogo%2Flogo.jpg&android_package=plus.H57B9E083`
        console.log(openurl)
        if (isiOS) {
          window.location.href = openurl
        } else {
          AlipayJSBridge.call('startCCB', { openurl: openurl }, function(result) {})
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
    .banner {
        height: 240px;
        margin: 0 auto;
        margin-top: 15px;
        margin-bottom: 55px;
        border-radius: 12px;
        text-align: center;
        line-height: 200px;
        img{
            width: 100%;
            height: 100%;
            border-radius: 12px;
        }
    }
    .ccb_box{
      width: 92%;
      margin: 0 auto;
      .ccb_title{
        height: 100px;
        line-height: 100px;
        font-size: 40px;
        font-family: PingFangSC-Medium;
        img{
          width: 50px;
          height: 50px;
          position: relative;
          top: 10px;
          margin-left: -10px;
        }
      }
      .ccb_privacy{
        margin-top: 20px;
      }
      .ccb_privacy_name{
        font-size: 30px;
        margin-bottom: 10px;
        font-family: PingFangSC-Medium;
      }
      .ccb_privacy_mag{
        font-size: 30px;
      }
      .ccb_radio{
        height: 60px;
        line-height: 60px;
        font-size: 28px;
        margin-top: 30px;
        display: flex;
      }
      .link{
        color: #3c51f0;
      }
      .ccb_btn{
        width: 93%;
        margin: 0 auto;
        display: flex;
        justify-content: space-between;
        margin-top: 50px;
        .btn{
          width: 280px;
          border-radius: 10px;
          font-size: 30px;
        }
      }
    }
    .privacy_mag{
      width: 600px;
      font-size: 28px;
      padding: 20px;
      margin-top: 80px;
      height: 800px;
      overflow-y: auto;
    }
</style>
