/*
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-05-22 15:22:55
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-09-21 15:31:31
 */
import Vue from 'vue'
import Vuex from 'vuex'

import Index from './modules/index'
import tabbar from './modules/tabbar'
import My from './modules/my'
import market from './modules/market'
import cart from './modules/market/cart'
import mall from './modules/market/mall'
import order from './modules/order'
import school from './modules/school'

import userMap from './modules/userMap'
import login from './modules/login'

import setMail from './modules/setMail'

Vue.use(Vuex)

export default new Vuex.Store({
  state: {
    regionId: 1,
    regionName: '遂昌县',
    isMenuMarket: false,
    location: {
      // longitude: null,
      // latitude: null,
      latitude: 28.592388,
      longitude: 119.275865,
      address: {
        poiName: ''
      }
    },
    searchKey: '', // 搜索关键字
    home: {
      historyKey: [] // 关键字
    },
    scanPay: {
      sum: null,
      payValueElse: '',
      payValue: '',
      receiveId: '',
      payradio: '',
      discount: null,
      coupon: {
        couponId: null
      }
    },
    userId: '',
    userNo: '',
    statusHeight: '',
    bottomBarHeight: '',
    token: '',
    phone: '',
    accountType: 2 // 1-公职 2-社会
  },
  getters: {
    // token
    getToken(state) {
      if (!state.token) {
        state.token = localStorage.getItem('token')
      }
      return state.token
    },
    getRegionId(state) {
      if (!state.regionId) {
        state.regionId = localStorage.getItem('regionId')
        return Number(state.regionId)
      } else if (localStorage.getItem('regionId') != null && state.regionId != localStorage.getItem('regionId')) {
        state.regionId = localStorage.getItem('regionId')
        return Number(state.regionId)
      } else {
        return Number(state.regionId)
      }
    },
    getRegionName(state) {
      if (!state.regionName) {
        state.regionName = localStorage.getItem('regionName')
        return state.regionName
      } else if (localStorage.getItem('regionName') != null && state.regionName != localStorage.getItem('regionName')) {
        state.regionName = localStorage.getItem('regionName')
        return state.regionName
      } else {
        return state.regionName
      }
    },
    getUserId(state) {
      if (!state.token) {
        state.userId = localStorage.getItem('userId')
      }
      return state.userId
    },
    getUserNo(state) {
      if (!state.userNo) {
        state.userNo = localStorage.getItem('userNo')
      }
      return state.userNo
    },
    // 获取定位
    getLocation(state) {
      if (!state.location.latitude && !state.location.longitude) {
        let datas = JSON.parse(localStorage.getItem('location'))
        if (datas != null) {
          state.location.latitude = datas.latitude
          state.location.longitude = datas.longitude
          state.location.address = datas.address
        }
      }
      return state.location
    },
    getStatusHeight(state) { // 获取状态栏高度
      if (!state.statusHeight) {
        state.statusHeight = localStorage.getItem('statusHeight')
      }
      if (state.statusHeight == 0 || state.statusHeight == '' || state.statusHeight == null) {
        return 20
      } else {
        return state.statusHeight
      }
    },
    getNavigationBarHeight(state) { // 获取底部安全区域高度
      if (!state.bottomBarHeight) {
        state.bottomBarHeight = localStorage.getItem('bottomBarHeight')
      }
      if (state.bottomBarHeight == 0 || state.bottomBarHeight == '' || state.bottomBarHeight == null) {
        return 0
      } else {
        console.log(state.bottomBarHeight)
        return state.bottomBarHeight
      }
      // return state.bottomBarHeight
    },
    getPhone(state) {
      if (!state.phone) {
        state.phone = localStorage.getItem('phone')
      }
      return state.phone
    }
  },
  mutations: {
    setToken(state, token) {
      state.token = token
      localStorage.token = token // 同步存储token至localStorage
    },
    // 获取定位
    setLocation(state) {
      AlipayJSBridge.call('LocationMsg', {}, function(result) {
        // eslint-disable-next-line no-prototype-builtins
        let ifTYpe = result.hasOwnProperty('locationMsg')
        if (ifTYpe) {
          let locationdata = JSON.parse(result.locationMsg)
          state.location.latitude = locationdata.latitude
          state.location.longitude = locationdata.longitude
          state.location.address = locationdata.address
          localStorage.location = JSON.stringify(locationdata)
        }
      })
      // state.location.latitude = 28.592388
      // state.location.longitude = 119.275865
      // state.location.address = {
      //   poiName: '遂昌县'
      // }
    },
    setStatusHeight(state) { // 获取状态栏
      AlipayJSBridge.call('StatusHeight', {}, function(result) {
        console.log('--状态栏---')
        console.log(result)

        console.log('--end状态栏---')
        if (result.error == 1) {
          result.statusHeight = 25
        }
        state.statusHeight = result.statusHeight
        localStorage.statusHeight = JSON.stringify(result.statusHeight)
      })
    },
    setNavigationBarHeight(state) { // 获取底部安全区域
      AlipayJSBridge.call('NavigationBarHeight', {}, function(result) {
        console.log('--底部安全区域---')
        console.log(result)
        state.bottomBarHeight = result.navigationBarHeight
        localStorage.bottomBarHeight = JSON.stringify(result.navigationBarHeight)
      })
    },
    // 更新定位
    updateLocation(state, location) {
      state.location.latitude = location.latitude
      state.location.longitude = location.longitude
      localStorage.location = location
    },
    setRegionName(state, regionName) { // 大区
      state.regionName = regionName
      localStorage.regionName = regionName
    },
    setRegionId(state, regionId) { // 大区id
      state.regionId = regionId
      localStorage.regionId = regionId
    },
    setUserId(state, userId) {
      state.userId = userId
      localStorage.userId = userId // 同步存储userId至localStorage
    },
    setUserNo(state, userNo) {
      state.userNo = userNo
      localStorage.userNo = userNo // 同步存储userId至localStorage
    },
    setPhone(state, phone) {
      state.phone = phone
      localStorage.phone = phone // 同步存储phone至localStorage
    }
  },
  actions: {
  },
  modules: {
    Index,
    tabbar,
    My,
    market,
    order,
    userMap,
    school,
    login,
    cart,
    mall,
    setMail
  }
})
