/*
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2021-05-29 13:45:04
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-10-24 17:15:20
 */
export default {
  state: {
    bannerIndex: 0,
    classify: '', // 首页分类icon缓存
    activeTttj: '', // 天天特价
    activeThzq: '', // 特惠专区
    preferred: '', // 优选店铺
    activeTttjLq: '', // 龙泉天天特价
    activeThzqLq: '', // 龙泉特惠专区
    banner: [],
    template: {
      'activityBackgroundThree': '',
      'activityBackgroundTwo': '',
      'activityOne': '',
      'activityThree': '',
      'activityTwo': '',
      'bottomDefaultFour': '',
      'bottomDefaultOne': '',
      'bottomDefaultThree': '',
      'bottomDefaultTwo': '',
      'bottomSelectedFour': '',
      'bottomSelectedOne': '',
      'bottomSelectedThree': '',
      'bottomSelectedTwo': '',
      'iconEight': '',
      'iconFive': '',
      'iconFour': '',
      'iconNine': '',
      'iconOne': '',
      'iconSeven': '',
      'iconSix': '',
      'iconTen': '',
      'iconThree': '',
      'iconTwo': '',
      'isUse': 0,
      'notice': '',
      'regionId': 0,
      'skinName': '',
      'topBackground': '',
      'version': 0
    }
  }
}
