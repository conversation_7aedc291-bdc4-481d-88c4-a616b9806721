/*
 * @Descripttion:
 * @version: 1.0.0
 * @Author: zhaoyu<PERSON>
 * @Date: 2021-05-24 17:42:28
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-06-24 20:38:11
 */
import Maths from '@/utils/math.js'
// 店铺数据
export default {
  state: {
    address: {},
    isBtn: 3,
    isEditAddressId: '',
    showPop: false,
    addressShow: false,
    goodsData: {
      pintuanPrice: 0
    },
    creatForm: {
      delieveryType: 1,
      coupon: ''
    },
    coupon: '',
    skuQuantity: 1,
    payradio: '',
    distributionInfo: [],
    ztData: {},
    isActive: {
      pt: false,
      activityJoinNo: '',
      groupNo: '',
      orderNo: ''
    }
  },
  getters: {
    // 计算购物车总金额 含包装费
    mallCartSumPrice(state) {
      // let result1 = new Maths(state.goodsData.pintuanPrice, state.goodsData.freightFee).sum()
      let result1 = new Maths(state.goodsData.pintuanPrice, state.skuQuantity).multipliedBy()

      // 如果有优惠券
      if (state.coupon) {
        result1 = new Maths(result1, state.coupon.preferentialAmount).minus()
      }

      let result2 = new Maths(result1, state.goodsData.freightFee).sum()
      console.log(result1, result2)
      return state.creatForm.delieveryType === 1 ? result2 : result1
    }
  },
  mutations: {},
  actions: {
  }
}
