/*
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-05-24 17:42:28
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-03-22 17:14:07
 */
// 店铺数据
import NP from 'number-precision'
import BigNumber from 'bignumber.js'
export default {
  namespaced: true,
  state: {
    cartShow: true, // 控制店铺显示与隐藏,
    cartList: [ // 购物车
      {
        shopid: '1',
        goods: []
      }
    ],
    marketData: { // 当前进入店铺的信息
      type: 10,
      marketSn: '',
      marketId: '',
      stationSn: '',
      delieveryType: 1,
      marketName: '',
      adderssrId: '',
      payradio: '', // 支付方式
      balance: '', // 账户余额
      remark: '口味、偏好等要求',
      postFee: 0, // 运费
      coupon: '',
      pic: '',
      address: '',
      distance: '',
      isSelfMention: false,
      map: {
        marketName: '',
        latitude: '',
        longitude: ''
      },
      required: {
        tagId: '',
        isShow: 0
      }
    },
    takeData: {
      checkedAgreement: true,
      selfMentionTime: '',
      userPhone: ''
    },
    isLock: true, // 锁
    photoList: []// 店铺相册
  },
  getters: {
    // 计算购物车总金额 含包装费
    cartSumPrice(state) {
      let sum = 0
      let listDtata = state.cartList[0].goods
      for (let i = 0; i < listDtata.length; i++) {
        sum = BigNumber(sum).plus(Number(listDtata[i].price))
      }
      for (let i = 0; i < listDtata.length; i++) {
        sum = BigNumber(sum).plus(Number(listDtata[i].packPrice))
      }
      return Number(sum)
    },
    cartSumPriceOne(state) { // 计算购物车总金额无其他费用
      let sum = 0
      let listDtata = state.cartList[0].goods
      for (let i = 0; i < listDtata.length; i++) {
        sum = BigNumber(sum).plus(Number(listDtata[i].price))
      }
      return Number(sum)
      // return NP.strip(sum);
    },
    cartSumOriPrice(state) { // 计算购物车总原价
      let sum = 0
      let listDtata = state.cartList[0].goods
      for (let i = 0; i < listDtata.length; i++) {
        sum += NP.minus(Number(listDtata[i].oriPrice), Number(listDtata[i].price))
      }
      return NP.strip(sum)
    },
    cartOriPrice(state) {
      let sum = 0
      let listDtata = state.cartList[0].goods
      for (let i = 0; i < listDtata.length; i++) {
        sum = BigNumber(sum).plus(Number(listDtata[i].oriPrice))
      }
      return Number(sum)
    }
  },
  mutations: {
    // 更新是否显示购物车状态
    updateCartShow(state, status) {
      state.cartShow = status
    }
  },
  actions: {
  }
}
