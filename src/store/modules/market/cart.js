/*
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2021-05-24 17:42:28
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-04-07 14:10:21
 */
// 购物车数据
import Maths from '@/utils/math.js'
export default {
  namespaced: true,
  state: {
    cartData: [
      {
        shopId: '', // 店铺id
        required: '', // 是否存在必点分类
        goodsList: [
          // {
          //   goodsName: '', // 商品名称
          //   goodsId: '', // 商品id
          //   tagTitle: '', // 分类名称
          //   tagId: '', // 分类ID
          //   appendTitle: '', // 标签名称-热销等
          //   appendId: '', // 标签id
          //   sku: { // sku
          //     skuId: '', // skuID
          //     skuName: '', // sku名称
          //     pic: ''// sku图片
          //   },
          //   price: { // 价格
          //     oriPrice: 0, // 原价
          //     price: 0, // 现价
          //     packPrice: 0// 包装费
          //   },
          //   numberOfPackages: '', // 包装份数
          //   cover: '', // 商品主图
          //   stock: '', // 库存
          //   required: '', // 是否必选商品
          //   quantity: { // 购买数量限制
          //     leastCopies: 0, // 起购
          //     max: 0, // 限购--暂时预留
          //     slice: 0
          //   }
          // }
        ]
      }
    ]
  },
  getters: {
    // 计算购物车总金额
    sumPrice(state) {
      let sum = 0
      let data = state.cartData[0].goodsList
      for (let i = 0; i < data.length; i++) {
        sum = new Maths(sum, data[i].price.price).sum()
      }
      return sum
    },
    // 计算包装费：包装费=商品数/包装份数(若有余数+1)*包装价格
    sumPackPrice(state) {
      let sum = 0
      let data = state.cartData[0].goodsList
      let obj = {}
      // 去重
      let arr = data.reduce(function(item2, next) {
        obj[next.skuId] ? '' : obj[next.skuId] = true && item2.push(next)
        return item2
      }, [])
      for (let i = 0; i < arr.length; i++) {
        // 有余数加1
        let slice = badgeNum(arr[i].skuId) % arr[i].numberOfPackages == 0 ? (badgeNum(arr[i].skuId) / arr[i].numberOfPackages) : (Math.floor(badgeNum(arr[i].skuId) / arr[i].numberOfPackages) + 1)
        let slicePrice = new Maths(slice, arr[i].price.packPrice).multipliedBy()
        console.log(slicePrice)
        sum = new Maths(sum, slicePrice).sum()
      }
      function badgeNum(val) {
        let n = 0
        for (let i in data) {
          if (data[i].skuId == val) {
            n++
          }
        }
        return n
      }

      return sum
    },
    // 计算购物车总原价
    cartSumOriPrice(state) {
      let sum = 0
      let data = state.cartData[0].goodsList
      for (let i = 0; i < data.length; i++) {
        sum = new Maths(sum, data[i].price.oriPrice).sum()
      }
      return sum
    },
    // 判断购物车满足逻辑
    cartStatus(state, commit, rootState) {
      // status--0/单点不送----1/未点必需品
      let data = state.cartData[0].goodsList
      // 存在必选分类
      if (rootState.market.marketData.required.isShow == true) {
        let requiredData = []
        for (let i = 0; i < data.length; i++) {
          if (data[i].tagId == rootState.market.marketData.required.tagId) {
            requiredData.push(data[i])
          }
        }
        if (requiredData.length == 0) {
          return 1
        }
      }
      let distributionModeYes = [] // 单点可送商品
      let distributionModeNo = []// 单点不送商品
      for (let i = 0; i < data.length; i++) {
        if (data[i].distributionMode == 1) {
          distributionModeYes.push(data[i])
        }
        if (data[i].distributionMode == 2) {
          distributionModeNo.push(data[i])
        }
      }
      if (distributionModeNo.length > 0 && distributionModeYes.length == 0) {
        return 0
      }
    }
  },
  mutations: {}
}
