/*
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2021-06-02 17:20:10
 * @LastEditors: zhaoyuxin
 * @LastEditTime: 2021-06-09 18:10:21
 */
export default {
  namespaced: true,
  state: {
    nickName: '',
    telephone: '',
    blance: 0,
    headImg: '',
    canteenFlag: false
  },
  getters: {
    getHeadImg(state) { // 用户头像
      if (!state.headImg) {
        state.headImg = localStorage.getItem('headImg')
      }
      return state.headImg
    }
  },
  mutations: {
    setHeadImg(state, headImg) { // 用户头像
      console.log(headImg)
      if (headImg) {
        if (state.headImg !== headImg) {
          state.headImg = headImg
          localStorage.headImg = headImg
        }
      }
    }
  }
}
