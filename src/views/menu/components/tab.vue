<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <PERSON>haoyu<PERSON>
 * @Date: 2021-05-22 16:44:42
 * @LastEditors: 孙立政
 * @LastEditTime: 2021-07-12 15:10:36
-->
<template>
  <div class="home">
    <div class="recommendTitle">做饭灵感</div>

    <ul class="tab">
      <li v-for="(item,index) in list" :key="item.id" class="tab-item" :class="currentIndex==index?'active':''">
        <span :data-current="index" @click="clickIndex(index)">{{ item.name }}</span>
        <div v-show="currentIndex==index" class="tab-item-icon" />
      </li>
    </ul>

    <!-- 列表 -->
    <ul class="menuList">
      <li v-for="(item,index) in menuList" :key="index" @click="goDetail(item.id)">
        <div class="menuListImg">
          <img :src="item.cover" alt="">
        </div>
        <div class="menuListRight">
          <div class="menuListTitle">{{ item.recipeName|ellipsis(8) }}</div>
          <div class="menuListbrief">{{ item.description|ellipsis(28) }}</div>
          <div class="menuListNum">
            人气值
            <van-icon v-for="(lista) in item.difficulty>5?5:item.difficulty" :key="lista" class="menuListtimeHot" name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/menu/hot.png" size="16" />
          </div>
        </div>
      </li>
      <div style="height: 10px" />
    </ul>

    <div style="height: 80px" />
  </div>
</template>

<script>
import { getMenuCate, getMenuList } from '@/api/menu'
export default {
  data() {
    return {
      list: [],
      currentIndex: 0,
      menuList: [],
      query: {
        categoryId: 2,
        lat1: 28.593201,
        lng1: 119.276651,
        page: 1,
        recipeName: '',
        regionId: 1,
        size: 30
      }
    }
  },
  created() {
    this.getMenuCate()
    this.getMenuList()
  },
  methods: {
    // 获取tab
    getMenuCate() {
      getMenuCate().then(res => {
        this.list = res.data
      })
    },
    // 获取菜谱列表
    getMenuList() {
      getMenuList(this.query).then(res => {
        this.menuList = res.data.list
      })
    },
    // 切换tab
    clickIndex(index) {
      let current = index
      if (this.currentIndex == current) {
        return false
      } else {
        this.currentIndex = current
      }

      this.query.categoryId = this.list[index].id

      this.getMenuList()
    },
    // 详情
    goDetail(row) {
      this.$router.push({
        path: 'menuDetails',
        query: {
          id: row
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
    .home {
      width: 710px;
      margin: 0 auto;
        .recommendTitle{
            height: 77px;
            line-height: 77px;
            font-size: 36px;
            font-weight: bold;
            margin-top: 10px;
            font-family: PingFangSC-Medium;
        }
        .tab {
            display: flex;
            height: 85px;
            margin-left: 10px;
            margin-top: 15px;
            color: #333;
            .tab-item{
                font-size: 28px;
                margin-right: 23px;
                // font-family: PingFangSC-Medium;
                font-family: PingFangSC;
            }
            .active {
                font-weight: 600;
                font-size: 32px;
                font-family: PingFangSC-Medium;
            }
            .tab-item-icon {
                width: 45px;
                height: 15px;
                background-image: url(https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/user/classify/tab-active.png);
                background-size: 100%;
                margin: 3px auto 0;
            }
        }
        .menuList{
          background-color: #fff;
          border-radius: 11px;
          padding-top: 33px;
          li {
            width: 664px;
            min-height: 196px;
            margin: 0 auto;
            display: flex;
            margin-bottom: 24px;
            .menuListImg{
              width: 264px;
              height: 196px;
              img {
                width: 264px;
              height: 196px;
                border-radius: 12px;
              }
            }
            .menuListRight{
              margin-left: 24px;
            }
            .menuListTitle{
              font-weight: 500;
              font-size: 32px;
              color: #333333;
              margin-top: 12px;
              font-family: PingFangSC-Medium;
            }
            .menuListbrief{
              font-size: 24px;
              color: #999999;
              margin-top: 10px;
              font-family: PingFangSC;
            }
            .menuListNum{
              font-size: 24px;
              color: #999999;
              margin-top: 20px;
              font-weight: bold;
              font-family: PingFangSC-Medium;
              .menuListtimeHot{
                  position: relative;
                  top: 5px;
                  margin-right: 2px;
                }
            }
          }
        }

    }
</style>
