<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2021-05-28 10:09:01
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-07-08 09:07:23
-->
<template>
  <div class="home">
    <div class="recommend">
      <!-- <div class="recommendTitle">为你推荐</div> -->
      <div class="recommendTitle">为你推荐</div>
      <div class="recommendList">
        <div v-for="(item,index) in menuList" :key="index" class="menuList" @click="goDetail(item.id)">
          <img :src="item.cover" class="menuimg" alt="">
          <div class="menuListTitle">{{ item.recipeName|ellipsis(7) }}</div>
          <div class="menuListtime">人气值
            <van-icon v-for="(list,indexs) in item.difficulty>5?5:item.difficulty" :key="indexs" class="menuListtimeHot" name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/menu/hot.png" size="16" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getMenuList } from '@/api/menu'
export default {
  data() {
    return {
      menuList: [],
      query: {
        categoryId: 2,
        lat1: 28.593201,
        lng1: 119.276651,
        page: 1,
        recipeName: '',
        regionId: 1,
        size: 30
      }
    }
  },
  created() {
    this.getMenuList()
  },
  methods: {
    // 获取菜谱列表
    getMenuList() {
      getMenuList(this.query).then(res => {
        this.menuList = this.getRandomArrayElements(res.data.list, 4)
      })
    },
    // 详情
    goDetail(row) {
      this.$router.push({
        path: 'menuDetails',
        query: {
          id: row
        }
      })
    },
    getRandomArrayElements(arr, count) {
      let shuffled = arr.slice(0); let i = arr.length; let min = i - count; let temp; let index
      while (i-- > min) {
        index = Math.floor((i + 1) * Math.random())
        temp = shuffled[index]
        shuffled[index] = shuffled[i]
        shuffled[i] = temp
      }
      return shuffled.slice(min)
    }
  }
}
</script>

<style lang="scss" scoped>
.home{
    width: 100%;
    height: 630px;
    background: linear-gradient(180deg,#ffffff, #ffffff 94%, #f5f5f5);
    overflow: hidden;
    .recommend{
        width: 710px;
        min-height: 392px;
        margin: 0 auto;
        margin-top: 87px;
        .recommendTitle{
            height: 77px;
            line-height: 77px;
            font-size: 36px;
            font-weight: bold;
            font-family: PingFangSC-Medium;
        }
        .recommendList{
            width: 100%;
            height: 440px;
            background-color: #eee;
            border-radius: 15px;
            background-image: url(https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/menu/bg.png);
            background-size: 100% 100%;
            overflow: hidden;
            display: flex;
            overflow-x: auto;
        }
        .menuList{
            min-width: 272px;
            max-width: 310px;
            height: 384px;
            background: #ffffff;
            border-radius: 13px;
            margin-top: 28px;
            margin-left: 16px;
            flex-shrink: 0;
            .menuimg {
                width: 100%;
                height: 272px;
                border-top-left-radius: 12px;
                border-top-right-radius: 12px;
                vertical-align: middle;
                margin-bottom: 10px;
            }
            .menuListTitle{
                font-size: 28px;
                font-weight: bold;
                color: #333333;
                margin-left: 20px;
                font-family: PingFangSC-Medium;
            }
            .menuListtime{
                color: #a8a8a8;
                font-size: 26px;
                margin-left: 20px;
                // margin-top: 7px;
                font-family: PingFangSC;
                .menuListtimeHot{
                  position: relative;
                  top: 5px;
                  margin-right: 2px;
                }
            }
        }
    }
}

</style>
