<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2021-05-31 15:07:31
 * @LastEditors: 孙立政
 * @LastEditTime: 2021-06-17 11:39:13
-->
<template>
  <div class="details">
    <NavHeight bgc="#fff" />
    <div class="detailsNav" :style="styleVar">
      <div class="detailsNavLeft" @click="goBack"><van-icon class="detailsNavLeftIcon" name="arrow-left" size="25" /></div>
      <div class="detailsNavCenter">{{ data.recipeName }}</div>
      <div class="detailsNavRight" />
    </div>
    <div class="detailsImg">
      <img :src="data.cover" alt="">
    </div>
    <div class="detailsTitle">
      {{ data.recipeName }}
    </div>
    <div class="detailsDifficulty">
      <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/menu/cook.png" alt="">
      难度：易上手
    </div>
    <div class="detailsBrief">
      {{ data.description }}
    </div>
    <div class="detailsFood">
      <div class="listTitle">
        <div class="listTitleBg" />
        <div>主要食材</div>
      </div>
      <div class="detailsFoodList">
        <table>
          <tr v-for="(item, index) in data.ingredients" :key="index">
            <td>{{ item.name }}</td>
            <td>{{ item.number }}g</td>
          </tr>
        </table>
      </div>
    </div>
    <div v-for="(item, index) in data.recipeSteps" :key="index" class="detailsDescription">
      <div class="listTitle">
        <div class="listTitleBg" />
        <div>步骤{{ index + 1 }}/{{ data.recipeSteps.length }}</div>
      </div>
      <img :src="item.image" mode="">
      <div>{{ item.description }}</div>
    </div>
    <div style="height: 80px" />

    <Loading :show="loadingShow" />
  </div>
</template>

<script>
import { getMenuDetail } from '@/api/menu'
import Loading from '@/components/Loading/index'
export default {
  components: {
    Loading
  },
  data() {
    return {
      data: '',
      loadingShow: true
    }
  },
  computed: {
    styleVar() {
      return {
        '---nav-height': this.$store.getters.getStatusHeight + 'px'
      }
    }
  },
  created() {
    this.getDetail()
  },
  methods: {
    // 获取详情
    getDetail() {
      getMenuDetail(this.$route.query.id).then((res) => {
        this.data = res.data
        this.loadingShow = false
      })
    },
    goBack() {
      this.$router.go(-1)
    }
  }
}
</script>

<style lang="scss" scoped>
.details{
    .detailsNav{
        width: 100%;
        height: 77px;
        line-height: 77px;
        display: flex;
        font-size: 36px;
        color: #222222;
        background-color: #fff;
        position: fixed;
        top: var(---nav-height);
        z-index: 2;
        .detailsNavLeft{
            width: 10%;
            text-align: center;
            .detailsNavLeftIcon{
                margin-top: 16px;
            }
        }
        .detailsNavCenter{
            width: 80%;
            text-align: center;
        }
        .detailsNavRight{
            width: 10%;
        }
    }
    .detailsImg{
        width: 100%;
        height: 606px;
        margin-top: 77px;
        img{
            width: 100%;
            height: 606px;
        }
    }
    .detailsTitle{
        width: 669px;
        height: 65px;
        margin: 0 auto;
        font-size: 46px;
        font-family: PingFangSC, PingFangSC-Semibold;
        font-weight: 600;
        color: #333333;
        line-height: 65px;
        margin-top: 40px;
    }
    .detailsDifficulty{
        width: 669px;
        height: 30px;
        margin: 0 auto;
        font-size: 24px;
        color: #333333;
        margin-top: 8px;
        img{
            width: 28px;
            height: 26px;
            position: relative;
            top: 2px;
        }
    }
    .detailsBrief{
        width: 669px;
        margin: 0 auto;
        font-size: 28px;
        line-height: 40px;
        color: #222222;
        margin-top: 20px;
    }
    .detailsFood{
        width: 669px;
        margin: 0 auto;
        margin-top: 32px;
    }
    .detailsFoodList{
        margin-left: 15px;
        margin-top: 20px;
        tr {
            height: 60px;
        }
        td {
            width: 350px;
            color: #333333;
            font-size: 28px;
        }
    }
    .detailsDescription{
        width: 669px;
        margin: 0 auto;
        margin-top: 32px;
        img{
            width: 670px;
            height: 530px;
            border-radius: 11px;
            margin-top: 20px;
        }
        font-size: 28px;
        color: #333333;
        line-height: 40px;
    }
    .listTitle{
        display: flex;
        .listTitleBg{
            width: 8px;
            height: 24px;
            opacity: 1;
            background: #39cf3f;
            margin-right: 10px;
            position: relative;
            top: 7px;
        }
        font-size: 28px;
        font-weight: bold;
    }
}
</style>
