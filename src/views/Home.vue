<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2021-05-22 15:22:43
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-10-25 10:52:29
-->

<template>
  <div class="box">
    <router-view />
    <v-tabbar :items="setItems()" :idx="$store.state.tabbar.index" @change="change" />
  </div>
</template>

<script>
import { getAppMallMenu } from '@/api/market'
import Tabbar from '@/components/Tabbar'
export default {
  components: {
    'v-tabbar': Tabbar
  },
  data() {
    return {
      showIconOrSrc: false,
      items: [
        {
          cls: 'index',
          name: '首页',
          push: '/index',
          src: 'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/tabBar/index.png',
          srcSelect: 'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/tabBar/index-select.png'
        },
        {
          cls: this.$store.state.isMenuMarket == true ? 'market' : 'menu',
          name: this.$store.state.isMenuMarket == true ? '商城' : '发现',
          push: this.$store.state.isMenuMarket == true ? '/market' : '/menu',
          src:
            this.$store.state.isMenuMarket == true ? 'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/tabBar/market.png' : 'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/tabBar/menu.png',
          srcSelect:
            this.$store.state.isMenuMarket == true ? 'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/tabBar/market-select.png' : 'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/tabBar/menu-select.png'
        },
        {
          cla: 'order',
          name: '订单',
          push: '/order',
          src:
            'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/tabBar/order.png',
          srcSelect:
            'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/tabBar/order-select.png'
        },
        {
          cla: 'my',
          name: '我的',
          push: '/my',
          src:
            'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/tabBar/my.png',
          srcSelect:
            'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/tabBar/my--select.png'
        }
      ],
      isMenuMarket: false
    }
  },
  created() {
    // this.getAppMallMenu()
    this.setItems()
  },
  methods: {
    // 检测商城开关
    getAppMallMenu() {
      getAppMallMenu({ poolId: this.$store.getters.getRegionId }).then(res => {
        if (res.status === 200) {
          if (res.data != null) {
            this.$store.state.isMenuMarket = true

            this.items = [
              {
                cls: 'index',
                name: '首页',
                push: '/index',
                src:
            'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/tabBar/index.png',
                srcSelect:
            'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/tabBar/index-select.png'
              },
              {
                cls: 'market',
                name: '商城',
                push: '/market/home',
                src:
            'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/tabBar/marketgif2.gif',
                srcSelect:
            'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/tabBar/marketgif2.gif'
              },
              {
                cla: 'order',
                name: '订单',
                push: '/order',
                src:
            'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/tabBar/order.png',
                srcSelect:
            'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/tabBar/order-select.png'
              },
              {
                cla: 'my',
                name: '我的',
                push: '/my',
                src:
            'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/tabBar/my.png',
                srcSelect:
            'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/tabBar/my--select.png'
              }
            ]
          } else {
            this.$store.state.isMenuMarket = false
            this.items = [
              {
                cls: 'index',
                name: '首页',
                push: '/index',
                src:
            'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/tabBar/index.png',
                srcSelect:
            'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/tabBar/index-select.png'
              },
              {
                cls: 'menu',
                name: '发现',
                push: '/menu',
                src:
            'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/tabBar/menu.png',
                srcSelect:
            'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/tabBar/menu-select.png'
              },
              {
                cla: 'order',
                name: '订单',
                push: '/order',
                src:
            'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/tabBar/order.png',
                srcSelect:
            'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/tabBar/order-select.png'
              },
              {
                cla: 'my',
                name: '我的',
                push: '/my',
                src:
            'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/tabBar/my.png',
                srcSelect:
            'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/tabBar/my--select.png'
              }
            ]
          }
        }
      })
    },
    change(index) {
      if (index === 1 && this.$store.getters.getRegionId === 7) {
        return
      }
      this.$store.state.tabbar.index = index
    },
    goClassify(id) {
      let isTakeaway = id == undefined ? true : false
      this.$router.push({
        path: '/classify',
        query: {
          id: id,
          isTakeaway: isTakeaway
        }
      })
    },
    setItems() {
      if (this.$store.getters.getRegionId === 1) {
        return [
          {
            cls: 'index',
            name: '首页',
            push: '/index',
            src: this.$store.state.Index.template.bottomDefaultOne,
            srcSelect: this.$store.state.Index.template.bottomSelectedOne
          },
          {
            cls: 'market',
            name: '云仓',
            push: '/classify?id=12&name=美食&cateId=12&isTakeaway',
            src: 'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/home/<USER>/tabbar/shangcheng.png',
            srcSelect: 'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/home/<USER>/tabbar/shangcheng.png'
          },
          // {
          //   cls: 'market',
          //   name: '商城',
          //   push: '/market/home',
          //   src:
          //   'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/tabBar/marketgif2.gif',
          //   srcSelect:
          //   'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/tabBar/marketgif2.gif'
          // },
          {
            cla: 'order',
            name: '订单',
            push: '/order',
            src: this.$store.state.Index.template.bottomDefaultThree,
            srcSelect: this.$store.state.Index.template.bottomSelectedThree
          },
          {
            cla: 'my',
            name: '我的',
            push: '/my',
            src: this.$store.state.Index.template.bottomDefaultFour,
            srcSelect: this.$store.state.Index.template.bottomSelectedFour
          }
        ]
      } else {
        return [
          {
            cls: 'index',
            name: '首页',
            push: '/index',
            src: 'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/tabBar/index.png',
            srcSelect: 'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/tabBar/index-select.png'
          },
          {
            cls: 'menu',
            name: '发现',
            push: '/menu',
            src: 'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/tabBar/menu.png',
            srcSelect: 'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/tabBar/menu-select.png'
          },
          {
            cla: 'order',
            name: '订单',
            push: '/order',
            src:
            'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/tabBar/order.png',
            srcSelect:
            'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/tabBar/order-select.png'
          },
          {
            cla: 'my',
            name: '我的',
            push: '/my',
            src:
            'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/tabBar/my.png',
            srcSelect:
            'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/tabBar/my--select.png'
          }
        ]
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.box {
  width: 100%;
  height: 100%;
}
</style>

