<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-05-29 16:32:22
 * @LastEditors: 孙立政
 * @LastEditTime: 2021-12-07 03:52:16
-->
<template>
  <!-- 添加地址 -->
  <div class="addressAdd">
    <!-- 头部 -->
    <NavHeight bgc="#f5f5f5" />
    <!-- 头部 -->
    <Top :message="message" />
    <div class="address-wrap">
      <van-form>
        <van-popover v-model="showPopover" placement="bottom" trigger="" :close-on-click-outside="false">
          <div class="show-popover-css">
            <van-cell v-for="(item,index) in lists" :key="index" :title="item.name" :value="distance(item)+'km'" size="large" :label="typeof item.address == 'object'?'':item.address" @click="setAmap(item)" />
          </div>
          <template #reference>
            <van-field v-model="address.address" label="地址" placeholder="小区/街道/大厦" @focus="focus" />
          </template>
        </van-popover>
        <van-field v-model="address.street" label="门牌号" type="text" maxlength="50" placeholder="例如9幢5401室" />
        <van-field
          v-model="address.username"
          label="联系人"
          type="text"
          maxlength="10"
          placeholder="请填写收货人姓名"
        />
        <van-field
          v-model="address.mobile"
          label="手机号"
          type="number"
          placeholder="请填写手机号"
        />
        <van-field label="设为默认地址" disabled placeholder="">
          <template #button>
            <van-switch
              v-model="address.isDefault"
              size="25"
              active-color="#39cf3f"
              inactive-color="#ccc"
            />
          </template>
        </van-field>
      </van-form>
    </div>

    <div class="btn" @click="confirm">保存</div>
    <Loading :show="loadingShow" />
  </div>
</template>

<script>
import Top from './components/top'
import { newAddress } from '@/api/address'
import Loading from '@/components/Loading/index'
export default {
  components: {
    Top, Loading
  },
  data() {
    return {
      message: '新增收货地址',
      address: {
        street: '',
        username: '',
        mobile: '',
        address: '',
        isDefault: 'false',
        latitude: '', // 维度
        longitude: '', // 经度
        province: '', // 省
        city: '', // 市
        district: '' // 区
      },
      detailtypeid: '',
      keywordsType: 0,
      status: false,
      loadingShow: false,
      showPopover: false,
      lists: [],
      positions: {
        lat: '',
        lng: ''
      }
    }
  },
  watch: {
    address: {
      handler(val, oldVal) {
        if (this.keywordsType == 0) {
          this.getData(val.address)
        }
      },
      deep: true
    },
    lists(val) {
      if (val.length > 0) {
        this.showPopover = true
      }
    }
  },
  created() {
    this.detailtypeid = this.$route.query.detailtypeid
  },
  mounted() {
    this.getGeolocation()
  },
  methods: {
    getData(val) {
      let self = this
      if (val == '') {
        return
      }
      AMap.plugin('AMap.Autocomplete', function() {
        console.log(self.address.city)
        let autoOptions = { city: self.address.city }
        var autoComplete = new AMap.Autocomplete(autoOptions)
        autoComplete.search(val, function(status, result) {
          if (status == 'complete') {
            self.lists = result.tips
          } else {
            self.$toast('未查询到具体地址请检查')
            self.address.address = ''
            self.lists = []
          }
        })
      })
    },
    // 计算距离
    distance(row) {
      let self = this
      var m1 = new AMap.Marker({
        position: new AMap.LngLat(self.positions.lng, self.positions.lat)
      })
      var m2 = new AMap.Marker({
        position: new AMap.LngLat(row.location.lng, row.location.lat)
      })

      var p1 = m1.getPosition()
      var p2 = m2.getPosition()
      var distance = Math.round(p1.distance(p2))
      return (distance / 1000).toFixed(2)
    },
    getMap() {
      this.$router({
        name: 'InputAmap'
      })
    },
    selCancel() {
      this.selShow = false
    },
    // 确认添加地址
    getPois() {
      let self = this
      let data = JSON.parse(JSON.stringify(this.address))
      // data.address = this.address.province + this.address.city + this.address.district + this.address.address + this.address.street
      // data.street = this.address.address + this.address.street
      newAddress(data).then((res) => {
        if (res.status == 200) {
          self.$toast('添加成功')
          setTimeout(() => {
            if (self.status == false) {
              self.$router.go(-1)
            }
          }, 1500)
        } else {
          self.$toast(res.message)
        }
      })
    },
    confirm() {
      this.$throttle(() => {
        this.addNew()
      }, 3000)
    },
    addNew() {
      if (this.address.street.trim() == '') {
        this.$toast('请输入门牌号')
        return false
      }
      if (this.address.username.trim() == '') {
        this.$toast('姓名不能为空')
        return false
      }
      const isEmoji = (str) =>
        /(\ud83c[\udf00-\udfff])|(\ud83d[\udc00-\ude4f\ude80-\udeff])|[\u2600-\u2B55]/g.test(
          str
        )
      if (isEmoji(this.address.username)) {
        this.$toast('姓名含非法参数')
        return false
      }
      if (this.address.mobile.length != 11) {
        this.$toast('手机号不正确')
        return false
      }
      this.getPois()
    },
    // 获取定位
    getGeolocation() {
      let self = this
      this.loadingShow = true
      AlipayJSBridge.call('LocationMsg', {}, function(result) {
        self.loadingShow = false
        // eslint-disable-next-line no-prototype-builtins
        let ifTYpe = result.hasOwnProperty('locationMsg')
        if (ifTYpe) {
          let locationdata = JSON.parse(result.locationMsg)
          self.address.city = locationdata.address.city
          self.positions.lat = locationdata.latitude
          self.positions.lng = locationdata.longitude
        }
      })
    },
    // 获取焦点
    focus() {
      this.keywordsType = 0
      if (this.lists.length > 0) {
        this.showPopover = true
      }
    },
    // 选择地址
    setAmap(row) {
      let self = this
      let location = row.location.lng + ',' + row.location.lat
      var geocoder = new AMap.Geocoder({})
      geocoder.getAddress(location, function(status, result) {
        if (status === 'complete' && result.regeocode) {
          self.address.longitude = row.location.lng
          self.address.latitude = row.location.lat
          self.address.address = row.name
          self.address.province = result.regeocode.addressComponent.province
          self.address.city = result.regeocode.addressComponent.city == '' ? result.regeocode.addressComponent.province : result.regeocode.addressComponent.city
          self.address.district = result.regeocode.addressComponent.district == '' ? result.regeocode.addressComponent.city : result.regeocode.addressComponent.district
          self.keywordsType = 1
          self.showPopover = false
        } else {
          console.error('根据经纬度查询地址失败')
        }
      })
    }
  }
}
</script>

<style>
.van-popover--light{
  left: 10% !important;
}
</style>

<style lang="scss" scoped>
  .content::before {
    // 利用伪元素设置整个页面的背景色
    content: " ";
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    z-index: -100;
    min-height: 100%;
    background-color: #fff;
  }

  .show-popover-css{
    height: 600px;
    overflow-y: auto;
  }

  .addressAdd {
    width: 750px;
    height: 100vh;
    overflow-y: hidden;
    .address-wrap {
      padding-top: 10px;
      .cell1 {
        display: flex;
        padding: 30px 0;
        margin: 0 32px;
        border-bottom: 1px solid #f4f4f4;
        .cell1-name {
          width: 164px;
          margin-right: 24px;
          font-size: 30px;
          color: #222;
          font-family:PingFangSC-Medium;
          font-weight: 500;
        }
        .cell1-address {
          font-size: 30px;
          color: #323233;
          img {
            vertical-align: middle;
          }
        }
        .city {
          font-size: 30px;
          font-family: PingFangSC;
          color: #333333;
        }
      }

      ::v-deep .van-field__label {
        width: 164px;
        color: #222;
        font-size: 30px;
        font-family:PingFangSC-Medium;
        font-weight: 500;
      }

      ::v-deep .van-field__control:disabled {
        color: #b2b2b7;
      }
      ::v-deep .van-field__control {
        font-size:30px;
        font-family: PingFangSC;
        color: #333333;
      }
      input::placeholder {
        color: #999;
      }

      ::v-deep .van-field:nth-of-type(1) {
        ::v-deep .van-icon {
          top: 4px;
        }

        ::v-deep .van-field__body {
          position: relative;

          ::v-deep .van-field__button:nth-of-type(1) {
            position: absolute;
            left: -20px;
            top: 0;
          }
        }
      }

      ::v-deep .van-cell {
        padding: 30px;
        font-size: 30px;
      }
    }

    .btn {
      position: fixed;
      left: 149px;
      bottom: 107px;
      width: 452px;
      height: 84px;
      line-height: 84px;
      text-align: center;
      color: #ffffff;
      font-size: 32px;
      background: linear-gradient(90deg,#40d243, #1fc432);
      border-radius: 8px;
      font-family:PingFangSC-Medium;
      font-weight: 500;
    }

  }
  </style>
