<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-05-29 16:32:22
 * @LastEditors: 孙立政
 * @LastEditTime: 2021-11-29 16:05:44
-->
<template>
  <div class="content">
    <!-- 头部 -->
    <NavHeight bgc="#f5f5f5" />
    <Top :message="message" />
    <div class="address-wrap">
      <div class="address">
        <div
          v-for="(item, index) in addresslist"
          :key="index"
          class="address-cell"
        >
          <div class="cell1">
            <div class="cell1-name">
              <div>
                {{ item.username }}：
                {{ item.mobile }}<span v-if="item.isDefault" class="default">默认</span>
              </div>
              <div class="cell2">
                {{ showAddress(item) }}
                <!-- {{ item.address+item.street }} -->
              </div>
            </div>
            <div class="edit" @click="goEdit(item)" />
          </div>
        </div>
      </div>
    </div>
    <!-- <div class="btnbox">
      <div class="btn" @click="goAdd()">新增收货地址</div>
    </div> -->
    <div style="height:54px" />
    <div class="fixedBottom">
      <div class="addBtn" @click="goAdd">
        <van-icon name="add-o" color="#39CF3F" size="17" style="margin-right:5px" />
        新增收货地址
      </div>
    </div>
    <Loading :show="loadingShow" />
  </div>
</template>

<script>
import Top from './components/top'
import { AddressList } from '@/api/address'
import Loading from '@/components/Loading/index'
// import Kong from '@/components/kong.vue'
export default {
  components: {
    Top,
    Loading
    // Kong
  },
  data() {
    return {
      message: '收货地址',
      addresslist: [],
      loadingShow: true
    }
  },
  mounted() {
    this.getAllAddress()
  },
  methods: {
    getAllAddress() {
      let marketSn = 'SH800430'
      AddressList(marketSn).then((res) => {
        this.loadingShow = false
        if (res.status == 200) {
          this.addresslist = res.data.withinDistance.concat(
            res.data.beyondDistance
          )
        }
      })
    },
    goAdd() {
      this.$router.push({
        name: 'AddressAdd'
      })
    },
    // 地址编辑
    goEdit(item) {
      this.$router.push({
        name: 'AddressEdit',
        query: {
          id: item.id,
          detailtypeid: 0
        }
      })
    },
    // 格式化地址
    showAddress(row) {
      let dz = row.province + row.city + row.district
      let dzLength = dz.length
      let newdz = row.address.slice(0, dzLength)
      if (dz == newdz) {
        return row.address.slice(dzLength)
      } else {
        return row.address + row.street
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.content::before {
  // 利用伪元素设置整个页面的背景色
  content: " ";
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: -100;
  min-height: 100%;
  background-color: #f5f5f5;
}

.content {
  .address-wrap {
    width: 710px;
    margin: auto;
    background-color: #fff;
    margin-bottom: 40px;
    border-radius: 16px;
  }
  .address {
    width: 100%;

    .address-cell {
      margin: 0 32px;
      padding: 32px 0;

      color: #000010;
      font-size: 30px;
      .cell1 {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 34px;
        font-family:PingFangSC-Medium;
        font-weight: 500;
        color: #222222;
        // margin-bottom: 20px;
        .default {
          flex-shrink: 0;
          display: inline-block;
          width: 64px;
          height: 30px;
          line-height: 30px;
          text-align: center;
          background: #fe2828;
          border-radius: 19px;
          margin-left: 16px;
          font-size: 20px;
          font-family:PingFangSC-Medium;
          font-weight: 500;
          color: #ffffff;
          vertical-align: top;
          margin-top: 10px;
        }
        .edit {
          width: 34px;
          height: 34px;
          flex-shrink: 0;
          background-image: url(https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/user/icon/address-edit.png);
          background-repeat: no-repeat;
          background-size: 100% ;
        }
        .cell1-top {
          display: flex;
          // align-items: center;
        }
        .cell2 {
          padding-top: 8px;
          font-size: 32px;
          font-family: PingFangSC;
          font-weight: 400;;
          color: #999999;
          line-height: 45px;
        }
      }
    }
    .address-cell:not(:last-child){
      border-bottom: 1px solid #f4f4f4;
    }
  }
  .btnbox {
    width: 100%;
    height: 180px;
    background-color: #fff;
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
  }
  .btn {
    width: 452px;
    height: 84px;
    line-height: 84px;
    text-align: center;
    color: #ffffff;
    font-size: 32px;
    margin: 30px auto 0;
    background: linear-gradient(45deg, #71d774 0%, #5ecc52 100%);
    border-radius: 12px;
  }
  .warp {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
  }
}
.fixedBottom {
  position: fixed;
  width: 100%;
  height: 127px;
  background-color: #fff;
  bottom: 0px;
  .addBtn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 690px;
    height: 88px;
    background: #f5f6f7;
    border-radius: 7px;
    margin: 10px auto 0;
    font-size: 28px;
    color: #222222;
  }
}
</style>
