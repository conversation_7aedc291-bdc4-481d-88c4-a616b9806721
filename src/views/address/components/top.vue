<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-05-29 16:38:07
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-01-20 18:02:40
-->
<template>
  <div class="content">
    <div class="top">
      <van-nav-bar title="" left-text="" left-arrow>
        <template #title>
          <div>{{ message }}</div>
        </template>
        <template #left>
          <van-icon name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/user/icon/arrow-black.png" size="18" @click="goback" />
        </template>
        <template #right>
          <span v-if="message=='编辑收货地址'" style="font-size:14px" @click="okDelAdr()">删除</span>
        </template>
      </van-nav-bar>
    </div>
    <div class="occupyHeight" />
  </div>
</template>

<script>
export default {
  components: {},
  props: {
    message: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
    }
  },
  mounted() {
  },
  methods: {
    goback() {
      this.$router.go(-1)
    },
    //   删除地址弹框
    okDelAdr() {
      let self = this
      this.$dialog
        .confirm({
          title: '确认删除该地址吗？',
          message: '',
          confirmButtonColor: '#6095f0'
        })
        .then(() => {
          self.$emit('delFunction')
        })
    }
  }
}
</script>

<style lang="scss" scoped>
	.content {
		width: 100%;
		background-color: #f5f5f5;
    .top {
      position: fixed;
      left: 0;
      // top: 0;
      width: 100%;
      background-color: #f5f5f5;
    }
    .occupyHeight {
      height: 92px;
    }
		::v-deep .van-nav-bar__title {
			font-size: 36px;
			color: #222;
      font-family: PingFangSC;
		}

		::v-deep .van-nav-bar {
			background: none;
		}

		::v-deep .van-nav-bar__right {
			font-size:30px;
		}

		::v-deep .van-hairline--bottom {
			border-bottom-width: 0;
		}

		::v-deep .van-hairline--bottom::after {
			border-bottom-width: 0;
		}
	}
</style>
