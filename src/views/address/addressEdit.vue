<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-05-29 16:32:22
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-05-17 16:47:26
-->
<template>
  <div class="addressEdit">
    <!-- 头部 -->
    <NavHeight bgc="#f5f5f5" />
    <Top :message="message" @delFunction="deleteAddress" />
    <div class="address-wrap">
      <van-form>
        <van-popover v-model="showPopover" placement="bottom" trigger="" :close-on-click-outside="false">
          <div class="show-popover-css">
            <van-cell v-for="(item,index) in lists" :key="index" :title="item.name" :value="distance(item)+'km'" size="large" :label="typeof item.address == 'object'?'':item.address" @click="setAmap(item)" />
          </div>
          <template #reference>
            <van-field v-model="address.address" label="地址" placeholder="小区/街道/大厦" @focus="focus" />
          </template>
        </van-popover>
        <van-field
          v-model="address.street"
          label="门牌号"
          type="text"
          maxlength="50"
          placeholder="请填写详细地址  例如9幢5401室"
        />
        <van-field
          v-model="address.username"
          label="联系人"
          type="text"
          maxlength="10"
          placeholder="请填写收货人姓名"
        />
        <van-field
          v-model="address.mobile"
          label="手机号"
          type="number"
          placeholder="请填写手机号"
        />
        <van-field label="设为默认地址" disabled placeholder="">
          <template #button>
            <van-switch
              v-model="address.isDefault"
              size="25"
              active-color="#39cf3f"
              inactive-color="#ccc"
            />
          </template>
        </van-field>
      </van-form>
    </div>

    <div class="btn" @click="confirm">保存</div>

    <Loading :show="loadingShow" />
  </div>
</template>

<script>
import Top from './components/top'
import area from '@/utils/area'
import Loading from '@/components/Loading/index'
import { DetailAdr, updateAddress, delAddress } from '@/api/address'
export default {
  components: {
    Top,
    Loading
  },
  data() {
    return {
      selShow: false,
      message: '编辑收货地址',
      checked: false,
      address: {
        street: '',
        username: '',
        mobile: '',
        address: '',
        isDefault: 'false',
        latitude: '', // 纬度
        longitude: '', // 经度
        province: '', // 省
        city: '', // 市
        district: '' // 区
      },
      address1: '',
      detailtypeid: '',
      keywords: '',
      keyword100: '',
      areaList: area,
      detailId: '',
      clickNum: 0,
      status: false,
      loadingShow: true,
      showPopover: false,
      keywordsType: 0,
      lists: []
    }
  },
  watch: {
    address: {
      handler(val, oldVal) {
        if (this.keywordsType == 0) {
          this.getData(val.address)
        }
      },
      deep: true
    },
    lists(val) {
      if (val.length > 0) {
        console.log(11)
        this.showPopover = true
      }
    }
  },
  created() {
    this.detailtypeid = 0
    this.detailId = this.$route.query.id
  },
  mounted() {
    // 获取当前地址
    this.getDetail()
  },
  methods: {
    getDetail() {
      DetailAdr(this.detailId)
        .then((res) => {
          this.loadingShow = false
          if (res.status == 200) {
            this.address = res.data

            this.keywordsType = 1
            // 兼容老版本
            let dz = res.data.province + res.data.city + res.data.district
            let dzLength = dz.length
            let newdz = res.data.address.slice(0, dzLength)
            if (dz == newdz) {
              this.address.address = res.data.address.slice(dzLength)
            } else {
              this.address.address = res.data.address
            }
          } else {
            this.$toast(res.message)
          }
        })
    },
    selCancel() {
      this.selShow = false
    },
    // 计算距离
    distance(row) {
      let self = this
      var m1 = new AMap.Marker({
        position: new AMap.LngLat(self.$store.state.location.longitude, self.$store.state.location.latitude)
      })
      if (row.id != '') {
        var m2 = new AMap.Marker({
          position: new AMap.LngLat(row.location.lng, row.location.lat)
        })

        var p1 = m1.getPosition()
        var p2 = m2.getPosition()
        var distance = Math.round(p1.distance(p2))
        return (distance / 1000).toFixed(2)
      } else {
        return 0
      }
    },
    //  获取经纬度
    getPois() {
      let self = this
      // 地址编辑保存
      updateAddress(self.address)
        .then((res) => {
          if (res.status == 200) {
            self.$toast('编辑成功')
            if (self.detailtypeid == 0) {
              setTimeout(() => {
                if (self.status == false) {
                  self.$router.go(-1)
                  if (self.$route.query.from == 'market') {
                    self.$store.state.mall.isEditAddressId = self.$route.query.id
                  }
                }
              }, 1500)
            }
          } else {
            self.$toast(res.message)
          }
        })
    },
    confirm() {
      this.$throttle(() => {
        this.addNew()
      }, 3000)
    },
    addNew() {
      if (this.address.street.trim() == '') {
        this.$toast('请输入门牌号')
        return false
      }
      if (this.address.username.trim() == '') {
        this.$toast('姓名不能为空')
        return false
      }
      const isEmoji = (str) =>
        /(\ud83c[\udf00-\udfff])|(\ud83d[\udc00-\ude4f\ude80-\udeff])|[\u2600-\u2B55]/g.test(
          str
        )
      if (isEmoji(this.address.username)) {
        this.$toast('姓名含非法参数')
        return false
      }
      if (this.address.mobile.length != 11) {
        this.$toast('手机号不正确')
        return false
      }
      this.getPois()
    },
    deleteAddress() {
      delAddress(this.detailId)
        .then((res) => {
          if (res.status == 200) {
            this.$toast('删除成功')
            setTimeout(() => {
              this.$router.go(-1)
            }, 1000)
          } else {
            this.$toast(res.message)
          }
        })
    },
    // 获取焦点
    focus() {
      this.keywordsType = 0
      if (this.lists.length > 0) {
        this.showPopover = true
      }
    },
    getData(val) {
      let self = this
      if (val == '') {
        return
      }
      AMap.plugin('AMap.Autocomplete', function() {
        console.log(self.address.city)
        let autoOptions = { city: self.address.city }
        var autoComplete = new AMap.Autocomplete(autoOptions)
        autoComplete.search(val, function(status, result) {
          if (status != 'no_data') {
            self.lists = result.tips
          } else {
            self.$toast('未查询到具体地址请检查')
            self.address.address = ''
            self.lists = []
          }
        })
      })
    },
    // 选择地址
    setAmap(row) {
      let self = this
      let location = row.location.lng + ',' + row.location.lat
      var geocoder = new AMap.Geocoder({})
      geocoder.getAddress(location, function(status, result) {
        if (status === 'complete' && result.regeocode) {
          self.address.longitude = row.location.lng
          self.address.latitude = row.location.lat
          self.address.address = row.name
          self.address.province = result.regeocode.addressComponent.province
          self.address.city = result.regeocode.addressComponent.city == '' ? result.regeocode.addressComponent.province : result.regeocode.addressComponent.city
          self.address.district = result.regeocode.addressComponent.district == '' ? result.regeocode.addressComponent.city : result.regeocode.addressComponent.district
          self.keywordsType = 1
          self.showPopover = false
        } else {
          console.error('根据经纬度查询地址失败')
        }
      })
    }
  }
}
</script>

<style>
.van-popover--light{
  left: 10% !important;
}
</style>

<style lang="scss" scoped>
  .addressEdit::before {
    // 利用伪元素设置整个页面的背景色
    content: " ";
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    z-index: -100;
    min-height: 100%;
    background-color: #fff;
  }

  .show-popover-css{
    height: 600px;
    overflow-y: auto;
  }

  .addressEdit {
    width: 750px;
    height: 100vh;
    overflow-y: hidden;
    .address-wrap {
      padding-top: 10px;
      .cell1 {
        display: flex;
        padding: 30px 0;
        margin: 0 32px;
        border-bottom: 1px solid #f4f4f4;
        .cell1-name {
          width: 164px;
          margin-right: 24px;
          font-size:30px;
          color: #222;
          font-family:PingFangSC-Medium;
          font-weight: 500;
        }
        .cell1-address {
            font-size:30px;
          color: #323233;
          img {
            vertical-align: middle;
          }
        }
        .city {
          font-size: 30px;
          font-family: PingFangSC;
          color: #333333;
          margin-left: 8px;
        }
      }
      ::v-deep .van-field__label {
        color: #222;
        width: 164px;
        font-size: 30px;
        font-family:PingFangSC-Medium;
        font-weight: 500;
      }
      ::v-deep .van-field__control {
        font-size:30px;
        font-family: PingFangSC;
        color: #333333;
      }
      ::v-deep .van-field__control:disabled {
        color: #b2b2b7;
      }

      input::placeholder {
        color: #999;
      }

      ::v-deep .van-field:nth-of-type(1) {
        ::v-deep .van-icon {
          top: 4px;
        }

        ::v-deep .van-field__body:nth-of-type(1) {
          ::v-deep .van-field__button:nth-of-type(1) {
            position: absolute;
            padding-left: 0;
          }
        }
      }

      ::v-deep .van-cell {
        padding: 30px;
        font-size: 30px;
      }
    }

    .btn {
      position: fixed;
      left: 149px;
      bottom: 107px;
      width:452px;
      height: 84px;
      line-height: 84px;
      text-align: center;
      color: #ffffff;
      font-size: 32px;
      background: linear-gradient(90deg,#40d243, #1fc432);
      border-radius: 8px;
      font-family:PingFangSC-Medium;
      font-weight: 500;
    }

  }
  </style>

