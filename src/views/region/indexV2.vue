<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2022-02-11 18:07:32
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-02-15 10:27:36
-->
<template>
  <div class="content">
    <NavHeight bgc="#fff" />
    <div class="region">
      <div class="region-title1">选择服务区域</div>
      <div class="region-title2">使用过程中</div>
      <div class="region-title3">可更改服务城市</div>

      <div class="region-lq" :class="radio == 3?'region-in':''" @click="radioChange(3,'龙泉市')">
        <div>
          <img v-if="radio == 3" src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/region/lq-in.png" alt="">
          <img v-else src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/region/lq.png" alt="">
        </div>
        <div>龙泉市</div>
      </div>
      <div class="region-sc" :class="radio == 1?'region-in':''" @click="radioChange(1,'遂昌县')">
        <div>
          <img v-if="radio == 1" src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/region/sc-in.png" alt="">
          <img v-else src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/region/sc.png" alt="">
        </div>
        <div>遂昌县</div>
      </div>

    </div>
    <Loading :show="loadingShow" />
  </div>
</template>

<script>
import { regionList } from '@/api/index'
import Loading from '@/components/Loading/index'
export default {
  components: { Loading },
  data() {
    return {
      radio: null,
      listdata: [],
      loadingShow: true
    }
  },
  created() {},
  mounted() {
    this.getList()
  },
  methods: {
    getList() {
      let self = this
      regionList()
        .then((res) => {
          this.loadingShow = false
          if (res.status == 200) {
            self.listdata = res.data
          } else {
            self.$toast(res.message)
          }
        })
    },
    radioChange(e, v) {
      this.$store.commit('setRegionName', v)
      this.$store.commit('setRegionId', e)
      this.$router.push('/')
    }
  }
}
</script>
<style lang="scss" scoped>
.content {
    width: 100%;
    height: 100vh;
    background-color: #fff;
    overflow-y: hidden;
    .region{
        font-family: PingFangSC-Medium;
        text-align: center;
        .region-title1{
            font-size: 58px;
            color: #222222;
            margin-top: 162px;
            margin-bottom: 25px;
        }
        .region-title2,.region-title3{
            font-size: 38px;
            color: #666666;
        }
        .region-sc,.region-lq{
            width: 494px;
            height: 304px;
            background: #ffffff;
            border: 2px solid #e8e8e8;
            border-radius: 16px;
            font-size: 52px;
            color: #333;
            text-align: center;
            margin: 0 auto;
            margin-top: 47px;
            img{
                width: 110px;
                height: 110px;
                margin-top: 44px;
            }
        }
        .region-in{
            border: 2px solid #39cf3f;
            box-shadow: 0px 2px 24px 0px rgba(57,207,63,0.12);
            color: #39cf3f;
        }
    }
}
</style>
