<template>
  <div class="content">
    <NavHeight bgc="#fff" />
    <div class="top">
      <van-nav-bar title="选择大区" left-text="返回" left-arrow>
        <template #left>
          <van-icon name="arrow-left" size="21" color="#000" @click="goback" />
        </template>
      </van-nav-bar>
    </div>
    <div class="occupyHeight" />
    <van-radio-group v-model="radio" @change="radioChange">
      <div
        v-for="(item, index) in listdata"
        :key="index"
        class="region-list"
        @click="radioClick(item.id)"
      >
        <van-radio :name="item.id" checked-color="#5dcb4f">{{
          item.regionName | ellipsis(10)
        }}</van-radio>
        <span class="region-name" />
      </div>
    </van-radio-group>
    <Loading :show="loadingShow" />
  </div>
</template>

<script>
import { regionList } from '@/api/index'
import Loading from '@/components/Loading/index'
export default {
  components: { Loading },
  data() {
    return {
      radio: this.$store.getters.getRegionId,
      listdata: [],
      loadingShow: true
    }
  },
  created() {},
  mounted() {
    this.getList()
    // this.$store.state.home.classify = ''
  },
  beforeRouteEnter(to, from, next) {
    console.log(from)
    from.meta.keepAlive = false
    next()
  },
  beforeRouteLeave(to, from, next) {
    // ...
    to.meta.keepAlive = false
    next()
  },
  methods: {
    getList() {
      let self = this
      regionList()
        .then((res) => {
          this.loadingShow = false
          if (res.status == 200) {
            self.listdata = res.data
          } else {
            self.$toast(res.message)
          }
        })
    },
    radioChange(e) {
      const list = this.listdata.filter((item) => item.id == e)
      this.$store.commit('setRegionName', list[0].regionName)
      this.$store.commit('setRegionId', list[0].id)
      setTimeout(() => {
        this.goback()
      }, 500)
    },
    radioClick(id) {
      this.radio = id
    },
    goback() {
      this.$router.go(-1)
    }
  }
}
</script>
<style lang="scss" scoped>
.content {
  .top {
    position: fixed;
    left: 0;
    // top: 0;
    z-index:1;
    width: 100%;
    background: #fff;
  }
  .occupyHeight {
    height: 92px;
  }
  ::v-deep .van-nav-bar__title {
    font-size: 32px;
    font-family: PingFangSC-Medium;
    color: #000010;
  }
  .region-list {
    display: flex;
    width: 100%;
    height: 100px;
    margin-top: 10px;
    background-color: #fff;
    color: #4c4c57;
    font-family: PingFangSC;
    font-size: 30px;
    padding-left: 60px;
    box-sizing: border-box;
    ::v-deep .van-radio__label {
      margin-left: 34px;
    }
  }
}
</style>
