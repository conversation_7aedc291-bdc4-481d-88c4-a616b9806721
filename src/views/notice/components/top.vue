<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: zhaoyu<PERSON>
 * @Date: 2021-06-05 14:53:08
 * @LastEditors: zhaoyuxin
 * @LastEditTime: 2021-07-31 10:41:30
-->
<template>
  <div class="home">
    <div class="statusHeight">
      <div class="contenta">
        <div class="left" @click="goBack">
          <van-icon
            class="back"
            name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/user/icon/arrow-black.png"
            size="17"
          />
        </div>
        <div class="center">
          {{ msg }}
        </div>
        <div class="right" />
      </div>
    </div>
    <div style="height:46px" />
  </div>
</template>

<script>
export default {
  name: 'Home',
  components: {},
  props: {
    msg: {
      type: String,
      default: ''
    }
  },
  data() {
    return {}
  },
  mounted() {},
  methods: {
    goBack() {
      if (this.$router.history.current.name == 'NoticeDetail') {
        this.$router.push({
          name: 'Notice'
        })
      } else {
        this.$router.push('/')
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.home {
  .statusHeight {
    width: 100%;
    position: fixed;
    z-index: 1;
    background-color: #fff;
    .contenta {
      height: 92px;
      line-height:  92px;
      display: flex;
      .left {
        width: 10%;
        .back {
          position: relative;
          top: 0px;
          left: 34px;
        }
      }
      .center {
        width: 80%;
        text-align: center;
        font-size: 36px;
        font-family: PingFangSC;
        color: #222222;
      }
    }
  }
}
</style>
