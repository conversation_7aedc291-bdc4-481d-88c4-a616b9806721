<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-06-05 14:53:08
 * @LastEditors: 孙立政
 * @LastEditTime: 2021-12-13 11:21:36
-->
<template>
  <div class="home">
    <div style="height:10px;background:#f5f5f5" />
    <div class="wrap">
      <div
        v-for="(item, index) in dataList"
        :key="index"
        class="list"
        @click="goDetile(item)"
      >
        <div class="title">
          {{ item.articleName | ellipsis(20) }}
          <!-- 为了进一步提高消费费者的体验平台提供了... -->
        </div>
        <div class="time">
          {{ item.createTime }}
          <!-- 2020-10-28 12:00 -->
        </div>
      </div>
    </div>

    <div v-if="dataList.length == 0" style="background-color: #f3f4f9" />
    <div v-if="dataList.length == 0" class="empty">暂无公告</div>
    <Loading :show="loadingShow" />
  </div>
</template>

<script>
import { getTbTripArticle } from '@/api/notice'
import Loading from '@/components/Loading/index'
export default {
  name: 'Home',
  components: {
    Loading
  },
  data() {
    return {
      dataList: [],
      loadingShow: true
    }
  },
  mounted() {
    this.getTbTripArticle()
  },
  methods: {
    goDetile(row) {
      this.$router.push({
        name: 'NoticeDetail',
        query: {
          id: row.id
        }
      })
    },
    getTbTripArticle() {
      getTbTripArticle(this.$store.getters.getRegionId)
        .then((res) => {
          this.loadingShow = false
          if (res.status == 200) {
            if (res.data != null) {
              this.dataList = res.data || []
            }
          }
        })
    }
  }
}
</script>

<style lang="scss" scoped>
.home {
  background-color: #fff;
  .wrap {
    padding-top: 8px;
    padding-bottom: 50px;
  }
  .list {
    width:710px;
    margin: 0 auto;
    overflow: hidden;
    border-bottom: 1px solid #f5f5f5;
    .title {
      margin-top: 22px;
      font-size: 32px;
      font-family:PingFangSC-Medium;
      font-weight: 500;
      color: #222222;
    }
    .time {
      font-size: 24px;
      font-family: PingFangSC;
      color: #999999;
      margin-top: 6px;
      margin-bottom: 25px;
    }
    .border {
      width: 100%;
      height: 1px;
      background-color: rgba(0, 0, 0, 0.1);
      margin-top: 22px;
    }
  }
  .empty {
    text-align: center;
    margin-top: 380px;
    font-size: 36px;
    font-family: PingFangSC;
    color: #222222;
  }
}
</style>
