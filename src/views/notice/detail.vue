<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: zhaoyu<PERSON>
 * @Date: 2021-06-05 14:53:08
 * @LastEditors: zhaoyuxin
 * @LastEditTime: 2021-07-31 10:56:15
-->
<template>
  <div class="home">
    <NavHeight bgc="#fff" />
    <Top :msg="msg" />

    <div class="body">
      <div class="title">
        {{ dataDetile.articleName }}
      </div>
      <div class="time">
        {{ dataDetile.createTime }}
      </div>

      <div class="html">
        <div v-html="dataDetile.content" />
      </div>
    </div>
    <Loading :show="loadingShow" />
  </div>
</template>

<script>
import Top from './components/top.vue'
import Loading from '@/components/Loading/index'
import { getDetail } from '@/api/notice'
export default {
  name: 'Home',
  components: {
    Top,
    Loading
  },
  data() {
    return {
      msg: '公告详情',
      dataDetile: '',
      loadingShow: true
    }
  },
  mounted() {
    this.getDetail()
  },
  methods: {
    getDetail() {
      getDetail(this.$route.query.id)
        .then((res) => {
          this.loadingShow = false
          if (res.status == 200) {
            this.dataDetile = res.data
          }
        })
    }
  }
}
</script>

<style lang="scss" scoped>
.home {
  .body {
    width: 708px;
    margin: 0 auto;
    margin-top: 88px;
    overflow: hidden;
    .title {
      margin-top: 32px;
      font-size: 40px;
      font-family: PingFangSC-Medium;
      font-weight: 500;
      color: #222222;
      line-height: 56px;
    }
    .time {
      margin-top: 6px;
      font-size: 24px;
      font-family: PingFangSC;
      color: #999999;
    }
    .html {
      margin-top: 40px;
    }
  }
}
</style>
