<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2021-06-15 10:38:10
 * @LastEditors: zhaoyuxin
 * @LastEditTime: 2021-07-31 10:42:56
-->
<template>
  <div class="home">
    <NavHeight bgc="#fff" />
    <Top :msg="msg" />
    <List />
  </div>
</template>

<script>
import Top from './components/top.vue'
import List from './components/list.vue'
export default {
  name: 'Home',
  components: { Top, List },
  data() {
    return {
      msg: '公告列表'
    }
  },
  mounted() {

  },
  methods: {

  }
}
</script>

<style lang="scss" scoped>
  .home::before {
  // 利用伪元素设置整个页面的背景色
  content: " ";
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: -100;
  min-height: 100%;
  background-color: #fff;
}
</style>
