<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2021-07-30 10:00:46
 * @LastEditors: 孙立政
 * @LastEditTime: 2023-07-28 10:04:58
-->
<template>
  <div class="home">
    <Top path="info" />
    <div class="occpyHeight" :style="styleVar" />
    <div class="body" :style="styleVar">
      <div class="nav">
        - 欢迎来到智慧校园系统 -
      </div>

      <div class="people">
        <div class="left">
          <img v-if="$store.state.school.hik.personPhoto!=null" :src="$store.state.school.hik.personPhoto.pictureUrl" alt="">
          <img v-else class="default" src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/school/avtor.png" alt="">
          <!-- <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/share/pic.jfif?x-oss-process=image/resize,w_700/format,jpg/quality,q_85" alt=""> -->
        </div>
        <div class="center">
          <div>
            <span class="center-left">姓名：</span>
            <span class="center-right"> {{ $store.state.school.hik.personName }}</span>
          </div>
          <div>
            <span class="center-left">学校：</span>
            <span class="center-right"> {{ $store.state.school.hik.orgPathName }}</span>
          </div>
          <div>
            <span class="center-left">班级：</span>
            <span class="center-right">
              {{ $store.state.school.hik.stuGrade?$store.state.school.hik.stuGrade+'级':'' }}{{ $store.state.school.hik.stuClass?$store.state.school.hik.stuClass+'班':'' }}
            </span>
          </div>
          <div>
            <span class="center-left">余额：</span>
            <span class="center-right"> ￥{{ $store.state.school.balance }}</span>
          </div>
        </div>
      </div>

      <div class="btn">
        <div class="left" @click="goPay">
          充值
        </div>
        <div class="right" @click="goRefund">
          退款
        </div>
      </div>

      <div class="logList">
        <div class="logListTop1">
          <div class="tabLeft" :class="tabIndex == 0?'tabIn':''" @click="tabIndex = 0">
            消费记录
          </div>
          <div class="tabRight" :class="tabIndex == 1?'tabIn':''" @click="tabIndex = 1">
            充值记录
          </div>
        </div>

        <div class="logItem">
          <div v-if="tabIndex == 0" class="times">
            <div class="leftTime" @click="getTime(1)">
              <span v-if="!query.startTime"><van-icon name="clock-o" class="icon" />开始时间</span>
              {{ query.startTime }}
            </div>
            <div class="centTime">至</div>
            <div class="rightTime" @click="getTime(2)">
              <span v-if="!query.endTime"><van-icon name="clock-o" class="icon" />结束时间</span>
              {{ query.endTime }}
            </div>
          </div>
          <div v-if="tabIndex == 0" class="tips">(*每次最多选择/显示 <span style="color: red;">30天</span> )</div>
          <van-list v-model="loading" :finished="finished" finished-text="~ 到底啦 ~" @load="onLoad">
            <ul>
              <li v-for="(item,index) in list" :key="index">
                <div class="left">
                  <img v-if="tabIndex == 0&&item.transactionType == 1" src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/school/listicon.png" alt="">
                  <img v-if="tabIndex == 1" src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/school/listiconfood.png" alt="">
                  <img v-if="tabIndex == 0&&item.transactionType == 2" src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/school/tuikuan.png" alt="">
                </div>
                <div class="center">
                  <div class="shopName">
                    <div>
                      {{ tabIndex == 1?'余额充值':item.marketName }}
                    </div>
                    <div class="right">
                      <span v-if="tabIndex == 1"> + </span>
                      <span v-if="tabIndex == 0&&item.transactionType == 1"> - </span>
                      <span v-if="tabIndex == 0&&item.transactionType == 2"> + </span>
                      ￥{{ item.tradeAmount }}
                    </div>
                  </div>
                  <div class="no">
                    <span v-if="tabIndex == 1">交易单号：{{ item.orderNo }}</span>
                    <span v-if="tabIndex == 0&&item.transactionType == 1">消费</span>
                    <span v-if="tabIndex == 0&&item.transactionType == 2">退款</span>
                  </div>
                  <div class="time">
                    <span v-if="tabIndex == 0">交易时间：</span>
                    <span v-if="tabIndex == 1">充值时间：</span>
                    {{ item.createTime }}
                  </div>
                </div>
              </li>
            </ul>
            <van-empty v-if="list==null||list.length==0" description="" />
          </van-list>
        </div>
      </div>
    </div>

    <van-popup v-model="showpicker" position="bottom">
      <van-datetime-picker v-model="currentDate" type="date" title="选择年月日" @cancel="showpicker = false" @confirm="setTime" />
    </van-popup>

  </div>
</template>

<script>
import { formatDate } from '@/utils/date'
import Top from './components/top'
import { getFlowHik, getRechargeRecordHik } from '@/api/school'
export default {
  components: {
    Top
  },
  data() {
    return {
      list: [],
      finished: false,
      loading: false,
      query: {
        page: 0,
        size: 30,
        personId: this.$store.state.school.hik.personId,
        startTime: '',
        endTime: ''
      },
      query2: {
        pageNum: 1,
        pageSize: 1000000,
        search: {
          idCard: this.$store.state.school.hik.certificateNo
        }
      },
      tabIndex: 0,
      showpicker: false,
      currentDate: new Date()
    }
  },
  computed: {
    styleVar() {
      return {
        '---nav-height': this.$store.getters.getStatusHeight + 'px'
      }
    }
  },
  watch: {
    'tabIndex': function(val) {
      this.list = []
      this.query.page = 1
      this.finished = false
      if (val == 1) {
        this.getRechargeRecord()
      } else {
        this.getList()
      }
    }
  },
  created() {
    // 回到顶部
    document.body.scrollTop = 0
    document.documentElement.scrollTop = 0

    this.query.startTime = this.oldTime()[1]
    this.query.endTime = this.oldTime()[0]
  },
  methods: {
    // 获取流水
    onLoad() {
      if (this.tabIndex == 0) {
        this.query.page++
        this.getList()
      }
    },
    // 流水
    getList() {
      this.$toast.loading({ duration: 0, forbidClick: true })
      getFlowHik(this.query).then((res) => {
        this.$toast.clear()
        if (res.status == 200) {
          this.loading = false
          if (res.data.list != null) {
            this.list.push(...res.data.list)
          } else {
            this.finished = true
          }
        } else {
          this.finished = true
          this.$toast(res.message)
        }
      })
    },
    // 充值记录
    getRechargeRecord() {
      this.$toast.loading({
        duration: 0,
        forbidClick: true,
        message: ''
      })
      getRechargeRecordHik(this.query2).then((res) => {
        this.$toast.clear()
        if (res.status == 200) {
          this.loading = false
          this.list = res.data.list
          this.finished = true
        } else {
          this.finished = true
          this.$toast(res.message)
        }
      })
    },
    // 充值
    goPay() {
      if (this.$store.state.school.userType != 2) {
        this.$router.push({
          path: '/school/pay'
        })
      } else {
        this.$router.push({
          path: '/school/pay'
        })
      }
    },
    // 退款
    goRefund() {
      this.$router.push({
        path: '/school/refund',
        query: {
          type: 'hik'
        }
      })
    },
    // 获取时间
    getTime(val) {
      this.showpicker = true
      this.showpickerIndex = val
      if (val == 1) {
        this.currentDate = new Date(this.oldTime()[1])
      } else {
        this.currentDate = new Date()
      }
    },
    setTime(data) {
      if (this.showpickerIndex == 1) {
        this.query.startTime = formatDate(data, 'yyyy-MM-dd')
      } else {
        this.query.endTime = formatDate(data, 'yyyy-MM-dd')
        this.list = []
        this.query.page = 1
      }
      if (this.query.endTime != '' && this.query.startTime != '') {
        this.list = []
        this.query.page = 1
        this.getList()
      }
      this.showpicker = false
    },
    oldTime() {
      var date = new Date()
      var year = date.getFullYear()
      var month = date.getMonth() + 1
      var day = date.getDate()
      var nowDate = year + '-' + (month < 10 ? '0' + month : month) + '-' + (day < 10 ? '0' + day : day)
      var lastDate = new Date(date - 1000 * 60 * 60 * 24 * 30)
      var lastY = lastDate.getFullYear()
      var lastM = lastDate.getMonth() + 1
      var lastD = lastDate.getDate()
      var LDate = lastY + '-' + (lastM < 10 ? '0' + lastM : lastM) + '-' + (lastD < 10 ? '0' + lastD : lastD)
      return [nowDate, LDate]
    },
    // 6月8号到7月31号之间不显示
    isPayBtn() {
      var today = new Date()
      var startDate = new Date(today.getFullYear(), 5, 1)
      var endDate = new Date(today.getFullYear(), 8, 1)
      if (today >= startDate && today <= endDate) {
        return true
      } else {
        return false
      }
    }
  }
}
</script>

<style scoped lang="scss">
    .home {
      .occpyHeight {
        height: 80px;
      }
        .body{
            .nav{
                width: 100%;
                height: 64px;
                font-size: 26px;
                background: #fffbeb;
                line-height: 64px;
                text-align: center;
            }
            .people{
                width: 100%;
                height: 302px;
                background-color: #fff;
                margin-top: 21px;
                display: flex;
                .left{
                    width: 20%;
                    height: 180px;
                    border-radius: 50%;
                    margin-top: 30px;
                    margin-left: 30px;
                    margin-right: 20px;
                    img {
                      width: 190px;
                      height: 280px;
                      border-radius: 3px;
                    }
                    .default{
                      width: 180px;
                      height: 180px;
                    }
                }
                .center{
                    width: 80%;
                    margin-left: 50px;
                    overflow: hidden;
                    div{
                      height: 50px;
                    }
                    .center-left{
                        font-size: 30px;
                        font-family: PingFangSC-Medium;
                        font-weight: 500;
                        color: #333333;
                    }
                    .center-right{
                        font-size: 30px;
                        font-family: PingFangSC;
                        font-weight: 500;
                        color: #333333;
                    }
                    .center-kqjl{
                      width: 110px;
                      height: 38px;
                      float: right;
                      margin-right: 28px;
                      margin-top: 24px;
                    }
                }
            }
            .btn{
              display: flex;
              height: 100px;
              background-color: #fff;
              margin-top: -20px;
              .left{
                width: 160px;
                height: 64px;
                border-radius: 8px;
                line-height: 64px;
                text-align: center;
                color: #fff;
                font-size: 30px;
                margin-left: 250px;
                background: #25d9cd;
              }
              .right{
                width: 160px;
                height: 64px;
                border: 2px solid #ea7575;
                border-radius: 8px;
                line-height: 64px;
                text-align: center;
                color: #ea7575;
                font-size: 30px;
                margin-left: 35px;
              }
              .tips{
                width: 300px;
                font-size: 30px;
                margin-left: 20px;
                color:red;
              }
            }
            .logList{
                width: 710px;
                margin: 0 auto;
                margin-top: 40px;
                .logListTop1{
                    font-family: PingFangSC;
                    width: 100%;
                    max-height: 88px;
                    display: flex;
                    div{
                      width: 50%;
                      height: 86px;
                      line-height: 86px;
                      text-align: center;
                      font-size: 30px;
                      background-color: #F6F6F6;
                      color: #666666;
                      border: 1px solid #e7e7e7;
                    }
                    .tabIn{
                      background: #77D4F8;
                      color: #fff;
                      font-family: PingFangSC-Medium;
                    }
                    .tabLeft{
                      border-top-left-radius: 12px;
                    }
                    .tabRight{
                      border-top-right-radius: 12px;
                    }
                }

                .logItem{
                    height: 60vh;
                    overflow-y: auto;
                    background-color: #fff;
                    box-shadow: 0px 2px 26px 11px rgba(0, 0, 0, 0.04);
                    padding-top: 10px;
                    .times{
                      width: 600px;
                      font-size: 30px;
                      display: flex;
                      margin: 0 auto;
                      line-height: 58px;
                      text-align: center;
                      margin-top: 20px;
                      margin-bottom: 16px;
                      color: #666666;
                      .centTime{
                        width: 100px;
                        height: 58px;
                      }
                      .leftTime,.rightTime{
                        width: 292px;
                        height: 58px;
                        font-size: 28px;
                        background: #f6f6f6;
                        border: 1px solid #e7e7e7;
                        border-radius: 8px;
                        .icon{
                          position: relative;
                          top: 3px;
                          right: 5px;
                        }
                      }

                    }
                    .tips{
                      width: 600px;
                      font-size: 26px;
                      margin: 0 auto;
                      text-align: right;
                      color: #999999;
                      margin-bottom: 16px;
                    }
                    li{
                        width: 90%;
                        height: 175px;
                        margin: 0 auto;
                        display: flex;
                        .left{
                            width: 10%;
                            margin-top: 15px;
                            img{
                                width: 54px;
                                height: 54px;
                            }
                        }
                        .center{
                            width: 90%;
                            font-family: PingFangSC;
                            border-bottom: 1px solid #ececec;
                            margin-left: 10px;
                            .shopName{
                                font-size: 32px;
                                color: #333333;
                                margin-top: 20px;
                                display: flex;
                                justify-content: space-between;
                                 .right{
                                    // margin-top: 30px;
                                    font-size: 32px;
                                    color: #333333;
                                }
                            }
                            .no{
                              font-size: 26px;
                              color: #999999;
                              margin-top: 6px;
                            }
                            .time{
                                color: #666666;
                                font-size: 22px;
                                margin-top: 6px;
                            }
                        }

                    }
                }
            }
        }

    }

</style>
