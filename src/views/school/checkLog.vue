<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2021-09-27 10:32:39
 * @LastEditors: 孙立政
 * @LastEditTime: 2021-10-15 09:31:07
-->
<template>
  <div class="home" :style="styleVar">

    <NavHeight bgc="linear-gradient(273deg,#4eb2ec 2%, #16e9c2 98%)" />
    <div class="top" :class="ifTh?'topfix':''">
      <div class="left" @click="goBack">
        <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/common/arrowlbft.png" alt="">
      </div>
      <div>智慧校园</div>
      <div />
    </div>

    <!-- <Top path="checkLog" /> -->
    <div class="info">
      <div class="user">
        <div>
          <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/logo/logo.jpg" alt="">
        </div>
        <div class="userName">
          {{ $store.state.school.username }}
        </div>
        <div class="userNum" />
      </div>
      <div class="tabList">
        <div :class="tabIndex == 1?'tabIn':''" @click="getTab(1)">本周</div>
        <div :class="tabIndex == 2?'tabIn':''" @click="getTab(2)">近三个月</div>
        <div :class="tabIndex == 0?'tabIn':''" @click="getTab(0)">全部</div>
      </div>
    </div>
    <div class="th" :class="ifTh?'thfix':''">
      <div>日期</div>
      <div>进/出</div>
      <div>时间段</div>
    </div>
    <van-list v-model="loading" :finished="finished" finished-text="~ 到底啦 ~" @load="onLoad">
      <div v-for="(item,index) in tableData" :key="index" class="td" :class="ifTdClor(item)">
        <div class="time">{{ item.accessTime }}</div>
        <div :class="item.entrySign == 1?'out':''">{{ item.entrySign == 1?'进':'出' }}</div>
        <div>
          <span v-if="item.timeSlot==1">上午</span>
          <span v-if="item.timeSlot==2">下午</span>
          <span v-if="item.timeSlot==3">晚上</span>
        </div>
      </div>
    </van-list>
  </div>
</template>

<script>
// import Top from './components/top'
import { selectAttendanceRecord } from '@/api/school'
export default {
  components: {
    // Top
  },
  data() {
    return {
      tabIndex: 1,
      tableData: [],
      ifTh: false,
      loading: false,
      finished: false,
      form: {
        'idCard': this.$store.state.school.idCard,
        'thirdUserId': this.$store.state.school.id,
        'queryTimeType': 1,
        'page': 0,
        'size': 10
      }
    }
  },
  computed: {
    styleVar() {
      return {
        '---nav-height': this.$store.getters.getStatusHeight + 'px'
      }
    }
  },
  mounted() {
    // 回到顶部
    document.body.scrollTop = 0
    document.documentElement.scrollTop = 0
    //   (data%2 ==0) ?"偶数":"奇数"
    window.addEventListener('scroll', this.handleScrollx, true)
  },
  destroyed() {
    window.removeEventListener('scroll', this.handleScrollx, true)
  },
  methods: {
    onLoad() {
      this.form.page++
      this.selectAttendanceRecord()
    },
    selectAttendanceRecord() {
      this.$toast.loading({
        duration: 0, // 持续展示 toast
        forbidClick: true
      })
      selectAttendanceRecord(this.form).then(res => {
        this.$toast.clear()
        if (res.status == 200) {
          this.loading = false
          if (res.data.list != null) {
            this.tableData.push(...res.data.list)
          } else {
            console.log(1)
            this.finished = true
          }
        } else {
          this.finished = true
          this.$toast(res.message)
        }
      })
    },
    // 获取页面滚动距离
    handleScrollx() {
      let top = document.documentElement.scrollTop || document.body.scrollTop || window.pageYOffset
      if (top > 170) {
        this.ifTh = true
      } else {
        this.ifTh = false
      }
    },
    getTab(index) {
      this.tabIndex = index
      this.form.queryTimeType = index
      this.form.page = 1
      this.tableData = []
      this.finished = false
      this.loading = true
      this.selectAttendanceRecord()
    },
    // 判断表格显示颜色
    ifTdClor(row) {
      var dayjs = require('dayjs')
      let data = dayjs(row.accessTime).format('DD')
      if (data % 2 == 0) {
        return 'tdBgColor'
      }
    },
    goBack() {
      this.$router.go(-1)
    }
  }
}
</script>

<style scoped lang="scss">
.home{
    .top {
      width: 100%;
      height: 80px;
      text-align: center;
      line-height: 80px;
      background: linear-gradient(273deg,#4eb2ec 2%, #16e9c2 98%);
      font-size: 36px;
      font-family: PingFangSC-Medium;
      color: #ffffff;
      display: flex;
      div {
          width: 33.5%;
      }
      .left{
          text-align: left;
          img{
              width: 23px;
              height: 35px;
              margin-left: 33px;
              position: relative;
              top: 5px;
          }
      }
    }
    .info{
        width: 100%;
        height: auto;
        background: linear-gradient(273deg,#4eb2ec 2%, #16e9c2 98%);
        .user{
            margin: 0 auto;
            text-align: center;
            font-family: PingFangSC;
            color: #ffffff;
            img{
                width: 120px;
                height: 120px;
                border-radius: 50%;
                margin-top: 50px;
            }
            .userName{
                font-size: 34px;
                margin-top: 15px;
                font-family: PingFangSC-Regular;
            }
            .userNum{
                font-size: 26px;
                margin-top: 22px;
            }
        }
        .tabList{
            width: 100%;
            height: 100px;
            display: flex;
            line-height: 100px;
            font-size: 33px;
            font-family: PingFangSC-Regular;
            margin-top: 5px;
            div{
                width: 33%;
                color: #98E6EB;
                text-align: center;
            }
            .tabIn{
                color: #fff;
                font-size: 34px;
            }
        }
    }
    .thfix{
      width: 100%;
      position: fixed;
      top: calc(80px + var(---nav-height));
      background-color: #f9f9f9;
    }
    .topfix{
      width: 100%;
      position: fixed;
      top: calc(var(---nav-height));
    }
    .th{
        width: 100%;
        height: 85px;
        line-height: 85px;
        text-align: center;
        font-size: 32px;
        font-family: PingFangSC;
        color: #333333;
        background-color: #fff;
        display: flex;
        div{
            width: 33%;
        }
    }
    .td{
        width: 100%;
        min-height: 70px;
        line-height: 70px;
        text-align: center;
        font-size: 28px;
        font-family: PingFangSC;
        color: #666666;
        background-color: #fff;
        display: flex;
        div{
            width: 33%;
        }
        .out{
            color: #40BFE2;
        }
        .time{
            line-height: 50px;
        }

    }
    .tdBgColor{
        background-color: #E9F5FF;
    }
}
</style>
