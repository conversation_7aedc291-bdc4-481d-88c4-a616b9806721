<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-06-03 17:17:18
 * @LastEditors: 孙立政
 * @LastEditTime: 2023-02-01 15:43:28
-->
<template>
  <!-- 为自己充值 -->
  <div class="content">
    <NavHeight bgc="#fff" />
    <!-- 头部 -->
    <van-nav-bar title="" left-text="" left-arrow :border="false">
      <template #title>
        <div>充值</div>
      </template>
      <template #left>
        <van-icon name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/user/icon/arrow-black.png" size="20" @click="goBack" />
      </template>
    </van-nav-bar>
    <div class="line" />
    <div class="pay">
      <div class="title">充值金额</div>
      <div class="inputBorder">
        <van-field
          v-model="amount"
          label=""
          left-icon="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/user/icon/yuan.png"
          placeholder="请输入充值金额"
          clearable
          type="number"
          maxlength="10"
        />
      </div>
    </div>
    <div class="title">选择支付方式.</div>
    <van-radio-group v-model="paymentType">
      <div class="payment">
        <div v-if="payChannel!=2" class="payment-line" @click="paymentType = '1'">
          <div class="line-left">
            <van-icon name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/school/payicon.png" size="23" />
            <span>余额</span>
          </div>
          <div class="line-right">
            <span>￥ {{ userBlance }}</span>
            <van-radio name="1" icon-size="23" checked-color="#5dcb4f" />
          </div>
        </div>
        <!-- 银联支付 -->
        <div v-if="payChannel == 2">
          <div class="payment-line" @click="paymentType = '7'">
            <div class="line-left">
              <van-icon name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/pay/balance.png" size="23px" />
              <span>余额</span>
            </div>
            <div class="line-right">
              <span>{{ userBlance }}元</span>
              <van-radio name="7" icon-size="19px" checked-color="#5dcb4f" />
            </div>
          </div>
        </div>
        <div class="payment-line" @click="paymentType = '3'">
          <div class="line-left">
            <van-icon name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/pay/weixinpay.png" size="23" />
            <span>微信支付</span>
          </div>
          <div class="line-right">
            <van-radio name="3" icon-size="19" checked-color="#5dcb4f" />
          </div>
        </div>
        <div class="payment-line" @click="paymentType = '2'">
          <div class="line-left">
            <van-icon name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/pay/alipay.png" size="23" />
            <span>支付宝支付</span>
          </div>
          <div class="line-right">
            <van-radio name="2" icon-size="19" checked-color="#5dcb4f" />
          </div>
        </div>

        <div v-if="true" class="payment-line" @click="paymentType = '9'">
          <div class="line-left">
            <van-icon name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/shop/goods/unipay.png" size="26" />
            <span>云闪付</span>
          </div>
          <div class="line-right">
            <van-radio name="9" icon-size="19" checked-color="#5dcb4f" />
          </div>
        </div>

        <div v-if="false" class="payment-line" @click="paymentType = '4'">
          <div class="line-left">
            <van-icon name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/pay/dragon.png" size="26" />
            <span>龙支付</span>
          </div>
          <div class="line-right">
            <van-radio name="4" icon-size="19" checked-color="#5dcb4f" />
          </div>
        </div>

      </div>
    </van-radio-group>
    <div class="btn" @click="fastSetOrder">立即充值</div>

    <van-popup v-model="show" round closeable :style="{ width: '309px', height: '315px', }">
      <div class="box">
        <div class="title">输入支付密码</div>
        <div class="price">
          ¥{{ amount }}
        </div>
        <div class="balance">
          <div class="payment-line">
            <div class="line-left">
              <van-icon name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/school/payicon.png" size="23px" />
              <span>账户余额</span>
            </div>
            <div class="line-right">
              <span>{{ userBlance }}元</span>
            </div>
          </div>
          <van-password-input
            :value="payPassword"
            info=""
            :length="6"
            :focused="showKeyboard"
            @focus="showKeyboard = true"
          />
        </div>
      </div>
      <div class="forgetPwd" @click="goPwd">找回密码</div>
    </van-popup>

    <van-number-keyboard
      v-model="payPassword"
      :show="showKeyboard"
      z-index="9000"
      @blur="showKeyboard = false"
    />

    <!-- 验证码弹框 -->
    <van-popup v-model="codeLog" round :style="{ height: '130px', }">
      <div class="popBox">
        <van-field v-model="checkUnionPay.smsCode" center clearable :border="false" label="验证码" placeholder="请输入验证码" />
        <div class="popBtn">
          <div class="cancel" @click="codeLog = false">取消</div>
          <div class="confirm" @click="confirmFinishCheckUnionPay">确认</div>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script>
import { payBlance
} from '@/api/school'
import {
  UserBalance
} from '@/api/my'
import { version } from '@/config/settings'
import { checkInitPwd, payLoading } from '@/api/takeout'
import BigNumber from 'bignumber.js'
import { finishCheckUnionPay, fundAccountQuery } from '@/api/bank'
import { query } from '@/utils/sign'
// var sensors = window['sensorsDataAnalytic201505']
export default {
  data() {
    return {
      content: '是否支付完成？',
      amount: '',
      paymentType: '',
      regionId: '',
      capitalAccountId: '',
      balance: 0,
      payPassword: '',
      show: false,
      showKeyboard: false,
      userBlance: 0,
      orderNo: '',
      payChannel: null,
      codeLog: false,
      checkUnionPay: {
        smsId: '',
        smsCode: '',
        unionPayData: ''
      }
    }
  },
  watch: {
    payPassword(val, old) {
      let self = this
      if (val.length >= 6) {
        this.$throttle(() => {
          self.pay()
        }, 3000)
      }
    },
    'amount': function(newVal, oldVal) {
      if (newVal < 0) {
        this.$toast('输入金额不能为负数！')
        this.parems.paymoney = ''
      }
      this.amount = newVal.toString().match(/^\d*(\.?\d{0,2})/g)[0]
      // 最大值
      if (newVal > **********) {
        this.amount = oldVal
        this.$toast('输入数值太大！')
        return
      }
    },
    paymentType(val) {
      console.log(val)
      if (val == 1 || val == 7) {
        this.checkInitPwd()
      }
    }
  },
  created() {
    this.getUserBlance()
    // this.checkInitPwd()
  },
  methods: {
    // 下单前检查
    fastSetOrder() {
      if (this.paymentType == 7) {
        let data = this.$store.getters.getUserId
        UserBalance(data).then((res) => {
          if (res.status == 200) {
            res.data.map(item => {
              if (item.regionId == 1) {
                if (item.payChannel == 2) {
                  // 检查是否开户成功
                  this.fundAccountQuery()
                } else {
                  this.goPay()
                }
              }
            })
          }
        })
      } else {
        this.goPay()
      }
    },
    // 检查开户
    fundAccountQuery() {
      let queryVO = {
        'bankType': 'UnionPay',
        'userId': this.$store.getters.getUserId
      }
      fundAccountQuery(queryVO).then(res => {
        if (res.data != null) {
          if (res.data.status != 2) {
            this.$dialog.confirm({
              message: '请先完成开户流程！',
              confirmButtonText: '去开通',
              cancelButtonText: '暂不开通'
            }).then(() => {
              this.$router.push('/wallet')
            })
          } else {
            this.goPay()
          }
        } else {
          this.$dialog.confirm({
            message: '请先完成开户流程！',
            confirmButtonText: '去开通',
            cancelButtonText: '暂不开通'
          }).then(() => {
            this.$router.push('/wallet')
          })
        }
      })
    },
    goPay() {
      if (this.amount == '') {
        this.$toast('请输入充值金额')
        return
      } else if (this.paymentType == '') {
        this.$toast('请选择充值方式')
        return
      } else if (this.amount < 0.02) {
        this.$toast('充值金额最低0.02元')
        return
      } else if (this.amount > 99999) {
        this.$toast('充值金额过大请确认')
        return
      }
      if (this.paymentType == 1 || this.paymentType == 7) {
        this.show = true
        this.showKeyboard = true
      } else {
        this.pay()
      }
    },
    pay() {
      let self = this
      let Base64 = require('js-base64').Base64
      var u = navigator.userAgent
      var isiOS = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/) // ios终端
      this.$toast.loading({
        duration: 0,
        forbidClick: true,
        message: '充值'
      })

      // sensors.track('BuyProduct', {
      //   schoolData: 'schoolpay'
      // })

      let data = {
        amount: this.amount,
        paymentType: this.paymentType,
        schoolStatus: this.$store.state.school.schoolStatus,
        terminalSysVer: version,
        thirdIdCard: this.$store.state.school.idCard,
        schoolNo: this.$store.state.school.schoolNo,
        payPassword: Base64.encode(this.payPassword),
        // eslint-disable-next-line no-undef
        clientIp: '127.0.0.1',
        'deviceId': localStorage.getItem('deviceId') ? localStorage.getItem('deviceId') : 'test',
        longitude: this.$store.getters.getLocation.longitude,
        latitude: this.$store.getters.getLocation.latitude
      }

      // sensors.track('BuyProduct', {
      //   schoolData: JSON.stringify(data)
      // })

      if (data.payPassword === '' || data.payPassword === null) {
        delete data.payPassword
      }

      if (data.thirdIdCard === '' || data.thirdIdCard === null) {
        delete data.thirdIdCard
      }

      data.sign = query(data)

      payBlance(data).then((res) => {
        if (res.status == 200) {
          this.$toast.clear()
          if (self.paymentType == 1 || self.paymentType == 7) {
            this.showKeyboard = false
            this.show = false

            // 强校验
            if (self.paymentType == 7 && res.data.thirdPartPayData.resultCode == '00018') {
              this.$toast('交易存在风险，请输入银联验证码进行确认')
              this.checkUnionPay.smsId = res.data.thirdPartPayData.smsId
              this.checkUnionPay.unionPayData = res.data.thirdPartPayData.unionPayData
              this.codeLog = true
              return
            }
            // 交易中
            if (self.paymentType == 7 && res.data.thirdPartPayData.resultCode == '00004') {
              self.orderNo = res.data.orderNo
              const timerccb = window.setInterval(() => {
                self.appPay()
              }, 2000)

              this.$toast.loading({
                duration: 3000,
                forbidClick: true,
                message: '交易处理中...'
              })

              this.$once('hook:beforeDestroy', () => {
                window.clearInterval(timerccb)
              })
              return
            }

            this.$toast('支付成功')

            this.$store.state.school.balance = BigNumber(Number(this.$store.state.school.balance)).plus(Number(this.amount))
            this.getUserBlance()
            this.amount = ''
            this.payPassword = ''
          } else if (self.paymentType == 2) { // 支付宝
            if (!isiOS) {
              AlipayJSBridge.call('IsAvailable', {
                packageName: 'com.eg.android.AlipayGphone'
              }, function(result) {
                if (result.error == 1) {
                  self.$toast({
                    duration: 5000, // 持续展示 toast
                    forbidClick: true,
                    message: 'APP启动失败,请关闭进程重新打开使用'
                  })
                }
                if (result.available == true) {
                  let url = 'alipays://platformapi/startapp?saId=10000007&qrcode=' + res.data.thirdPartPayData.qrUrl
                  window.location.href = url
                  setTimeout(() => {
                    self.okPayMess()
                    self.orderNo = res.data.orderNo
                  }, 2000)
                } else {
                  self.$toast('未安装支付宝')
                }
              })
            } else {
              AlipayJSBridge.call('IsAvailable', {
                packageName: 'alipay://'
              }, function(result) {
                if (result.available == true) {
                  let url = 'alipays://platformapi/startapp?saId=10000007&qrcode=' + res.data.thirdPartPayData.qrUrl
                  window.location.href = url
                  setTimeout(() => {
                    self.okPayMess()
                    self.orderNo = res.data.orderNo
                  }, 2000)
                } else {
                  self.$toast('未安装支付宝')
                }
              })
            }
          } else if (self.paymentType == 3) { // 微信支付
            AlipayJSBridge.call('HsbFun', { orderNo: res.data.thirdPartPayData.pyTrnNo }, function(result) {
              self.$toast('支付中')
              if (result.payResult == 'success') {
                // 支付成功,跳转
                self.$toast('支付成功')
                self.$store.state.school.balance = BigNumber(Number(self.$store.state.school.balance)).plus(Number(self.amount))
                self.getUserBlance()
                self.amount = ''
              } else if (result.payResult == 'failed') {
                // 支付失败跳转
                self.$toast('支付失败')
              }
            })
          } else if (self.paymentType == 9) {
            // 云闪付
            let u = navigator.userAgent
            let isiOS = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/) // ios终端
            if (!isiOS) {
              AlipayJSBridge.call(
                'IsAvailable',
                {
                  // 判断ios和安卓包名
                  packageName: 'com.unionpay'
                },
                function(result) {
                  if (result.available == true) {
                    let url = res.data.thirdPartPayData.qrUrl
                    let newurl = 'upwallet://html/' + url.slice(8)
                    window.location.href = newurl
                    setTimeout(() => {
                      self.okPayMess()
                      self.orderNo = res.data.orderNo
                    }, 2000)
                  } else {
                    self.$toast('未安装云闪付')
                    setTimeout(() => {
                      self.okPayMess()
                    }, 2000)
                    AlipayJSBridge.call('OpenUrlForAPP', { openurl: 'https://wallet.95516.com/s/wl/webV3/activity/yhtzbtoc/html/snsIndex.html?r=6f040ef12df584d1a98ee25aefacf97c&code=' }, function(result) {})
                  }
                }
              )
            } else {
              let url = res.data.thirdPartPayData.qrUrl
              let newurl = 'upwallet://html/' + url.slice(8)
              window.location.href = newurl
              setTimeout(() => {
                self.okPayMess()
                self.orderNo = res.data.orderNo
              }, 2000)
              // AlipayJSBridge.call('IsAvailable',
              //   {
              //     // 判断ios和安卓包名
              //     packageName: 'upwallet://'
              //   },
              //   function(result) {
              //     if (result.available == true) {
              //       let url = res.data.thirdPartPayData.qrUrl
              //       let newurl = 'upwallet://html/' + url.slice(8)
              //       window.location.href = newurl
              //       setTimeout(() => {
              //         self.okPayMess()
              //         self.orderNo = res.data.orderNo
              //       }, 2000)
              //     } else {
              //       self.$toast('未安装云闪付')
              //     }
              //   }
              // )
            }
          }
        } else {
          this.$toast.clear()
          this.payPassword = ''
          this.$toast(res.message)
        }
      })
    },
    goBack() {
      this.$router.go(-1)
    },
    // 获取余额
    getUserBlance() {
      let data = this.$store.getters.getUserId
      UserBalance(data).then((res) => {
        if (res.status == 200) {
          res.data.map(item => {
            if (item.regionId == 1) {
              this.userBlance = item.balance
              this.payChannel = item.payChannel
            }
          })
        }
      })
    },
    //   确认支付提示
    okPayMess() {
      let self = this
      this.$dialog
        .confirm({
          title: '是否支付完成？',
          message: ''
        })
        .then(() => {
          self.appPay()
        })
        .catch(() => {
          self.appPay()
        })
    },
    // 加查支付
    appPay() {
      payLoading(this.orderNo).then(res => {
        if (res.status == 200) {
          if (res.data == true) {
            this.$toast('支付成功')
            this.$store.state.school.balance = BigNumber(Number(this.$store.state.school.balance)).plus(Number(this.amount))
            this.getUserBlance()
            this.amount = ''
          } else {
            this.$toast('支付失败')
          }
        } else {
          this.$toast(res.message)
        }
      })
    },
    checkInitPwd() { // 检查初始支付密码
      let self = this
      checkInitPwd(this.$store.getters.getUserId).then(res => {
        if (res.status == 200) {
          if (res.data == true) {
            self.$dialog.alert({
              message: '当前为初始支付密码，请尽快修改'
            }).then(() => {
              self.$router.push({ path: '/editPayPwd', query: {
                path: 'school'
              }})
            })
          }
        }
      })
    },
    goPwd() {
      this.$router.push('/editPayPwd')
    },
    // 强校验
    confirmFinishCheckUnionPay() {
      finishCheckUnionPay(this.checkUnionPay).then(res => {
        if (res.status == 200) {
          if (res.data.resultCode == '00000') {
            this.$toast('支付成功')
            this.$store.state.school.balance = BigNumber(Number(this.$store.state.school.balance)).plus(Number(this.amount))
            this.getUserBlance()
            this.amount = ''
          } else {
            this.$toast('支付失败')
          }
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
	.content {
        height: 100vh;
		background-color: #fff;
        ::v-deep .van-nav-bar__title {
            font-family:PingFangSC-Medium;
            font-weight: 500;
            color: #222222;
            font-size: 36px;
        }
        .line {
            background-color: #f5f5f5;
            height: 20px;
        }
		.title {
            font-size: 38px;
            font-family:  PingFangSC-Medium;
            font-weight: 500;
            color: #222222;
            margin: 48px 32px;
        }
        .inputBorder {
            width: 686px;
            height: 108px;
            border: 2px solid #333333;
            border-radius: 14px;
            margin:0  auto 16px;
            ::v-deep .van-field {
                // margin: 10px auto 0;
                margin-top: 10px;
                padding: 20px 28px;
            }
            ::v-deep .van-icon{
                font-size: 44px;
            }
            ::v-deep .van-cell__value--alone {
            font-size: 32px;
            }
        }

		::v-deep input::placeholder {
			font-size: 32px;
		}
		.payment {
			margin: -24px 32px 0;
			.payment-line {
				display: flex;
				justify-content: space-between;
				align-items: center;
				font-size: 32px;
				color: #000010;
				// height: 0.5rem;
				// line-height: 0.5rem;
				padding: 34px 0 34px 0;

				.line-left {
					display: flex;
					align-items: center;
					>span {
						margin-left: 24px;
                        color: #222222;
                        font-family: PingFangSC;
					}
				}

				.line-right {
					display: flex;
					align-items: center;

					>span {
						margin-right: 32px;
					}
				}

				::v-deep .van-radio__icon .van-icon {
					border: 1px solid #CCCCCC;
				}
			}
		}

		.btn {
      width: 452px;
      height: 88px;
      background: linear-gradient(90deg,#40d243, #1fc432);
      border-radius: 8px;
      line-height: 84px;
      font-size: 32px;
      font-family: PingFangSC-Medium;
      font-weight: 500;
			text-align: center;
			color: #ffffff;
			margin: 0 auto;
			margin-top: 100px;
		}
    .van-popup--center{
      top: 33%;
    }
    .box {
			.title {
				width: 100%;
				box-sizing: border-box;
				text-align: center;
				font-size: 34px;
				color: #222;
        margin-top: 56px;
        margin-bottom: 48px;
			}
      .price {
        text-align:center;
        font-size:60px;
        margin-bottom:60px;
      }
			.balance {
				margin: 0 54px;
				.payment-line {
					display: flex;
					justify-content: space-between;
					align-items: center;
					font-size: 34px;
					color: #222;
          margin-bottom: 47px;
					.line-left {
						display: flex;
						align-items: center;

						>span {
              font-size: 34px;
							margin-left: 10px;
						}
					}

					.line-right {
            color: #999;
            font-size: 32px;
						display: flex;
						align-items: center;
					}
				}
			}
		}
    .forgetPwd {
      color: #39CF3F;
			text-align: right;
			margin-right: 54px;
			font-size: 28px;
			margin-top: 32px;
		}

	}

    .popBox {
  position: relative;
  width: 561px;
  height: 32px;
  padding-top:48px;
  box-sizing: border-box;

  .title {
    color: #7f7f87;
    font-size: 30px;
    padding-left: 42px;
  }

  ::v-deep .van-field__label{
    width: 100px;
    margin-right: 0;
  }
}
.popBtn {
  position: fixed;
  left: 0;
  bottom: 0px;
  display: flex;
  width: 561px;
  height: 97px;
  line-height: 97px;
  font-size: 32px;
  justify-content: space-between;
  border-top: 1px solid #cfcece;
  text-align: center;
  .cancel {
    width: 50%;
    border-right: 1px solid #cfcece;
    color: #999999;
  }
  .confirm {
    width: 50%;
    color: #6095f0;
  }
  }
</style>
