<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2021-07-30 11:09:08
 * @LastEditors: 孙立政
 * @LastEditTime: 2023-05-19 15:05:57
-->
<template>
  <div class="home">
    <NavHeight bgc="linear-gradient(273deg,#4eb2ec 2%, #16e9c2 98%)" />
    <div class="top" :style="styleVar">
      <div class="left" @click="goBack">
        <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/common/arrowlbft.png" alt="">
      </div>
      <div>智慧校园</div>
      <div />
    </div>
  </div>
</template>

<script>
import { getBindByUserId, getBindByUserIdHik } from '@/api/school'
export default {
  props: {
    path: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      schoolList: []
    }
  },
  computed: {
    styleVar() {
      return {
        '---nav-height': this.$store.getters.getStatusHeight + 'px'
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    goBack() {
      console.log(this.path)
      if (this.path == 'list') {
        this.$router.push('/index')
      } else if (this.path == 'info') {
        if (this.schoolList.length == 1) {
          this.$router.push('/index')
        } else {
          this.$router.push('/school/list')
        }
      } else if (this.path == 'checkLog') {
        this.$router.go(-1)
      } else if (this.path == 'refund') {
        this.$router.go(-1)
      } else {
        this.$router.go(-1)
      }
    },
    // 获取绑定列表
    getList() {
      getBindByUserId().then(res => {
        if (res.data != null) {
          this.schoolList.push(...res.data)
        }
      })
      getBindByUserIdHik().then(res => {
        if (res.data != null) {
          this.schoolList.push(...res.data)
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
    .home {
        .top {
            width: 100%;
            height: 80px;
            text-align: center;
            line-height: 80px;
            background: linear-gradient(273deg,#4eb2ec 2%, #16e9c2 98%);
            font-size: 36px;
            font-family: PingFangSC-Medium;
            color: #ffffff;
            display: flex;
            position: fixed;
            top:calc(var(---nav-height));
            z-index: 9;
            div {
                width: 33.5%;
            }
            .left{
                text-align: left;
                img{
                    width: 23px;
                    height: 35px;
                    margin-left: 33px;
                    position: relative;
                    top: 5px;
                }
            }
        }
    }
</style>
