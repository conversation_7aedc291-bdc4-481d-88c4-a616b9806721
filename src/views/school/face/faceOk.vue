<template>
  <div class="photohome">
    <NavHeight bgc="#fff" />
    <div class="top">
      <van-nav-bar title="" left-text="" left-arrow>
        <template #left>
          <van-icon
            name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/user/icon/arrow-black.png"
            size="18"
            @click="goBack"
          />
        </template>
      </van-nav-bar>
    </div>
    <!-- 头像 -->
    <div class="face_icon">
      <img :src="$store.state.school.photo" alt="">
    </div>

    <div class="info">
      <div class="h1">
        个人信息
      </div>
      <div style="margin-top: .2rem;">
        <van-field label="姓名" :value="$store.state.school.username" readonly />
      </div>
    </div>

    <div class="info">
      <div class="h1">
        主要作用
      </div>
      <div class="h2">
        使用人脸在食堂付款
      </div>
    </div>

    <!-- 上传验证 -->
    <div class="upload" @click="goFace">
      <div class="btn">
        重新上传
      </div>
    </div>

  </div>
</template>

<script>
export default {
  data() {
    return {}
  },
  created() {
  },
  methods: {
    goFace() {
      this.$router.push('/school/face')
    },
    goBack() {
      this.$router.go(-1)
    }
  }
}
</script>

<style lang="scss" scoped>

	.photohome {
		height: 100%;
		background-color: #fff;
		.top_face {
			text-align: center;

			.h1 {
				font-weight: bold;
				font-size: 0.28rem;
				margin-top: 0.3rem;
			}

			.h2 {
				font-size: 0.08rem;
				color: #8e8e8e;
				margin-top: 0.08rem;
			}
		}

		.face_icon {
			text-align: center;

			img {
				width: 2.56rem;
				height: 3.2rem;
			}

			margin-top: 1rem;
		}

		.info {
			width: 80%;
			margin: 0 auto;
			margin-top: 50px;

			.h1 {
				font-size: 0.25rem;
				font-weight: bold;
			}

			.h2 {
				width: 90%;
				margin: 0 auto;
				font-size: 0.2rem;
				margin-top: 20px;
			}
		}

		.info_img {
			width: 80%;
			margin: 0 auto;
			display: flex;
			justify-content: space-between;

			img {
				width: 1.1rem;
				height: 1.4rem;
			}

			margin-top: 0.5rem;
		}

		.upload {
			width: 500px;
			margin: 0 auto;
			margin-top: 160px;

			.btn {
				width: 500px;
				height: 80px;
				background-color: #68d266;
				text-align: center;
				line-height: 80px;
				color: #fff;
				border-radius: 0.4rem;
				font-size: 30px;
			}
		}
	}
</style>
