<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2021-06-03 10:31:43
 * @LastEditors: zhaoyuxin
 * @LastEditTime: 2021-06-09 18:00:13
-->
<template>
  <div class="home">
    <NavHeight bgc="#fff" />
    <div class="top">
      <van-nav-bar title="" left-text="" left-arrow>
        <template #left>
          <van-icon
            name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/user/icon/arrow-black.png"
            size="18"
            @click="goBack"
          />
        </template>
      </van-nav-bar>
    </div>
    <div class="body">
      <div>
        <van-icon name="passed" size="80" />
      </div>
      <div>
        <span class="reMsg">验证成功</span>
      </div>
    </div>
    <div class="btn" @click="goBack">
      完成
    </div>

    <!-- 失败 -->
    <!-- <div class="bodyErr">
			<div>
				<van-icon name="close" size="80" />
			</div>
			<div>
				<span class="reMsg">验证失败</span>
			</div>
			<div>
				提交照片不符合审核标准，请重试！
			</div>
		</div> -->
    <!-- <div class="btn">
			重新上传
		</div> -->

  </div>
</template>

<script>
export default {
  components: {
  },
  data() {
    return {
      message: '人脸录入'
    }
  },
  created() {},
  methods: {
    goBack() {
      this.$router.push('/My')
    }
  }
}
</script>
<style lang="scss" scoped>
	.home::before {
	// 利用伪元素设置整个页面的背景色
	content: " ";
	position: absolute;
	left: 0;
	right: 0;
	top: 0;
	bottom: 0;
	z-index: -100;
	min-height: 100%;
	background-color: #fff;
	}
	.home {
		overflow: hidden;
		.body{
			text-align: center;
			margin-top: 180px;
			color: #68D266;
			.reMsg{
				font-weight: bold;
				font-size: .38rem;
			}
		}
		.bodyErr{
			text-align: center;
			margin-top: 1rem;
			color: #D42D00;
			.reMsg{
				font-weight: bold;
				font-size: .38rem;
			}
			div{
				margin-bottom: .3rem;
			}
		}
		.btn {
			width: 452px;
			height: 88px;
			margin: 0 auto;
			margin-top: 200px;
			line-height: 88px;
			background: linear-gradient(90deg,#40d243, #1fc432);
			text-align: center;
			color: #fff;
			border-radius: 8px;
			font-size: 32px;
			font-family: PingFangSC-Medium;
			font-weight: 500;
		}
	}
</style>
