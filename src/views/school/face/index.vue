<template>
  <div class="photohome">
    <NavHeight bgc="#fff" />
    <div class="top">
      <van-nav-bar title="" left-text="" left-arrow>
        <template #left>
          <van-icon
            name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/user/icon/arrow-black.png"
            size="18"
            @click="goBack"
          />
        </template>
      </van-nav-bar>
    </div>
    <!-- 确认 -->
    <div class="top_face">
      <div class="h1">
        确认为本人操作
      </div>
    </div>
    <!-- 头像 -->
    <div class="face_icon">
      <img :src="defaults" alt="">
    </div>

    <div class="info">
      <div class="h1">
        注意事项：
      </div>
      <div class="h2">
        1、选择前置摄像保持正脸在取景框内
      </div>
      <div class="h2">
        2、为了您的账户资金安全，请确保为本人进行安全验证
      </div>
      <div class="h2" style="color: red;">
        3、点击开始验证，选择相机前置摄像头，将脸部全部置于屏幕内，确保脸部清晰无遮挡。
      </div>
    </div>

    <div class="info_img">
      <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/user/face/icon1.png" alt="">
      <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/user/face/icon2.png" alt="">
      <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/user/face/icon3.png" alt="">
    </div>

    <!-- 上传验证 -->
    <div class="upload">
      <van-uploader v-if="false" v-model="fileList" :before-delete="afterDelete" :before-read="beforeRead" max-count="1" accept="image/*" capture="camera">
        <div class="btn">
          开始验证
        </div>
      </van-uploader>

      <div class="btn">
        开始验证
      </div>
      <h5-cropper ref="cropper" class="upbtn" :option="option" @getbase64Data="getbase64Data" />
    </div>
  </div>
</template>

<script>
import H5Cropper from '@/components/CropperH5/cropper.vue'
// import H5Cropper from 'vue-cropper-h5'
import OSS from 'ali-oss'
// import {
//   upload
// } from '@/api/my'
import {
  checkFace
} from '@/api/my/face'
import {
  uploads
} from '@/api/school'
export default {
  components: { H5Cropper },
  data() {
    return {
      message: '人脸录入',
      fileList: [],
      type: '',
      file: '',
      maxSize: 2 * 1024,
      minSize: 10,
      defaults: 'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/user/face/usericon.png',
      option: { // 配置
        autoCropWidth: '400',
        autoCropHeight: '400',
        full: true
      }
    }
  },
  mounted() {
    // 回到顶部
    document.body.scrollTop = 0
    document.documentElement.scrollTop = 0
  },
  methods: {
    getbase64Data(data) {
      this.compress(data)
    },
    // 上传入库
    uploads(row) {
      this.$toast.loading({
        duration: 0, // 持续展示 toast
        forbidClick: true,
        message: '识别中'
      })
      let data = {
        'userId': this.$store.state.school.id,
        faceUrl: row
      }
      uploads(data).then((res) => {
        this.$toast.clear()
        if (res.status == 200) {
          this.$toast('人脸识别成功')
          this.$store.state.school.photo = row
          this.$router.push('/school/info')
        } else {
          this.$toast(res.message)
        }
      })
    },
    // 校验
    checkFace(row) {
      this.$toast.loading({
        duration: 0, // 持续展示 toast
        forbidClick: true,
        message: '识别中'
      })
      let data = {
        faceUrl: row
      }
      checkFace(data).then((res) => {
        this.$toast.clear()
        if (res.status == 200) {
          this.uploads(row)
        } else {
          this.$toast(res.message)
        }
      })
    },
    // 图片上传接口
    afterRead(file) {
      let self = this
      this.$toast.loading({
        duration: 0, // 持续展示 toast
        forbidClick: true,
        message: '上传中'
      })
      let formData = new FormData()
      formData.append('file', file)

      let client = new OSS({
        region: 'oss-cn-hangzhou',
        accessKeyId: 'LTAI4GHzemXAwKszXKiniT9s',
        accessKeySecret: '******************************',
        bucket: 'diandi-video'
      })
      let now = Date.parse(new Date())
      let names = now + file.name
      client.put('face/' + names, file)
        .then(function(res) {
          self.$toast.clear()
          self.checkFace(res.url)
          return false
        })
        .catch(function(err) {
          this.$toast.clear()
          console.log(err)
        })
    },
    beforeRead(file) {
      // console.log(file)
      this.type = file.type
      let size = file.size / 1024
      // 如果图片小于300k 提示图片质量不佳，大于3M进行压缩，
      if (size <= this.minSize) {
        this.$toast('图片质量不佳，请重新上传！')
        return false
      } else if (size <= this.maxSize) {
        // 直接上传
        // this.afterRead(file)
        this.imgPreview(file)
      } else {
        // 对图片进行压缩
        this.imgPreview(file)
      }
    },
    // 将图片转成 base64 格式
    imgPreview(file) {
      let self = this
      // 看支持不支持FileReader
      if (!file || !window.FileReader) return
      let reader = new FileReader()
      reader.readAsDataURL(file)
      reader.onloadend = function() {
        // 此处的this是reader
        let result = this.result
        self.compress(result)
      }
    },
    // 压缩图片
    compress(base64) {
      let cvs = document.createElement('canvas')
      let img = document.createElement('img')
      img.crossOrigin = 'anonymous'
      img.src = base64
      // 图片偏移值
      // eslint-disable-next-line no-unused-vars
      let offetX = 0
      img.onload = () => {
        if (img.width > 400) {
          // cvs.width = 800
          // cvs.height = (img.height * 800) / img.width
          cvs.width = 400
          cvs.height = 400
          offetX = (img.width - 800) / 2
        } else {
          cvs.width = img.width
          cvs.height = img.height
        }
        // eslint-disable-next-line no-unused-vars
        const ctx = cvs.getContext('2d').drawImage(img, 0, 0, cvs.width, cvs.height)
        let imageData = cvs.toDataURL(this.type, 0.85)
        this.convertBase64UrlToBlob(imageData)
      }
    },
    // 将base64转为文件流
    convertBase64UrlToBlob(imageData) {
      let filename = ''
      let arr = imageData.split(',')
      let mime = arr[0].match(/:(.*?);/)[1]
      if (!filename) {
        filename = `${new Date().getTime()}.${mime.substr(mime.indexOf('/') + 1)}`
      }
      let bstr = atob(arr[1])
      let n = bstr.length
      let u8arr = new Uint8Array(n)
      while (n--) {
        u8arr[n] = bstr.charCodeAt(n)
      }
      this.afterRead(new File([u8arr], filename, {
        type: mime
      }))
    },
    // 删除之后的回调
    async afterDelete(file) {
      console.log('del' + JSON.stringify(file))
      return new Promise((resolve, reject) => {
        resolve()
      })
    },
    goBack() {
      this.$router.go(-1)
    }
  }
}
</script>

<style lang="scss" scoped>
    .photohome::before {
  // 利用伪元素设置整个页面的背景色
  content: " ";
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: -100;
  min-height: 100%;
  background-color: #fff;
}
	.photohome {
		.top_face {
			text-align: center;
      overflow: hidden;

			.h1 {
				font-weight: bold;
				font-size: 28px;
        margin-top: 20px;
			}

			.h2 {
				font-size: 0.08rem;
				color: #8e8e8e;
				margin-top: 0.08rem;
			}
		}

		.face_icon {
			text-align: center;

			img {
				width: 2.56rem;
				height: 3.2rem;
			}

			margin-top: 1rem;
		}

		.info {
			width: 80%;
			margin: 0 auto;
			margin-top: 1rem;

			.h1 {
				font-size: 0.25rem;
				font-weight: bold;
			}

			.h2 {
				width: 90%;
				margin: 0 auto;
				font-size: 0.2rem;
				margin-top: 0.05rem;
			}
		}

		.info_img {
			width: 80%;
			margin: 0 auto;
			display: flex;
			justify-content: space-between;

			img {
				width: 100px;
				height: 130px;
			}

			margin-top: 50px;
		}

		.upload {
			width: 452px;
      height: 88px;
      margin: 0 auto;
      margin-top: 1.6rem;
      line-height: 88px;
      background: linear-gradient(90deg,#40d243, #1fc432);
      text-align: center;
      color: #fff;
      border-radius: 8px;
      font-size: 32px;
      font-family: PingFangSC-Medium;
      font-weight: 500;
      .btn{
        width: 452px;
        height: 88px;
      }
      .upbtn{
        width: 100%;
        height: 100%;
        margin-top:-88px;
      }
		}
	}
</style>
