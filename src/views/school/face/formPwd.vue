<template>
  <div class="facehome">
    <NavHeight bgc="#fff" />
    <div class="top">
      <van-nav-bar title="" left-text="" left-arrow>
        <template #left>
          <van-icon
            name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/user/icon/arrow-black.png"
            size="18"
            @click="goBack"
          />
        </template>
      </van-nav-bar>
    </div>
    <!-- 表单 -->
    <div class="user">
      <img v-if="userdata.headImg!=null" :src="userdata.headImg" alt="">
      <img v-else src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/school/avtor.png" alt="">
    </div>
    <div class="form">
      <van-field v-model="userdata.username" center label="姓名" readonly />
      <van-field v-model="userdata.telephone" center label="账号" readonly />
    </div>

    <div class="upload" @click="goFace">
      <div class="btn">
        信息正确，下一步
      </div>
    </div>

  </div>
</template>

<script>// checkPassword
import {
  UserInfo
} from '@/api/my'
export default {
  data() {
    return {
      message: '人脸录入',
      userdata: {
        username: '',
        telephone: '',
        headImg: ''
      },
      pwd: ''
    }
  },
  created() {
    this.getUserInfo()
  },
  methods: {
    // 获取账户信息
    getUserInfo() {
      let self = this
      let data = this.$store.getters.getUserId
      UserInfo(data).then((res) => {
        if (res.status == 200) {
          self.userdata = res.data
          if (res.data.headImg == '') {
            self.userdata.headImg = 'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/my/touxiang.png'
          }
        } else if (res.status == 401) {
          self.$store.state.userHeadImg = ''
          self.token = ''
        }
      })
    },
    goFace() {
      this.$router.push('/school/face')
    },
    goPwd() {
      this.$router.push('/editPayPwd')
    },
    goBack() {
      this.$router.go(-1)
    }
  }
}
</script>

<style lang="scss" scoped>
  .facehome::before {
  // 利用伪元素设置整个页面的背景色
  content: " ";
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: -100;
  min-height: 100%;
  background-color: #fff;
}
.facehome{
  overflow: hidden;
	.user{
		text-align: center;
		margin-bottom: 72px;
    margin-top: 98px;

		img{
			width: 120px;
			height: 120px;
			border-radius: 50%;
		}
	}
	.form{
		width: 90%;
		margin: 0 auto;
	}
	.backPwd{
		color: #68D266;
	}
	.upload{
		width: 452px;
    height: 88px;
		margin: 0 auto;
		margin-top: 1.6rem;
    line-height: 88px;
		background: linear-gradient(90deg,#40d243, #1fc432);
    text-align: center;
		color: #fff;
		border-radius: 8px;
    font-size: 32px;
    font-family: PingFangSC-Medium;
    font-weight: 500;
	}
}
</style>
