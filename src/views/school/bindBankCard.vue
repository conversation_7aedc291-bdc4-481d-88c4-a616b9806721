<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2023-05-18 11:09:44
 * @LastEditors: 孙立政
 * @LastEditTime: 2023-06-14 18:13:15
-->
<template>
  <div class="bindBankCard">
    <Top path="bindBankCard" />
    <div class="occpyHeight" :style="styleVar" />
    <div class="body" :style="styleVar">
      <div class="nav">
        - 欢迎来到智慧校园系统 -
      </div>

      <div class="content">
        <!-- <div class="title">
          添加银行卡
        </div> -->
        <van-form @submit="onSubmit">
          <van-field
            v-model="form.bankCard"
            type="tel"
            maxlength="19"
            label="银行卡号"
            placeholder="请填写(可拍照识别)"
            :rules="[{ required: true, message: '请填写银行卡号' }]"
            @blur="getBankname"
          >
            <template #right-icon>
              <van-uploader capture="camera" :max-count="1" :after-read="afterRead">
                <van-icon name="credit-pay" @click="disBankCard" />
              </van-uploader>

            </template>
          </van-field>
          <van-field
            readonly
            clickable
            name="picker"
            :value="form.bankName"
            label="银行名称"
            placeholder="点击选择银行"
            :rules="[{ required: true, message: '请选择银行' }]"
            @click="getBankname"
          />
          <van-field
            readonly
            clickable
            name="picker"
            :value="areaCode.provinceCode+areaCode.cityCode"
            label="地区"
            placeholder="点击选择开户地区"
            :rules="[{ required: true, message: '请选择开户地区' }]"
            @click="getAreaCode"
          />
          <van-field
            readonly
            clickable
            name="picker"
            :value="form.openingBank"
            label="开户行"
            placeholder="点击选择开户行"
            :rules="[{ required: true, message: '请选择开户行' }]"
            @click="getQrylhhPicker"
          />
          <van-field
            v-model="form.realName"
            label="姓名"
            maxlength="10"
            placeholder="请填写"
            :rules="[{ required: true, message: '请填写姓名' }]"
          />
          <van-field
            v-model="form.bankTelephone"
            label="手机号"
            maxlength="11"
            type="tel"
            placeholder="请填写"
            :rules="[{ required: true, message: '请填写银行预留手机号' }]"
          />
          <van-field
            v-model="form.idCard"
            label="身份证号"
            maxlength="18"
            placeholder="请填写"
            :rules="[{ required: true, message: '请填写银行账户对应身份证号' }]"
          />
          <van-field v-if="false" name="radio" label="账户类型">
            <template #input>
              <van-radio-group v-model="form.bankType" direction="horizontal">
                <van-radio name="1" checked-color="#07c160">个人账户</van-radio>
                <van-radio name="2" checked-color="#07c160">对公账户</van-radio>
              </van-radio-group>
            </template>
          </van-field>
          <div style="margin: 16px;display: flex;justify-content: space-between;">
            <van-button v-if="false" style="margin-left: 15px;width: 120px;margin-right: 10px;" round block type="danger" native-type="submit">删除</van-button>
            <van-button round block type="primary" native-type="submit">确认</van-button>
          </div>
        </van-form>
      </div>

      <div v-if="false" class="card_list">
        <div class="title">我的银行卡</div>
        <div class="card">
          <div class="card_name">
            <div>{{ bankInfo.bankName }}</div>
            <div>{{ bankInfo.bankCard }}</div>
          </div>
          <div>储蓄卡</div>
        </div>
      </div>
    </div>

    <!-- 银行 -->
    <van-popup v-model="showBankPicker" position="bottom">
      <van-picker
        show-toolbar
        :columns="bankList"
        @confirm="onBankConfirm"
        @cancel="showBankPicker = false"
      />
    </van-popup>

    <!-- 省市 -->
    <van-popup v-model="areaPicker" position="bottom">
      <van-area title="地区" :area-list="areaList" :columns-num="2" @cancel="areaPicker = false" @confirm="confirmArea" />
    </van-popup>

    <!-- 网点 -->
    <van-popup v-model="qrylhhPicker" position="bottom">
      <van-picker
        show-toolbar
        :columns="qrylhhList"
        @confirm="qrylhhConfirm"
        @cancel="qrylhhPicker = false"
      />
    </van-popup>

  </div>
</template>

<script>
import { areaList } from '@vant/area-data'
import banksObj from '@/utils/banks'
import Top from './components/top'
import { getBankcard, getOcr, upload, qrylhh } from '@/api/bank'
import { bindingBankCard, findByUserBankCard, updateBankCard } from '@/api/school'
export default {
  components: {
    Top
  },
  data() {
    return {
      form: {
        'realName': '',
        'bankCard': '',
        'bankTelephone': '',
        'bankName': '',
        'bankType': '1',
        'capitalPoolId': '1',
        'idCard': '',
        'bankSn': '',
        'openingBank': ''
      },
      showBankPicker: false,
      bankList: [],
      bankInfo: {
        bankName: '',
        bankCard: ''
      },
      editType: 0,
      qrylhhPicker: false,
      qrylhhList: [],
      areaPicker: false,
      areaList: areaList,
      areaCode: {
        provinceCode: '',
        cityCode: '',
        page: 1
      }
    }
  },
  computed: {
    styleVar() {
      return {
        '---nav-height': this.$store.getters.getStatusHeight + 'px'
      }
    }
  },
  watch: {
    'form.bankCard': function(val) {
      if (val.length > 6) {
        console.log(555555)
      }
    }
  },
  created() {
    for (let i in banksObj) {
      this.bankList.push(banksObj[i])
    }
    this.findByUserBankCard()
  },
  methods: {
    // 绑卡
    onSubmit(values) {
      if (this.form.openingBank == '') {
        this.$toast('请选择开户行')
        return
      } else if (this.form.realName == '') {
        this.$toast('请填写姓名')
        return
      } else if (this.form.bankTelephone == '') {
        this.$toast('请填写手机号')
        return
      } else if (this.form.idCard == '') {
        this.$toast('请填写身份证号')
        return
      } else if (this.form.bankCard == '') {
        this.$toast('请填写银行卡号')
        return
      } else if (this.form.bankName == '') {
        this.$toast('请选择银行')
        return
      }

      this.$toast.loading({
        duration: 0,
        forbidClick: true
      })
      if (this.editType == 1) { // 新增
        updateBankCard(this.form).then(res => {
          if (res.status == 200) {
            this.$toast('操作成功')
            this.$router.go(-1)
          }
        })
      } else { // 修改
        bindingBankCard(this.form).then(res => {
          if (res.status == 200) {
            this.$toast('操作成功')
            this.$router.go(-1)
          }
        })
      }
      let self = this
      setTimeout(() => {
        self.$toast.clear()
      }, 2000)
    },
    // 获取银行卡列表
    findByUserBankCard() {
      findByUserBankCard(1).then(res => {
        if (res.status == 200) {
          if (res.data != null) {
            this.form = res.data
            this.editType = 1
          }
        }
      })
    },
    // 获取银行名称
    getBankname() {
      if (this.form.bankCard == '') {
        return
      }
      this.qrylhhList = []
      this.$toast.loading({
        duration: 0,
        forbidClick: true
      })
      getBankcard(this.form.bankCard).then(res => {
        this.$toast.clear()
        if (res.data.code == 0) {
          this.form.bankName = res.data.data.bankName
          this.form.bankSn = res.data.data.bankCode
        } else {
          this.$toast('银行卡号未能识别请检查或手动选择')
          this.showBankPicker = true
        }
      })
    },
    onBankConfirm(value) {
      console.log(value)
    },
    // 上传图片
    afterRead(file) {
      this.$toast.loading({
        duration: 0,
        forbidClick: true
      })
      const formData = new FormData()
      formData.append('file', file.file)
      upload(formData).then(res => {
        if (res.status == 200) {
          console.log(res)
          this.disBankCard('bankcard', res.data)
        }
      })
    },
    // 银行卡识别
    disBankCard(type, url) {
      getOcr({
        'type': type,
        'idCardSide': type == 'idcard' ? 'front' : '',
        'url': url
      }).then(res => {
        this.$toast.clear()
        if (res.status === 200) {
          // 银行卡
          if (type == 'bankcard') {
            console.log(res)
            let data = res.data.result
            if (data.bank_card_type == 1) {
              this.form.bankCard = data['bank_card_number'].replace(/\s+/g, '')
              this.getBankname()
            } else {
              this.$message.error('银行卡识别失败，请重新上传识别')
            }
          }
        }
      })
    },
    // 联行号
    getQrylhh() {
      this.$toast.loading({
        duration: 0,
        forbidClick: true
      })
      let data = {
        'cardno': this.form.bankCard,
        'city': this.areaCode.cityCode,
        'province': this.areaCode.provinceCode,
        'page': this.areaCode.page
      }
      qrylhh(data).then(res => {
        this.$toast.clear()
        if (res.status == 200) {
          let result = res.data.data
          if (res.data.code != 0) {
            return
          }
          if (result.length == 0) {
            return
          }
          for (let i = 0; i < result.length; i++) {
            this.qrylhhList.push(result[i].lName)
          }
          if (this.areaCode.page < res.data.totalpage) {
            this.areaCode.page++
            this.getQrylhh()
          }
        }
      })
    },
    // 联行号选择
    getQrylhhPicker() {
      if (this.form.bankCard == '') {
        this.$toast('请先填写银行卡号相关信息')
        return
      }
      if (this.areaCode.provinceCode == '' || this.areaCode.cityCode == '') {
        this.$toast('请先选择地区')
        return
      }
      this.qrylhhPicker = true
    },
    qrylhhConfirm(value) {
      console.log(value)
      this.form.openingBank = value
      this.qrylhhPicker = false
    },
    // 打开地区
    getAreaCode() {
      if (this.form.bankCard == '') {
        this.$toast('请先填写银行卡号相关信息')
        return
      }
      this.areaPicker = true
    },
    // 地区选择
    confirmArea(val) {
      this.areaCode.provinceCode = val[0].name
      this.areaCode.cityCode = val[1].name
      this.areaPicker = false
      this.qrylhhList = []
      this.form.openingBank = ''
      this.getQrylhh()
    }
  }
}
</script>

<style scoped lang="scss">
.bindBankCard {
    height: 100vh;
    background-color: #fff;
    overflow-y: hidden;

    .occpyHeight {
        height: 80px;
    }
    .body {
        .nav {
            width: 100%;
            height: 64px;
            font-size: 26px;
            background: #fffbeb;
            line-height: 64px;
            text-align: center;
        }
        .content{
            padding: 15px;
            .title{
                font-size: 38px;
                font-family: PingFangSC-Medium;
            }
        }
    }
    .card_list{
        .title{
            font-size: 38px;
            font-family: PingFangSC-Medium;
            margin: 20px 20px;
        }
        .card{
            width: 92%;
            height: 140px;
            margin: 0 auto;
            border-radius: 12px;
            padding: 20px;
            color: #fff;
            background: linear-gradient(33deg,#7ac0d5 0,#66c7e5 50%,#585dff 120%);
            font-size: 30px;

            .card_name{
                font-size: 36px;
                display: flex;
                justify-content: space-between;
                font-family: PingFangSC-Medium;
            }
        }
    }
}
</style>
