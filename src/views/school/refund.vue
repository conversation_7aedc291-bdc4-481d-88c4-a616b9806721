<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2023-05-18 10:02:55
 * @LastEditors: 孙立政
 * @LastEditTime: 2023-06-06 09:22:35
-->
<template>
  <div class="home">
    <Top path="refund" />
    <div class="occpyHeight" :style="styleVar" />
    <div class="body" :style="styleVar">
      <div class="nav">
        - 欢迎来到智慧校园系统 -
      </div>

      <div class="content">
        <div class="bank" @click="addBankCard">
          <div>退款至</div>
          <div v-if="bankInfo != null">
            <van-icon :name="getBankIcon(bankInfo.bankName)" style="margin-right: 5px;" />
            <span>{{ bankInfo.bankName }}({{ bankInfo.bankCard.substring(bankInfo.bankCard.length - 4) }})</span>
            <van-icon color="#999" name="arrow" />
          </div>
          <div v-else @click="addBankCard">
            <span>去绑卡</span>
            <van-icon color="#999" name="arrow" />
          </div>
        </div>
        <div class="input">
          <div class="input_name">退款金额</div>
          <div class="input_value">
            <div class="input_value_icon">￥</div>
            <div class="input_value_in">
              <van-field v-model="amount" type="number" />
            </div>
          </div>
        </div>
        <div class="balance">
          可退款金额￥{{ $store.state.school.balance }}
          <span @click="amount = $store.state.school.balance">全部</span>
        </div>
      </div>

      <div class="btn">
        <van-button type="primary" block round @click="getRefund">确认</van-button>
      </div>

      <div v-if="refundLogList.length > 0" class="log_list">
        <div class="title">
          退款记录
        </div>
        <div>
          <van-cell v-for="item in refundLogList" :key="item.id">
            <template #title>
              <span>{{ item.orgPathName }}-</span>
              <span>{{ item.personName }}</span>
            </template>
            <template #label>
              <div>
                {{ item.createTime }}
              </div>
            </template>
            <template #default>
              <div>
                <span v-if="item.status == 1" style="color: #ff976a;">退款中</span>
                <span v-if="item.status == 2" style="color: #07c160;">已完成</span>
              </div>
              <div>￥{{ item.amount }}</div>
            </template>
          </van-cell>
        </div>
      </div>

      <!-- 选择提现卡 -->
      <van-dialog v-model="bankListShow" :show-confirm-button="false">
        <div slot="title" class="bank_list_title">
          <span>更换银行卡</span>

          <van-icon class="close" name="cross" size="18" color="#999" @click="bankListShow = false" />
        </div>
        <div class="bank_list">
          <van-radio-group v-model="radio">
            <van-cell-group>
              <van-cell title="建设银行(5656)" clickable @click="radio = '1'">
                <template #right-icon>
                  <van-radio name="1" checked-color="#07c160" />
                </template>
              </van-cell>
              <van-cell title="招商银行(5656)" clickable @click="radio = '2'">
                <template #right-icon>
                  <van-radio name="2" checked-color="#07c160" />
                </template>
              </van-cell>
            </van-cell-group>
          </van-radio-group>
          <div class="add_bank" @click="addBankCard">
            <van-cell icon="plus" title="添加新卡" />
          </div>
        </div>
      </van-dialog>

    </div>
  </div>
</template>

<script>
import Top from './components/top'
import { balanceRefund, findByUserBankCard, refundList, balanceRefundBase } from '@/api/school'
import Maths from '@/utils/math.js'
import banksObj from '@/utils/banks'
export default {
  components: {
    Top
  },
  data() {
    return {
      amount: null,
      radio: '1',
      bankListShow: false,
      bankInfo: null,
      bankList: [],
      refundLogList: []
    }
  },
  computed: {
    styleVar() {
      return {
        '---nav-height': this.$store.getters.getStatusHeight + 'px'
      }
    }
  },
  created() {
    for (let i in banksObj) {
      this.bankList.push(banksObj[i])
    }
    this.findByUserBankCard()

    if (this.$route.query.type == 'hik') {
      this.refundList()
    }
  },
  methods: {
    // 确认退款
    getRefund() {
      if (!this.bankInfo) return this.$toast('请选择退款银行卡')
      if (!this.amount || this.amount <= 0) return this.$toast('请输入准确退款金额')
      if (this.amount > this.$store.state.school.balance) return this.$toast('退款金额不能大于可退款金额')
      this.$dialog.confirm({
        title: '是否确认发起退款？',
        message: `<div style="margin-top:20px">退款金额<span style="color: #ec2b09;font-size: 23px;">￥${this.amount}</span></div>`
      }).then(() => {
        if (this.$route.query.type == 'hik') {
          this.balanceRefund()
        } else {
          this.balanceRefundBase()
        }
      })
    },
    // 海康退款
    balanceRefund() {
      let self = this
      this.$toast.loading({
        duration: 0,
        forbidClick: true
      })
      balanceRefund({
        amount: this.amount,
        personId: this.$store.state.school.hik.personId
      }).then(res => {
        setTimeout(() => {
          self.$toast.clear()
        }, 1000)

        console.log(res)
        if (res.status == 200) {
          setTimeout(() => {
            self.$toast('操作成功')
          }, 1000)

          this.$store.state.school.balance = new Maths(Number(this.$store.state.school.balance), Number(this.amount)).minus()
          this.amount = 0
          this.refundList()
        }
      })
    },
    // 其它退款
    balanceRefundBase() {
      let self = this
      this.$toast.loading({
        duration: 0,
        forbidClick: true
      })
      balanceRefundBase({
        idCard: this.$store.state.school.idCard,
        amount: this.amount,
        schoolNo: this.$store.state.school.schoolNo,
        remark: ''
      }).then(res => {
        setTimeout(() => {
          self.$toast.clear()
        }, 1000)

        console.log(res)
        if (res.status == 200) {
          setTimeout(() => {
            self.$toast('操作成功')
          }, 1000)

          this.$store.state.school.balance = new Maths(Number(this.$store.state.school.balance), Number(this.amount)).minus()
          this.amount = 0
          // this.refundList()
        }
      })
    },
    // 获取银行卡列表
    findByUserBankCard() {
      findByUserBankCard(1).then(res => {
        if (res.status == 200) {
          this.bankInfo = res.data
        }
      })
    },
    addBankCard() {
      this.$router.push('/school/bindBankCard')
    },
    // 银行卡icon
    getBankIcon(bankName) {
      let bankIcon = ''
      for (let i in this.bankList) {
        if (this.bankList[i].text == bankName) {
          bankIcon = this.bankList[i].logo
          break
        } else {
          bankIcon = 'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/bank/bankicon/bankicon.png'
        }
      }
      return bankIcon
    },
    // 退款记录
    refundList() {
      refundList({
        page: 1,
        size: 10000,
        personId: this.$store.state.school.hik.personId,
        status: null
      }).then(res => {
        if (res.status == 200) {
          this.refundLogList = res.data.list
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
.home {
  background-color: #fff;

  .occpyHeight {
    height: 80px;
  }

  .body {
    overflow-y: hidden;
    min-height: 1800px;

    .nav {
      width: 100%;
      height: 64px;
      font-size: 26px;
      background: #fffbeb;
      line-height: 64px;
      text-align: center;
    }

    .content {
      width: 83%;
      margin: 0 auto;
      margin-top: 30px;
      font-size: 33px;

      .bank {
        height: 100px;
        line-height: 100px;
        // border-top: 1px solid #eee;
        border-bottom: 1px solid #eee;
        display: flex;
        justify-content: space-between;
        color: #333;
        padding-left: 5px;
        padding-right: 5px;

        .bank_icon {
          width: 30px;
          height: 30px;
          margin-right: 8px;
          position: relative;
          top: 2px;
        }

        span {
          position: relative;
          top: -2px;
        }
      }

      .input {
        height: 220px;
        padding-left: 5px;
        padding-right: 5px;
        border-bottom: 1px solid #eee;

        .input_name {
          height: 100px;
          line-height: 100px;
        }

        .input_value {
          display: flex;

          .input_value_icon {
            font-size: 55px;
          }

          .input_value_in {
            margin-top: -20px;

            .van-cell {
              font-size: 60px;
              background-color: rgba(0, 0, 0, 0)
            }
          }
        }
      }

      .balance {
        height: 100px;
        line-height: 100px;
        color: #999;

        span {
          color: rgb(124, 170, 239);
          margin-left: 10px;
        }
      }
    }

    .btn {
      width: 520px;
      margin: 0 auto;
      margin-top: 100px;
    }

    .bank_list_title {
      font-size: 35px;
      margin-top: -20px;
      text-align: center;

      .close {
        position: absolute;
        left: 30px;
        top: 35px;

      }
    }

    .bank_list {
      margin-top: 20px;

      .add_bank {
        .van-cell {
          font-size: 35px;
        }
      }
    }
  }

  .log_list {
    padding: 15px;
    margin-top: 50px;

    .title {
      font-size: 35px;
      color: #333;
      font-family: PingFangSC-Medium;
      margin-bottom: 10px;
      margin-left: 15px;
    }
  }
}
</style>
