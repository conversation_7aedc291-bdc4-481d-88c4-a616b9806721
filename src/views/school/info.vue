<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2021-07-30 10:00:46
 * @LastEditors: 孙立政
 * @LastEditTime: 2023-02-10 13:19:29
-->
<template>
  <div class="home">
    <Top path="info" />
    <div class="occpyHeight" :style="styleVar" />
    <div class="body" :style="styleVar">
      <div class="nav">
        - 欢迎来到智慧校园系统 -
      </div>

      <div class="people">
        <div class="left">
          <div>
            <img v-if="$store.state.school.photo" :src="$store.state.school.photo" alt="">
            <img v-else src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/school/avtor.png" alt="">
            <!-- <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/school/avtor.png" alt=""> -->
          </div>
          <div>
            <img v-if="$store.state.school.schoolStatus==0" class="status_img" src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/school/status/s1-1.png" alt="">
            <img v-if="$store.state.school.schoolStatus==1&&$store.state.school.departureReason==2" class="status_img" src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/school/status/s2-1.png" alt="">
            <img v-if="$store.state.school.schoolStatus==1&&$store.state.school.departureReason==1" class="status_img" src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/school/status/s3-1.png" alt="">
          </div>

        </div>
        <div class="center">
          <div>
            <span class="center-left">姓名：</span>
            <span class="center-right"> {{ $store.state.school.username }}</span>
            <img class="center-kqjl" src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/school/kqjl.png" alt="" @click="goCheckLog">
          </div>
          <div>
            <span class="center-left">学校：</span>
            <div class="center-right"> {{ $store.state.school.schoolName }}</div>
          </div>
          <div>
            <span class="center-left">余额：</span>
            <span class="center-right"> ￥{{ $store.state.school.balance }}</span>
            <div v-if="$store.state.school.refundStatus===1" class="center-tf">
              退费处理中
            </div>
          </div>
        </div>

      </div>

      <div class="btn">
        <div class="left" :class="$store.state.school.schoolStatus==1&&$store.state.school.departureReason==1?'left_die':''" @click="goPay">
          充值
        </div>
        <div class="right" @click="goRefund">
          退款
        </div>
        <!-- <div class="right" @click="goFace">
          人脸录入
        </div> -->

      </div>
      <!-- <div class="tips">
        为保证识别率，人脸照片由工作人员统一录入
      </div> -->

      <div class="logList">

        <div class="logListTop1">
          <div class="tabLeft" :class="tabIndex == 0?'tabIn':''" @click="tabIndex = 0">
            充值记录
          </div>
          <div class="tabCenter" :class="tabIndex == 1?'tabIn':''" @click="tabIndex = 1">
            消费记录
          </div>
          <div class="tabRight" :class="tabIndex == 2?'tabIn':''" @click="tabIndex = 2">
            退款记录
          </div>
        </div>

        <div class="logItem">
          <van-list v-model="loading" :finished="finished" finished-text="~ 到底啦 ~" @load="onLoad">
            <ul>
              <li v-for="(item,index) in list" :key="index">
                <div class="left">
                  <img v-if="tabIndex == 1" src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/school/listicon.png" alt="">
                  <img v-if="tabIndex == 0" src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/school/listiconfood.png" alt="">
                  <img v-if="tabIndex == 2" src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/school/tuikuan.png" alt="">
                </div>
                <div class="center">
                  <div class="shopName">
                    <div>
                      {{ tabIndex == 0?'余额充值':item.marketName }}
                    </div>
                    <div class="right">
                      <span v-if="tabIndex == 0">+</span>
                      <span v-if="tabIndex == 1"> - </span>
                      <span v-if="tabIndex == 2">+</span>
                      <span v-if="tabIndex != 2">￥{{ item.tradeAmount }}</span>
                      <span v-else>￥{{ item.transAmt }}</span>
                    </div>
                  </div>
                  <div class="no">
                    <span v-if="tabIndex == 0">交易单号：{{ item.orderNo }}</span>
                    <span v-if="tabIndex == 1&&item.transactionType != 4">消费</span>
                    <span v-if="tabIndex == 1&&item.transactionType == 4">校园扣款</span>
                    <span v-if="tabIndex == 2">退款</span>
                    <span v-if="item.refundAmount>0" style="float:right;color:red">退款 +￥{{ item.refundAmount }}</span>
                  </div>
                  <div class="time">
                    交易时间：{{ tabIndex != 2?item.createTime:item.recordTime }}
                  </div>
                </div>
              </li>
            </ul>
          </van-list>
        </div>
      </div>

    </div>

    <van-dialog v-model="tipsDialog">
      <template #title>
        <div class="tipsDialogTitle">
          充值通道已暂时关闭
        </div>
      </template>
      <div class="tipsDialogBox" @click="callPhone">
        <div>如有疑问，请联系客服员</div>
        <div>15306780331
          <van-icon name="phone" class="phone_icon" />
        </div>
      </div>

    </van-dialog>

  </div>
</template>

<script>
import Top from './components/top'
import { getFlow, getAllRechargeRecord, selectRefundByUserId } from '@/api/school'
export default {
  components: {
    Top
  },
  data() {
    return {
      list: [],
      finished: false,
      loading: false,
      query: {
        page: 0,
        size: 10,
        thirdUserId: this.$store.state.school.id
      },
      query2: {
        pageNum: 1,
        pageSize: 1000000,
        search: {
          userId: this.$store.state.school.id,
          idCard: this.$store.state.school.idCard
        }
      },
      tabIndex: 0,
      tipsDialog: false
    }
  },
  computed: {
    styleVar() {
      return {
        '---nav-height': this.$store.getters.getStatusHeight + 'px'
      }
    }
  },
  watch: {
    'tabIndex': function(val) {
      this.list = []
      this.query.page = 0
      this.finished = false
      if (val == 1) {
        // this.getList()
      } else if (val == 0) {
        this.getRechargeRecord()
      } else if (val == 2) {
        this.selectRefundByUserId()
      }
    }
  },
  created() {
    this.getRechargeRecord()
    // 回到顶部
    document.body.scrollTop = 0
    document.documentElement.scrollTop = 0
  },
  mounted() {
    // this.$dialog.alert({
    //   title: '温馨提示',
    //   message: '因支付系统升级，智慧校园充值系统的支付宝通道临时关闭，预计6月5号开放，具体时间以技术升级结束为准。给您带来的不便，敬请谅解。如有充值需求可选择其它支付通道。'
    // }).then(() => {})
  },
  methods: {
    // 获取流水
    onLoad() {
      if (this.tabIndex == 1) {
        this.query.page++
        this.getList()
      }
    },
    // 流水
    getList() {
      getFlow(this.query).then((res) => {
        if (res.status == 200) {
          this.loading = false
          if (res.data.list != null) {
            this.list.push(...res.data.list)
          } else {
            this.finished = true
          }
        } else {
          this.finished = true
          this.$toast(res.message)
        }
      })
    },
    // 查看考勤记录
    goCheckLog() {
      this.$router.push({
        path: '/school/checkLog'
      })
    },
    // 充值记录
    getRechargeRecord() {
      this.$toast.loading({
        duration: 0,
        forbidClick: true,
        message: ''
      })
      getAllRechargeRecord(this.query2).then((res) => {
        this.$toast.clear()
        if (res.status == 200) {
          this.loading = false
          this.list = res.data.list
          this.finished = true
        } else {
          this.finished = true
          this.$toast(res.message)
        }
      })
    },
    // 退款记录
    selectRefundByUserId() {
      this.$toast.loading({
        duration: 0,
        forbidClick: true,
        message: ''
      })
      let data = {
        thirdUserNo: this.$store.state.school.thirdUserNo
      }
      selectRefundByUserId(data).then((res) => {
        this.$toast.clear()
        if (res.status == 200) {
          this.loading = false
          this.list = res.data
          this.finished = true
        } else {
          this.finished = true
          this.$toast(res.message)
        }
      })
    },
    goFace() {
      if (this.$store.state.school.photo == null) {
        this.$router.push({
          path: '/school/face'
        })
      } else {
        this.$router.push({
          path: '/school/faceOk'
        })
      }
    },
    // 充值
    goPay() {
      if (!this.$store.state.school.rechargeSwitch) {
        this.tipsDialog = true
        return
      }
      if (this.$store.state.school.schoolStatus == 1 && this.$store.state.school.departureReason == 1) {
        return
      }
      if (this.$store.state.school.userType != 2) {
        this.$router.push({
          path: '/school/pay'
        })
      } else {
        this.$router.push({
          path: '/school/pay'
        })
        // this.$toast('暂未开放')
      }
    },
    // 拨打客服电话
    callPhone() {
      AlipayJSBridge.call('CallPhone', {
        phoneNum: 15306780331
      }, function(result) {})
    },
    // 退款
    goRefund() {
      this.$router.push({
        path: '/school/refund',
        query: {
          type: 'base'
        }
      })
    },
    // 6月8号到7月31号之间不显示
    isPayBtn() {
      var today = new Date()
      var startDate = new Date(today.getFullYear(), 5, 1)
      var endDate = new Date(today.getFullYear(), 8, 4)
      if (today >= startDate && today <= endDate) {
        if (this.$store.state.school.schoolNo == '16288550680003' || this.$store.state.school.schoolNo == '16292770620008') {
          return true
        }
      } else {
        return false
      }
    }
  }
}
</script>

<style scoped lang="scss">
    .home {
      .tipsDialogTitle{
        font-size: 40px;
        font-weight: bold;
        color: #333;
      }
      .tipsDialogBox{
        color: #333;
        padding: 50px;
        text-align: center;
        div{
          margin-bottom: 10px;
        }
        .phone_icon{
          position: relative;
          top: 5px;
        }
      }
      .occpyHeight {
        height: 80px;
      }
        .body{
            .nav{
                width: 100%;
                height: 64px;
                font-size: 26px;
                background: #fffbeb;
                line-height: 64px;
                text-align: center;
            }
            .people{
                width: 100%;
                height: 232px;
                background-color: #fff;
                margin-top: 21px;
                display: flex;
                .left{
                    width: 20%;
                    height: 180px;
                    border-radius: 50%;
                    margin-top: 30px;
                    margin-left: 40px;
                    margin-right: 20px;
                    img {
                      width: 180px;
                      height: 180px;
                      border-radius: 3px;
                    }
                    .status_img{
                      width: 120px;
                      height: 42px;
                      display: block;
                      margin: 0 auto;
                      margin-left: 25px;
                    }
                }
                .center{
                    width: 80%;
                    margin-top: 23px;
                    margin-left: 30px;
                    overflow: hidden;
                    div{
                      height: 50px;
                      display: flex;
                    }
                    .center-left{
                        font-size: 30px;
                        font-family: PingFangSC-Medium;
                        font-weight: 500;
                        color: #333333;
                    }
                    .center-right{
                        font-size: 30px;
                        font-family: PingFangSC;
                        font-weight: 500;
                        color: #333333;
                        overflow: hidden;
                    }
                    .center-kqjl{
                      width: 110px;
                      height: 38px;
                      float: right;
                      margin-right: 28px;
                      margin-top: 5px;
                      margin-left: 5px;
                    }
                    .center-tf{
                      width: 144px;
                      height: 40px;
                      background: rgba(255,48,30,0.14);
                      border-radius: 20px;
                      font-size: 24px;
                      color: #ff301e;
                      text-align: center;
                      line-height: 40px;
                      display: inline-block;
                      margin-left: 15px;
                    }
                }
            }
            .btn{
              display: flex;
              height: 100px;
              background-color: #fff;
              .left{
                width: 160px;
                height: 64px;
                border-radius: 8px;
                line-height: 64px;
                text-align: center;
                color: #fff;
                font-size: 30px;
                margin-left: 250px;
                background: #25d9cd;
              }
              .left_die{
                background: #999;
              }
              .right{
                width: 160px;
                height: 64px;
                border: 2px solid #16e9c2;
                border-radius: 8px;
                line-height: 64px;
                text-align: center;
                color: #16e9c2;
                font-size: 30px;
                margin-left: 35px;
              }
            }
            .tips{
                width: 300px;
                font-size: 27px;
                margin-left: 20px;
                color:red;
              }
            .logList{
                width: 710px;
                margin: 0 auto;
                margin-top: 40px;
                .logListTop1{
                    font-family: PingFangSC;
                    width: 100%;
                    max-height: 88px;
                    display: flex;
                    div{
                      width: 33.3%;
                      height: 86px;
                      line-height: 86px;
                      text-align: center;
                      font-size: 30px;
                      background-color: #F6F6F6;
                      color: #666666;
                      border: 1px solid #e7e7e7;
                    }
                    .tabIn{
                      background: #77D4F8;
                      color: #fff;
                      font-family: PingFangSC-Medium;
                    }
                    .tabLeft{
                      border-top-left-radius: 12px;
                    }
                    .tabRight{
                      border-top-right-radius: 12px;
                    }
                }

                .logItem{
                    height: 60vh;
                    overflow-y: auto;
                    background-color: #fff;
                    box-shadow: 0px 2px 26px 11px rgba(0,0,0,0.04);
                    padding-top: 10px;
                    li{
                        width: 90%;
                        height: 175px;
                        margin: 0 auto;
                        display: flex;
                        .left{
                            width: 10%;
                            margin-top: 15px;
                            img{
                                width: 54px;
                                height: 54px;
                            }
                        }
                        .center{
                            width: 90%;
                            font-family: PingFangSC;
                            border-bottom: 1px solid #ececec;
                            margin-left: 10px;
                            .shopName{
                                font-size: 32px;
                                color: #333333;
                                margin-top: 20px;
                                display: flex;
                                justify-content: space-between;
                                 .right{
                                    // margin-top: 30px;
                                    font-size: 32px;
                                    color: #333333;
                                }
                            }
                            .no{
                              font-size: 26px;
                              color: #999999;
                              margin-top: 6px;
                            }
                            .time{
                                color: #666666;
                                font-size: 22px;
                                margin-top: 6px;
                            }
                        }

                    }
                }
            }
        }

    }

</style>
