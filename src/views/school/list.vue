<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2021-07-30 10:00:20
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-10-27 10:01:01
-->
<template>
  <div class="home">
    <Top path="list" />
    <div class="item" :style="styleVar">
      <ul>
        <li v-for="(item,index) in list" :key="index" @click="goInfo(item)">
          <div class="left">
            <!-- <img :src="item.photo" alt=""> -->
            <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/school/avtor.png" alt="">
          </div>
          <div class="center">
            <div class="name">{{ item.username }}</div>
            <div class="schoolName">{{ item.schoolName }}</div>
            <div class="schoolName">
              <span>余额 ￥{{ item.balance }}</span>
              <div v-if="item.refundStatus === 1" class="center-tf">
                退费处理中
              </div>
            </div>
          </div>
          <div class="btn">
            进入
          </div>
          <div class="rightIcon">
            <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/school/right.png" alt="">
          </div>
          <div class="shool_status">
            <img v-if="item.schoolStatus == 0" src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/school/status/s1.png" alt="">
            <img v-if="item.schoolStatus == 1&&item.departureReason == 1" src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/school/status/s2.png" alt="">
            <img v-if="item.schoolStatus == 1&&item.departureReason == 2" src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/school/status/s3.png" alt="">
          </div>

        </li>
      </ul>
      <!-- 海康 -->
      <ul>
        <li v-for="(item,index) in listHik" :key="index" @click="goPay(item)">
          <div class="left">
            <!-- <img :src="item.photo" alt=""> -->
            <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/school/avtor.png" alt="">
          </div>
          <div class="center">
            <div class="name">{{ item.personName }}-{{ item.orgPathName }}</div>
            <div class="schoolName">余额 ￥{{ item.balance }}</div>
          </div>
          <div class="btn">
            进入
          </div>
          <div class="rightIcon">
            <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/school/right.png" alt="">
          </div>

        </li>
      </ul>
      <van-empty v-if="list.length==0&&listHik.length==0" description="暂无相关数据哦~" />
    </div>

  </div>
</template>

<script>
import Top from './components/top'
import { getBindByUserId, getBindByUserIdHik } from '@/api/school'
export default {
  components: {
    Top
  },
  data() {
    return {
      list: [],
      listHik: []
    }
  },
  computed: {
    styleVar() {
      return {
        '---nav-height': this.$store.getters.getStatusHeight + 'px'
      }
    }
  },
  created() {
    this.getList()
    this.getBindByUserIdHik()
  },
  methods: {
    getList() {
      getBindByUserId().then(res => {
        if (res.data != null) {
          this.list.push(...res.data)
        }
      })
    },
    // 海康平台
    getBindByUserIdHik() {
      getBindByUserIdHik().then(res => {
        if (res.data != null) {
          this.listHik.push(...res.data)
        }
      })
    },
    // 跳转学生详情
    goInfo(item) {
      this.$store.state.school.id = item.id
      this.$store.state.school.username = item.username
      this.$store.state.school.schoolName = item.schoolName
      this.$store.state.school.photo = item.photo
      this.$store.state.school.balance = item.balance
      this.$store.state.school.idCard = item.idCard
      this.$store.state.school.schoolNo = item.schoolNo
      this.$store.state.school.thirdUserNo = item.thirdUserNo
      this.$store.state.school.userType = item.userType
      this.$store.state.school.schoolStatus = item.schoolStatus
      this.$store.state.school.refundStatus = item.refundStatus
      this.$store.state.school.departureReason = item.departureReason
      this.$store.state.school.rechargeSwitch = item.rechargeSwitch

      this.$router.push({
        path: '/school/info'
      })
    },
    // 海康直接进入充值
    goPay(item) {
      // this.$toast('暂未开放')

      this.$store.state.school.hik.personId = item.personId
      this.$store.state.school.hik.personName = item.personName
      this.$store.state.school.hik.orgPathName = item.orgPathName
      this.$store.state.school.hik.stuGrade = item.stuGrade
      this.$store.state.school.hik.stuClass = item.stuClass
      this.$store.state.school.hik.balance = item.balance
      this.$store.state.school.hik.certificateNo = item.certificateNo
      console.log(item.personPhoto)
      this.$store.state.school.hik.personPhoto = item.personPhoto
      this.$store.state.school.schoolNo = item.orgNo
      this.$store.state.school.schoolStatus = 0

      this.$store.state.school.balance = item.balance
      this.$store.state.school.idCard = item.certificateNo

      this.$router.push({
        path: '/school/hikInfo',
        query: {
          type: 'hik'
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
    .home {

        .item{
            margin-top:calc(70px + var(---nav-height));
            li{
                width: 690px;
                min-height: 184px;
                background: #ffffff;
                border-radius: 20px;
                box-shadow: 0px 2px 26px 11px rgba(0,0,0,0.04);
                margin: 0 auto;
                margin-bottom: 20px;
                display: flex;
                position: relative;
                .left{
                    margin-top: 44px;
                    margin-left: 30px;
                    img{
                        width: 96px;
                        height: 96px;
                        border-radius: 50%;
                    }
                }
                .center{
                    width: 50%;
                    margin-top: 44px;
                    margin-bottom: 20px;
                    margin-left: 20px;
                    .name{
                        font-size: 34px;
                        color: #333333;
                        font-family: PingFangSC-Medium;
                    }
                    .schoolName{
                        font-size: 28px;
                        color: #666;
                        font-family: PingFangSC-Medium;
                        margin-top: 10px;
                    }
                    .center-tf{
                      width: 144px;
                      height: 40px;
                      background: rgba(255,48,30,0.14);
                      border-radius: 20px;
                      font-size: 24px;
                      color: #ff301e;
                      text-align: center;
                      line-height: 40px;
                      display: inline-block;
                      margin-left: 15px;
                      position: relative;
                      top: -2px;
                    }
                }
                .btn{
                    width: 124px;
                    height: 54px;
                    font-size: 28px;
                    font-family: PingFangSC-Medium;
                    color: #16e9c2;
                    border: 2px solid #16e9c2;
                    border-radius: 8px;
                    text-align: center;
                    line-height: 54px;
                    margin-top: 65px;
                }
                .rightIcon{
                    margin-left: 16px;

                    img{
                        width: 24px;
                        height: 24px;
                        margin-top: 80px;
                    }
                }
                .shool_status{
                  position:absolute;
                  right: 0;
                  top: 0;
                  font-size: 20px;
                  img{
                    width: 98px;
                    height: 38px;
                  }
                }
            }
        }
    }
</style>
