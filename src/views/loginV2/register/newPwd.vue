<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2021-06-01 10:32:13
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-02-23 14:56:37
-->
<template>
  <div class="codeLogins">
    <NavHeight bgc="#fff" />
    <div class="close" @click="goBack">
      <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/loginV2/close.png" alt="">
    </div>

    <div class="box">
      <div class="msg">
        为您的账号设置一个新密码
      </div>
      <div class="input">
        <van-field v-model="form.password" clearable type="password" maxlength="20" placeholder="新密码" />
      </div>

      <div class="tipMsg1">
        请设置6-20位新密码（数字+字母）
      </div>

      <div v-if="!isBtn" class="btn">保存新密码</div>
      <div v-if="isBtn" class="btn btn-in" @click="getSure">保存新密码</div>

    </div>

  </div>
</template>

<script>
import { resetLoginPwd } from '@/api/login'
export default {
  data() {
    return {
      form: {
        telephone: this.$route.query.telephone,
        code: this.$route.query.validCode,
        password: ''
      },
      isBtn: false,
      count: 0
    }
  },
  watch: {
    'form.password': function(val) {
      if (!/^(?!([a-zA-Z]+|\d+)$)[a-zA-Z\d]{6,20}$/.test(val)) {
        this.isBtn = false
      } else {
        this.isBtn = true
      }
    }
  },
  mounted() { },
  methods: {
    getSure() {
      if (!/^(?!([a-zA-Z]+|\d+)$)[a-zA-Z\d]{6,20}$/.test(this.form.password)) {
        this.$toast('请输入6-20位新密码同时包含数字和字母')
        return false
      }

      resetLoginPwd(this.form).then((res) => {
        if (res.status == 200) {
          this.$toast('密码修改成功')
          this.$router.push({ name: 'wxLogin2' })
          sessionStorage.setItem('isPwd', 0)
        }
      })
    },
    goBack() {
      this.$router.go(-1)
    }
  }
}
</script>

<style lang="scss" scoped>
.codeLogins{
  min-height: 100vh;
  background-color: #fff;
  .close{
    height: 80px;
    overflow: hidden;
    img{
      width: 40px;
      height: 40px;
      margin-left: 34px;
      display: block;
      margin-top: 20px;
    }
  }
  .box{
    width: 590px;
    margin: 0 auto;
    margin-top: 90px;
    .msg{
      font-size: 55px;
      font-family: PingFangSC-Medium;
      color: #222222;
    }
    .input{
      margin-top: 74px;
      ::v-deep .van-cell{
        border-bottom: 2px solid #dddddd;
        padding: 0;
        .van-field__control{
          height: 100px;
          font-size:39px;
        }
      }
      .codeLoginFormBtn{
        width: 160px;
        height: 50px;
        text-align: center;
        line-height: 50px;
        background: linear-gradient(90deg,#40d243, #1fc432);
        border-radius: 12px;
        font-size: 26px;
        font-weight: 500;
        color: #ffffff;
      }
    }
    .tipMsg1{
      font-size: 30px;
      color: #909090;
      margin-top: 18px;
    }
    .errorMsg{
      font-size: 30px;
      color: #909090;
      margin-top: 11px;
      color: #ff301e;
    }
    .btn{
      width: 590px;
      height: 84px;
      opacity: 0.4;
      background: linear-gradient(90deg,#40d243, #1fc432);
      border-radius: 57px;
      text-align: center;
      line-height: 84px;
      color: #ffffff;
      font-size: 38px;
      margin-top: 60px;
    }
    .btn-in{
      opacity: 1;
    }
  }
}
</style>
