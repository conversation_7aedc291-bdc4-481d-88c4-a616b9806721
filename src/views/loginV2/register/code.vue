<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2021-06-01 10:32:13
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-02-23 14:56:03
-->
<template>
  <div class="codeLogins">
    <NavHeight bgc="#fff" />
    <div class="close" @click="goBack">
      <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/loginV2/left.png" alt="">
    </div>

    <div class="box">
      <div class="msg">
        输入验证码
      </div>

      <div class="tipMsg3">
        验证码已发送至<span> +86 {{ $route.query.telephone }}</span>
      </div>

      <div class="input">
        <van-password-input gutter="20px" :value="form.code" :focused="true" :mask="false" @click="showKeyboard" />
      </div>

      <div v-if="count !== 0" class="tipMsg1">
        {{ count }} 秒后重新获取验证码
      </div>

      <div v-if="count === 0" class="tipMsg2" @click="getCode">
        重新发送
      </div>

    </div>

    <Keyboard ref="showKeyboardRef" :length="6" default-val="" :text.sync="form.code" :keyboard-type="1" />
  </div>
</template>

<script>
import { smsCode } from '@/api/login'
import Keyboard from '@/components/Keyboard'
export default {
  components: { Keyboard },
  data() {
    return {
      form: {
        code: ''
      },
      count: 0,
      timer: 0
    }
  },
  watch: {
    'form.code': function(val) {
      val.length == 6 ? this.getPwd() : ''
    }
  },
  mounted() {
    this.$refs.showKeyboardRef.show()
    if (sessionStorage.getItem('isPwd') != 1) {
      this.getCode()
    }
  },
  methods: {
    getPwd() {
      sessionStorage.setItem('isPwd', 1)
      this.$router.push({ name: 'RegisterNewPwd', query: { telephone: this.$route.query.telephone, validCode: this.form.code }})
    },
    // 发送验证码
    getCode() {
      let self = this
      let str = this.$route.query.telephone.replace(/\s*/g, '')
      console.log(str)
      let data = {
        tel: str,
        name: 'changePassword'
      }
      smsCode(data).then((res) => {
        if (res.status == 200) {
          const TIME_COUNT = 60
          if (this.count === 0) {
            this.count = TIME_COUNT
            this.timer = setInterval(() => {
              if (self.count > 0 && self.count <= TIME_COUNT) {
                self.count--
              } else {
                clearInterval(self.timer)
                self.count = 0
              }
            }, 1000)
          }
          this.$toast('短信已发送，有效期5分钟')
        } else {
          this.$toast(res.message)
          this.count = 0
        }
      })
    },
    showKeyboard() {
      this.$refs.showKeyboardRef.show()
    },
    goBack() {
      this.$router.go(-1)
    }
  }
}
</script>

<style lang="scss" scoped>
.codeLogins{
  min-height: 100vh;
  background-color: #fff;
  .close{
    height: 80px;
    overflow: hidden;
    img{
      width: 40px;
      height: 40px;
      margin-left: 34px;
      display: block;
      margin-top: 20px;
    }
  }
  .box{
    width: 590px;
    margin: 0 auto;
    margin-top: 90px;
    .msg{
      font-size: 60px;
      font-family: PingFangSC-Medium;
      color: #222222;
    }
    .input{
      margin-top: 74px;
      ::v-deep .van-password-input{
          margin: 0;
      }
      ::v-deep [class*=van-hairline]::after{
        border: 0;
      }
      ::v-deep .van-password-input__item{
        border-bottom: 1px solid #c2c2c2;
      }
      ::v-deep .van-password-input__item--focus{
        border-bottom: 1px solid #222222;
      }
    }
    .tipMsg1{
      font-size: 30px;
      color: #333333;
      margin-top: 70px;
    }
    .tipMsg2{
        font-size: 30px;
        color: #3488FF;
        margin-top: 70px;
    }
    .tipMsg3{
      font-size: 30px;
      color: #909090;
      margin-top: 20px;
      span{
        color: #222222;
      }
    }
  }
}
</style>
