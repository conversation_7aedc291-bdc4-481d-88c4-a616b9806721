<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2021-06-01 10:32:13
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-02-18 18:01:21
-->
<template>
  <div class="codeLogins">
    <NavHeight bgc="#fff" />
    <div class="close" @click="goBack">
      <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/loginV2/close.png" alt="">
    </div>

    <div class="box">
      <div class="msg">
        输入你要找回密码的账号
      </div>
      <div class="input">
        <van-field v-model="form.telephone" clearable type="tel" maxlength="13" placeholder="请输入手机号" @clear="clearError" />
      </div>
      <div v-if="errSatuts" class="errorMsg animate__animated animate__pulse">
        手机号码输入错误，请检查
      </div>
      <div v-if="form.telephone.length==13" class="btn btn-in" @click="goToNewPwd">获取短信验证码</div>
      <div v-else class="btn">获取短信验证码</div>

    </div>

  </div>
</template>

<script>
export default {
  data() {
    return {
      telephone: '',
      form: {
        telephone: '',
        validCode: ''
      },
      showCode: false,
      count: 0,
      errSatuts: false
    }
  },
  watch: {
    'form.telephone': function(newValue, oldValue) {
      if (newValue.length > oldValue.length) { // 文本框中输入
        if (newValue.length === 3 || newValue.length === 8) {
          this.form.telephone += ' '
        }
      } else {
        if (newValue.length === 9 || newValue.length === 4) {
          this.form.telephone = this.form.telephone.trim()
        }
      }
    }
  },
  mounted() {},
  methods: {
    goToNewPwd() {
      let str = this.form.telephone.replace(/\s*/g, '')
      this.$router.push({ name: 'RegisterNewPwdCode', query: { telephone: str }})
    },
    // 清除输入框
    clearError() {
      this.errSatuts = false
    },
    goBack() {
      this.$router.go(-1)
    }
  }
}
</script>

<style lang="scss" scoped>
.codeLogins{
  min-height: 100vh;
  background-color: #fff;
  .close{
    height: 80px;
    overflow: hidden;
    img{
      width: 40px;
      height: 40px;
      margin-left: 34px;
      display: block;
      margin-top: 20px;
    }
  }
  .box{
    width: 590px;
    margin: 0 auto;
    margin-top: 90px;
    .msg{
      font-size: 60px;
      font-family: PingFangSC-Medium;
      color: #222222;
    }
    .input{
      margin-top: 74px;
      ::v-deep .van-cell{
        border-bottom: 2px solid #dddddd;
        padding: 0;
        .van-field__control{
          height: 100px;
          font-size:39px;
        }
      }
      .codeLoginFormBtn{
        width: 160px;
        height: 50px;
        text-align: center;
        line-height: 50px;
        background: linear-gradient(90deg,#40d243, #1fc432);
        border-radius: 12px;
        font-size: 26px;
        font-weight: 500;
        color: #ffffff;
      }
    }
    .errorMsg{
      font-size: 30px;
      color: #909090;
      margin-top: 11px;
      color: #ff301e;
    }
    .btn{
      width: 590px;
      height: 84px;
      opacity: 0.4;
      background: linear-gradient(90deg,#40d243, #1fc432);
      border-radius: 57px;
      text-align: center;
      line-height: 84px;
      color: #ffffff;
      font-size: 38px;
      margin-top: 30px;
    }
    .btn-in{
      opacity: 1;
    }
  }
}
</style>
