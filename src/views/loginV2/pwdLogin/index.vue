<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2021-06-01 10:32:13
 * @LastEditors: 孙立政
 * @LastEditTime: 2023-05-23 13:49:23
-->
<template>
  <div class="codeLogins">
    <NavHeight bgc="#fff" />
    <div class="close" @click="goBack">
      <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/loginV2/close.png" alt="">
    </div>

    <div class="box">
      <div class="msg">
        欢迎登录
      </div>
      <div class="input">
        <van-field v-model="form.telephone" :border="false" clearable type="tel" maxlength="13" placeholder="请输入已注册手机号" @clear="clearError" />
        <!-- <van-field v-model="form.password" readonly :border="false" clearable type="password" maxlength="30" placeholder="密码" @click="getInput" @clear="clearError" /> -->
        <van-field v-model="form.password" :border="false" clearable type="password" maxlength="30" placeholder="密码" @clear="clearPwd" />
      </div>
      <div v-if="errSatuts" class="errorMsg animate__animated animate__pulse">
        手机号码输入错误，请检查
      </div>
      <div class="tipMsg1">
        密码登录仅支持已设置过密码的用户
      </div>
      <div class="agreement">
        <img v-if="pwdtipsStatus" class="pwdtips" src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/loginV2/pwdtips.png" alt="">
        <van-checkbox v-model="agreement" checked-color="#39CF3F" icon-size="14px" @click="pwdtipsStatus = false">
          <span :class="pwdtipsStatus?'animate__animated animate__flash':''"> 已阅读并同意 <span class="remind" @click="goUseragt">《用户服务协议》</span>和<span class="remind" @click="goPrivacy">《隐私协议》</span></span>
        </van-checkbox>
      </div>

      <div v-if="form.telephone.length >= 11 && form.password.length > 5" class="btn btn-in" @click="getLogin">登录</div>
      <div v-else class="btn">登录</div>

      <div class="tipMsg2">
        <div v-show="false" style="color: #999" @click="goToCodeLogin">手机号登录</div>
        <div @click="goToRegister">忘记密码</div>
      </div>
    </div>

    <!-- 微信登录 -->
    <WxLogin v-if="true" />

    <div id="app" @dblclick="() => {return false;}">
      <div class="cus-keyboard">
        <Keyboard ref="showKeyboard" :length="length" :default-val="defaultVal" :text.sync="form.password" :keyboard-type="0" />
      </div>
    </div>

  </div>
</template>

<script>
import { isPhone } from '@/utils/validate'
import { loginByPwd } from '@/api/login'
import WxLogin from '../wxLogin/login.vue'
import Keyboard from '@/components/Keyboard'
export default {
  components: { WxLogin, Keyboard },
  data() {
    return {
      form: {
        telephone: '',
        password: ''
      },
      agreement: false,
      pwdtipsStatus: false,
      errSatuts: false,
      logShow: false,
      defaultVal: '',
      length: 50
    }
  },
  watch: {
    'form.telephone': function(newValue, oldValue) {
      if (newValue.length > oldValue.length) { // 文本框中输入
        if (newValue.length === 3 || newValue.length === 8) {
          this.form.telephone += ' '
        }
      } else {
        if (newValue.length === 9 || newValue.length === 4) {
          this.form.telephone = this.form.telephone.trim()
        }
      }
    },
    agreement(val) {
      this.$store.state.login.agreement = val
    }
  },
  mounted() {
    this.$store.state.login.telephone !== '' ? this.form.telephone = this.$store.state.login.telephone : ''
  },
  destroyed() {
    AlipayJSBridge.call('OffScreenshot', {}, function(result) {
      console.log(result)
    })
  },
  methods: {
    getLogin() {
      let Base64 = require('js-base64').Base64
      let str = this.form.telephone.replace(/\s*/g, '')
      if (!isPhone(str)) { this.errSatuts = true; return }
      if (!this.agreement) { this.pwdtipsStatus = true; return }
      this.$store.state.login.telephone = this.form.telephone

      this.$toast.loading({ duration: 0, forbidClick: true })

      let data = {
        telephone: str,
        password: Base64.encode(this.form.password)
      }
      loginByPwd(data).then(res => {
        if (res.status == 200 && res.data.loginStatus == '100') {
          this.$toast('登录成功')
          this.$store.state.login.telephone = ''
          this.$store.commit('setUserId', res.data.id)
          this.$store.commit('setToken', res.data.token)
          this.$store.commit('setPhone', res.data.telephone)
          this.$store.commit('setUserNo', res.data.userNo)
          localStorage.setItem('phone', res.data.telephone)
          localStorage.setItem('userNo', res.data.userNo)
          AlipayJSBridge.call('UserLogin', { userId: res.data.id, token: res.data.token }, function(result) {})
          AlipayJSBridge.call('SetPushId', { id: res.data.id }, function(result) {})
          setTimeout(() => {
            this.$router.push('/index')
            this.$toast.clear()
          }, 1000)
        } else if (res.status == 200 && res.data.loginStatus == '101') {
          this.$toast('当前密码为默认密码，为了您的账户安全，请更改密码后再使用APP')
          localStorage.setItem('phone', res.data.telephone)
          this.$router.push('/editLoginPwd')
        }
      })
    },
    // 打开键盘
    getInput() {
      this.$refs.showKeyboard.show()
      AlipayJSBridge.call('OnScreenshot', {}, function(result) {
        console.log(result)
      })
    },
    // 密码登录
    goPwdLogin() {
      this.$router.push('/pwdLogin')
    },
    // 清除输入框
    clearError() {
      this.form.telephone = ''
      this.errSatuts = false
    },
    clearPwd() {
      this.form.password = ''
      this.errSatuts = false
    },
    goToCodeLogin() {
      this.$store.state.login.telephone = this.form.telephone
      this.$store.state.login.agreement = false
      this.$router.push({ name: 'CodeLogin' })
    },
    goToRegister() {
      this.$router.push({ name: 'Register' })
    },
    goBack() {
      this.$router.push('/index')
    },
    goUseragt() {
      this.$router.push('/useragt')
    },
    goPrivacy() {
      this.$router.push('/privacy')
    }
  }
}
</script>

<style lang="scss" scoped>
.codeLogins{
  min-height: 100vh;
  background-color: #fff;
  .close{
    height: 80px;
    overflow: hidden;
    img{
      width: 40px;
      height: 40px;
      margin-left: 34px;
      display: block;
      margin-top: 20px;
    }
  }
  .box{
    width: 590px;
    margin: 0 auto;
    margin-top: 90px;
    .msg{
      font-size: 60px;
      font-family: PingFangSC-Medium;
      color: #222222;
    }
    .input{
      margin-top: 74px;
      ::v-deep .van-cell{
        border-bottom: 1px solid #dddddd;
        padding: 0;
        .van-field__control{
          height: 100px;
          font-size:39px;
        }
      }
    }
    .errorMsg{
      font-size: 30px;
      color: #909090;
      margin-top: 11px;
      color: #ff301e;
    }
    .tipMsg1{
      font-size: 30px;
      color: #909090;
      margin-top: 18px;
    }
    .agreement{
      font-size: 25px;
      color: #909090;
      margin-top: 95px;
      position: relative;
      .pwdtips{
        width: 224px;
        height: 54px;
        position: absolute;
        top: -55px;
      }
      .remind{
        color: #39CF3F;
      }
    }
    .btn{
      width: 590px;
      height: 84px;
      opacity: 0.4;
      background: linear-gradient(90deg,#40d243, #1fc432);
      border-radius: 57px;
      text-align: center;
      line-height: 84px;
      color: #ffffff;
      font-size: 38px;
      margin-top: 30px;
    }
    .btn-in{
      opacity: 1;
    }
    .tipMsg2{
      color: #666666;
      font-size: 30px;
      margin-top: 48px;
      display: flex;
      justify-content: space-between;
    }
  }
}
</style>
