<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2021-06-01 10:32:13
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-02-23 22:04:11
-->
<template>
  <div class="codeLogins">
    <NavHeight bgc="#fff" />
    <div class="close" @click="goBack">
      <img
        src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/loginV2/close.png"
        alt=""
      >
    </div>

    <div class="box">
      <div class="msg">欢迎登录点滴</div>
      <div class="input">
        <van-field
          v-model="form.telephone"
          :border="false"
          clearable
          type="tel"
          maxlength="13"
          placeholder="请输入手机号"
          @clear="clearError"
        />
      </div>
      <div v-if="errSatuts" class="errorMsg animate__animated animate__pulse">
        手机号码输入错误，请检查
      </div>
      <div class="input" style="margin-top: 10px">
        <van-field
          v-model="form.captchaCode"
          :border="false"
          clearable
          type="tel"
          maxlength="4"
          placeholder="验证码"
          @clear="clearError"
        >
          <template #button>
            <img :src="captchaImg" alt="" class="captchaImg" @click="getCaptcha">
          </template>
        </van-field>
      </div>
      <div class="tipMsg1">未注册手机验证后自动创建点滴账户</div>
      <div class="agreement">
        <van-checkbox
          v-model="agreement"
          checked-color="#39CF3F"
          icon-size="14px"
        >
          已阅读并同意
          <span class="remind" @click="goUseragt">《用户服务协议》</span>和<span
            class="remind"
            @click="goPrivacy"
          >《隐私协议》</span>
        </van-checkbox>
      </div>

      <div
        v-if="form.telephone.length >= 11"
        class="btn btn-in"
        @click="getCode"
      >
        获取短信验证码
      </div>
      <div v-else class="btn">获取短信验证码</div>

      <div class="tipMsg2" @click="goToPwdLogin">账号密码登录</div>
    </div>

    <!-- 微信登录 -->
    <WxLogin v-if="true" />

    <!-- 提示弹出 -->
    <Dialog :show="logShow" @close="logShow = false" @confirm="agree" />
  </div>
</template>

<script>
import { isPhone } from '@/utils/validate'
import Dialog from '@/components/Dialog/login'
import WxLogin from '../wxLogin/login.vue'
import { getCaptcha } from '@/api/login'
export default {
  components: { Dialog, WxLogin },
  data() {
    return {
      form: {
        telephone: '',
        captchaCode: ''
      },
      agreement: false,
      isBtn: false,
      errSatuts: false,
      logShow: false,
      captchaImg: '',
      captchakey: ''
    }
  },
  watch: {
    agreement(val) {
      this.$store.state.login.agreement = val
    }
  },
  mounted() {
    this.getCaptcha()
    this.$store.state.login.telephone !== ''
      ? (this.form.telephone = this.$store.state.login.telephone)
      : ''
  },
  methods: {
    async getCaptcha() {
      this.$toast.loading({
        message: '',
        duration: 0,
        forbidClick: true
      })
      const response = await getCaptcha()

      const blob = new Blob([response.data], { type: 'image/jpeg' })
      const imageUrl = URL.createObjectURL(blob)

      this.captchaImg = imageUrl
      this.captchakey = response.headers['captchakey']
      this.$toast.clear()
    },
    getCode() {
      let str = this.form.telephone.replace(/\s*/g, '')
      if (!isPhone(str)) {
        this.errSatuts = true
        return
      }
      if (this.form.captchaCode.length !== 4) {
        this.$toast('请输入正确的验证码')
        return
      }

      if (!this.agreement) {
        this.logShow = true
        return
      }

      this.$store.state.login.telephone = this.form.telephone
      this.$router.push({ name: 'CodeLoginCode', query: { telephone: str, captchaCode: this.form.captchaCode, captchakey: this.captchakey }})
    },
    // 同意协议
    agree() {
      this.agreement = true
      this.logShow = false
      let str = this.form.telephone.replace(/\s*/g, '')

      this.$store.state.login.telephone = this.form.telephone
      this.$router.push({ name: 'CodeLoginCode', query: { telephone: str }})
    },
    // 密码登录
    goPwdLogin() {
      this.$router.push('/pwdLogin')
    },
    // 清除输入框
    clearError() {
      this.errSatuts = false
    },
    goToPwdLogin() {
      this.$store.state.login.telephone = this.form.telephone
      this.$store.state.login.agreement = false
      this.$router.push({ name: 'PwdLogin' })
    },
    goBack() {
      this.$router.push('/index')
    },
    goUseragt() {
      this.$router.push('/useragt')
    },
    goPrivacy() {
      this.$router.push('/privacy')
    }
  }
}
</script>

<style lang="scss" scoped>
.codeLogins {
  min-height: 100vh;
  background-color: #fff;
  .close {
    height: 80px;
    overflow: hidden;
    img {
      width: 40px;
      height: 40px;
      margin-left: 34px;
      display: block;
      margin-top: 20px;
    }
  }
  .box {
    width: 590px;
    margin: 0 auto;
    margin-top: 90px;
    .msg {
      font-size: 60px;
      font-family: PingFangSC-Medium;
      color: #222222;
    }
    .input {
      display: flex;
      align-items: center;
      .captchaImg {
        width: 200px;
        height: 80px;
        border: 1px solid #d5d5d5;
        border-radius: 5px;
      }

      margin-top: 64px;
      ::v-deep .van-cell {
        border-bottom: 1px solid #dddddd;
        padding: 0;
        .van-field__control {
          height: 100px;
          font-size: 39px;
        }
      }
    }
    .errorMsg {
      font-size: 30px;
      color: #909090;
      margin-top: 11px;
      color: #ff301e;
    }
    .tipMsg1 {
      font-size: 30px;
      color: #909090;
      margin-top: 11px;
    }
    .agreement {
      font-size: 25px;
      color: #909090;
      margin-top: 95px;
      .remind {
        color: #39cf3f;
      }
    }
    .btn {
      width: 590px;
      height: 84px;
      opacity: 0.4;
      background: linear-gradient(90deg, #40d243, #1fc432);
      border-radius: 57px;
      text-align: center;
      line-height: 84px;
      color: #ffffff;
      font-size: 38px;
      margin-top: 30px;
    }
    .btn-in {
      opacity: 1;
    }
    .tipMsg2 {
      color: #666666;
      font-size: 30px;
      margin-top: 48px;
    }
  }
  .wx-login {
    margin-top: 250px;
    .divider {
      width: 580px;
      margin: 0 auto;
    }
  }
}
</style>
