<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2021-06-01 10:32:13
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-11-28 16:55:45
-->
<template>
  <div class="codeLogins">
    <NavHeight bgc="#fff" />
    <div class="close" @click="goBack">
      <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/loginV2/left.png" alt="">
    </div>

    <div class="box">
      <div class="msg">
        输入验证码
      </div>
      <div class="tipMsg3">
        验证码已发送至<span> +86 {{ $store.state.login.telephone }}</span>
      </div>
      <div class="input">
        <van-password-input gutter="20px" :value="form.code" :focused="true" :mask="false" @click="showKeyboard" />
      </div>

      <div v-if="count !== 0" class="tipMsg1">
        {{ count }} 秒后重新获取验证码
      </div>
      <div v-if="count === 0" class="tipMsg2" @click="getCode">
        重新发送
      </div>

    </div>

    <Keyboard ref="showKeyboardRef" :length="6" default-val="" :text.sync="form.code" :keyboard-type="1" />
  </div>
</template>

<script>
import { sendCodeV2, login } from '@/api/login'
import Keyboard from '@/components/Keyboard'
export default {
  components: { Keyboard },
  data() {
    return {
      form: {
        code: ''
      },
      count: 0,
      timer: 0
    }
  },
  watch: {
    'form.code': function(val) {
      val.length == 6 ? this.getLogin() : ''
    }
  },
  mounted() {
    this.$refs.showKeyboardRef.show()
    this.getCode()
  },
  methods: {
    getLogin() {
      this.$toast.loading({ duration: 0, forbidClick: true })
      let data = {
        mobile: this.$route.query.telephone,
        validCode: this.form.code
      }
      login(data).then((res) => {
        if (res.status == 200 && res.data.loginStatus == '100') {
          this.$toast('登录成功')
          this.$store.state.login.telephone = ''
          this.$store.commit('setUserId', res.data.id)
          this.$store.commit('setPhone', res.data.telephone)

          this.$store.commit('setToken', res.data.token)
          this.$store.commit('setUserNo', res.data.userNo)
          localStorage.setItem('phone', res.data.telephone)
          localStorage.setItem('userNo', res.data.userNo)
          AlipayJSBridge.call('UserLogin', { userId: res.data.id, token: res.data.token }, function(result) { })
          AlipayJSBridge.call('SetPushId', { id: res.data.id }, function(result) { })
          this.$router.push('/index')
        } else if (res.status == 200 && res.data.loginStatus == '101') {
          this.$toast('当前密码为默认密码，为了您的账户安全，请更改密码后再使用APP')
          localStorage.setItem('phone', res.data.telephone)
          this.$router.push('/editLoginPwd')
        } else {
          this.form.code = ''
        }
      })
    },
    // 发送验证码
    getCode() {
      let self = this
      let data = {
        phone: this.$route.query.telephone,
        teleplateName: 'login',
        captchaCode: this.$route.query.captchaCode,
        captchakey: this.$route.query.captchakey
      }
      sendCodeV2(data).then((res) => {
        if (res.status == 200) {
          const TIME_COUNT = 60
          if (this.count === 0) {
            this.count = TIME_COUNT
            this.showCode = false
            this.timer = setInterval(() => {
              if (self.count > 0 && self.count <= TIME_COUNT) {
                self.count--
              } else {
                clearInterval(self.timer)
                self.count = 0
              }
            }, 1000)
          }
          this.$toast('短信已发送，有效期5分钟')
          this.$refs.showKeyboardRef.show()
        } else {
          this.$toast(res.message)
          this.count = 0
        }
      })
    },
    showKeyboard() {
      this.$refs.showKeyboardRef.show()
    },
    goBack() {
      this.$router.go(-1)
    }
  }
}
</script>

<style lang="scss" scoped>
.codeLogins {
  min-height: 100vh;
  background-color: #fff;

  .close {
    height: 80px;
    overflow: hidden;

    img {
      width: 40px;
      height: 40px;
      margin-left: 34px;
      display: block;
      margin-top: 20px;
    }
  }

  .box {
    width: 590px;
    margin: 0 auto;
    margin-top: 90px;

    .msg {
      font-size: 60px;
      font-family: PingFangSC-Medium;
      color: #222222;
    }

    .input {
      margin-top: 74px;

      ::v-deep .van-password-input {
        margin: 0;
      }

      ::v-deep [class*=van-hairline]::after {
        border: 0;
      }

      ::v-deep .van-password-input__item {
        border-bottom: 1px solid #c2c2c2;
      }

      ::v-deep .van-password-input__item--focus {
        border-bottom: 1px solid #222222;
      }
    }

    .tipMsg1 {
      font-size: 30px;
      color: #333333;
      margin-top: 70px;
    }

    .tipMsg2 {
      font-size: 30px;
      color: #3488FF;
      margin-top: 70px;
    }

    .tipMsg3 {
      font-size: 30px;
      color: #909090;
      margin-top: 20px;

      span {
        color: #222222;
      }
    }
  }
}
</style>
