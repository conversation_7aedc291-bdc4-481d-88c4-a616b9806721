<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2021-06-01 10:32:13
 * @LastEditors: 孙立政
 * @LastEditTime: 2023-02-01 09:12:13
-->
<template>
  <div class="wxLogins">
    <div class="btn" @click="wxLogin">微信一键登录</div>

    <!-- 提示弹出 -->
    <Dialog :show="logShow" @close="logShow = false" @confirm="agree" />
  </div>
</template>

<script>
import { wechatLogin } from '@/api/login'
import Dialog from '@/components/Dialog/login'
export default {
  components: { Dialog },
  data() {
    return {
      logShow: false,
      isWx: true,
      loginNum: 0
    }
  },
  mounted() {
    let self = this
    // this.isweixin()
    document.addEventListener('mobSever', function(e) {
      if (e.data.data) {
        self.$store.state.login.wx.nickname = '微信用户'
        self.$store.state.login.wx.headimgurl = e.data.data.headimgurl
        self.$store.state.login.wx.openid = e.data.data.openid
        self.$store.state.login.wx.unionid = e.data.data.unionid
        self.$store.state.login.wx.loginNum = self.$store.state.login.wx.loginNum + 1
        let userwxtone = { openId: e.data.data.openid, unionId: e.data.data.unionid }

        self.isWxLogin(userwxtone)
        return false
      } else {
        self.$toast('微信登录失败')
      }

      console.log('====微信返回====')
      console.log(e)
      console.log('====微信end====')
    })
  },
  destroyed() {
    document.removeEventListener('mobSever', function(e) {})
  },
  methods: {
    // 检测微信登录
    isWxLogin(val) {
      console.log(this.$store.state.login.wx.loginNum)
      // if (this.$store.state.login.wx.loginNum > 1) {
      //   return false
      // }
      this.$toast.loading({ duration: 0, forbidClick: true })
      wechatLogin(val).then(res => {
        this.$toast.clear()
        if (res.status == 200) {
          if (res.data.status == 0) {
            this.$toast('登录成功')
            this.$store.commit('setUserId', res.data.id)
            this.$store.commit('setToken', res.data.token)
            this.$store.commit('setPhone', res.data.telephone)
            this.$store.commit('setUserNo', res.data.userNo)
            localStorage.setItem('phone', res.data.telephone)
            localStorage.setItem('userNo', res.data.userNo)
            AlipayJSBridge.call('UserLogin', { userId: res.data.id, token: res.data.token }, function(result) {})
            AlipayJSBridge.call('SetPushId', { id: res.data.id }, function(result) {})
            this.$router.push('/index')
          } else {
            if (localStorage.getItem('isWxBind') == 1) {
              return false
            }
            this.$router.push({ name: 'WxLogin' })
          }
        }
      })
    },
    // 跳转微信
    wxLogin() {
      if (!this.$store.state.login.agreement) {
        this.logShow = true
      } else {
        this.agree()
      }
    },
    agree() {
      let self = this
      console.log('------dgfgfg---------')
      console.log(self.$store.state.login.wx.loginNum)
      console.log('--------dgfgfg-------')
      if (self.$store.state.login.wx.loginNum > 0) {
        console.log(self.$store.state.login.wx.loginNum)
        let userwxtone = { openId: self.$store.state.login.wx.openid, unionId: self.$store.state.login.wx.unionid }
        self.isWxLogin(userwxtone)
        return
      }
      AlipayJSBridge.call('WatchLogin', {}, function(result) {
        console.log('====微信返回ios====')
        console.log(result)
        console.log('====微信end====')
        var u = navigator.userAgent
        var isiOS = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/) // ios终端
        if (isiOS) {
          if (result.success == true) {
            self.$store.state.login.wx.nickname = result.data.nickname
            self.$store.state.login.wx.headimgurl = result.data.headimgurl
            self.$store.state.login.wx.openid = result.data.openid
            self.$store.state.login.wx.unionid = result.data.unionid
            let userwxtone = { openId: result.data.openid, unionId: result.data.unionid }
            console.log('====微信返回888====')
            console.log(userwxtone)
            console.log('====微信返回888====')
            self.isWxLogin(userwxtone)
          } else {
            self.$toast('登录失败')
          }
        }
      })
    },
    isweixin() {
      let self = this
      var u = navigator.userAgent
      var isiOS = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/) // ios终端
      if (isiOS) {
        AlipayJSBridge.call('IsAvailable', {
          packageName: 'weixin://'
        }, function(result) {
          if (result.available != true) {
            self.isWx = false
          }
        })
      } else {
        AlipayJSBridge.call('IsAvailable', {
          packageName: 'com.tencent.mm'
        }, function(result) {
          if (result.available != true) {
            self.isWx = false
          }
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.wxLogins{
  .btn{
      width: 590px;
      height: 84px;
      background: linear-gradient(90deg,#40d243, #1fc432);
      border-radius: 57px;
      text-align: center;
      line-height: 84px;
      color: #ffffff;
      font-size: 38px;
      margin-top: 30px;
    }
    .wx-login{
        margin-top: 250px;
        .divider{
            width: 580px;
            margin: 0 auto;

            .t{
              font-size: 31px;
            }
        }
        .wx-logo{
            text-align: center;
            img{
              width: 78px;
              height: 78px;
              display: block;
              margin: 0 auto;
            }
        }
    }
}
</style>
