<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2021-06-01 10:32:13
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-07-01 15:24:03
-->
<template>
  <div class="codeLogins">
    <NavHeight bgc="#fff" />
    <div class="close" @click="goBack">
      <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/loginV2/left.png" alt="">
    </div>

    <div class="box">
      <div class="msg">
        首次微信登录
      </div>
      <div class="tipMsg2">
        依照《网络安全法》要求，账号需绑定手机号
      </div>
      <div class="input">
        <van-field v-model="form.telephone" :border="false" clearable type="tel" maxlength="11" placeholder="请输入手机号" @clear="clearError" />
      </div>
      <div v-if="errSatuts" class="errorMsg animate__animated animate__pulse">
        手机号码输入错误，请检查
      </div>
      <div class="tipMsg1">
        未注册手机验证后自动创建点滴账户，下次可以用 手机号直接登录
      </div>
      <div class="input" style="margin-top: 10px">
        <van-field
          v-model="form.captchaCode"
          :border="false"
          clearable
          type="tel"
          maxlength="4"
          placeholder="验证码"
          @clear="clearError"
        >
          <template #button>
            <img :src="captchaImg" alt="" class="captchaImg" @click="getCaptcha">
          </template>
        </van-field>
      </div>

      <div v-if="form.telephone.length == 11" class="btn btn-in" @click="getCode">获取短信验证码</div>
      <div v-else class="btn">获取短信验证码</div>

    </div>

  </div>
</template>

<script>
import { isPhone } from '@/utils/validate'
import { getCaptcha, sendCodeV2 } from '@/api/login'
export default {
  data() {
    return {
      form: {
        telephone: '',
        captchaCode: ''
      },
      isBtn: false,
      errSatuts: false,
      logShow: false,
      captchaImg: '',
      captchakey: ''
    }
  },
  watch: {},
  mounted() {
    this.$store.state.login.telephone !== '' ? this.form.telephone = this.$store.state.login.telephone : ''
    localStorage.setItem('isWxBind', 0)
    this.getCaptcha()
  },
  methods: {
    async getCaptcha() {
      this.$toast.loading({
        message: '',
        duration: 0,
        forbidClick: true
      })
      const response = await getCaptcha()

      const blob = new Blob([response.data], { type: 'image/jpeg' })
      const imageUrl = URL.createObjectURL(blob)

      this.captchaImg = imageUrl
      this.captchakey = response.headers['captchakey']
      this.$toast.clear()
    },
    getCode() {
      let str = this.form.telephone.replace(/\s*/g, '')
      if (!isPhone(str)) { this.errSatuts = true; return }
      if (this.form.captchaCode.length !== 4) {
        this.$toast('请输入正确的验证码')
        return
      }
      let data = {
        phone: str,
        teleplateName: 'login',
        captchaCode: this.form.captchaCode,
        captchakey: this.captchakey
      }
      sendCodeV2(data).then((res) => {
        if (res.status == 200) {
          this.$store.state.login.telephone = this.form.telephone
          this.$router.push({ name: 'WxLoginCode', query: { telephone: str, captchaCode: this.form.captchaCode, captchakey: this.captchakey }})
          this.$toast('短信已发送，有效期5分钟')
        } else {
          this.$toast(res.message)
        }
      })
    },
    // 密码登录
    goPwdLogin() {
      this.$router.push('/pwdLogin')
    },
    // 清除输入框
    clearError() {
      this.errSatuts = false
    },
    goBack() {
      this.$router.go(-1)
    }
  }
}
</script>

<style lang="scss" scoped>
.codeLogins{
  min-height: 100vh;
  background-color: #fff;
  .close{
    height: 80px;
    overflow: hidden;
    img{
      width: 40px;
      height: 40px;
      margin-left: 34px;
      display: block;
      margin-top: 20px;
    }
  }
  .box{
    width: 590px;
    margin: 0 auto;
    margin-top: 90px;
    .msg{
      font-size: 60px;
      font-family: PingFangSC-Medium;
      color: #222222;
    }
    .input{
      margin-top: 58px;
      display: flex;
      align-items: center;
      .captchaImg {
        width: 200px;
        height: 80px;
        border: 1px solid #d5d5d5;
        border-radius: 5px;
      }
      ::v-deep .van-cell{
        border-bottom: 2px solid #dddddd;
        padding: 0;
        .van-field__control{
          height: 100px;
          font-size:39px;
        }
      }
    }
    .errorMsg{
      font-size: 30px;
      color: #909090;
      margin-top: 11px;
      color: #ff301e;
    }
    .tipMsg1{
      font-size: 30px;
      color: #909090;
      margin-top: 11px;
    }
    .btn{
      width: 590px;
      height: 84px;
      opacity: 0.4;
      background: linear-gradient(90deg,#40d243, #1fc432);
      border-radius: 57px;
      text-align: center;
      line-height: 84px;
      color: #ffffff;
      font-size: 38px;
      margin-top: 30px;
    }
    .btn-in{
      opacity: 1;
    }
    .tipMsg2{
      color: #333333;
      font-size: 32px;
      margin-top: 36px;
    }
  }
}
</style>
