<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-04-02 17:33:56
 * @LastEditors: 孙立政
 * @LastEditTime: 2021-12-30 11:08:36
-->
<template>
  <div class="content">
    <NavHeight bgc="linear-gradient(45deg, #71d774 0%, #5ecc52 100%)" />
    <Top message="详情" />
    <div class="title">
      <div class="color999">{{ detail.quartersType }}</div>
      <div style="font-size:15px">{{ detail.itemName }}</div>
      <div class="price">
        {{ detail.minSalary }}-{{ detail.maxSalary }}/{{ detail.priceUnit }}
      </div>
      <ul class="title-other">
        <li
          v-for="(item, index) in JSON.parse(detail.quartersDemand)"
          :key="index"
        >
          {{ item }}
        </li>
      </ul>
    </div>
    <div class="line" />
    <div class="other">
      <p style="font-size:16px;margin:30px 0 15px 0">职位要求</p>
      <div class="details" v-html="detail.serveDesc" />
    </div>
    <div class="all">
      <div v-if="allList.length" class="title1">相近职位</div>
      <!-- <div class="line"></div> -->
      <div
        v-for="(item1, index1) in allList"
        :key="index1"
        class="section"
        @click="goDetail(item1.id)"
      >
        <div class="section-cell">
          <div class="cell-title">
            <span>{{ item1.itemName | ellipsis(14) }}</span>
            <span>{{ item1.minSalary }}-{{ item1.maxSalary
            }}{{ item1.priceUnit }}</span>
          </div>
          <div class="cell-desc">
            <span>{{ item1.quartersType }}</span>
            <!-- <span>{{ item1.quartersNature }}</span> -->
          </div>
          <ul class="cell-other">
            <li
              v-for="(item11, index11) in JSON.parse(item1.quartersDemand)"
              :key="index11"
            >
              {{ item11 }}
            </li>
          </ul>
        </div>
      </div>
    </div>
    <div class="btn">
      <van-button color="#323131" @click="goPhone(detail)">联系我们</van-button>
      <van-button type="primary" @click="goNext">立即预约</van-button>
    </div>
  </div>
</template>

<script>
import Top from './components/top'
import { getListDetailRT, recruAppoint, getRelatedList, getPhone } from '@/api/workIndex'
export default {
  components: {
    Top
  },
  data() {
    return {
      detail: {},
      allList: []
    }
  },
  created() {
    this.getListDetail()
  },
  mounted() {
    document.body.scrollTop = 0
    document.documentElement.scrollTop = 0
  },
  methods: {
    getRecruList() {
      let data = {
        id: this.detail.itemId
      }
      getRelatedList(data)
        .then((res) => {
          console.log(res.data)
          if (res.status == 200) {
            if (res.data.length <= 10) {
              this.allList = res.data
              console.log(this.allList)
              // 回到顶部
              document.body.scrollTop = 0
              document.documentElement.scrollTop = 0
            } else {
              this.allList = res.data.slice(0, 10)
            }
          } else {
            this.$toast(res.message)
          }
        })
    },
    getListDetail(id1) {
      let data = {
        id: id1 ? id1 : this.$route.query.id
      }
      getListDetailRT(data)
        .then((res) => {
          console.log(res)
          if (res.status == 200) {
            this.detail = res.data
            this.getRecruList()
          } else {
            this.$toast(res.message)
          }
        })
    },
    goNext() {
      console.log(this.detail)
      let data = {
        userId: this.$store.getters.getUserId,
        quartersType: this.detail.quartersType,
        userName: localStorage.getItem('username'),
        itemId: this.detail.itemId,
        maxSalary: this.detail.maxSalary,
        minSalary: this.detail.maxSalary,
        telephone: localStorage.getItem('phone'),
        priceUnit: this.detail.priceUnit
      }
      recruAppoint(data)
        .then((res) => {
          if (res.status == 200) {
            this.$toast('预约成功')
          } else {
            this.$toast(res.message)
          }
        })
    },
    goPhone() {
      console.log(this.detail)
      let data = {
        id: this.detail.itemId
      }
      getPhone(data).then((res) => {
        console.log(res.data.facilitatorTel)
        AlipayJSBridge.call('CallPhone', {
          phoneNum: res.data.facilitatorTel
        }, function(result) {})
      })
    },
    goDetail(id) {
      this.getListDetail(id)
    }
  }
}
</script>

<style scoped lang="scss">
.content::before {
  // 利用伪元素设置整个页面的背景色
  content: " ";
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: -100;
  min-height: 100%;
  background-color: #fff;
}
.content {
  ::v-deep .details{
    width: 710px;
    img{
      width: 100%;
    }
  }
  background-color: #fff;
  .title {
    width: 100%;
    box-sizing: border-box;
    padding: 26px;
    .price {
      font-size: 42px;
      color: #fb636d;
    }
    .color999 {
      font-size: 24px;
      color: #999;
      line-height: 40px;
    }
    .title-other {
      display: flex;
      flex-wrap: wrap;
      margin-top: 12px;
      font-size: 20px;
      color: #666;
      li {
        flex-shrink: 0;
        background-color: #f5f5f5;
        padding: 4px 16px;
        margin-bottom:20px;
        margin-right: 12px;
        border-radius: 4px;
      }
    }
  }
  .line {
    width: 100%;
    height: 15px;
    background-color: #faf9f9;
    margin-top: 30px;
  }
  .other {
    margin: 0 20px;
    font-size: 24px;
    padding-bottom: 50px;
    // background-color: #fff;
    // border-bottom: 1px solid #e5e5e5;
    .color999 {
      color: #999;
      line-height: 40px;
    }
    .blue {
      color: #6095f0;
      margin-top: 30px;
    }
  }
}
.section:last-child {
  margin-bottom: 30px;
}
.all {
  background-color: #f3f4f9;
  padding-bottom: 20px;
  .title1 {
    padding: 30px 0 20px 15px;
    font-size: 36px;
  }
  .line {
    width: 100%;
    height: 15px;
  }
}
.section {
  min-height: 150px;
  background-color: #fff;
  margin: 14px  20px 0;
  border-radius: 12px;
  box-sizing: border-box;
  .section-cell {
    padding: 22px;
    .cell-title {
      display: flex;
      justify-content: space-between;
      font-size: 34px;
      font-family: PingFangSC;
      margin-bottom: 14px;
      span:nth-of-type(2) {
        font-size: 32px;
      }
    }
    .cell-desc {
      font-size: 24px;
    }
    .cell-other {
      display: flex;
      flex-wrap: wrap;
      margin-top: 12px;
      font-size:20px;
      color: #666;
      li {
        flex-shrink: 0;
        background-color: #f5f5f5;
        padding: 4px 16px;
        margin-right: 12px;
        margin-bottom: 12px;
        border-radius: 4px;
      }
    }
  }
}
.btn {
  padding-top: 78px;
  padding-bottom: 78px;
  ::v-deep .van-button--primary {
    width: 352px;
    border: 0;
    background: linear-gradient(45deg, #71d774 0%, #5ecc52 100%);
  }
  ::v-deep .van-button--default {
    width: 258px;
    margin-left: 64px;
    margin-right: 12px;
  }
}
</style>
