<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-03-31 19:01:31
 * @LastEditors: 孙立政
 * @LastEditTime: 2021-12-30 10:12:14
-->
<template>
  <div class="content">
    <NavHeight bgc="#fff" />
    <div class="status" :style="statusVar" />
    <div class="statusOccupancy" :style="statusVar" />
    <div class="navbar" :style="'top:' + $store.getters.getStatusHeight + 'px'">
      <div class="backimg">
        <img
          src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/common/arrowlwft.png"
          @click="goback"
        >
      </div>
    </div>
    <div class="headOccupancy" />

    <Tips :detail="detail" />
    <PrimaryInfo :detail="detail" />
    <Content :detail="detail" />
    <OrderInfo :detail="detail" />
    <!-- 待服务商接单，预约成功进行中；两种状态才显示 -->
    <div
      v-if="detail.status == 10 || detail.status == 20 || detail.status == 30"
      class="btn"
    >
      <van-button @click="clickBtn(detail, 100)">取消预约</van-button>
      <van-button
        type="primary"
        @click="clickBtn(detail, 200)"
      >已完成</van-button>
    </div>
  </div>
</template>

<script>
import Tips from './components/tips'
import PrimaryInfo from './components/primaryInfo'
import OrderInfo from './components/orderInfo'
import Content from './components/content'
// import Top1 from './components/top1'
import { orderDetail, btnOrder } from '@/api/workIndex'
export default {
  components: {
    Tips,
    PrimaryInfo,
    OrderInfo,
    Content
  },
  data() {
    return {
      detail: {}
    }
  },
  computed: {
    statusVar() {
      return {
        '--top-status-height': this.$store.getters.getStatusHeight + 'px'
      }
    }
  },
  created() {
    this.getDetail()
  },
  mounted() {},
  methods: {
    goback() {
      this.$router.go(-1)
    },
    clickBtn(item, type) {
      console.log(item)
      let data = {
        userId: this.$store.getters.getUserId,
        orderId: item.orderId,
        type: 'cancel'
      }
      if (type == 100) {
        // 取消
      } else if (type == 200) {
        // 完成
        data.type = 'finish'
      }
      btnOrder(data)
        .then((res) => {
          if (res.status == 200) {
            this.$toast(res.message)
            this.$router.push({
              path: '/workIndex/appointment/myOrder'
            })
          } else {
            this.$toast(res.message)
          }
        })
    },
    getDetail() {
      let data = {
        type: this.$route.query.type,
        orderId: this.$route.query.orderId
      }
      orderDetail(data)
        .then((res) => {
          if (res.status == 200) {
            this.detail = res.data
          }
        })
    }
  }
}
</script>

<style scoped lang="scss">
.content {
  padding-bottom: 30px;
  .status {
    //状态栏
    position: fixed;
    left: 0;
    top: 0;
    z-index: 999;
    width: 100%;
    height: var(--top-status-height);
    background: #fff;
  }
  .statusOccupancy {
    height: var(--top-status-height);
  }
  .headOccupancy {
    width: 100%;
    height:114px;
  }
  .navbar {
    display: flex;
    justify-content: space-between;
    width: 100%;
    position: fixed;
    top: 0;
    z-index: 2;
    background-color: #fff;
    .backimg {
      width: 20px;
      height: 31px;
      margin-left: 36px;
      img{
        width: 25px;
        height: 35px;
      }
    }
    height:90px;
    line-height: 90px;
    font-size: 30px;
    font-weight: bold;
  }
  .btn {
    display: flex;
    align-items: center;
    margin: 0 58px;
    padding: 120px 0 0 0;
  }

  // 按钮
  ::v-deep .van-button--primary {
    width: 300px;
    border: 0;
    background: linear-gradient(45deg, #71d774 0%, #5ecc52 100%);
    margin-left:32px;
  }
  ::v-deep .van-button--normal {
    width: 300px;
    border: 0;
    color: #fff;
    border-radius: 12px;
    background-color: #323131;
  }
}
</style>
