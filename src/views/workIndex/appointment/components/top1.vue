<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-03-31 16:26:39
 * @LastEditors: 孙立政
 * @LastEditTime: 2021-12-29 20:10:28
-->
<template>
  <div class="content">
    <div class="statusTop">
      <van-nav-bar title="" left-text="" left-arrow :border="false">
        <template #title>
          <div>我的发布</div>
        </template>
        <template #left>
          <van-icon
            name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/common/arrowlwft.png"
            size="18"
            @click="goback"
          />
        </template>
      </van-nav-bar>
    </div>
    <div style="height: 46px" />
  </div>
</template>

<script>
export default {
  components: {},
  data() {
    return {
      message: '本地服务'
    }
  },
  mounted() {},
  methods: {
    goback() {
      this.$router.go(-1)
    }
  }
}
</script>

<style lang="scss" scoped>
.content {
  .statusTop {
    width: 100%;
    position: fixed;
    // top: 0;
    left: 0;
    z-index: 1;
    background-color: #fff;
  }

  ::v-deep .van-nav-bar__title {
    font-size: 34px;
    font-family: PingFangSC;
    color: #000010;
  }

  ::v-deep .van-nav-bar {
    background: none;
  }
}

</style>
