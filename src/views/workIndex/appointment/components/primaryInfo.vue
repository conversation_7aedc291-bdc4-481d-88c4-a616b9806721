<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-03-31 19:40:53
 * @LastEditors: zhaoyuxin
 * @LastEditTime: 2021-06-07 11:43:40
-->
<template>
  <!-- 订单基本信息 -->
  <div class="content">
    <div class="info-cell">
      <div class="cell-name">
        <span>基本信息</span>
      </div>
      <div class="cell-line1">
        <p style="font-size:15px;color:#999">{{ detail.quartersType }}</p>
        <p style="color:#000010;font-size:15px">{{ detail.itemName }}</p>
        <p v-if="detail.categoryName=='家庭保洁'||detail.categoryName=='上门维修'" style="color:#FB636D;font-size:21px">{{ detail.maxPrice }}{{ detail.salaryUnit }}</p>
        <p v-else style="color:#FB636D;font-size:21px">{{ detail.minSalary }}-{{ detail.maxSalary }}/{{ detail.priceUnit }}</p>
        <ul style="border-bottom:1px solid #E8E9EA;padding-bottom:20px">
          <li v-for="(item,index) in JSON.parse(detail.quartersDemand)" :key="index">{{ item }}</li>
        </ul>
        <p style="color:#2C2C2C;font-size:16px;margin:12px 0">职位要求</p>
        <p style="color:#323131;font-size:12px;" v-html="detail.serveDesc" />
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: { detail: { type: Object, default: function() { return {} } }},
  data() {
    return {
      addressShow: false
    }
  },
  mounted() {},
  methods: {
  }
}
</script>

<style lang="scss" scoped>
	.content {
		width: 710px;
		margin: 0 auto;
		background-color: #fff;
		border-radius: 20px;
		margin-bottom: 12px;

		.info-cell {
			margin: 0 24px;
			.cell-name {
				height: 89px;
				line-height: 89px;
				color: #2c2c2c;
				font-size: 32px;
				font-family: PingFangSC;
				border-bottom: 1px solid #EFEFEF;
			}

			.cell-line {
				display: flex;
				justify-content: space-between;
				padding: 20px 0;
				font-size: 28px;
				.van-icon {
					top: 4px;
					margin-left: .14px;
				}
				>span {
					color: #7F7F87;
				}
				>div {
					width: 75%;
					text-align: right;
					color: #000010;
				}

			}
			.cell-line1 {
				font-size: 24px;
				ul {
					display: flex;
					flex-wrap: wrap;
					li {
						flex-shrink: 0;
						font-size: 24px;
						color: #666;
						padding: 6px  18px;
						background-color: #f5f5f5;
						border-radius: 4px;
						margin-right: 12px;
						margin-bottom: 12px;
					}
				}
			}
			.blue {
				font-size: 12px;
				color:#6095F0;
			}
		}
	}
</style>
