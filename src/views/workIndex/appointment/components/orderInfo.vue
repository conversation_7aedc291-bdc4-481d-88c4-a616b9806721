<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: zhaoyu<PERSON>
 * @Date: 2021-03-31 19:53:26
 * @LastEditors: zhaoyuxin
 * @LastEditTime: 2021-06-09 16:42:30
-->
<template>
  <!-- 订单信息 -->
  <div class="content">
    <div class="info-cell">
      <div class="cell-name">
        订单信息
      </div>
      <div class="cell-line">
        <span>订单号</span>
        <div>
          <span>{{ detail.orderNo }}</span>
          <div v-clipboard:copy="detail.orderNo" v-clipboard:success="onCopy" class="copy" />
        </div>
      </div>
      <div class="cell-line">
        <span>下单时间</span>
        <div>
          <span>{{ detail.orderTime }}</span>
        </div>
      </div>

    </div>
  </div>
</template>

<script>
export default {
  props: {
    detail: {
      type: Object,
      default: function() {
        return {}
      }
    }
  },
  data() {
    return {}
  },
  mounted() {},
  methods: {
    onCopy(e) {
      this.$toast({
        duration: 3000, // 持续展示 toast
        forbidClick: false,
        message: '复制成功'
      })
    }
  }
}
</script>

<style lang="scss" scoped>
	.content {
		width: 710px;
		height: auto;
		margin: 0 auto;
		background-color: #fff;
		border-radius:20px;

		.info-cell {
			margin: 0 24px 12px;
			.cell-name {
				height: 97px;
				line-height: 97px;
				border-bottom:1px solid #efefef;
				color: #2c2c2c;
				font-size: 32px;
				font-family: PingFangSC;
				margin-bottom: 15px;
			}

			.cell-line {
				display: flex;
				justify-content: space-between;
				align-items: center;
				min-height:62px;
				font-size:26px;
				color: #000010;
				opacity: 0.5;

				>div {
					display: flex;
					justify-content: space-between;
					align-items: center;
				}

				.copy {
					width:73px;
					height: 36px;
					margin-left: 22px;
					background-image: url("https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/order/copy.png");
					background-size: 100% 100%;
					margin-top: -4px;
				}
			}
		}
	}
</style>
