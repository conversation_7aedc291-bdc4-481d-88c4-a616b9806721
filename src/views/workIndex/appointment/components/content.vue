<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <PERSON>haoyu<PERSON>
 * @Date: 2021-03-31 19:53:26
 * @LastEditors: zhaoyuxin
 * @LastEditTime: 2021-06-09 16:41:44
-->
<template>
  <!-- 需求内容 -->
  <div v-if="detail.categoryName=='家庭保洁'||detail.categoryName=='上门维修'" class="content">
    <div class="info-cell">
      <div class="cell-name">
        需求内容
      </div>
      <div class="desc">
        <img :src="detail.itemImg" alt="">
        <div>
          <p>{{ detail.itemName }}</p>
          <p>{{ detail.itemDesc }}</p>
          <p>{{ detail.maxPrice }}{{ detail.priceUnit }}</p>
        </div>
      </div>
      <div v-if="detail.categoryName=='家庭保洁'" class="cell-line">
        <span>保洁面积</span>
        <span>{{ detail.area }}平方米</span>
      </div>
      <div v-if="detail.categoryName=='家庭保洁'" class="cell-line">
        <span>服务人员</span>
        <span>{{ detail.number }}人</span>
      </div>
      <div class="cell-line1">
        <span>服务明细：</span>
        <span v-html="detail.serveDesc" />
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: { detail: { type: Object, default: function() { return {} } }},
  data() {
    return {}
  },
  mounted() {},
  methods: {}
}
</script>

<style lang="scss" scoped>
	.content {
		width: 710px;
		height: auto;
		margin: 0 auto 12px;
		background-color: #fff;
		border-radius:20px;

		.info-cell {
			margin: 0 24px 12px;
			.cell-name {
				height: 97px;
				line-height: 97px;
				border-bottom: 1px solid #efefef;
				color: #2c2c2c;
				font-size: 32px;
				font-family: PingFangSC;
				margin-bottom:15px;
			}
			.desc {
				display: flex;
				width: 710px;
				min-height:188px;
				box-sizing: border-box;
				color: #000010;
				img {
					width: 134px;
					height:134px;
					border-radius: 8px;
					background-color: gray;
					margin-right: 18px;
					overflow: hidden;
				}
				p:nth-of-type(1){
					font-size: 28px;
				}
				p:nth-of-type(2){
					width: 506px;
					font-size: 20px;
					opacity: 0.47;
				}
				p:nth-of-type(3){
					font-size:20px;
					color: #FB636D;
				}
			}
			.cell-line {
				display: flex;
				justify-content: space-between;
				align-items: center;
				height:100px;
				font-size:28px;
				color: #5E5E5E;
				border-bottom: 1px solid #efefef;

			}
			.cell-line1 {
				font-size: 26px;
				margin: 36px 16px 0 0;
				>span:nth-of-type(2){
					color: #999;
				}
			}
		}
	}
</style>
