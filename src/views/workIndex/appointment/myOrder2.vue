<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-04-06 10:52:48
 * @LastEditors: 孙立政
 * @LastEditTime: 2021-12-13 13:52:01
-->
<template>
  <div class="content">
    <!-- <NavHeight bgc="#f3f4f9" /> -->
    <!-- <Top1 message="我的预约" /> -->
    <!-- tab切换 -->
    <div v-if="false" class="tabs-box">
      <div class="tabs">
        <van-tabs
          v-model="active"
          background="#f3f4f9"
          line-width="23px"
          title-inactive-color="#000"
          title-active-color="#000"
          @click="clickTab"
        >
          <van-tab
            v-for="item in list"
            :key="item.index"
            :title="item.title"
          />
        </van-tabs>
      </div>
    </div>
    <div style="height:46px" />
    <div class="wrap">
      <div v-for="(item,index) in allList" v-show="item.categoryName == '招聘信息'" :key="index" class="cell-order">
        <div class="order-status" @click="goDetail(item)">
          <span>{{ item.orderTime }}</span>
          <span v-if="item.status==10">新订单</span>
          <span v-if="item.status==20">待接单</span>
          <span v-if="item.status==30">已接单</span>
          <span v-if="item.status==40">已完成</span>
          <span v-if="item.status==50">已取消</span>
        </div>
        <div class="order-desc" @click="goDetail(item)">
          <!-- <img src="" alt=""> -->
          <div class="right">
            <p>{{ item.categoryName }}-{{ item.typeName }}</p>
            <!-- <p v-if="item.categoryName=='家庭保洁'||item.categoryName=='上门维修'">{{item.maxPrice}}{{item.salaryUnit}}</p> -->
            <p v-if="item.minPrice&&item.maxPrice">{{ item.minPrice }}-{{ item.maxPrice }}{{ item.salaryUnit }}</p>
            <p v-else>{{ item.maxPrice }}{{ item.salaryUnit }}</p>
          </div>
        </div>
        <div v-if="item.status==10||item.status==20||item.status==30" class="order-btn">
          <div class="cancel" @click="clickBtn(item,100)">取消</div>
          <div class="confirm" @click="clickBtn(item,200)">完成</div>
        </div>
      </div>
    </div>
    <!-- 空订单 -->
    <!-- <order-kong v-if="allList.length==0" /> -->
  </div>
</template>

<script>
// import Top1 from './components/top1'
import { allAppointOrder, pendingOrder, cancelOrder, finishOrder, btnOrder } from '@/api/work'
// import orderKong from '@/components/orderKong.vue'
export default {
  components: {
    // Top1
    // orderKong
  },
  data() {
    return {
      active: '',
      list: [
        {
          index: 1,
          title: '全部'
        },
        {
          index: 2,
          title: '发布中'
        },
        {
          index: 3,
          title: '已完成'
        },
        {
          index: 4,
          title: '已取消'
        }
      ],
      temp: [1, 2, 3, 4, 5, 6, 7, 8],
      allList: [],
      currentTab: 0
    }
  },
  created() {
    this.getAllOrder()
  },
  mounted() {},
  methods: {
    clickTab(name) {
      console.log(name)
      this.currentTab = name
      if (name == 0) {
        this.getAllOrder()
      } else if (name == 1) {
        this.getPendingOrder()
      } else if (name == 2) {
        this.getFinishOrder()
      } else if (name == 3) {
        this.getCancelOrder()
      }
    },
    getAllOrder() {
      let data = {
        userId: this.$store.getters.getUserId,
        pageNum: 1,
        pageSize: 10000
      }
      allAppointOrder(data).then((res) => {
        if (res.status == 200) {
          this.allList = res.data.data
        }
      })
    },
    getPendingOrder() {
      let data = {
        userId: this.$store.getters.getUserId,
        pageNum: 1,
        pageSize: 10000
      }
      pendingOrder(data).then((res) => {
        if (res.status == 200) {
          this.allList = res.data.data
        }
      })
    },
    getFinishOrder() {
      let data = {
        userId: this.$store.getters.getUserId,
        pageNum: 1,
        pageSize: 10000
      }
      finishOrder(data).then((res) => {
        if (res.status == 200) {
          this.allList = res.data.data
        }
      })
    },
    getCancelOrder() {
      let data = {
        userId: this.$store.getters.getUserId,
        pageNum: 1,
        pageSize: 10000
      }
      cancelOrder(data).then((res) => {
        if (res.status == 200) {
          this.allList = res.data.data
        }
      })
    },
    clickBtn(item, type) {
      console.log(item)
      let data = {
        userId: this.$store.getters.getUserId,
        orderId: item.orderId,
        type: 'cancel'
      }
      if (type == 100) { // 取消

      } else if (type == 200) { // 完成
        data.type = 'finish'
      }
      btnOrder(data).then((res) => {
        if (res.status == 200) {
          this.$toast(res.message)
          if (this.currentTab == 0) {
            this.getAllOrder()
          } else if (this.currentTab == 1) {
            this.getPendingOrder()
          }
        } else {
          this.$toast(res.message)
        }
      })
    },
    goDetail(item) {
      console.log(item)
      if (item.type == 20) { // 服务商发布
        this.$router.push({
          path: '/work/appointment/recruOrderDetail',
          query: {
            orderId: item.orderId,
            type: item.type
          }
        })
      } else { // 用户自己发布
        this.$router.push({
          path: '/work/appointment/cleanOrderDetail',
          query: {
            orderId: item.orderId,
            type: item.type
          }
        })
      }
    }
  }
}
</script>

<style scoped lang="scss">
.content {
  .tabs-box {
    position: fixed;
    width: 100%;
    margin-top: -1px;
    ::v-deep .van-tab--active {
      font-size: 30px;
    }
    ::v-deep .van-tabs__line {
      background-color: #37c204;
    }
    ::v-deep .van-tabs__wrap--scrollable .van-tabs__nav--complete {
      padding-left: 0;
      padding-right: 0;
    }
    ::v-deep .van-tabs--line .van-tabs__wrap {
      padding-bottom: 18px;
    }
  }
  .wrap {
      .cell-order {
          width: 710px;
          min-height: 258px;
          background-color: #fff;
          border-radius: 20px;
          margin: 18px auto;
          padding: 0 20px;
          box-sizing: border-box;
          .order-status {
          display: flex;
          justify-content: space-between;
          align-items: center;
          height: 87px;
          border-bottom: 1px solid #E5E5E5;
          color: #A7A3A1;
          font-size: 26px;
      }
      .order-desc {
          display: flex;
          padding-top: 28px;
          font-size: 28px;
          font-family: PingFangSC;
          img {
              width:153px;
              height: 116px;
              border-radius:10px;
              background-color: gray;
              overflow: hidden;
              margin-right: 26px;
          }
          .right {
              p:nth-of-type(1){
                  margin-bottom: 10px;
              }
          }
      }
      .order-btn {
          display: flex;
          justify-content: flex-end;
          padding-top: 24px;
          color: #666666;
          padding-bottom:26px;
          font-size: 24px;
          .cancel,.confirm {
              width: 132px;
              height: 54px;
              line-height: 54px;
              text-align: center;
              border: 1px solid #A7A3A1;
              border-radius: 10px;

          }
          .cancel {
              margin-right:24px;
          }
      }
      }

  }
}
</style>
