<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-04-02 10:08:31
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-10-09 16:44:59
-->
<template>
  <div class="content">
    <NavHeight bgc="linear-gradient(45deg, #71d774 0%, #5ecc52 100%)" />
    <Top />
    <div class="wrap">
      <div class="cell-set">
        <div class="set-line">
          <van-field
            v-model="clean.typeName"
            label="需求类型"
            placeholder="请选择类型"
            input-align="right"
            right-icon="arrow"
            readonly
            @click-input="repairChange()"
          />
        </div>
        <div class="set-line">
          <van-field
            v-model="clean.appointmentTime"
            label="服务时间"
            placeholder="请选择时间"
            input-align="right"
            right-icon="arrow"
            readonly
            @click-input="dateChange()"
          />
        </div>
        <div class="set-line">
          <van-field
            v-model="clean.orderAddress"
            label="服务地址"
            placeholder="请选择地址"
            input-align="right"
            right-icon="arrow"
            readonly
            @click-input="addressChange()"
          />
        </div>
        <div class="set-line">
          <span>预算金额</span>
          <div class="set-line-right">
            <input
              v-model="clean.minPrice"
              type="text"
              maxlength="6"
              placeholder="最低金额"
              onkeyup="this.value=this.value.replace(/\D/g,'')"
            >
            <div class="line" />
            <input
              v-model="clean.maxPrice"
              type="text"
              maxlength="6"
              placeholder="最高金额"
              onkeyup="this.value=this.value.replace(/\D/g,'')"
            >
            元
          </div>
        </div>
        <div v-if="type == 1" class="set-line">
          <van-field
            v-model="clean.area"
            label="保洁面积"
            placeholder="总面积"
            input-align="right"
            :border="false"
            type="digit"
            maxlength="6"
          />
          <span class="right">平方米</span>
        </div>
        <div v-if="type == 1" class="set-line">
          <van-field
            v-model="clean.number"
            label="服务人员"
            placeholder="总人数"
            input-align="right"
            :border="false"
            type="digit"
            maxlength="6"
          />
          <span style="margin-left:10px">人</span>
        </div>
        <div class="set-line area">
          <van-field
            v-model="clean.detailDesc"
            rows="4"
            autosize
            label=""
            type="textarea"
            maxlength="200"
            placeholder="如有其他需求，详细描述你想要的服务（至少10个字）"
            show-word-limit
          />
        </div>
      </div>
      <div class="bottom">
        <van-uploader
          v-model="imgList"
          :before-delete="afterDelete"
          :before-read="beforeRead"
          max-count="3"
        />
      </div>

      <div class="btn">
        <van-button type="primary" @click="goNext">立即发布</van-button>
      </div>
    </div>
    <!-- 保洁选择 -->
    <van-popup v-model="repairshow" position="bottom" :style="{}">
      <van-picker
        title=""
        show-toolbar
        :columns="columns"
        @confirm="repairConfirm"
        @cancel="repairCancel"
      />
    </van-popup>
    <!-- 时间选择器 -->
    <van-popup v-model="dateshow" position="bottom" :style="{}">
      <van-datetime-picker
        v-model="currentTime1"
        type="datehour"
        title="选择时间"
        :min-date="minDate"
        :max-date="maxDate"
        @confirm="dateConfirm1"
        @cancel="dateCancel1"
      />
    </van-popup>
    <!-- 选择地址弹框 -->
    <van-popup
      v-model="addressShow"
      position="bottom"
      round
      closeable
      :style="{ width: '100%', height: '350px' }"
    >
      <div class="box">
        <div class="title">
          <div />
          <div>选择地址</div>
          <div class="addaddress" @click="goAddress">添加地址</div>
        </div>
        <div class="body">
          <van-radio-group v-model="radiovalue" @change="radioChange">
            <div class="address-pop" style="margin-top: 50px;">
              <div v-for="(item, index) in temparr" :key="index" class="item">
                <div class="item-left">
                  <van-radio
                    :name="index"
                    icon-size="19px"
                    checked-color="#5dcb4f"
                  >
                    <div class="item-detail">
                      <!-- 最多可以输入18个字 -->
                      <div class="">{{ item.address | ellipsis(18) }}</div>
                      <div class="">
                        <span style="margin-right:13px">{{
                          item.username
                        }}</span>
                        <span>{{ item.mobile }}</span>
                      </div>
                    </div>
                  </van-radio>
                </div>
              </div>
            </div>
          </van-radio-group>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script>
import Top from './components/top'
import { formatDate } from '@/utils/date'
import { addressList } from '@/api/takeout'
import { postClean, postRepair, secCate } from '@/api/workIndex'
import { upload } from '@/api/my'
export default {
  components: {
    Top
  },
  data() {
    return {
      clean: {
        typeName: '',
        appointmentTime: '',
        orderAddress: '',
        area: '',
        number: '',
        detailDesc: '',
        demandImg: [],
        minPrice: '',
        maxPrice: ''
      },
      dateshow: false,
      repairshow: false,
      currentTime1: new Date(),
      minDate: new Date(),
      maxDate: new Date(2025, 10, 1),
      columns: [],
      addressShow: false,
      temparr: [],
      radiovalue: '',
      type: '',
      imgType: '',
      maxSize: 0.5,
      imgList: []
    }
  },
  beforeRouteEnter(to, from, next) {
    console.log(to, from)
    if (to.path == '/workIndex/needs/clean') {
      to.meta.keepAlive = true
    }
    next()
  },
  beforeRouteLeave(to, from, next) {
    if (to.path == '/address/index') {
      from.meta.keepAlive = true
    } else {
      this.removeKeepAliveCache()
    }
    next()
  },
  activated() {
    console.log(11)
    this.getAdrList()
  },
  created() {
    this.getAdrList()
    this.type = this.$route.query.type == 1 ? 1 : 2
    this.getsecCate()
  },
  mounted() {},
  methods: {
    getsecCate() {
      let self = this
      let data = {
        categoryName: this.type == 1 ? '家庭保洁' : '上门维修'
      }
      secCate(data)
        .then((res) => {
          console.log(res)
          if (res.status == 200) {
            res.data.map((item) => {
              self.columns.push(item.typeName)
            })
          }
        })
    },
    goNext() {
      if (this.clean.typeName == '') {
        this.$toast('请选择需求类型')
        return false
      }
      if (this.clean.appointmentTime == '') {
        this.$toast('请选择服务时间')
        return false
      }
      if (this.clean.orderAddress == '') {
        this.$toast('请选择服务地址')
        return false
      }
      if (this.clean.minPrice == '' || this.clean.maxPrice == '') {
        this.$toast('预算金额请填写完整')
        return false
      }
      var reg = /^\d+/
      if (reg.test(this.clean.minPrice) == '' || reg.test(this.clean.maxPrice) == '') {
        this.$toast('预算金额请输入数字')
        return false
      }
      if (this.clean.area == '' && this.type == 1) {
        this.$toast('请输入保洁面积')
        return false
      }
      if (this.clean.number == '' && this.type == 1) {
        this.$toast('请输入服务人数')
        return false
      }
      if (this.clean.detailDesc == '' || this.clean.detailDesc.length < 10) {
        this.$toast('详细信息最少输入10个字')
        return false
      }
      const isEmoji = str => /(\ud83c[\udf00-\udfff])|(\ud83d[\udc00-\ude4f\ude80-\udeff])|[\u2600-\u2B55]/g.test(str)
      if (isEmoji(this.clean.detailDesc)) {
        this.$toast('请勿输入表情')
        return false
      }
      this.okPress()
    },
    dateConfirm1(e) {
      console.log(e)
      this.clean.appointmentTime = formatDate(e, 'yyyy-MM-dd hh:00')
      this.currentTime1 = new Date()
      this.dateshow = false
    },
    dateCancel1(e) {
      this.currentTime1 = new Date()
      this.dateshow = false
    },
    dateChange() {
      this.dateshow = true
    },
    repairChange() {
      this.repairshow = true
    },
    repairConfirm(e) {
      console.log(e)
      this.clean.typeName = e
      this.repairshow = false
    },
    repairCancel() {
      this.repairshow = false
    },
    okPress() {
      let self = this
      this.$dialog
        .confirm({
          title: '确认要发布该需求？',
          message:
            '请确认您的发布不含色情违法、广告营销等垃圾信息，如多次发布垃圾信息，将会被封号处理哦',
          confirmButtonColor: '#6095F0'
        })
        .then(() => {
          self.clean.userId = self.$store.getters.getUserId
          self.clean.userName = localStorage.getItem('username')
          self.clean.telephone = localStorage.getItem('phone')
          let tempImg = []
          self.imgList.map((item) => {
            tempImg.push(item.url)
          })
          self.clean.demandImg = tempImg
          // type 为1表示保洁 2表示维修
          if (self.type == 1) {
            postClean(self.clean)
              .then((res) => {
                if (res.status == 200) {
                  self.$toast(res.message)
                  this.$router.push({
                    path: '/workIndex/appointment/myOrder'
                  })
                } else {
                  self.$toast(res.message)
                }
              })
          } else {
            let data = {
              userId: self.$store.getters.getUserId,
              userName: localStorage.getItem('username'),
              telephone: localStorage.getItem('phone'),
              appointmentTime: self.clean.appointmentTime,
              detailDesc: self.clean.detailDesc,
              orderAddress: self.clean.orderAddress,
              typeName: self.clean.typeName,
              maxPrice: self.clean.maxPrice,
              minPrice: self.clean.minPrice,
              demandImg: self.clean.demandImg
            }
            postRepair(data)
              .then((res) => {
                if (res.status == 200) {
                  self.$toast(res.message)
                  this.$router.push({
                    path: '/workIndex/appointment/myOrder'
                  })
                } else {
                  self.$toast(res.message)
                }
              })
          }
        })
    },
    addressChange() {
      this.addressShow = true
    },
    radioChange(e) {
      this.clean.orderAddress =
        this.temparr[e].address +
        this.temparr[e].username +
        this.temparr[e].mobile
      this.addressShow = false
    },
    goAddress() {
      this.addressShow = false
      this.$router.push({
        name: 'AddressAdd'
      })
    },
    getAdrList() {
      let self = this
      let marketSn = 'SH800430'
      addressList(marketSn)
        .then(function(res) {
          if (res.status == 200) {
            self.temparr = res.data
          }
        })
    },
    afterDelete(index) {
      const findIdx = this.imgList.findIndex((item) => item.url == index.url)
      this.imgList.splice(findIdx, 1)
    },
    // 图片上传接口
    afterRead(file) {
      this.$toast.loading({
        duration: 0, // 持续展示 toast
        forbidClick: true,
        message: '上传中'
      })
      let formData = new FormData()
      formData.append('file', file)
      upload(formData)
        .then((res) => {
          this.$toast.clear()
          if (res.status == 200) {
            this.imgList.push({
              url: res.data
            })
          } else {
            this.$toast(res.message)
          }
        })
    },
    beforeRead(file) {
      let self = this
      self.imgType = file.type
      let size = file.size / 1024
      // 只在ios端修正
      var isiOS = !!navigator.userAgent.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/) // ios终端
      if (isiOS) {
        // 修正图片旋转
        if (size <= self.maxSize) {
          // 直接上传
          self.afterRead(file)
        } else {
          // 对图片进行压缩
          self.imgPreview(file)
        }
      } else {
        if (size <= self.maxSize) {
          // 直接上传
          self.afterRead(file)
        } else {
          // 对图片进行压缩
          self.imgPreview(file)
        }
      }
    },
    fixImageOrientation(file) {
      return new Promise((resolve, reject) => {
        // 获取图片
        const img = new Image()
        img.src = window.URL.createObjectURL(file)
        img.onerror = () => resolve(file)
        img.onload = () => {
          // 获取图片元数据（EXIF 变量是引入的 exif-js 库暴露的全局变量）
          // eslint-disable-next-line no-undef
          EXIF.getData(img, function() {
            // 获取图片旋转标志位
            // eslint-disable-next-line no-undef
            var orientation = EXIF.getTag(this, 'Orientation')
            // 根据旋转角度，在画布上对图片进行旋转
            if (orientation == 3 || orientation == 6 || orientation == 8) {
              const canvas = document.createElement('canvas')
              const ctx = canvas.getContext('2d')
              switch (orientation) {
                case 3: // 旋转180°
                  canvas.width = img.width
                  canvas.height = img.height
                  ctx.rotate((180 * Math.PI) / 180)
                  ctx.drawImage(
                    img,
                    -img.width,
                    -img.height,
                    img.width,
                    img.height
                  )
                  break
                case 6: // 旋转90°
                  canvas.width = img.height
                  canvas.height = img.width
                  ctx.rotate((90 * Math.PI) / 180)
                  ctx.drawImage(img, 0, -img.height, img.width, img.height)
                  break
                case 8: // 旋转-90°
                  canvas.width = img.height
                  canvas.height = img.width
                  ctx.rotate((-90 * Math.PI) / 180)
                  ctx.drawImage(img, -img.width, 0, img.width, img.height)
                  break
              }
              // 返回新图片
              canvas.toBlob((file) => resolve(file), 'image/jpeg', 0.92)
            } else {
              return resolve(file)
            }
          })
        }
      })
    },
    // 将图片转成 base64 格式
    imgPreview(file) {
      let self = this
      // 看支持不支持FileReader
      if (!file || !window.FileReader) return
      let reader = new FileReader()
      reader.readAsDataURL(file)
      reader.onloadend = function() {
        // 此处的this是reader
        let result = this.result
        self.compress(result)
      }
    },
    // 压缩图片
    compress(base64) {
      let cvs = document.createElement('canvas')
      let img = document.createElement('img')
      img.crossOrigin = 'anonymous'
      img.src = base64
      // 图片偏移值
      // eslint-disable-next-line no-unused-vars
      let offetX = 0
      img.onload = () => {
        if (img.width > 800) {
          cvs.width = 800
          cvs.height = (img.height * 800) / img.width
          offetX = (img.width - 800) / 2
        } else {
          cvs.width = img.width
          cvs.height = img.height
        }
        // eslint-disable-next-line no-unused-vars
        let ctx = cvs
          .getContext('2d')
          .drawImage(img, 0, 0, cvs.width, cvs.height)
        let imageData = cvs.toDataURL(this.imgType, 0.85)
        this.convertBase64UrlToBlob(imageData)
      }
    },
    // 将base64转为文件流
    convertBase64UrlToBlob(imageData) {
      let filename = ''
      let arr = imageData.split(',')
      let mime = arr[0].match(/:(.*?);/)[1]
      if (!filename) {
        filename = `${new Date().getTime()}.${mime.substr(
          mime.indexOf('/') + 1
        )}`
      }
      let bstr = atob(arr[1])
      let n = bstr.length
      let u8arr = new Uint8Array(n)
      while (n--) {
        u8arr[n] = bstr.charCodeAt(n)
      }
      this.afterRead(
        new File([u8arr], filename, {
          type: mime
        })
      )
    },
    removeKeepAliveCache() {
      if (this.$vnode && this.$vnode.data.keepAlive && this.$vnode.parent) {
        const tag = this.$vnode.tag
        let caches = this.$vnode.parent.componentInstance.cache
        let keys = this.$vnode.parent.componentInstance.keys
        for (let [key, cache] of Object.entries(caches)) {
          if (cache.tag === tag) {
            if (keys.length > 0 && keys.includes(key)) {
              keys.splice(keys.indexOf(key), 1)
            }
            delete caches[key]
          }
        }
      }
      this.$destroy()
    }
  }
}
</script>

<style scoped lang="scss">
.content::before {
  // 利用伪元素设置整个页面的背景色
  content: " ";
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: -100;
  min-height: 100%;
  background-color: #fff;
}
.content {
  .wrap {
    font-size: 28px;
    color: #5e5e5e;
    background-color: #fff;
    .cell-set .set-line:not(:last-child) {
      border-bottom: 1px solid #e8e9ea;
    }
    .cell-set:nth-child(2) .set-line:last-child {
      border-bottom: 1px solid #e8e9ea;
    }
    ::v-deep .van-cell__value--alone {
      background: #faf9f9;
      height: 200px;
      border-radius: 16px;
      padding: 26px;
    }
    ::v-deep .van-field__control {
      box-sizing: border-box;
    }
    .cell-set {
      padding: 0 20px;
      background-color: #fff;

      .set-line {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 112px;
        background-color: #fff;
        .right {
          top: 35px;
          right: 0;
          width: 100px;
          margin-left: 20px;
        }
        .set-line-right {
          display: flex;
          align-items: center;
        }
        input {
          width: 132px;
          height: 54px;
          background-color: #f6f6f6;
          border: none;
          border-radius: 28px;
          padding-left: 20px;
          padding-right: 10px;
        }
        input::placeholder {
          color: #c8c9cc;
        }
        .line {
          width: 34px;
          height: 1px;
          background-color: #f6f6f6;
          margin: 0 15px;
        }
        ::v-deep .van-cell {
          padding-left: 0;
          padding-right: 0;
        }
      }
      .area {
        margin-top: 48px;
        min-height: 430px;
      }
      ::v-deep .van-cell__value--alone {
        height: 430px;
      }
    }

    .bottom {
      margin-top: 35px;
      margin-left: 36px;
      display: flex;
      height: 160px;
      z-index: 1;
    }
    .avatar {
      position: relative;
      width: 130px;
      height: 130px;
      margin-right: 20px;
      img {
        width: 100%;
        height: 100%;
      }
      .delicon {
        position: absolute;
        right: 0;
        top: 0;
        width: 36px;
        height: 36px;
      }
    }

    .btn {
      text-align: center;
      margin-top: 150px;
      padding-bottom: 50px;
    }

    // 按钮
    ::v-deep .van-button--primary {
      width: 512px;
      border: 0;
      background: linear-gradient(45deg, #71d774 0%, #5ecc52 100%);
    }
    ::v-deep .van-button--normal {
      font-size: 32px;
      border-radius: 12px;
    }
    ::v-deep .van-radio--horizontal {
      margin-left: 52px;
    }
  }
  .box {
    .title {
      position: absolute;
      top: 0;
      left: 0;
      z-index: 10;
      width: 100%;
      display: flex;
      justify-content: space-between;
      box-sizing: border-box;
      height: 104px;
      line-height: 104px;
      font-size: 32px;
      color: #000010;
      border-radius:20px 20px 0 0;
      background: #fff;
      div {
        width: 33.3%;
        text-align: center;
      }
      .addaddress {
        font-size: 26px;
        text-align: right;
        margin-right:20px;
      }
    }
    .body {
      width: 100%;
      height: 650px;
      overflow-y: auto;
    }
    .close-icon {
      position: fixed;
      bottom: 625px;
      right: 40px;
      z-index: 10;
    }
    .address-pop {
      margin: 4px 42px 20px 18px;
      .item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 30px;
        color: #000010;
        height: 130px;
        .item-left {
          display: flex;
          align-items: center;

          .item-detail {
            margin-left: 22px;

            > div:nth-of-type(1) {
              font-family: PingFangSC;
              color: #33333f;
              margin-bottom: 4px;
            }

            > div:nth-of-type(2) {
              font-family: PingFang SC;
              font-size: 22px;
              color: #696969;
            }
          }
        }

        .item-right {
          display: flex;
          align-items: center;
        }
      }
    }

    .newAddr {
      position: absolute;
      bottom: 0;
      left: 0;
      width: 100%;
      background-color: #fff;
      color: #000010;
      font-size: 30px;
      padding: 0 0 22px;

      > div {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 684px;
        height: 76px;
        line-height:76px;
        background-color: #f5f5f5;
        border-radius: 10px;
        margin: 0 auto;
      }
    }

    ::v-deep .van-radio__icon .van-icon {
      border: 1px solid #5dcb4f;
    }

    .righticon {
      position: relative;
      top: -2px;
    }
  }
}
</style>
