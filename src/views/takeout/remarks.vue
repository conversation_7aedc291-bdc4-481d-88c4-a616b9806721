<template>
  <div class="content">
    <NavHeight bgc="#fff" />
    <div class="top">
      <van-nav-bar title="" left-text="" left-arrow>
        <template #title>
          <div>添加备注</div>
        </template>
        <template #left>
          <van-icon
            name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/user/icon/arrow-black.png"
            size="17px"
            @click="goback"
          />
        </template>
        <template #right>
          <div @click="goSaveBack">完成</div>
        </template>
      </van-nav-bar>
    </div>
    <div class="line" />
    <div class="remarks-wrap">
      <van-field v-model="remark" type="textarea" rows="2" placeholder="请输入口味、偏好等要求哦~" input-align="left" show-word-limit maxlength="50" />
    </div>
    <div class="tag">
      <div class="tag-title">推荐标签</div>
      <div class="tag-cell">
        <van-tag
          v-for="(item,index) in list1"
          :key="index"
          plain
          type="primary"
          size="large"
          text-color="#FF5A30"
          @click="getTag(item)"
        >{{ item }}</van-tag>
      </div>
    </div>

    <div class="tag">
      <div class="tag-title flex">
        <div style="margin-top:12px">快捷标签</div>
        <div v-if="false">
          <van-icon name="edit" size="11px" />
          <span style="margin-left:10px">编辑</span>
        </div>
      </div>
      <div class="tag-cell1">
        <van-tag
          v-for="(item,index) in list2"
          :key="index"
          plain
          type="primary"
          size="large"
          text-color="#333333"
          color="#F5F6F7"
          @click="getTag(item)"
        >{{ item }}</van-tag>
      </div>
    </div>

  </div>
</template>

<script>
export default {
  components: {},
  data() {
    return {
      remark: '',
      list1: [
        '如地址封闭管理，请电话与我联系',
        '请挂门把手上',
        '请放门口',
        '请放前台桌子上'
      ],
      list2: [
        '微辣 不吃香菜',
        '不吃香菜',
        '不吃辣 不吃香菜',
        '少放辣'
      ]
    }
  },
  created() {
    this.remark = this.$store.state.market.marketData.remark
  },
  mounted() {},
  methods: {
    getTag(item) {
      this.remark = item
    },
    goback() {
      this.$router.go(-1)
    },
    goSaveBack() {
      this.$store.state.market.marketData.remark = this.remark
      this.$router.go(-1)
    }
  }
}
</script>

<style lang="scss" scoped>
	.content::before {
		// 利用伪元素设置整个页面的背景色
		content: " ";
		position: absolute;
		left: 0;
		right: 0;
		top: 0;
		bottom: 0;
		z-index: -100;
		min-height: 100%;
		background-color: #fff;
	}
	.content {
    .line {
      width: 100%;
      height: 20px;
      background-color: #f4f4f4;
    }
		::v-deep .van-nav-bar__title {
			font-size:36px;
			font-family: PingFang SC;
			color: #000010;
		}
		::v-deep .van-nav-bar {
			width: 100%;
			// height: 78px !important;
			background-color: #fff;
		}

		::v-deep .van-nav-bar__right {
			font-size: 30px;
		}

		::v-deep .van-hairline--bottom {
			border-bottom-width: 0;
		}

		::v-deep .van-hairline--bottom::after {
			border-bottom-width: 0;
		}

		.remarks-wrap {
			width:710px;
			height: 242px;
			border: 1px solid #efefef;
			border-radius: 10px;
			margin: 30px auto 48px;
			box-sizing: border-box;
			background: #f9f9f9;

			input {
				border: none;
			}
		}

		::v-deep .van-cell {
			padding: 0;
			padding-top: 12px;
			padding-left: 32px;
			padding-right: 26px;
			background-color: transparent;
		}

		::v-deep .van-field__word-limit {
			margin-top: 55px;
		}

		.tag {
			margin-left: 20px;
			margin-right:20px;
			margin-bottom: 24px;

			.flex {
				display: flex;
				justify-content: space-between;
			}

			.tag-title {
				color: #333;
				font-size: 30px;
			}
			::v-deep .van-tag--large {
				margin-right: 20px;
			}
      .tag-cell1 {
        ::v-deep .van-tag--large {
          background-color: #f5f6f7;
        }
      }
      .tag-cell {

        ::v-deep .van-tag--large {
          background-color: #FFF6F4;
        }
      }
		}
	}
</style>
