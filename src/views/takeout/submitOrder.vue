<template>
  <!-- 提交订单 -->
  <div class="content">
    <NavHeight bgi="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/takeout/statusbg.png" />
    <Top />
    <!-- 选择地址 -->
    <Address ref="reading" />
    <!-- 支付方式 -->
    <PayStyle />
    <!-- 商品列表 -->
    <Goods />
    <!-- 平台购物车 -->
    <PlateCart v-if="this.$store.state.market.marketData.type == 10" />
    <!-- 外卖购物车 -->
    <Cart v-else />
  </div>
</template>

<script>
import Top from './components/top'
import Goods from './components/goods.vue'
import Cart from './components/cart.vue'
import PayStyle from './components/payStyle'
import Address from './components/address'
import PlateCart from './components/plateCart'
import { checkInitPwd } from '@/api/takeout'
import { addGoodsData } from '@/utils/upLog.js'
export default {
  components: {
    Top,
    Goods,
    Cart,
    PayStyle,
    Address,
    PlateCart
  },
  data() {
    return {

    }
  },
  mounted() {
    // 回到顶部
    document.body.scrollTop = 0
    document.documentElement.scrollTop = 0
    this.$store.state.market.marketData.delieveryType = 1
    // this.checkInitPwd()
    let cart = this.$store.state.cart.cartData[0].goodsList
    // 记录下单数据
    addGoodsData(cart)
  },
  destroyed() {
    // 清空支付选择缓存
    this.$store.state.market.marketData.payradio = ''
  },
  beforeRouteEnter(to, from, next) {
    if (to.path == '/submitOrder') {
      to.meta.keepAlive = true
    }
    next()
  },
  beforeRouteLeave(to, from, next) {
    if (to.path != '/remarks') {
      this.removeKeepAliveCache()
    }
    if (to.path == '/address') {
      this.removeKeepAliveCache()
    }
    next()
  },
  methods: {
    removeKeepAliveCache() {
      if (this.$vnode && this.$vnode.data.keepAlive && this.$vnode.parent) {
        const tag = this.$vnode.tag
        let caches = this.$vnode.parent.componentInstance.cache
        let keys = this.$vnode.parent.componentInstance.keys
        for (let [key, cache] of Object.entries(caches)) {
          if (cache.tag === tag) {
            if (keys.length > 0 && keys.includes(key)) {
              keys.splice(keys.indexOf(key), 1)
            }
            delete caches[key]
          }
        }
      }
      this.$destroy()
    },
    onSend() {
      return this.$refs.reading.setadderssrId()
    },
    checkInitPwd() { // 检查初始支付密码
      checkInitPwd(this.$store.getters.getUserId).then(res => {
        if (res.status == 200) {
          if (res.data == true) {
            this.$dialog.alert({
              message: '当前为初始支付密码，请尽快修改'
            }).then(() => {
              this.$router.push('/editPayPwd')
            })
          }
        }
      })
    }
    // 获取下单画像数据
    // getOrderData() {
    //   let self = this
    //   // 获取天气
    //   let cityName = this.$store.state.address.city
    //   AMap.plugin('AMap.Weather', function() {
    //     var weather = new AMap.Weather()
    //     // 执行实时天气信息查询
    //     weather.getLive(cityName, function(err, data) {
    //       self.$store.state.payOrder.weather = data.weather
    //     })
    //   })
    //   // 获取身份信息
    //   getUserFace().then((res) => {
    //     if (res.status == 200) {
    //       let data = Number(res.data.idCard.substring(16, 17))
    //       if (data / 2 == 0) {
    //         this.$store.state.payOrder.sex = 0
    //       } else {
    //         this.$store.state.payOrder.sex = 1
    //       }
    //     }
    //   })
    // }
  }
}
</script>

<style lang="scss" scoped>

</style>
