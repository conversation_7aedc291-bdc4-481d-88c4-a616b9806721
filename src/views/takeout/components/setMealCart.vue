<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2022-09-22 10:22:49
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-09-22 21:56:08
-->
<template>
  <div class="setMeal_cart">
    <div class="bottom" @click="inspectOrder">
      <div>提交订单</div>
    </div>

    <!-- 数字键盘,和扫码支付样式有差别 -->
    <van-popup
      v-model="show"
      round
      closeable
      :style="{ width: '309px', height: '315px' }"
      @click-overlay="closePop"
      @click-close-icon="closePop"
    >
      <div class="box">
        <div class="title">输入支付密码</div>
        <div class="price">¥{{ $store.state.setMail.info.specialPrice }}</div>
        <div class="balance">
          <div class="payment-line">
            <div class="line-left">
              <van-icon
                name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/pay/balance.png"
                size="23px"
              />
              <span>账户余额</span>
            </div>
            <div class="line-right">
              <span>{{ $store.state.market.marketData.balance }}元</span>
            </div>
          </div>
        </div>
        <van-password-input
          :value="password"
          info=""
          :length="6"
          :focused="showKeyboard"
          @focus="showKeyboard = true"
        />
      </div>
      <div class="forgetPwd" @click="goPwd">找回密码</div>
    </van-popup>

    <van-number-keyboard
      v-model="password"
      :show="show"
      title="点滴安全键盘"
      z-index="9000"
      @blur="show = false"
    />

    <!-- 验证码弹框 -->
    <van-popup v-model="codeLog" round :style="{ height: '130px' }">
      <div class="popBox">
        <van-field
          v-model="checkUnionPay.smsCode"
          center
          clearable
          :border="false"
          label="验证码"
          placeholder="请输入验证码"
        />
        <div class="popBtn">
          <div class="cancel" @click="codeLog = false">取消</div>
          <div class="confirm" @click="confirmFinishCheckUnionPay">确认</div>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script>
import { version } from '@/config/settings'
import { payLoading, getUserAccount } from '@/api/takeout'
import { finishCheckUnionPay, fundAccountQuery } from '@/api/bank'
import { findUnionRcbAccount } from '@/api/bank/nsh'
import { query } from '@/utils/sign'
import { DPay } from '@/modules'
import { Toast } from 'vant'

import { createOrder, payOrder } from '@/api/order/serMeal'
export default {
  data() {
    return {
      form: {
        setMealId: this.$store.state.setMail.info.id,
        quantity: 1
      },
      show: false,
      showKeyboard: false,
      password: '',
      orderNo: '',
      ybalance: '',
      payData: '',
      codeLog: false,
      checkUnionPay: {
        smsId: '',
        smsCode: '',
        unionPayData: ''
      },
      defaultVal: '',
      length: 6,
      capitalAccountId: 0
    }
  },
  watch: {
    password(val, old) {
      if (val.length >= 6) {
        this.pay(this.payData)
      }
    },
    show(val) {
      if (val === false) {
        AlipayJSBridge.call('OffScreenshot', {}, function(result) {
          console.log(result)
        })
      }
    }
  },
  created() {},
  mounted() {},
  methods: {
    // 下单
    createOrder() {
      Toast.loading({
        duration: 10000,
        forbidClick: true
      })
      createOrder(this.form).then((res) => {
        setTimeout(() => {
          Toast.clear()
        }, 800)
        if (res.status == 200) {
          this.orderNo = res.data.orderNo
          if (this.$store.state.market.marketData.payradio != 1 && this.$store.state.market.marketData.payradio != 7) {
            this.pay()
          } else {
            this.show = true
            AlipayJSBridge.call('OnScreenshot', {}, function(result) {
              console.log(result)
            })
          }
        }
      })
    },
    // 节流操作
    throttle() {
      if (this.orderNo != '') {
        if (
          this.$store.state.market.marketData.payradio != 1 &&
            this.$store.state.market.marketData.payradio != 7
        ) {
          this.pay()
        } else {
          this.show = true
          // this.$refs.showKeyboardRef.show()
          AlipayJSBridge.call('OnScreenshot', {}, function(result) {
            console.log(result)
          })
        }
      } else {
        this.createOrder()
      }
    },
    // 下单前检查
    // 先检查是否满足二类户开通
    inspectOrder() {
      if (this.$store.state.market.marketData.payradio == 7) {
        getUserAccount(this.$store.state.market.marketData.marketSn).then(
          (res) => {
            if (res.status == 200) {
              if (res.data.payChannel == 2) {
                this.capitalAccountId = res.data.capitalAccountId
                // 检查是否开户成功
                console.log(this.$store.getters.getRegionId)
                if (this.$store.getters.getRegionId == 3) {
                  console.log('龙泉')
                  this.fundAccountLqQuery()
                } else {
                  this.fundAccountQuery()
                }
              } else {
                this.throttle()
              }
            }
          }
        )
      } else {
        this.throttle()
      }
    },
    pay() {
      // 请求支付
      let self = this
      let Base64 = require('js-base64').Base64
      this.$toast.loading({
        duration: 0,
        forbidClick: true
      })
      let paydata = {
        capitalAccountId: 1,
        paymentType: this.$store.state.market.marketData.payradio,
        payPassword: Base64.encode(this.password),
        terminalSysVer: version,
        // eslint-disable-next-line no-undef
        clientIp: '127.0.0.1',
        orderNo: this.orderNo,
        deviceId: localStorage.getItem('deviceId')
          ? localStorage.getItem('deviceId')
          : 'test',
        longitude: this.$store.getters.getLocation.longitude,
        latitude: this.$store.getters.getLocation.latitude
      }

      if (paydata.payPassword === '' || paydata.payPassword === null) {
        delete paydata.payPassword
      }
      paydata.sign = query(paydata)

      payOrder(paydata).then((res) => {
        self.$toast.clear()
        if (res.status == 200) {
          if (
            this.$store.state.market.marketData.payradio == 1 ||
            this.$store.state.market.marketData.payradio == 7
          ) {
            this.show = false
            this.password = ''
            // 余额支付
            // 强校验
            if (
              this.$store.state.market.marketData.payradio == 7 &&
              res.data.thirdPartPayData.resultCode == '00018'
            ) {
              this.checkUnionPay.smsCode = ''
              this.$toast('交易存在风险，请输入银联验证码进行确认')
              this.checkUnionPay.smsId = res.data.thirdPartPayData.smsId
              this.checkUnionPay.unionPayData =
                res.data.thirdPartPayData.unionPayData
              this.codeLog = true

              return
            }
            // 交易中
            if (
              this.$store.state.market.marketData.payradio == 7 &&
              res.data.thirdPartPayData.resultCode == '00004'
            ) {
              self.orderNo = res.data.orderNo
              const timerccb = window.setInterval(() => {
                self.appPay()
              }, 2000)

              this.$toast.loading({
                duration: 3000,
                forbidClick: true,
                message: '交易处理中...'
              })

              this.$once('hook:beforeDestroy', () => {
                window.clearInterval(timerccb)
              })
              return
            }

            this.$toast('付款成功')
            setTimeout(() => {
              self.$router.push({
                name: 'SetMealorderList',
                query: {}
              })
            }, 500)
            self.orderDataLog()
          } else if (this.$store.state.market.marketData.payradio == 2) {
            // 支付宝支付
            DPay.getPay(
              this.$store.state.market.marketData.payradio,
              res.data.thirdPartPayData.qrUrl,
              4,
              res.data.orderNo
            )
          } else if (this.$store.state.market.marketData.payradio == 3) {
            // 微信支付
            DPay.getPay(
              this.$store.state.market.marketData.payradio,
              res.data.thirdPartPayData.pyTrnNo,
              4,
              res.data.orderNo
            )
          } else if (this.$store.state.market.marketData.payradio == 9) {
            // 云闪付
            DPay.getPay(
              this.$store.state.market.marketData.payradio,
              res.data.thirdPartPayData.qrUrl,
              4,
              res.data.orderNo
            )
          }
        } else {
          this.password = ''
          this.$toast(res.message)
        }
      })
    },
    // 关闭支付
    closePop() {
      this.$router.push({
        name: 'SetMealorderDetail',
        query: {
          orderNo: this.orderNo,
          type: 7,
          from: 1
        }
      })
    },
    // 检查支付
    appPay() {
      payLoading(this.orderNo).then((res) => {
        if (res.status == 200) {
          if (res.data == true) {
            this.orderDataLog()
            this.$toast('支付成功')
            setTimeout(() => {
              this.$router.push({
                name: 'OrderDetail',
                query: {
                  orderNo: this.orderNo,
                  type: 7,
                  from: 1
                }
              })
            }, 500)
          } else {
            this.$toast('支付失败')
            this.$router.push({ name: 'Order' })
          }
        } else {
          this.$toast(res.message)
        }
      })
    },
    // 检查开户
    fundAccountQuery() {
      let queryVO = {
        bankType: 'UnionPay',
        userId: this.$store.getters.getUserId
      }
      fundAccountQuery(queryVO).then((res) => {
        if (res.data != null) {
          if (res.data.status != 2) {
            this.$dialog
              .confirm({
                message: '请先完成开户流程！',
                confirmButtonText: '去开通',
                cancelButtonText: '暂不开通'
              })
              .then(() => {
                this.$router.push('/wallet')
              })
          } else {
            this.throttle()
          }
        } else {
          this.$dialog
            .confirm({
              message: '请先完成开户流程！',
              confirmButtonText: '去开通',
              cancelButtonText: '暂不开通'
            })
            .then(() => {
              this.$router.push('/wallet')
            })
        }
      })
    },
    // 龙泉检查开户
    fundAccountLqQuery() {
      let data = {
        'userId': this.$store.getters.getUserId,
        'capitalAccountId': this.capitalAccountId
      }
      findUnionRcbAccount(data).then(res => {
        if (res.status == 200) {
          if (res.data != null) {
            if (res.data.status != 2) {
              this.$dialog.confirm({
                message: '请先完成开户流程！',
                confirmButtonText: '去开通',
                cancelButtonText: '暂不开通'
              }).then(() => {
                this.$router.push('/wallet')
              })
            } else {
              this.throttle()
            }
          } else {
            this.$dialog.confirm({
              message: '请先完成开户流程！',
              confirmButtonText: '去开通',
              cancelButtonText: '暂不开通'
            }).then(() => {
              this.$router.push('/wallet')
            })
          }
        }
      })
    },
    // 强校验
    confirmFinishCheckUnionPay() {
      let self = this
      finishCheckUnionPay(this.checkUnionPay).then(function(res) {
        if (res.status == 200) {
          if (res.data.resultCode == '00000') {
            self.$toast('支付成功')
            self.orderDataLog()
            setTimeout(() => {
              self.$router.push({
                name: 'OrderDetail',
                query: {
                  orderNo: self.orderNo,
                  type: 7,
                  from: 1
                }
              })
            }, 500)
          } else {
            self.$toast('支付失败')
          }
        }
      })
    },
    goPwd() {
      this.$router.push('/editPayPwd')
    }
  }
}
</script>

<style scoped lang="scss">
.setMeal_cart {
  .bottom {
    width: 100%;
    height: 186px;
    background-color: #fff;
    position: fixed;
    bottom: 0;
    box-shadow: -4px -4px 27px 0px rgba(220, 220, 220, 0.5);
    div {
      width: 634px;
      height: 88px;
      background: linear-gradient(90deg, #40d243, #1fc432);
      border-radius: 8px;
      text-align: center;
      line-height: 88px;
      color: #fff;
      font-size: 35px;
      font-weight: 500;
      font-family: PingFangSC-Medium;
      margin: 0 auto;
      margin-top: 26px;
    }
  }
}
.box {
  .title {
    width: 100%;
    box-sizing: border-box;
    text-align: center;
    font-size: 34px;
    color: #222;
    margin-top: 56px;
    margin-bottom: 38px;
  }
  .price {
    text-align: center;
    font-size: 60px;
    margin-bottom: 80px;
  }
  .balance {
    margin: 0 54px;
    .payment-line {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 34px;
      color: #222;
      margin-bottom: 47px;
      .line-left {
        display: flex;
        align-items: center;

        > span {
          font-size: 34px;
          margin-left: 10px;
        }
      }

      .line-right {
        color: #999;
        font-size: 32px;
        display: flex;
        align-items: center;
      }
    }
  }
}

.forgetPwd {
  color: #39cf3f;
  text-align: right;
  margin-right: 54px;
  font-size: 28px;
  margin-top: 32px;
  height: 50px;
}

::v-deep .van-password-input {
  margin: 0 54px;
}

::v-deep .van-popup__close-icon {
  font-size: 35px;
}

::v-deep .van-popup__close-icon--top-right {
  top: 60px;
}

::v-deep[class*="van-hairline"]::after {
  border-color: #ccc;
}

::v-deep .van-popup--center.van-popup--round {
  border-radius: 24px;
}

::v-deep .van-popup--center {
  top: 35%;
}
.popBox {
  position: relative;
  width: 561px;
  height: 32px;
  padding-top: 48px;
  box-sizing: border-box;
  ::v-deep .van-overlay {
    z-index: 9002 !important;
  }

  .title {
    color: #7f7f87;
    font-size: 30px;
    padding-left: 42px;
  }

  ::v-deep .van-field__label {
    width: 100px;
    margin-right: 0;
  }
}
.popBtn {
  position: fixed;
  left: 0;
  bottom: 0px;
  display: flex;
  width: 561px;
  height: 97px;
  line-height: 97px;
  font-size: 32px;
  justify-content: space-between;
  border-top: 1px solid #cfcece;
  text-align: center;
  .cancel {
    width: 50%;
    border-right: 1px solid #cfcece;
    color: #999999;
  }
  .confirm {
    width: 50%;
    color: #6095f0;
  }
}
</style>
