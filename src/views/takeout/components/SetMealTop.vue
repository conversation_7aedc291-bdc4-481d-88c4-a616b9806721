<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-05-28 16:50:20
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-09-20 14:10:48
-->
<template>
  <div class="content">
    <div class="top">
      <van-nav-bar title="" left-text="" left-arrow>
        <template #title>
          <div>提交订单</div>
        </template>
        <template #left>
          <van-icon
            name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/loginV2/left.png"
            size="21"
            @click="goback"
          />
        </template>
      </van-nav-bar>
    </div>
    <div style="height:46px" />
  </div>
</template>

<script>
export default {
  components: {},
  data() {
    return {
    }
  },
  created() {
  },
  mounted() {},
  methods: {
    goback() {
      this.$router.go(-1)
    }
  }
}
</script>

<style lang="scss" scoped>
	.content {
		height: 92px;
		overflow: hidden;
		font-family: PingFangSC-Medium;
		.statusBg {
			width: 100%;
		}

		.top {
			width: 100%;
			position: fixed;
			z-index: 1;
			left: 0;
		}

		::v-deep .van-nav-bar__title {
			font-size:36px;
			color: #000;
		}

		::v-deep .van-nav-bar {
			width: 100%;
			height: 92px;
		}

		::v-deep .van-nav-bar__right {
			font-size: 30px;
		}

		::v-deep .van-hairline--bottom {
			border-bottom-width: 0;
		}

		::v-deep .van-hairline--bottom::after {
			border-bottom-width: 0;
		}
	}
</style>
