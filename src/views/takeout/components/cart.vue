<template>
  <div class="home">
    <!-- 购物车 -->
    <div class="cart-box" :style="styleVar">
      <div class="cart">
        <div class="cart-left">
          <div class="cart-img">
            <!-- 如果购物车无商品，置灰 -->
            <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/takeout/carticon.png" alt="">
          </div>
          <div>
            <span class="small">¥</span>
            <span class="price">{{ cartPrice | Fmoney }}</span>
          </div>
        </div>
        <div class="cart-right" @click="fastSetOrder()">
          提交订单
        </div>
      </div>
    </div>

    <!-- 数字键盘,和扫码支付样式有差别 -->
    <van-popup v-model="show" round closeable :style="{ width: '309px', height: '315px', }" @click-overlay="closePop" @click-close-icon="closePop">
      <div class="box">
        <div class="title">输入支付密码</div>
        <div class="price">
          ¥{{ cartPrice }}
        </div>
        <div class="balance">
          <div class="payment-line">
            <div class="line-left">
              <van-icon name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/pay/balance.png" size="23px" />
              <span>账户余额</span>
            </div>
            <div class="line-right">
              <span>{{ $store.state.market.marketData.balance }}元</span>
            </div>
          </div>
        </div>
        <van-password-input :value="password" info="" :length="6" :focused="showKeyboard" @focus="showKeyboard = true" />
      </div>
      <div class="forgetPwd" @click="goPwd">找回密码</div>
    </van-popup>

    <van-number-keyboard
      v-model="password"
      :show="show"
      title="点滴安全键盘"
      z-index="9000"
      @blur="show = false"
    />

    <!-- <van-number-keyboard :hide-on-click-outside="false" :show="show" v-model="password" theme="custom" z-index="9000" extra-key="."
	close-button-text="完成" @blur="show = false" @input="onInput" @delete="onDelete" /> -->

    <!-- <van-number-keyboard
      v-model="password"
      :hide-on-click-outside="false"
      :show="show"
      z-index="9000"
    /> -->

    <!-- 验证码弹框 -->
    <van-popup v-model="codeLog" round :style="{ height: '130px', }">
      <div class="popBox">
        <van-field v-model="checkUnionPay.smsCode" center clearable :border="false" label="验证码" placeholder="请输入验证码" />
        <div class="popBtn">
          <div class="cancel" @click="codeLog = false">取消</div>
          <div class="confirm" @click="confirmFinishCheckUnionPay">确认</div>
        </div>
      </div>
    </van-popup>

    <!-- 安全键盘 -->
    <div v-if="false" id="app" @dblclick="() => {return false;}">
      <div class="cus-keyboard">
        <Keyboard ref="showKeyboardRef" :length="length" :default-val="defaultVal" :text.sync="password" :keyboard-type="1" />
      </div>
    </div>
  </div>
</template>

<script>
import {
  payOrder,
  getPay,
  payLoading,
  getWeatherConfig
} from '@/api/takeout'
import NP from 'number-precision'
import {
  addOrderData,
  logDataUp
} from '@/utils/upLog.js'
import {
  version
} from '@/config/settings'
import { checkInitPwd } from '@/api/takeout'
import { finishCheckUnionPay, fundAccountQuery } from '@/api/bank'
import { getUserAccount } from '@/api/takeout'
import { findUnionRcbAccount } from '@/api/bank/nsh'

import Keyboard from '@/components/Keyboard'
import { query } from '@/utils/sign'
import Maths from '@/utils/math.js'

import { DPay } from '@/modules'

export default {
  name: 'Home',
  components: { Keyboard },
  data() {
    return {
      show: false,
      showKeyboard: false,
      password: '',
      orderNo: '',
      ybalance: '',
      payData: '',
      codeLog: false,
      checkUnionPay: {
        smsId: '',
        smsCode: '',
        unionPayData: ''
      },
      defaultVal: '',
      length: 6
    }
  },
  computed: {
    cartPrice() { // 总价
      if (this.$store.state.market.marketData.coupon == '') {
        let sum = 0
        sum = new Maths(this.$store.getters['cart/sumPrice'], this.getPostFree()).sum()
        sum = new Maths(sum, this.$store.getters['cart/sumPackPrice']).sum()
        return sum
      } else {
        let sum = 0
        sum = new Maths(this.$store.getters['cart/sumPrice'], this.getPostFree()).sum()
        sum = new Maths(sum, this.$store.getters['cart/sumPackPrice']).sum()
        sum = new Maths(sum, this.$store.state.market.marketData.coupon.preferentialAmount).minus()
        return sum
      }
    },
    styleVar() {
      return {
        '---nav-height': this.$store.getters.getNavigationBarHeight + 'px'
      }
    }
  },
  watch: {
    password(val, old) {
      if (val.length >= 6) {
        this.pay(this.payData)
      }
    },
    show(val) {
      if (val === false) {
        // this.$refs.showKeyboardRef.hide()
        AlipayJSBridge.call('OffScreenshot', {}, function(result) {
          console.log(result)
        })
      }
    }
  },
  destroyed() {
    AlipayJSBridge.call('OffScreenshot', {}, function(result) {
      console.log(result)
    })
  },
  mounted() {
    this.$store.state.market.isLock = true
    // 非商城订单清除优惠券信息缓存
    this.$store.state.market.marketData.coupon = ''
  },
  methods: {
    // 下单前检查
    // 先检查是否满足二类户开通
    fastSetOrder() {
      if (this.$store.state.market.marketData.payradio == 7) {
        getUserAccount(this.$store.state.market.marketData.marketSn).then(res => {
          if (res.status == 200) {
            if (res.data.payChannel == 2) {
              // 检查是否开户成功
              if (this.$store.getters.getRegionId == 3) {
                this.fundAccountLqQuery(res.data.capitalAccountId)
              } else if (this.$store.getters.getRegionId == 1) {
                this.fundAccountQuery()
              } else if (this.$store.getters.getRegionId == 7) {
                this.fundAccountQuery()
              }
            } else {
              this.setOrder()
            }
          }
        })
      } else {
        this.setOrder()
      }
    },
    // 检查开户
    fundAccountQuery() {
      let queryVO = {
        'bankType': 'UnionPay',
        'userId': this.$store.getters.getUserId
      }
      fundAccountQuery(queryVO).then(res => {
        if (res.data != null) {
          if (res.data.status != 2) {
            this.$dialog.confirm({
              message: '请先完成开户流程！',
              confirmButtonText: '去开通',
              cancelButtonText: '暂不开通'
            }).then(() => {
              this.$router.push('/wallet')
            })
          } else {
            this.setOrder()
          }
        } else {
          this.$dialog.confirm({
            message: '请先完成开户流程！',
            confirmButtonText: '去开通',
            cancelButtonText: '暂不开通'
          }).then(() => {
            this.$router.push('/wallet')
          })
        }
      })
    },
    // 龙泉检查开户
    fundAccountLqQuery(val) {
      let data = {
        'userId': this.$store.getters.getUserId,
        'capitalAccountId': val
      }
      findUnionRcbAccount(data).then(res => {
        if (res.status == 200) {
          if (res.data != null) {
            if (res.data.status != 2) {
              this.$dialog.confirm({
                message: '请先完成开户流程！',
                confirmButtonText: '去开通',
                cancelButtonText: '暂不开通'
              }).then(() => {
                this.$router.push('/wallet')
              })
            } else {
              this.setOrder()
            }
          } else {
            this.$dialog.confirm({
              message: '请先完成开户流程！',
              confirmButtonText: '去开通',
              cancelButtonText: '暂不开通'
            }).then(() => {
              this.$router.push('/wallet')
            })
          }
        }
      })
    },
    setOrder() {
      // 检查配置
      let self = this
      if (this.$store.state.market.marketData.delieveryType == 2) {
        self.throttleBtn()
        return
      }
      getWeatherConfig(this.$store.getters.getRegionId).then(res => {
        if (res.status == 200) {
          if (res.data != null && res.data.cvalue != 100) {
            self.$dialog.confirm({
              title: '提醒',
              message: '受当前天气影响，您的订单可能会延迟送达，请确认是否下单'
            }).then(() => {
              self.throttleBtn()
            }).catch(() => {
              // on cancel
            })
          } else {
            self.throttleBtn()
          }
        } else {
          self.$toast(res.message)
        }
      })
    },
    // 计算运费
    getPostFree() {
      let data = this.$store.state.market.marketData.postFee
      if (data == '') {
        return 0
      } else if (this.$store.state.market.marketData.agentPostFee > 0) {
        if (this.$store.state.market.marketData.agentPostFee < data) {
          return NP.minus(Number(data), Number(this.$store.state.market.marketData.agentPostFee))
        }
        if (this.$store.state.market.marketData.agentPostFee >= data) {
          return 0
        }
      } else {
        return data
      }
    },
    goPwd() {
      this.$router.push('/editPayPwd')
    },
    // 条件检查
    throttleBtn() {
      if (this.$parent.onSend() == '' && this.$store.state.market.marketData.delieveryType != 2) {
        this.$toast('请选择下单地址')
        return
      } else if (this.$store.state.market.marketData.payradio == '') {
        this.$toast('请选择支付方式')
        return
      } else if (
        this.$store.state.market.marketData.payradio == 1 &&
				this.$store.state.market.marketData.balance < this.cartPrice
      ) {
        this.$toast('余额不足')
        return
      } else if (
        this.$store.state.market.marketData.payradio == 7 &&
				this.$store.state.market.marketData.balance < this.cartPrice
      ) {
        this.$toast('余额不足')
        return
      } else if (this.$store.state.market.marketData.payradio == 1) {
        checkInitPwd(this.$store.getters.getUserId).then(res => {
          if (res.status == 200) {
            if (res.data == true) {
              this.$dialog.alert({
                message: '当前为初始支付密码，请尽快修改'
              }).then(() => {
                this.$router.push('/editPayPwd')
              })
            } else {
              this.throttle()
            }
          }
        })
      } else {
        this.throttle()
      }
    },
    // 节流操作
    throttle() {
      this.$throttle(() => {
        if (this.orderNo != '') {
          let payData = {
            orderNo: this.orderNo,
            actualPay: this.ybalance,
            marketSn: this.$store.state.market.marketData.marketSn
          }
          if (this.$store.state.market.marketData.payradio != 1 && this.$store.state.market.marketData.payradio != 7) {
            this.pay(payData)
          } else {
            this.show = true
            // this.$refs.showKeyboardRef.show()
            AlipayJSBridge.call('OnScreenshot', {}, function(result) {
              console.log(result)
            })
          }
        } else {
          this.payOrder()
        }
      }, 3000)
    },
    // 下单
    payOrder() {
      console.log(this.$store.state.market.marketData.coupon)
      var dayjs = require('dayjs')
      if (this.$store.state.market.marketData.delieveryType != 2) {
        if (this.$parent.onSend() == '') {
          this.$toast('请选择下单地址')
          return
        } else if (this.$store.state.market.marketData.payradio == '') {
          this.$toast('请选择支付方式')
          return
        } else if (
          this.$store.state.market.marketData.payradio == 1 &&
					this.$store.state.market.marketData.balance < this.cartPrice
        ) {
          this.$toast('余额不足')
          return
        }
      }
      if (this.$store.state.market.marketData.delieveryType == 2) {
        if (this.$store.state.market.takeData.checkedAgreement == false) {
          this.$toast('请同意服务协议')
          return
        }
        if (this.$store.state.market.takeData.userPhone == '') {
          this.$toast('请填写预留电话')
          return
        }
        if (this.$store.state.market.takeData.selfMentionTime == '') {
          this.$toast('请选择自取时间')
          return
        }
        if (this.$store.state.market.takeData.userPhone.length < 6) {
          this.$toast('请检查预留电话格式是否正确')
          return
        }
      }
      let cart = this.$store.state.cart.cartData[0].goodsList // 获取加入购物车
      var obj = {}
      for (var i = 0, l = cart.length; i < l; i++) {
        var item = cart[i].skuId
        obj[item] = obj[item] + 1 || 1
      }
      var skuarr = []
      for (let i in obj) {
        var newobj = {}
        newobj.skuId = i
        newobj.skuQuantity = obj[i]
        skuarr.push(newobj) // 属性
      }
      if (this.$store.state.market.marketData.delieveryType == 2) {
        this.$store.state.market.marketData.remark = '到店自取 ' + this.$store.state.market.marketData.remark
      }

      let orderData = {
        couponsId: this.$store.state.market.marketData.coupon != '' ? this.$store.state.market.marketData.coupon.couponId : '',
        deliveryForm: {
          deliverAddressId: this.$parent.onSend(),
          userLatitude: this.$store.getters.getLocation.latitude,
          userLongitude: this.$store.getters.getLocation.longitude
        },
        goodsFormList: skuarr,
        marketSn: this.$store.state.market.marketData.marketSn,
        userNote: this.$store.state.market.marketData.remark,
        delieveryType: this.$store.state.market.marketData.delieveryType,
        selfMentionTime: this.$store.state.market.marketData.delieveryType == 1 ? '' : dayjs().format('YYYY-MM-DD') + ' ' + this.$store.state.market.takeData.selfMentionTime + ':00',
        userPhone: this.$store.state.market.marketData.delieveryType == 1 ? '' : this.$store.state.market.takeData.userPhone
      }
      this.$toast.loading({
        duration: 0,
        forbidClick: true,
        message: '下单'
      })
      payOrder(orderData).then(res => {
        if (res.status == 200) {
          this.$store.state.market.isLock = false
          this.$toast.clear()
          let payData = {
            orderNo: res.data.orderNo,
            actualPay: res.data.actualPay,
            marketSn: this.$store.state.market.marketData.marketSn
          }
          this.payData = payData
          this.orderNo = res.data.orderNo
          this.ybalance = res.data.actualPay
          if (this.$store.state.market.marketData.payradio != 1 && this.$store.state.market.marketData.payradio != 7) {
            this.pay(payData)
          } else {
            this.show = true
            // this.$refs.showKeyboardRef.show()
            AlipayJSBridge.call('OnScreenshot', {}, function(result) {
              console.log(result)
            })
          }
        } else {
          this.$toast.clear()
          this.password = ''
          this.$toast(res.message)
        }
      })
    },
    pay(data) {
      // 请求支付
      let self = this
      let Base64 = require('js-base64').Base64
      // var u = navigator.userAgent
      // var isiOS = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/) // ios终端
      this.$toast.loading({
        duration: 0,
        forbidClick: true
      })
      let paydata = {
        capitalAccountId: 1,
        paymentType: this.$store.state.market.marketData.payradio,
        payPassword: Base64.encode(self.password),
        terminalSysVer: version,
        // eslint-disable-next-line no-undef
        clientIp: '127.0.0.1',
        orderNo: data.orderNo,
        deviceId: localStorage.getItem('deviceId') ? localStorage.getItem('deviceId') : 'test',
        longitude: this.$store.getters.getLocation.longitude,
        latitude: this.$store.getters.getLocation.latitude
      }

      if (paydata.payPassword === '' || paydata.payPassword === null) {
        delete paydata.payPassword
      }
      paydata.sign = query(paydata)

      getPay(paydata)
        .then(res => {
          self.$toast.clear()
          if (res.status == 200) {
            if (this.$store.state.market.marketData.payradio == 1 || this.$store.state.market.marketData.payradio == 7) {
              this.show = false
              this.password = ''
              // 余额支付
              // 强校验
              if (this.$store.state.market.marketData.payradio == 7 && res.data.thirdPartPayData.resultCode == '00018') {
                this.checkUnionPay.smsCode = ''
                this.$toast('交易存在风险，请输入银联验证码进行确认')
                this.checkUnionPay.smsId = res.data.thirdPartPayData.smsId
                this.checkUnionPay.unionPayData = res.data.thirdPartPayData.unionPayData
                this.codeLog = true

                return
              }
              // 交易中
              if (this.$store.state.market.marketData.payradio == 7 && res.data.thirdPartPayData.resultCode == '00004') {
                self.orderNo = res.data.orderNo
                const timerccb = window.setInterval(() => {
                  self.appPay()
                }, 2000)

                this.$toast.loading({
                  duration: 3000,
                  forbidClick: true,
                  message: '交易处理中...'
                })

                this.$once('hook:beforeDestroy', () => {
                  window.clearInterval(timerccb)
                })
                return
              }

              this.$toast('付款成功')
              setTimeout(() => {
                self.$router.push({
                  name: 'OrderDetail',
                  query: {
                    orderNo: this.orderNo,
                    type: 7,
                    from: 1
                  }
                })
              }, 500)
              self.orderDataLog()
            } else if (this.$store.state.market.marketData.payradio == 2) {
              // 支付宝支付
              DPay.getPay(this.$store.state.market.marketData.payradio, res.data.thirdPartPayData.qrUrl, 1, res.data.orderNo)
            } else if (this.$store.state.market.marketData.payradio == 3) {
              // 微信支付
              DPay.getPay(this.$store.state.market.marketData.payradio, res.data.thirdPartPayData.pyTrnNo, 1, res.data.orderNo)
            } else if (this.$store.state.market.marketData.payradio == 9) {
              // 云闪付
              DPay.getPay(this.$store.state.market.marketData.payradio, res.data.thirdPartPayData.qrUrl, 1, res.data.orderNo)
            }
          } else {
            this.password = ''
            this.$toast(res.message)
          }
        })
    },
    // 检查支付提示
    okPayMess() {
      this.$dialog
        .confirm({
          title: '是否支付完成？',
          message: ''
        })
        .then((res) => {
          this.appPay()
        })
        .catch(() => {
          this.appPay()
        })
    },
    // 加查支付
    appPay() {
      payLoading(this.orderNo)
        .then(res => {
          if (res.status == 200) {
            if (res.data == true) {
              this.orderDataLog()
              this.$toast('支付成功')
              setTimeout(() => {
                this.$router.push({
                  name: 'OrderDetail',
                  query: {
                    orderNo: this.orderNo,
                    type: 7,
                    from: 1
                  }
                })
              }, 500)
            } else {
              this.$toast('支付失败')
              this.$router.push({ name: 'Order' })
            }
          } else {
            this.$toast(res.message)
          }
        })
    },
    // 关闭支付
    closePop() {
      this.$router.push({
        name: 'OrderDetail',
        query: {
          orderNo: this.orderNo,
          type: 7,
          from: 1
        }
      })
    },
    // 记录完成订单轨迹
    orderDataLog() {
      let data = {
        orderMoney: this.ybalance,
        orderTime: Date.parse(new Date()),
        orderPayType: this.$store.state.market.marketData.payradio,
        orderNo: this.orderNo
      }
      addOrderData(data)
      // 上报
      logDataUp()
    },
    // 强校验
    confirmFinishCheckUnionPay() {
      let self = this
      finishCheckUnionPay(this.checkUnionPay).then(function(res) {
        if (res.status == 200) {
          if (res.data.resultCode == '00000') {
            self.$toast('支付成功')
            self.orderDataLog()
            setTimeout(() => {
              self.$router.push({
                name: 'OrderDetail',
                query: {
                  orderNo: self.orderNo,
                  type: 7,
                  from: 1
                }
              })
            }, 500)
          } else {
            self.$toast('支付失败')
          }
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
	.home {
		width: 100%;
		margin: 0 auto;

		.cart-box {
			width: 100%;
			// height: 140px;
      height: calc(140px + var(---nav-height));
			position: fixed;
			background-color: #f1f2f8;
			bottom: 0;
		}

		.cart {
			display: flex;
			width: 678px;
			height: 108px;
			box-sizing: border-box;
			margin: 16px auto;

			.cart-left {
				display: flex;
				align-items: center;
				height: 108px;
				width: 500px;
				border-radius: 54px 0 0 54px;
				background-color: #000000;
				color: #ffffff;
				font-size:36px;
				.cart-img {
					width: 50px;
					height: 46px;
					margin-right: 26px;
					margin-left: 42px;

					img {
						width: 100%;
						height: 100%;
						position: relative;
						top: -5px;
					}
				}

				.price {
					font-size: 45px;
          font-family:PingFangSC-Medium;
				}
			}

			.cart-right {
				display: flex;
				justify-content: center;
				align-items: center;
				color: #ffffff;
				font-size: 30px;
				width: 176px;
				height: 108px;
				border-radius: 0 54px 54px 0;
        font-family:PingFangSC-Medium;
        background: linear-gradient(90deg,#ff1e29 50%, #ff5a25 100%);
			}
		}

		.cart-left .small {
			display: inline-block;
			margin-right: 4px;
			font-size: 30px;
			margin-right: 10px;
		}

		.box {
			.title {
				width: 100%;
				box-sizing: border-box;
				text-align: center;
				font-size: 34px;
				color: #222;
        margin-top: 56px;
        margin-bottom: 38px;
			}
      .price {
        text-align:center;
        font-size:60px;
        margin-bottom:80px;
      }
			.balance {
				margin: 0 54px;
				.payment-line {
					display: flex;
					justify-content: space-between;
					align-items: center;
					font-size: 34px;
					color: #222;
          margin-bottom: 47px;
					.line-left {
						display: flex;
						align-items: center;

						>span {
              font-size: 34px;
							margin-left: 10px;
						}
					}

					.line-right {
            color: #999;
            font-size: 32px;
						display: flex;
						align-items: center;
					}
				}
			}
		}

		.forgetPwd {
      color: #39CF3F;
			text-align: right;
			margin-right: 54px;
			font-size: 28px;
			margin-top: 32px;
      height: 50px;
		}

		::v-deep .van-password-input {
			margin: 0 54px;
		}

		::v-deep .van-popup__close-icon {
			font-size: 35px;
		}

		::v-deep .van-popup__close-icon--top-right {
			top: 60px;
		}

		::v-deep[class*="van-hairline"]::after {
			border-color: #ccc;
		}

		::v-deep .van-popup--center.van-popup--round {
			border-radius: 24px;
		}

		::v-deep .van-popup--center {
			top: 35%;
		}
      .popBox {
  position: relative;
  width: 561px;
  height: 32px;
  padding-top:48px;
  box-sizing: border-box;
  ::v-deep .van-overlay{
    z-index: 9002 !important;
  }

  .title {
    color: #7f7f87;
    font-size: 30px;
    padding-left: 42px;
  }

  ::v-deep .van-field__label{
    width: 100px;
    margin-right: 0;
  }
}
.popBtn {
  position: fixed;
  left: 0;
  bottom: 0px;
  display: flex;
  width: 561px;
  height: 97px;
  line-height: 97px;
  font-size: 32px;
  justify-content: space-between;
  border-top: 1px solid #cfcece;
  text-align: center;
  .cancel {
    width: 50%;
    border-right: 1px solid #cfcece;
    color: #999999;
  }
  .confirm {
    width: 50%;
    color: #6095f0;
  }
  }
	}
</style>
