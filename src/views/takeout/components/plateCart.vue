<template>
  <div class="home">
    <!-- 购物车 -->
    <div class="cart-box">
      <div class="cart">
        <div class="cart-left">
          <div class="cart-img">
            <!-- 如果购物车无商品，置灰 -->
            <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/takeout/carticon.png" alt="">
          </div>
          <div>
            <span class="small">¥</span>
            <span class="price">{{ cartPrice | Fmoney }}</span>
          </div>
        </div>
        <div class="cart-right" @click="fastSetOrder()">
          提交订单
        </div>
      </div>
    </div>

    <!-- 数字键盘,和扫码支付样式有差别 -->
    <van-popup v-model="show" round closeable :style="{ width: '310px', height: '320px', }">
      <div class="box">
        <div class="title">输入支付密码</div>
        <div class="price">
          ¥{{ cartPrice }}
        </div>
        <div class="balance">
          <div class="payment-line">
            <div class="line-left">
              <van-icon name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/pay/balance.png" size="23px" />
              <span>账户余额</span>
            </div>
            <div class="line-right">
              <span>{{ $store.state.market.marketData.balance }}元</span>
            </div>
          </div>
        </div>
        <van-password-input :value="password" info="" :length="6" :focused="showKeyboard" @focus="showKeyboard = true" />
      </div>
      <div class="forgetPwd" @click="goPwd">找回密码</div>
    </van-popup>

    <!-- <van-number-keyboard :hide-on-click-outside="false" :show="show" v-model="password" theme="custom" z-index="9000" extra-key="."
	close-button-text="完成" @blur="show = false" @input="onInput" @delete="onDelete" /> -->

    <!-- <van-number-keyboard
      v-model="password"
      :hide-on-click-outside="false"
      :show="show"
      z-index="9000"
    /> -->

    <!-- 验证码弹框 -->
    <van-popup v-model="codeLog" round :style="{ height: '130px', }">
      <div class="popBox">
        <van-field v-model="checkUnionPay.smsCode" center clearable :border="false" label="验证码" placeholder="请输入验证码" />
        <div class="popBtn">
          <div class="cancel" @click="codeLog = false">取消</div>
          <div class="confirm" @click="confirmFinishCheckUnionPay">确认</div>
        </div>
      </div>
    </van-popup>

    <!-- 安全键盘 -->
    <div id="app" @dblclick="() => {return false;}">
      <div class="cus-keyboard">
        <Keyboard ref="showKeyboardRef" :length="length" :default-val="defaultVal" :text.sync="password" :keyboard-type="1" />
      </div>
    </div>
  </div>
</template>

<script>
import {
  // payOrder,
  getPay,
  payLoading,
  getWeatherConfig
} from '@/api/takeout'
import { saveOrder } from '@/api/shoppingMall/order'
import NP from 'number-precision'
import { finishCheckUnionPay, fundAccountQuery } from '@/api/bank'
import { getUserAccount } from '@/api/takeout'
import {
  addOrderData,
  logDataUp
} from '@/utils/upLog.js'
import {
  version
} from '@/config/settings'
import Maths from '@/utils/math.js'
import { query } from '@/utils/sign'
import Keyboard from '@/components/Keyboard'

export default {
  name: 'Home',
  components: { Keyboard },
  data() {
    return {
      show: false,
      showKeyboard: false,
      password: '',
      orderNo: '',
      ybalance: '',
      payData: '',
      codeLog: false,
      checkUnionPay: {
        smsId: '',
        smsCode: '',
        unionPayData: ''
      },
      defaultVal: '',
      length: 6
    }
  },
  computed: {
    cartPrice() { // 总价
      if (this.$store.state.market.marketData.coupon == '') {
        let sum = 0
        sum = new Maths(this.$store.getters['cart/sumPrice'], this.getPostFree()).sum()
        sum = new Maths(sum, this.$store.getters['cart/sumPackPrice']).sum()
        return sum
      } else {
        let sum = 0
        sum = new Maths(this.$store.getters['cart/sumPrice'], this.getPostFree()).sum()
        sum = new Maths(sum, this.$store.getters['cart/sumPackPrice']).sum()
        sum = new Maths(sum, this.$store.state.market.marketData.coupon.preferentialAmount).minus()
        return sum
      }
    }
  },
  watch: {
    password(val, old) {
      if (val.length >= 6) {
        this.pay(this.payData)
      }
    },
    show(val) {
      if (val === false) {
        this.$refs.showKeyboardRef.hide()
        AlipayJSBridge.call('OffScreenshot', {}, function(result) {
          console.log(result)
        })
      }
    }
  },
  mounted() {
    this.$store.state.market.isLock = true
  },
  destroyed() {
    AlipayJSBridge.call('OffScreenshot', {}, function(result) {
      console.log(result)
    })
  },
  methods: {
    // 下单前检查
    // 先检查是否满足二类户开通
    fastSetOrder() {
      if (this.$store.state.market.marketData.payradio == 7) {
        getUserAccount(this.$store.state.market.marketData.marketSn).then(res => {
          if (res.status == 200) {
            if (res.data.payChannel == 2) {
              // 检查是否开户成功
              this.fundAccountQuery()
            } else {
              this.setOrder()
            }
          }
        })
      } else {
        this.setOrder()
      }
    },
    // 检查开户
    fundAccountQuery() {
      let queryVO = {
        'bankType': 'UnionPay',
        'userId': this.$store.getters.getUserId
      }
      fundAccountQuery(queryVO).then(res => {
        if (res.data != null) {
          if (res.data.status != 2) {
            this.$dialog.confirm({
              message: '请先完成开户流程！',
              confirmButtonText: '去开通',
              cancelButtonText: '暂不开通'
            }).then(() => {
              this.$router.push('/wallet')
            })
          } else {
            this.setOrder()
          }
        } else {
          this.$dialog.confirm({
            message: '请先完成开户流程！',
            confirmButtonText: '去开通',
            cancelButtonText: '暂不开通'
          }).then(() => {
            this.$router.push('/wallet')
          })
        }
      })
    },
    // 下单前检查
    setOrder() {
      // 检查配置
      let self = this
      getWeatherConfig(this.$store.getters.getRegionId).then(res => {
        if (res.status == 200) {
          if (res.data != null && res.data.cvalue != 100) {
            self.$dialog.confirm({
              title: '提醒',
              message: '受当前天气影响，您的订单可能会延迟送达，请确认是否下单'
            }).then(() => {
              self.throttleBtn()
            }).catch(() => {
              // on cancel
            })
          } else {
            self.throttleBtn()
          }
        } else {
          self.$toast(res.message)
        }
      })
    },
    // 计算运费
    getPostFree() {
      let data = this.$store.state.market.marketData.postFee
      if (data == '') {
        return 0
      } else if (this.$store.state.market.marketData.agentPostFee > 0) {
        if (this.$store.state.market.marketData.agentPostFee < data) {
          return NP.minus(Number(data), Number(this.$store.state.market.marketData.agentPostFee))
        }
        if (this.$store.state.market.marketData.agentPostFee > data) {
          return 0
        }
      } else {
        return data
      }
    },
    goPwd() {
      this.$router.push('/editPayPwd')
    },
    throttleBtn() {
      let self = this
      if (this.$parent.onSend() == '') {
        this.$toast('请选择下单地址')
        return
      } else if (this.$store.state.market.marketData.payradio == '') {
        this.$toast('请选择支付方式')
        return
      } else if (
        this.$store.state.market.marketData.payradio == 1 &&
					this.$store.state.market.marketData.balance < this.cartPrice()
      ) {
        this.$toast('余额不足')
        return
      }

      this.$throttle(() => {
        if (self.orderNo != '') {
          let payData = {
            orderNo: self.orderNo,
            actualPay: self.ybalance,
            marketSn: self.$store.state.market.marketData.marketSn
          }
          if (self.$store.state.market.marketData.payradio != 1 && self.$store.state.market.marketData.payradio != 7) {
            self.pay(payData)
          } else {
            self.show = true
            this.$refs.showKeyboardRef.show()
            AlipayJSBridge.call('OnScreenshot', {}, function(result) {
              console.log(result)
            })
          }
        } else {
          self.payOrder()
        }
      }, 3000)
    },
    // 下单
    payOrder() {
      if (this.$parent.onSend() == '') {
        this.$toast('请选择下单地址')
        return
      } else if (this.$store.state.market.marketData.payradio == '') {
        this.$toast('请选择支付方式')
        return
      } else if (
        this.$store.state.market.marketData.payradio == 1 &&
					this.$store.state.market.marketData.balance < this.cartPrice()
      ) {
        this.$toast('余额不足')
        return
      }
      let cart = this.$store.state.cart.cartData[0].goodsList // 获取加入购物车
      var obj = {}
      for (var i = 0, l = cart.length; i < l; i++) {
        var item = cart[i].skuId
        obj[item] = obj[item] + 1 || 1
      }
      console.log(obj)
      var skuarr = []
      for (let i in obj) {
        var newobj = {}
        newobj.skuId = i
        newobj.skuQuantity = obj[i]
        skuarr.push(newobj) // 属性
      }

      for (let i = 0; i < skuarr.length; i++) {
        for (let b = 0; b < cart.length; b++) {
          if (skuarr[i].skuId == cart[b].skuId) {
            skuarr[i].agentId = cart[b].agentId
          }
        }
      }

      let orderData = {
        couponsId: this.$store.state.market.marketData.coupon != '' ? this.$store.state.market.marketData.coupon.couponId : '',
        delieveryType: this.$store.state.market.marketData.delieveryType,
        stationSn: this.$store.state.market.marketData.stationSn,
        deliveryForm: {
          deliverAddressId: this.$parent.onSend(),
          userLatitude: this.$store.getters.getLocation.latitude,
          userLongitude: this.$store.getters.getLocation.longitude
        },
        goodsFormList: skuarr,
        marketSn: this.$store.state.market.marketData.marketSn,
        userNote: this.$store.state.market.marketData.remark
      }
      this.$toast.loading({
        duration: 0,
        forbidClick: true,
        message: '下单'
      })
      saveOrder(orderData).then(res => {
        if (res.status == 200) {
          this.$store.state.market.isLock = false
          this.$toast.clear()
          let payData = {
            orderNo: res.data.orderNo,
            actualPay: res.data.actualPay,
            marketSn: this.$store.state.market.marketData.marketSn
          }
          this.payData = payData
          this.orderNo = res.data.orderNo
          this.ybalance = res.data.actualPay
          if (this.$store.state.market.marketData.payradio != 1 && this.$store.state.market.marketData.payradio != 7) {
            this.pay(payData)
          } else {
            this.show = true
            this.$refs.showKeyboardRef.show()
            AlipayJSBridge.call('OnScreenshot', {}, function(result) {
              console.log(result)
            })
            // this.showKeyboard = true;
          }
        } else {
          this.$toast.clear()
          this.password = ''
          this.$toast(res.message)
        }
      })
    },
    pay(data) {
      // 请求支付
      let self = this
      let Base64 = require('js-base64').Base64
      var u = navigator.userAgent
      var isiOS = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/) // ios终端
      this.$toast.loading({
        duration: 0,
        forbidClick: true
      })
      let paydata = {
        capitalAccountId: 1,
        paymentType: this.$store.state.market.marketData.payradio,
        payPassword: Base64.encode(self.password),
        terminalSysVer: version,
        // eslint-disable-next-line no-undef
        clientIp: '127.0.0.1',
        orderNo: data.orderNo,
        deviceId: localStorage.getItem('deviceId') ? localStorage.getItem('deviceId') : 'test',
        longitude: this.$store.getters.getLocation.longitude,
        latitude: this.$store.getters.getLocation.latitude
      }

      if (paydata.payPassword === '' || paydata.payPassword === null) {
        delete paydata.payPassword
      }
      paydata.sign = query(paydata)

      getPay(paydata)
        .then(res => {
          self.$toast.clear()
          if (res.status == 200) {
            if (this.$store.state.market.marketData.payradio == 1 || this.$store.state.market.marketData.payradio == 7) {
              // 余额支付
              this.show = false
              this.password = ''

              // 强校验
              if (this.$store.state.market.marketData.payradio == 7 && res.data.thirdPartPayData.resultCode == '00018') {
                this.checkUnionPay.smsCode = ''
                this.$toast('交易存在风险，请输入银联验证码进行确认')
                this.checkUnionPay.smsId = res.data.thirdPartPayData.smsId
                this.checkUnionPay.unionPayData = res.data.thirdPartPayData.unionPayData
                this.codeLog = true

                return
              }
              // 交易中
              if (this.$store.state.market.marketData.payradio == 7 && res.data.thirdPartPayData.resultCode == '00004') {
                self.orderNo = res.data.orderNo
                const timerccb = window.setInterval(() => {
                  self.appPay()
                }, 2000)

                this.$toast.loading({
                  duration: 3000,
                  forbidClick: true,
                  message: '交易处理中...'
                })

                this.$once('hook:beforeDestroy', () => {
                  window.clearInterval(timerccb)
                })
                return
              }
              // self.orderDataLog()
              this.$toast('付款成功')
              setTimeout(() => {
                self.$router.push({
                  name: 'Order',
                  query: {
                    activeIdx: '0'
                  }
                })
                self.$store.state.tabbar.index = 2
              }, 500)
            } else if (this.$store.state.market.marketData.payradio == 2) { // 支付宝支付
              if (!isiOS) {
                AlipayJSBridge.call('IsAvailable', {
                  packageName: 'com.eg.android.AlipayGphone'
                }, function(result) {
                  if (result.error == 1) {
                    self.$toast({
                      duration: 5000, // 持续展示 toast
                      forbidClick: true,
                      message: 'APP启动失败,请关闭进程重新打开使用'
                    })
                  }
                  if (result.available == true) {
                    let url =
												'alipays://platformapi/startapp?saId=10000007&qrcode=' +
												res.data.thirdPartPayData.qrUrl
                    window.location.href = url
                    setTimeout(() => {
                      self.okPayMess()
                    }, 2000)
                  } else {
                    self.$toast('未安装支付宝')
                  }
                })
              } else {
                AlipayJSBridge.call('IsAvailable', {
                  packageName: 'alipay://'
                }, function(result) {
                  if (result.available == true) {
                    let url =
												'alipays://platformapi/startapp?saId=10000007&qrcode=' +
												res.data.thirdPartPayData.qrUrl
                    window.location.href = url
                    setTimeout(() => {
                      self.okPayMess()
                    }, 2000)
                  } else {
                    self.$toast('未安装支付宝')
                  }
                })
              }
            } else if (this.$store.state.market.marketData.payradio == 3) {
              AlipayJSBridge.call('HsbFun', { orderNo: res.data.thirdPartPayData.pyTrnNo }, function(result) {
                if (result.payResult === 'success') {
                  self.orderDataLog()
                  // 支付成功,跳转
                  self.$toast('支付成功')
                  setTimeout(() => {
                    self.$router.push({
                      name: 'Order'
                    })
                  }, 500)
                } else {
                  // 支付失败跳转
                  self.$toast('支付失败')
                  self.$router.push({
                    name: 'Order',
                    query: {
                      activeIdx: '0'
                    }
                  })
                  self.$store.state.tabbar.index = 2
                }
              })
            } else if (this.$store.state.market.marketData.payradio == 9) {
              // 云闪付
              let u = navigator.userAgent
              let isiOS = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/) // ios终端
              if (!isiOS) {
                AlipayJSBridge.call(
                  'IsAvailable',
                  {
                    // 判断ios和安卓包名
                    packageName: 'com.unionpay'
                  },
                  function(result) {
                    if (result.available == true) {
                      let url = res.data.thirdPartPayData.qrUrl
                      let newurl = 'upwallet://html/' + url.slice(8)
                      window.location.href = newurl
                      setTimeout(() => {
                        self.okPayMess()
                        self.orderNo = res.data.orderNo
                      }, 2000)
                    } else {
                      self.$toast('未安装云闪付')
                      setTimeout(() => {
                        self.okPayMess()
                      }, 2000)
                      AlipayJSBridge.call('OpenUrlForAPP', { openurl: 'https://wallet.95516.com/s/wl/webV3/activity/yhtzbtoc/html/snsIndex.html?r=6f040ef12df584d1a98ee25aefacf97c&code=' }, function(result) {})
                    }
                  }
                )
              } else {
                let url = res.data.thirdPartPayData.qrUrl
                let newurl = 'upwallet://html/' + url.slice(8)
                window.location.href = newurl
                setTimeout(() => {
                  self.okPayMess()
                  self.orderNo = res.data.orderNo
                }, 2000)
                // AlipayJSBridge.call('IsAvailable',
                //   {
                //     // 判断ios和安卓包名
                //     packageName: 'upwallet://'
                //   },
                //   function(result) {
                //     if (result.available == true) {
                //       let url = res.data.thirdPartPayData.qrUrl
                //       let newurl = 'upwallet://html/' + url.slice(8)
                //       window.location.href = newurl
                //       setTimeout(() => {
                //         self.okPayMess()
                //         self.orderNo = res.data.orderNo
                //       }, 2000)
                //     } else {
                //       self.$toast('未安装云闪付')
                //     }
                //   }
                // )
              }
            }
          } else {
            this.password = ''
            this.$toast(res.message)
          }
        })
    },
    // 检查支付提示
    okPayMess() {
      this.$dialog
        .confirm({
          title: '是否支付完成？',
          message: ''
        })
        .then((res) => {
          this.appPay()
        })
        .catch(() => {
          this.appPay()
        })
    },
    // 加查支付
    appPay() {
      let self = this
      payLoading(this.orderNo)
        .then(res => {
          if (res.status == 200) {
            if (res.data == true) {
              this.orderDataLog()
              this.$toast('支付成功')
              setTimeout(() => {
                this.$router.push({
                  name: 'Order',
                  query: {
                    activeIdx: '0'
                  }
                })
                self.$store.state.tabbar.index = 2
              }, 500)
            } else {
              this.$toast('支付失败')
              this.$router.push({
                name: 'Order',
                query: {
                  activeIdx: '0'
                }
              })
              self.$store.state.tabbar.index = 2
            }
          } else {
            this.$toast(res.message)
          }
        })
    },
    // 记录完成订单轨迹
    orderDataLog() {
      let data = {
        orderMoney: this.ybalance,
        orderTime: Date.parse(new Date()),
        orderPayType: this.$store.state.market.marketData.payradio,
        orderNo: this.orderNo
      }
      addOrderData(data)
      // 上报
      logDataUp()
    },
    // 强校验
    confirmFinishCheckUnionPay() {
      let self = this
      finishCheckUnionPay(this.checkUnionPay).then(res => {
        if (res.status == 200) {
          if (res.data.resultCode == '00000') {
            this.$toast('支付成功')
            this.orderDataLog()
            setTimeout(() => {
              this.$router.push({
                name: 'Order',
                query: {
                  activeIdx: '0'
                }
              })
              self.$store.state.tabbar.index = 2
            }, 500)
          } else {
            this.$toast('支付失败')
          }
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
	.home {
		width: 100%;
		margin: 0 auto;

		.cart-box {
			width: 100%;
			height: 140px;
			position: fixed;
			background-color: #f1f2f8;
			bottom: 0;
		}

		.cart {
			display: flex;
			width: 678px;
			height: 108px;
			box-sizing: border-box;
			margin: 16px auto;

			.cart-left {
				display: flex;
				align-items: center;
				height: 108px;
				width: 500px;
				border-radius: 54px 0 0 54px;
				background-color: #000000;
				color: #ffffff;
				font-size:36px;
				.cart-img {
					width: 50px;
					height: 46px;
					margin-right: 26px;
					margin-left: 42px;

					img {
						width: 100%;
						height: 100%;
						position: relative;
						top: -5px;
					}
				}

				.price {
					font-size: 45px;
				}
			}

			.cart-right {
				display: flex;
				justify-content: center;
				align-items: center;
				color: #ffffff;
				font-size: 30px;
				width: 176px;
				height: 108px;
				border-radius: 0 54px 54px 0;
        background: linear-gradient(90deg,#ff1e29 50%, #ff5a25 100%);
			}
		}

		.cart-left .small {
			display: inline-block;
			margin-right: 4px;
			font-size: 30px;
			margin-right: 10px;
		}

		.box {
			.title {
				width: 100%;
				box-sizing: border-box;
				text-align: center;
				font-size: 34px;
				color: #222;
        margin-top: 56px;
        margin-bottom: 48px;
			}
      .price {
        text-align:center;
        font-size:60px;
        margin-bottom:80px;
      }
			.balance {
				margin: 0 54px;
				.payment-line {
					display: flex;
					justify-content: space-between;
					align-items: center;
					font-size: 34px;
					color: #222;
          margin-bottom: 47px;
					.line-left {
						display: flex;
						align-items: center;

						>span {
              font-size: 34px;
							margin-left: 10px;
						}
					}

					.line-right {
            color: #999;
            font-size: 32px;
						display: flex;
						align-items: center;
					}
				}
			}
		}

		.forgetPwd {
      color: #39CF3F;
			text-align: right;
			margin-right: 54px;
			font-size: 28px;
			margin-top: 32px;
		}

		::v-deep .van-password-input {
			margin: 0 54px;
		}

		::v-deep .van-popup__close-icon {
			font-size: 35px;
		}

		::v-deep .van-popup__close-icon--top-right {
			top: 60px;
		}

		::v-deep[class*="van-hairline"]::after {
			border-color: #ccc;
		}

		::v-deep .van-popup--center.van-popup--round {
			border-radius: 24px;
		}

		::v-deep .van-popup--center {
			top: 35%;
		}
      .popBox {
  position: relative;
  width: 561px;
  height: 32px;
  padding-top:48px;
  box-sizing: border-box;

  .title {
    color: #7f7f87;
    font-size: 30px;
    padding-left: 42px;
  }

  ::v-deep .van-field__label{
    width: 100px;
    margin-right: 0;
  }
}
.popBtn {
  position: fixed;
  left: 0;
  bottom: 0px;
  display: flex;
  width: 561px;
  height: 97px;
  line-height: 97px;
  font-size: 32px;
  justify-content: space-between;
  border-top: 1px solid #cfcece;
  text-align: center;
  .cancel {
    width: 50%;
    border-right: 1px solid #cfcece;
    color: #999999;
  }
  .confirm {
    width: 50%;
    color: #6095f0;
  }
  }
	}
</style>
