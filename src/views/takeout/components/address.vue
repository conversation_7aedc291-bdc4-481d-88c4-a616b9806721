<template>
  <!-- 选择地址 -->
  <div class="content">
    <!-- 自营商城 -->
    <div v-if="$store.state.market.marketData.type == 10" class="tabAddress">
      <div :class="{'intab':tabStatus==1}" @click="goTab(1)">订单配送</div>
      <div :class="{'intab':tabStatus==2}" @click="goTab(2)">到店自提</div>
    </div>
    <!-- 普通外卖 -->
    <div v-else class="tabAddress">
      <div :class="{'intab':tabStatus==1}" @click="goTab(1)">配送地址</div>
      <div v-if="this.$store.state.market.marketData.isSelfMention == true" :class="{'intab':tabStatus==3}" @click="goTab(3)">到店自提</div>
    </div>

    <div v-if="tabStatus==1||tabStatus==2" class="address">
      <div class="address-line">
        <div class="aIn" @click="addressShowTrue">
          <div v-if="inAddress == '选择收货地址'" class="nullAdd" style="color: #5ECC52;">选择收货地址<van-icon style="margin-left:9px" name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/common/arrowRightBlack.png" size="11px" /></div>
          <div v-else style="margin-top:5px">{{ inAddress | ellipsis(16) }}<van-icon style="margin-left:5px" name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/common/arrowRightBlack.png" size="11px" /></div>
        </div>
        <div>
          <span>{{ username }}</span>
          <span>{{ mobile }}</span>
        </div>
      </div>
      <div class="address-line2">
        <span v-if="tabStatus == 1">立即送出</span>
        <span v-else>请到店自提</span>
      </div>
    </div>

    <div v-if="tabStatus == 3" class="address">
      <div class="formShop">
        <div class="formShopLeft">
          <div class="formShopAddress" @click="goMarket">{{ $store.state.market.marketData.address }}</div>
          <div class="formShopTimePhone">
            <div class="formShopTime" @click="onShowTime">
              <div>自取时间</div>
              <div class="formShopTimeShow">{{ onTime }}</div>
            </div>
            <div class="formShopPhone">
              <div>预留电话</div>
              <div class="formShopTimeShow" @click="keyboard = true">
                <van-field v-model="$store.state.market.takeData.userPhone" size="11" readonly label="" placeholder="请输入预留号码" />
              </div>
            </div>
          </div>
          <div class="formShopMsg">
            <van-checkbox v-model="$store.state.market.takeData.checkedAgreement" />
            <div>
              同意<span style="color:#2E84DB" @click="infoMsg = true">《到店自取服务协议》</span>
            </div>
          </div>
        </div>
        <div class="formShopRight" @click="goMarket">
          <div class="formShopRightBox">
            <div v-if="$store.state.market.marketData.distance!=null" class="formShopRightDistance">
              距您{{ $store.state.market.marketData.distance.toFixed(2) }}km
            </div>
            <div class="formShopRightShopImg">
              <img :src="$store.state.market.marketData.pic" class="" alt="">
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 选择地址弹框 -->
    <van-popup v-model="addressShow" position="bottom" round closeable :style="{ width: '100%', height: '350px', }">
      <div class="box">
        <div class="title">地址</div>
        <!-- <div class="close-icon" @click="addressShow=false">
          <van-icon name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/order/closeicon.png" size="15px" />
        </div> -->
        <div class="body">
          <van-radio-group v-model="radiovalue" @change="radioChange">
            <div class="address-pop" style="margin-top: 62px;">
              <div v-for="(item,index) in temparr" :key="index" class="item">
                <div class="item-left">
                  <van-radio :name="index" icon-size="19px" checked-color="#5dcb4f">
                    <div class="item-detail">
                      <!-- 最多可以输入18个字 -->
                      <div class="item-detail-address"><van-tag v-if="item.isDefault == true" plain type="danger">默认</van-tag> {{ showAddress(item) | ellipsis(18) }}</div>
                      <div class="item-detail-user">
                        <span style="margin-right:26px">{{ tabStatus==1?item.username:item.contacts }}</span>
                        <span>{{ tabStatus==1?item.mobile:item.mobilePhone }}</span>
                      </div>
                    </div>
                  </van-radio>
                </div>
                <div v-if="tabStatus==1" class="item-right" @click="editAddress(item)">
                  <van-icon name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/order/edit.png" size="17px" />
                </div>
              </div>
            </div>
          </van-radio-group>
          <!-- 超出配送距离 -->
          <div v-if="temparrNo.length>0" class="disaddress">超出配送范围地址</div>
          <!-- <van-radio-group disabled> -->
          <div class="address-pop">
            <div v-for="(item,index) in temparrNo" :key="index" class="item">
              <div class="item-left" @click="disAddrShow=true">
                <!-- <van-radio :name="index" icon-size="19px" checked-color="#5dcb4f" /> -->
                <div class="item-detail opacity6">
                  <div style="color: #222;"><van-tag v-if="item.isDefault == true" plain type="danger">默认</van-tag> {{ showAddress(item) | ellipsis(18) }}</div>
                  <div style="color: #999;">
                    <span style="margin-right:26px">{{ item.username }}</span>
                    <span>{{ item.mobile }}</span>
                  </div>
                </div>
              </div>
              <div class="item-right" @click="editAddress(item)">
                <van-icon name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/order/edit.png" size="17px" />
              </div>
            </div>
          </div>
          <div style="height:50px" />
          <!-- 添加地址按钮 -->
          <div v-if="tabStatus==1" class="newAddr" @click="newAddr">
            <div />
          </div>
        </div>
      </div>
    </van-popup>
    <van-overlay :show="disAddrShow" z-index="999999" @click="disAddrShow = false">
      <div class="overlay-wrapper" @click.stop>
        <div class="overlay-img">
          <div class="tips">该地址超出商家配送范围</div>
          <div class="tips2">该地址与商家距离过远，需重新选择收货地址</div>
          <div class="btn" @click="disAddrShow = false">确定</div>
          <div />
        </div>
      </div>
    </van-overlay>

    <!-- 预留电话数字键盘 -->
    <van-number-keyboard
      v-model="$store.state.market.takeData.userPhone"
      :show="keyboard"
      :maxlength="11"
      @blur="keyboard = false"
    />
    <!-- 时间选择 -->
    <van-popup v-model="showTime" round closeable :lock-scroll="false" :safe-area-inset-bottom="true" position="bottom" :style="{ height: '300px' }">
      <div class="timeTitle">选择自取时间</div>
      <div class="showTimeBox">
        <div v-for="(item,index) in columns" :key="index" :class="{'timeItem':onTime == item}" class="timeItemNo" @click="inTime(item)">
          <div>{{ item }}</div>
          <div><van-icon v-if="onTime == item" name="success" /></div>
        </div>
      </div>
    </van-popup>

    <van-dialog v-model="infoMsg" title="协议">
      <div class="infoMsg">
        本服务协议是您与点滴外卖（点滴外卖含义与点滴用户协议一致）之间就点滴外卖提供的信息服务等相关事宜所订立的协议。请您仔细阅读本协议，您勾选"同意并接受《到店自取用户协议》"并点击使用服务后，本服务协议对您产生约束力。您应当在使用服务平台的服务之前认真阅读、充分理解全部条款的内容，如您对条款有任何疑问的，应向平台客服咨询。

        1. 定义

        1.1 服务平台：是搭建、提供及维护到店自取服务信息发布的平台，即点滴外卖平台。

        1.2 用户：即本协议中的“您”，是指在服务平台上下到店自取订单的点滴外卖用户。

        1.3 商家：是指通过服务平台为用户提供到店自取服务的商家。用户在服务平台下到店自取订单后，服务平台将向商家推送订单，商家在在成功接受订单后，将在规定完成商品准备并等待您到店自取。

        2. 服务协议的确认和接受

        2.1 在您使用服务平台的服务之前认真阅读全部条款内容。如您对服务协议有任何疑问的，应联系客服咨询，如不同意，您有权拒绝使用平台提供的服务。一旦您使用服务平台的服务，即表示您已充分阅读、理解并接受本服务协议的全部内容，服务协议即对您产生约束力。届时您不应以未阅读（理解或同意）服务协议的内容为由，主张服务协议无效，或要求撤销服务协议。

        2.2 您在阅读并同意本服务协议并实际使用服务平台的服务时，您即受本服务协议的约束。

        2.3 本协议内容包括服务协议正文及所有服务平台已经发布的或将来可能发布的各类规则。所有规则为本协议不可分割的组成部分，与服务协议正文具有同等法律效力。您承诺接受并遵守本协议的约定，如果您不同意本协议的约定，您应立即停止使用服务平台的服务。

        2.4 服务平台有权根据国家法律法规的更新、产品和服务规则的调整需要不时地制订、修改本协议或各类规则，并在服务平台公示或向您的账户推送消息通知您。如您继续使用服务平台的服务的，即表示您接受经修订的协议和规则。

        3. 服务平台提供的服务

        3.1 搭建、提供及维护服务信息，为用户和商家提供信息发布和撮合交易。

        3.2 用户可以通过服务平台下到店自取订单。
      </div>
    </van-dialog>

    <Loading :show="loadingShow" />

    <!-- 导航 -->
    <van-popup v-model="showPop" position="bottom" round>
      <div class="mapList">
        <div @click="openMap(1)">百度导航</div>
        <div @click="openMap(2)">高德导航</div>
        <div @click="showPop=false">取消</div>
      </div>
    </van-popup>
  </div>
</template>

<script>
import {
  getAddressList,
  getPostFree,
  getStationForUserAddress,
  getConfig
} from '@/api/takeout'
import Loading from '@/components/Loading/index'
import { formatDate } from '@/utils/orderTime'
import {
  addOrderAddressData
} from '@/utils/upLog.js'
export default {
  components: {
    Loading
  },
  data() {
    return {
      addressShow: false,
      radiovalue: '',
      temparr: [],
      temparrNo: [],
      inAddress: '选择收货地址',
      adderssrId: '',
      username: '',
      mobile: '',
      disAddrShow: false,
      loadingShow: true,
      tabStatus: 1,
      keyboard: false,
      columns: [],
      showTime: false,
      marketDelay: {
        endTime: '',
        startTime: '',
        minute: '',
        closingHours: ''
      },
      onTime: '请选择',
      checkedAgreement: true,
      infoMsg: false,
      mapData: '',
      showPop: false
    }
  },
  watch: {
    'tabStatus': function(val) {
      if (val == 3) {
        this.$store.state.market.takeData.userPhone = localStorage.getItem('phone')
        this.$store.state.market.marketData.delieveryType = 2
        this.$store.state.market.marketData.postFee = 0
      } else {
        this.$store.state.market.marketData.delieveryType = val
      }
      this.$toast.loading({
        duration: 0,
        forbidClick: true,
        message: '加载中'
      })
      if (val == 2) {
        this.getStationForUserAddress()
      } else if (val == 1) {
        this.fastGetList()
      } else if (val == 3) {
        this.$toast.clear()
      }
    }
  },
  created() {
    this.getConfig()
  },
  mounted() {
    this.fastGetList()

    // 临时方案--龙泉大区默认到店自提
    // if (this.$store.getters.getRegionId == 3) {
    //   this.tabStatus = 3
    // }
  },
  methods: {
    // 切换tab
    goTab(val) {
      if (this.$store.getters.getRegionId == 3 && val == 1) {
        this.$toast('暂未开放，敬请期待')
        return
      }
      if (this.$store.state.market.isLock == true) {
        this.tabStatus = val
        this.username = ''
        this.mobile = ''
        this.inAddress = ''
        this.$store.state.market.marketData.stationSn = ''
        this.adderssrId = ''
        this.inAddress = '选择收货地址'
      } else {
        this.$toast('暂不支持更换配送方式,请重新下单')
      }
    },
    addressShowTrue() {
      if (this.$store.state.market.isLock == true) {
        if (this.tabStatus == 2) {
          console.log(1)

          this.getStationForUserAddressOn()
        } else {
          console.log(2)
          this.getList()
        }
      } else {
        this.$toast('暂不支持更改地址,请重新下单')
      }
    },
    newAddr() { // 新增地址
      this.$router.push({
        name: 'AddressAdd'
      })
    },
    editAddress(item) { // 修改地址
      this.$router.push({
        name: 'AddressEdit',
        query: {
          id: item.id
        }
      })
    },
    radioChange(e) {
      if (this.tabStatus == 2) {
        this.username = this.temparr[e].contacts
        this.mobile = this.temparr[e].mobilePhone
        this.inAddress = this.temparr[e].address
        this.$store.state.market.marketData.stationSn = this.temparr[e].stationSn
        this.adderssrId = this.temparr[e].id
        this.addressShow = false
      } else {
        // 外卖订单
        let data = {
          marketSn: this.$store.state.market.marketData.marketSn,
          addressId: this.temparr[e].id
        }
        getPostFree(data).then(res => {
          if (res.status == 200) {
            if (this.$store.state.market.marketData.type == 10) {
              // this.$store.state.market.marketData.postFee = 0
              this.$store.state.market.marketData.postFee = res.data.postFee
            } else {
              this.$store.state.market.marketData.postFee = res.data.postFee
            }
            this.$store.state.market.marketData.adderssrId = this.temparr[e].id
            this.adderssrId = this.temparr[e].id

            this.addressShow = false
            // this.inAddress = this.temparr[e].address
            // 兼容老版本
            let dz = this.temparr[e].province + this.temparr[e].city + this.temparr[e].district
            let dzLength = dz.length
            let newdz = this.temparr[e].address.slice(0, dzLength)
            if (dz == newdz) {
              this.inAddress = this.temparr[e].address.slice(dzLength)
            } else {
              this.inAddress = this.temparr[e].address + this.temparr[e].street
            }

            this.username = this.temparr[e].username
            this.mobile = this.temparr[e].mobile

            this.loadingShow = false
            addOrderAddressData(this.temparr[e])
          } else {
            this.loadingShow = false
          }
        })
      }
    },
    // 获取地址列表
    getList() {
      this.$toast.loading({
        message: '',
        duration: 0,
        forbidClick: true
      })
      // let marketSn = this.$store.state.market.marketData.marketSn
      let data = {
        marketSn: this.$store.state.market.marketData.marketSn,
        userLongitude: this.$store.getters.getLocation.longitude,
        userLatitude: this.$store.getters.getLocation.latitude
      }
      getAddressList(data).then(res => {
        this.$toast.clear()
        if (res.status == 200) {
          this.temparr = res.data.withinDistance
          // if (this.$store.state.market.marketData.type == 10) {
          //   this.temparr.push(...res.data.beyondDistance)
          // }
          this.temparrNo = res.data.beyondDistance
          this.addressShow = true
        }
      })
    },
    // 首次进入获取
    fastGetList() {
      this.$store.state.market.takeData.selfMentionTime = ''
      this.$store.state.market.takeData.userPhone = ''
      // let marketSn = this.$store.state.market.marketData.marketSn
      let data = {
        marketSn: this.$store.state.market.marketData.marketSn,
        userLongitude: this.$store.getters.getLocation.longitude,
        userLatitude: this.$store.getters.getLocation.latitude
      }
      getAddressList(data).then(res => {
        this.$toast.clear()
        if (res.status == 200) {
          this.temparr = res.data.withinDistance
          if (this.$store.state.market.marketData.type == 10) {
            this.temparr.push(...res.data.beyondDistance)
          }
          this.temparrNo = res.data.beyondDistance
          if (this.temparr.length) {
            if (this.radiovalue) {
              this.radioChange(this.radiovalue)
            } else {
              this.radioChange(0)
            }
          } else {
            this.loadingShow = false
          }
        } else {
          this.loadingShow = false
        }
      })
    },
    // 获取配送点
    getStationForUserAddress() {
      let data = {
        regionId: this.$store.getters.getRegionId,
        longitude: this.$store.getters.getLocation.longitude,
        latitude: this.$store.getters.getLocation.latitude,
        type: 2
      }
      getStationForUserAddress(data).then(res => {
        this.$toast.clear()
        if (res.status == 200) {
          this.$store.state.market.marketData.postFee = 0
          this.temparr = res.data
          if (res.data.length == 0) {
            this.inAddress = '选择收货地址'
            this.mobile = ''
            this.username = ''
          }
          this.radioChange(0)
        }
      })
    },
    // 弹框获取配送点
    getStationForUserAddressOn() {
      let data = {
        regionId: this.$store.getters.getRegionId,
        longitude: this.$store.getters.getLocation.longitude,
        latitude: this.$store.getters.getLocation.latitude,
        type: 2
      }
      getStationForUserAddress(data).then(res => {
        if (res.status == 200) {
          this.temparr = res.data
          this.addressShow = true
        } else {
          this.$toast(res.message)
        }
      })
    },
    setadderssrId() {
      return this.adderssrId
    },
    // 获取时间区间
    getConfig() {
      getConfig(this.$store.state.market.marketData.marketId).then(res => {
        if (res.status == 200) {
          this.marketDelay.closingHours = res.data.selfTakeEndTime
          this.marketDelay.minute = res.data.marketDelay.minute
        }
      })
    },
    // 时间选择
    onShowTime() {
      this.showTime = true
      var dayjs = require('dayjs')
      let times = formatDate(dayjs().format('HH:mm:ss'), this.marketDelay.closingHours, this.marketDelay.minute)
      this.columns = times
    },
    // 确认选择
    inTime(val) {
      this.onTime = val
      this.$store.state.market.takeData.selfMentionTime = val
      this.showTime = false
    },
    // 打开地图
    openMap(val) {
      let self = this
      if (val == 1) {
        var urlBaiduMap =
						`baidumap://map/marker?location=${this.mapData.bd_lat},${this.mapData.bd_lng}&title=${this.mapData.marketName}&content=${this.mapData.marketName}&src=Hello%20uni-app`
        AlipayJSBridge.call(
          'IsAvailable', {
            packageName: 'com.baidu.BaiduMap'
          },
          function(result) {
            if (result.available == true) {
              window.location.href = urlBaiduMap
            } else {
              self.$toast('未安装百度地图')
            }
          }
        )
      } else {
        var urlAmap =
						`androidamap://viewMap?sourceApplication=${this.mapData.marketName}&poiname=${this.mapData.marketName}&lat=${this.mapData.gd_lat}&lon=${this.mapData.gd_lng}&dev=0`

        var iosAmap =
						`iosamap://viewMap?sourceApplication=${this.mapData.marketName}&poiname=${this.mapData.marketName}&lat=${this.mapData.gd_lat}&lon=${this.mapData.gd_lng}&dev=0`
        var u = navigator.userAgent
        var isiOS = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/) // ios终端
        if (!isiOS) {
          AlipayJSBridge.call(
            'IsAvailable', {
              packageName: 'com.autonavi.minimap'
            },
            function(result) {
              if (result.available == true) {
                window.location.href = urlAmap
              } else {
                self.$toast('未安装高德地图')
              }
            }
          )
        } else {
          window.location.href = iosAmap
        }
      }
    },
    // 导航店铺
    goMarket() {
      let data = this.$store.state.market.marketData.map
      // 判断是系统环境
      var u = navigator.userAgent
      var isiOS = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/) // ios终端
      if (isiOS) {
        const input = document.createElement('input')
        document.body.appendChild(input)
        input.setAttribute('value', data.marketName)
        input.select()
        if (document.execCommand('copy')) {
          document.execCommand('copy')
          this.$toast({
            duration: 5000, // 持续展示 toast
            forbidClick: false,
            message: '复制成功,请打开地图应用,粘贴店铺地址进行导航'
          })
        }
        document.body.removeChild(input)
        return
      }

      // 高德转百度坐标
      function bd_encrypt(gg_lng, gg_lat) {
        var X_PI = Math.PI * 3000.0 / 180.0
        var x = gg_lng
        var y = gg_lat
        var z = Math.sqrt(x * x + y * y) + 0.00002 * Math.sin(y * X_PI)
        var theta = Math.atan2(y, x) + 0.000003 * Math.cos(x * X_PI)
        var bd_lng = z * Math.cos(theta) + 0.0065
        var bd_lat = z * Math.sin(theta) + 0.006
        return {
          bd_lat: bd_lat,
          bd_lng: bd_lng
        }
      }
      let zuobiao = bd_encrypt(data.longitude, data.latitude)

      this.mapData = {
        bd_lng: zuobiao.bd_lng,
        bd_lat: zuobiao.bd_lat,
        gd_lng: data.longitude,
        gd_lat: data.latitude,
        marketName: data.marketName
      }
      this.showPop = true
    },
    // 格式化地址
    showAddress(row) {
      let dz = row.province + row.city + row.district
      let dzLength = dz.length
      let newdz = row.address.slice(0, dzLength)
      if (dz == newdz) {
        return row.address.slice(dzLength)
      } else {
        return row.address + row.street
      }
    }
  }
}
</script>

<style lang="scss" scoped>
	.content {
		width: 100%;
		// height: 396px;
		background-image: url(https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/takeout/addressbg.png);
		background-size: 100% 100%;
    overflow: hidden;
    .infoMsg{
      height: 360px;
      overflow: auto;
      padding: 30px;
      font-size: 24px;
    }
    .tabAddress{
      width: 710px;
      height: 72px;
      background-color: #fff;
      margin: 0 auto;
      border-top-left-radius: 20px;
			border-top-right-radius: 20px;
      margin-top: 44px;
      display: flex;
      div {
        width: 50%;
        text-align: center;
        line-height: 72px;
        font-size: 28px;
        border-top-left-radius: 20px;
        border-top-right-radius: 20px;
        background-color: #EBFFF1;
      }
      .intab{
        height: 92px;
        background-color: #fff !important;
        font-weight: bold;
        position: relative;
        top: -25px;
        line-height: 122px;
      }
    }
    .notab{
      border-top-left-radius: 20px;
      border-top-right-radius: 20px;
      margin-top: 15px;
    }
		.address {
			width: 710px;
			margin: 0 auto;
			background-color: #fff;
			border-bottom-left-radius: 20px;
			border-bottom-right-radius: 20px;
			font-size: 28px;
			box-sizing: border-box;
			color: #191927;

      .formShop{
        width: 93%;
        margin: 0 auto;
        display: flex;
        font-size: 22px;
        .formShopLeft{
          width: 60%;
          .formShopAddress{
            font-size: 40px;
            font-family: PingFangSC-Medium;
            margin-top: 10px;
          }

          .formShopTimePhone{
            display: flex;
            margin-top: 16px;
            color: #999999;
            font-family: PingFangSC;
            justify-content: space-between;
            font-size: 28px;
            .formShopTime{
              width: 50%;
            }
            .formShopPhone{
              width: 50%;
              .van-cell{
                padding: 0;
              }
            }

            .formShopTimeShow{
              color: #333333;
            }
          }
          .formShopMsg{
            display: flex;
            height: 70px;
            line-height: 70px;
            ::v-deep .van-checkbox__icon .van-icon{
              width: 26px;
              height: 26px;
              line-height: 26px;
              margin-top: 6px;
              margin-right: 10px;
              font-size: 20px;
            }
            ::v-deep .van-checkbox__icon--checked .van-icon {
                color: #3DCF43;
                background-color: #fff;
                border-color: #3DCF43;
            }
          }
        }
        .formShopRight{
          width: 40%;
          height: 238px;
          background-image: url(https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/takeout/amapbj.png);
          background-size: 100% 100%;
          .formShopRightBox{
            width: 220px;
            margin: 0 auto;
            margin-top: 10px;
            .formShopRightDistance{
              min-width: 200px;
              height: 98px;
              background-image: url(https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/takeout/distance.png);
              background-size: 100% 100%;
              text-align: center;
              line-height: 80px;
              margin: 0 auto;
              font-family: PingFangSC-Medium;
              color: #333333;
            }
            .formShopRightShopImg{
              width: 130px;
              height: 130px;
              background-image: url(https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/takeout/shopimg.png);
              background-size: 100% 100%;
              margin: 0 auto;
              position: relative;
              top: -25px;
              text-align: center;
            }
          }
          img{
            width: 60px;
            height: 60px;
            margin-top: 25px;
          }
        }
      }

			.address-line {
				font-size: 36px;
        font-family: PingFangSC-Medium;
				box-sizing: border-box;
				border-bottom: 1px solid #f4f4f4;
				overflow: hidden;
        ::v-deep .van-icon {
          top: -3px;
        }
        .nullAdd{
          margin-top: 24px;
        }
				>div:nth-of-type(1) {
					margin-left: 30px;
					// margin-top: 24px;
					>span {
						margin-right: 16px;
					}
				}

				>div:nth-of-type(2) {
					color: #666;
					font-size: 29px;
					margin-left: 30px;
					margin-top: 6px;
					margin-bottom: 10px;

					>span {
						margin-right: 16px;
					}
				}

			}

			.address-line2 {
				color: #191927;
        font-family: PingFangSC;
				padding: 30px 26px 24px;
			}
		}

		.box {
			.title {
				position: absolute;
				top: 0;
				left: 0;
				z-index: 10;
				width: 100%;
				box-sizing: border-box;
				text-align: center;
				height: 124px;
				line-height: 104px;
				font-size: 32px;
				color: #333;
				border-radius: 20px 20px 0 0;
				background: #fff
			}

			.body {
				width: 100%;
				height: 600px;
				overflow-y: auto;
			}

			.close-icon {
				position: fixed;
				bottom: 625px;
				right: 40px;
				z-index: 10;
			}
      .disaddress {
        font-size:32px;
        color: #ff301e;
        margin-left: 40px;
        margin-bottom: 20px;
        padding: 20px 0;
      }
			.address-pop {
				margin: 4px 42px 20px 18px;
				.item {
					display: flex;
					justify-content: space-between;
					align-items: center;
					font-size: 30px;
					color: #000010;
					height: 111px;
          border-bottom: 1px solid #f4f4f4;
          margin-bottom: 20px;
					.item-left {
						display: flex;
						align-items: center;
						.item-detail {
							margin-left: 16px;
							.item-detail-address{
                font-size: 32px;
								color: #222;
								margin-bottom: 8px;
							}
							.item-detail-user {
								font-size: 26px;
								color: #999;
							}
						}
            .opacity6 {
              opacity: .6;
            }
					}

					.item-right {
						display: flex;
						align-items: center;
					}
				}
			}

			.newAddr {
				position: fixed;
				bottom: 0;
				left: 0;
				width: 100%;
				background-color: #fff;
				font-size: 30px;
				padding: 10px 0 9px;

				>div {
          width: 690px;
          height: 88px;
					margin: 0 auto;
          background-image: url('https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/user/address/address-btn.png?x-oss-process=image/resize,w_700/format,jpg/quality,q_80');
          background-size: 100%
				}
			}

			::v-deep .van-radio__icon .van-icon {
				border: 1px solid #5dcb4f;
			}
		}
    .overlay-wrapper {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 100%;
    }
    .overlay-img {
        width: 578px;
        height: 540px;
        background-image: url(https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/user/address/dis-addr-img.png);
        background-size: 100%;
        text-align: center;
        .tips {
          color: #222222;
          font-size: 34px;
          margin-bottom: 16px;
          margin-top: 293px;
        }
        .tips2 {
          font-size: 26px;
          color: #333;
          margin-bottom: 32px;
        }
        .btn {
          width: 160px;
          height: 80px;
          line-height: 80px;
          opacity: 1;
          background: linear-gradient(90deg,#40d243, #1fc432);
          border-radius: 8px;
          font-size: 28px;
          color: #fff;
          margin: auto;
        }
      }
    .timeTitle{
      width: 100%;
      height: 100px;
      line-height: 100px;
      text-align: center;
      font-family: PingFangSC;
      font-size: 34px;
      color: #333333;
    }
    .showTimeBox{
      width: 100%;
      height: 400px;
      overflow-y: auto;
    }
    .timeItemNo{
      width: 95%;
      height: 100px;
      line-height: 100px;
      margin: 0 auto;
      border-bottom: 1px solid #f4f4f4;
      color: #333;
      font-family: PingFangSC;
      font-size: 28px;
      display: flex;
      justify-content: space-between;
    }
    .timeItem{
      width: 95%;
      height: 100px;
      line-height: 100px;
      margin: 0 auto;
      border-bottom: 1px solid #f4f4f4;
      color: #FE9C16;
      font-family: PingFangSC;
      font-size: 28px;
      display: flex;
      justify-content: space-between;
    }
    .mapList {
			div {
				height: 150px;
				line-height: 150px;
				text-align: center;
        font-size: 28px;
			}

			div:not(:last-child) {
				border-bottom: 1px solid #EEEEEE;
				font-weight: bold;
			}
		}
    }
</style>
