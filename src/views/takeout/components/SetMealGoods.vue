<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-05-28 16:50:20
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-10-22 10:20:04
-->
<template>
  <div class="goods">
    <div class="box">
      <div>
        <img class="goods_img" :src="info.headPicture" alt="">
      </div>
      <div class="center">
        <div class="goods_name">
          <span>{{ info.setMealName }}</span>
          <span v-if="info.minNumberOfDiners!=info.maxNumberOfDiners">{{ info.minNumberOfDiners }}-{{ info.maxNumberOfDiners }}人餐</span>
          <span v-else>{{ info.maxNumberOfDiners }}人餐</span>
        </div>
        <div class="goods_time">
          <span>{{ info.setMealConfig.notAvailableWeekly===''?'周一至周天':formWeekly(info.setMealConfig.notAvailableWeekly) }}</span>
          <span v-if="info.setMealConfig.makeAnAppointment === 0">·免预约</span>
          <span v-else>·需预约</span>
        </div>
        <div class="goods_time goods_sold">
          <div>随时退 过期自动退</div>
          <div>x1</div>
          <!-- <div class="goods_sold_btn">
            <div>
              <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/shop/jian.png" alt="">
            </div>
            <div class="num">6</div>
            <div>
              <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/shop/jia.png" alt="">
            </div>
          </div> -->
        </div>
      </div>
    </div>
    <div class="goods_price">
      <span>￥{{ info.originalPrice }}</span>
      <span>小计</span>
      <span>￥</span>
      <span>{{ info.specialPrice }}</span>
    </div>
  </div>
</template>

<script>
export default {
  components: {},
  data() {
    return {
      info: this.$store.state.setMail.info
    }
  },
  created() {
  },
  mounted() {},
  methods: {
    // 格式化周几
    formWeekly(val) {
      let week = ['周一', '周二', '周三', '周四', '周五', '周六', '周天']
      let arr = val.split('、')
      let newarr = []
      for (let i = 0; i < week.length; i++) {
        for (let j = 0; j < arr.length; j++) {
          if (week[i] == arr[j]) {
            newarr.push(week[i])
          }
        }
      }
      let result = []
      for (let i = 0; i < week.length; i++) {
        let obj = week[i]
        let isExist = false
        for (let j = 0; j < newarr.length; j++) {
          let aj = newarr[j]
          if (aj == obj) {
            isExist = true
            break
          }
        }
        if (!isExist) {
          result.push(obj)
        }
      }
      const format1 = ['周一', '周二', '周三', '周四', '周五']
      const format2 = ['周一', '周二', '周三', '周四', '周五', '周六']
      if (result == format1) {
        return '周一至周五'
      } else if (result == format2) {
        return '周一至周六'
      } else {
        return result.join('、')
      }
    },
    goback() {
      this.$router.go(-1)
    }
  }
}
</script>

<style lang="scss" scoped>
	.goods {
		width: 706px;
		height: 248px;
		margin: 0 auto;
		background-color: #fff;
		border-radius: 10px;
		margin-top: 20px;
		overflow: hidden;
		.box{
			width: 93%;
			height: 120px;
			margin: 0 auto;
			display: flex;
			align-items: center;
			margin-top: 30px;
			.goods_img{
				width: 120px;
				height: 120px;
				border-radius: 8px;
				margin-right: 16px;
				float: left;
        object-fit: cover;
			}
			.goods_name{
				font-size: 32px;
				font-family: PingFangSC-Medium;
				font-weight: 500;
			}
			.center{
				width: 520px;
			}
			.goods_time{
				color: #999999;
				font-weight: 400;
				font-size: 26px;
			}
			.goods_sold{
				display: flex;
				justify-content: space-between;
				img{
					width: 36px;
					height: 36px;
					float: left;
				}
				.goods_sold_btn{
					display: flex;
					color: #222;
					.num{
						margin-left: 18px;
						margin-right: 18px;
					}
				}
			}
		}
		.goods_price{
			text-align: right;
			span:nth-child(1){
				font-size: 26px;
				color: #999999;
				text-decoration: line-through;
			}
			span:nth-child(2){
				font-size: 26px;
				color: #999999;
			}
			span:nth-child(3){
				font-size: 26px;
				color: #ff301e;
				font-weight: 500;
				font-family: PingFangSC-Medium;
			}
			span:nth-child(4){
				font-size: 42px;
				color: #ff301e;
				font-weight: 500;
				font-family: PingFangSC-Medium;
				margin-right: 25px;
			}
		}
	}
</style>
