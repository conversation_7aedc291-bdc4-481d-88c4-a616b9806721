<template>
  <!-- 商品信息 -->
  <div class="content">
    <div class="info-cell">
      <div class="cell-name">
        <span>{{ $store.state.market.marketData.marketName | ellipsis(15) }}</span>
      </div>
      <div v-for="(item, index) in cartlist" :key="index">
        <div v-show="show||index<3" class="cell-line">
          <div class="cell-line-left">
            <div class="cell-line-img">
              <img :src="item.cover" style="width: 100%;height: 100%;">
            </div>
            <div class="cell-line-goods">
              <div class="line-name">
                {{ item.goodsName | ellipsis(15) }}
              </div>
              <div class="line-tag">
                {{ item.difference }}
              </div>
              <div class="line-count">×<span>&nbsp;{{ badgeNum(item) }}</span></div>
            </div>
          </div>
          <div class="cell-line-right">
            <div class="price">
              <div class="small">¥</div>
              {{ item.price.price }}
            </div>
            <div v-if="item.price.price!=item.price.oriPrice&&item.price.price<item.price.oriPrice" class="oriprice">
              <div class="small">¥</div>
              {{ item.price.oriPrice }}
            </div>
          </div>
        </div>
      </div>

      <div v-if="moreListShow">
        <!-- 判断商品数量，大于三条才会显示 -->
        <div v-show="readmore" class="readmore">
          <span @click="goMore">展开更多</span>
          <van-icon name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/order/bottom.png" size="10px" />
        </div>
        <div v-show="!readmore" class="readmore">
          <span @click="goMore">点击收起</span>
          <van-icon name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/order/top.png" size="10px" />
        </div>
      </div>

      <div class="orderfree">
        <div v-if="getPostFree()>0" class="flex">
          <span>配送费</span>
          <div style="position: relative;top: -5px;">
            <div class="small">¥</div>
            {{ getPostFree() }}
          </div>
        </div>
        <div v-if="cartPackPrice!=0" class="flex">
          <span>包装费</span>
          <div style="position: relative;top: -5px;">
            <div class="small">¥</div>
            {{ cartPackPrice }}
          </div>
        </div>
        <div v-if="cartOriPrice>0" class="flex">
          <span>店铺商品优惠</span>
          <div style="position: relative;top: -5px;">
            <div class="small">- ¥</div>
            {{ cartOriPrice }}
          </div>
        </div>
        <div v-if="$store.state.market.marketData.type == 10&&couponList.length>0" class="flex" @click="getCoupon">
          <span>优惠券</span>
          <div>
            <div v-if="$store.state.market.marketData.coupon!=''" class="couponNum">- ¥</div>
            <div v-if="$store.state.market.marketData.coupon!=''" class="couponNum">{{ $store.state.market.marketData.coupon.usedAmount }}</div>
            <div class="couponNumRight" style="color:#333"><van-icon name="arrow" /></div>
          </div>
        </div>
        <div v-if="$store.state.market.marketData.type == 10&&activeList.length>0" class="flex">
          <span>满赠优惠</span>
          <div>
            <div v-for="item in activeList" :key="item.id" class="couponNumRight">
              <span style="margin-left:20px">{{ item.description }}</span>
            </div>
          </div>
        </div>

        <Coupon />
      </div>
      <div class="all">
        <div v-if="allDiscount>0" class="cutprice">
          <div>总优惠</div>
          <div>¥</div>
          <div>{{ allDiscount }}</div>
        </div>
        <div class="allprice">
          <div>合计</div>
          <div>¥</div>
          <div>{{ cartPrice }}</div>
        </div>
      </div>
    </div>
    <div class="bz" @click="goRemark">
      <span class="left">备注</span>
      <div class="right">
        <span v-if="$store.state.market.marketData.remark==''">口味、偏好等要求</span>
        <span v-else>{{ $store.state.market.marketData.remark | ellipsis(13) }}</span>
        <van-icon name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/common/arrowRGray.png" size="12px" />
      </div>
    </div>
    <div style="height: 160px" />

    <!-- 优惠券弹出 -->
    <van-popup v-model="couponShow" round closeable close-icon-position="top-right" position="bottom" :style="{ height: '70%' }">
      <div class="pop-title">
        <span>优惠券</span>
        <van-icon
          name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/shoppingMall/index/icon4.png"
          size="10"
          style="top:-4px"
        />
      </div>
      <div class="coupon-pop">
        <ul style="margin-top:59px">
          <div class="pop-packet">红包</div>
          <van-radio-group v-model="couponRadio" @change="setListCoupon">
            <li v-for="(item,index) in couponList" :key="index" class="pop-card">
              <div class="pop-card-top">
                <div class="pop-card-topLeft">
                  <img class="pop-card-img" src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/shoppingMall/index/shopmallavatar.png">
                  <div class="pop-card-shop">
                    <span>点滴严选店铺</span>
                    <div class="pop-card-deadline">有效期至{{ item.validEndTime }}</div>
                  </div>
                </div>
                <div class="pop-card-topRight">
                  <div class="pop-card-price">
                    <div class="pop-card-amount">
                      <span>¥</span>
                      <span>{{ item.usedAmount }}</span>
                    </div>
                    <div class="pop-card-limit">满{{ item.withAmount }}可用</div>
                  </div>
                  <van-radio :name="item.id" icon-size="15px" checked-color="#39cf3f" />
                </div>

                <div class="pop-card-tip">全品类券</div>
              </div>
              <div class="pop-card-bottom">仅限点滴严选商品使用</div>
            </li>
          </van-radio-group>
        </ul>
        <div class="noCoupon">
          <div class="noCouponBox" @click="setNoCoupon">
            不使用优惠券
          </div>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script>
import NP from 'number-precision'
import Maths from '@/utils/math.js'
import { getOnlineActivityGift } from '@/api/takeout'
import { orderCouList } from '@/api/shoppingMall'

import Coupon from './coupon.vue'
export default {
  components: { Coupon },
  data() {
    return {
      readmore: true,
      moreListShow: false,
      show: false,
      image1: 'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/order/goodsimg.png',
      temparr: [],
      couponShow: false,
      couponList: [],
      couponRadio: null,
      activeList: []
    }
  },
  computed: {
    cartlist() {
      let cart = this.$store.state.cart.cartData[0].goodsList
      let newcart = []
      const res = new Map()
      newcart = cart.filter(a => !res.has(a.skuId) && res.set(a.skuId, 1))
      return newcart
    },
    cartPrice() { // 总价
      if (this.$store.state.market.marketData.coupon == '') {
        let sum = 0
        sum = new Maths(this.$store.getters['cart/sumPrice'], this.getPostFree()).sum()
        sum = new Maths(sum, this.$store.getters['cart/sumPackPrice']).sum()
        return sum
      } else {
        let sum = 0
        sum = new Maths(this.$store.getters['cart/sumPrice'], this.getPostFree()).sum()
        sum = new Maths(sum, this.$store.getters['cart/sumPackPrice']).sum()
        // sum = new Maths(sum, this.$store.state.market.marketData.coupon.usedAmount).minus()
        sum = new Maths(sum, this.$store.state.market.marketData.coupon.preferentialAmount).minus()
        return sum
      }
    },
    cartOriPrice() { // 原价与售卖价差价优惠价
      let sum = 0
      sum = new Maths(this.$store.getters['cart/cartSumOriPrice'], this.$store.getters['cart/sumPrice']).minus()
      return sum
    },
    // 总优惠:原价与售卖价差价+优惠券优惠
    allDiscount() {
      if (this.$store.state.market.marketData.coupon == '') {
        let sum = 0
        sum = new Maths(this.$store.getters['cart/cartSumOriPrice'], this.$store.getters['cart/sumPrice']).minus()
        return sum
      } else {
        let sum = 0
        sum = new Maths(this.$store.getters['cart/cartSumOriPrice'], this.$store.getters['cart/sumPrice']).minus()
        sum = new Maths(sum, this.$store.state.market.marketData.coupon.preferentialAmount).sum()
        return sum
      }
    },
    cartPackPrice() { // 包装费
      return this.$store.getters['cart/sumPackPrice']
    }
  },
  created() {
    if (this.$store.state.market.marketData.type == 10) {
      this.getOnlineActivityGift()
      this.orderCouList()
    }
  },
  mounted() {
    this.goodsListShow()
  },
  methods: {
    // 选择优惠券弹出
    getCoupon() {
      this.couponShow = true
    },
    // 确认选择
    setListCoupon(val) {
      this.couponList.map(item => {
        if (item.id == val) {
          this.$store.state.market.marketData.coupon = item
        }
      })
      this.couponShow = false
    },
    // 查询当天用户符合的线上满赠活动
    getOnlineActivityGift() {
      let data = {
        'userId': this.$store.getters.getUserId,
        'regionId': this.$store.getters.getRegionId,
        'orderAmount': this.$store.getters['cart/sumPrice'],
        'queryType': 2
      }
      getOnlineActivityGift(data).then(res => {
        if (res.status == 200) {
          this.activeList = res.data
        }
      })
    },
    // 查询本单可用优惠券
    orderCouList() {
      let data = {
        'userId': this.$store.getters.getUserId,
        'marketId': this.$store.state.market.marketData.marketId,
        'orderAmount': this.$store.getters['cart/sumPrice']
      }
      orderCouList(data).then(res => {
        if (res.status == 200) {
          if (res.data.length > 0) {
            this.couponList = res.data
            this.$store.state.market.marketData.coupon = res.data[0]
            this.couponRadio = res.data[0].id
          } else {
            this.$store.state.market.marketData.coupon = ''
          }
        }
      })
    },
    // 去掉优惠券
    setNoCoupon() {
      this.$store.state.market.marketData.coupon = []
      this.couponRadio = null
    },
    // 计算运费
    getPostFree() {
      let data = this.$store.state.market.marketData.postFee

      if (data == '') {
        return 0
      } else if (this.$store.state.market.marketData.agentPostFee > 0) {
        if (this.$store.state.market.marketData.agentPostFee < data) {
          return NP.minus(Number(data), Number(this.$store.state.market.marketData.agentPostFee))
        }
        if (this.$store.state.market.marketData.agentPostFee >= data) {
          return 0
        }
      } else {
        return data
      }
    },
    goRemark() {
      this.$router.push({ path: '/remarks' })
    },
    // 检查商品选中数量
    badgeNum(item) {
      let cart = this.$store.state.cart.cartData[0].goodsList
      let n = 0
      if (cart) {
        for (let i in cart) {
          if (cart[i].skuId == item.skuId) {
            n++
          }
        }
      }
      return n
    },
    goodsListShow() {
      // 通过接口获取订单数据，对订单中的商品进行判断，如果大于三个
      if (this.cartlist.length > 3) {
        this.moreListShow = true
      }
    },
    goMore() {
      this.readmore = !this.readmore
      this.show = !this.show
    }
  }
}
</script>

<style lang="scss" scoped>
	.content {
		.info-cell {
			width: 710px;
			margin: 0 auto;
			background-color: #fff;
			border-radius: 20px;
			margin-bottom: 12px;
			margin-top: 12px;
			padding: 0 24px;
			box-sizing: border-box;
			.cell-name {
				height: 93px;
				line-height: 93px;
				color: #2c2c2c;
				font-size: 32px;
				margin-bottom: 10px;
        font-family: PingFangSC-Medium;
				>span {
					margin-right: 20px;
				}
			}

			.cell-line {
				display: flex;
				justify-content: space-between;
				height: 150px;
				font-size: 30px;
				color: #000010;

				.cell-line-left {
					display: flex;
					justify-content: space-between;
					padding-top: 20px;

					.cell-line-img {
						width: 100px;
						height: 104px;
						border-radius: 8px;
						overflow: hidden;
						margin-right: 16px;
					}

					.cell-line-goods {
						color: #000010;
            font-family: PingFangSC-Medium;

						.line-name {
							font-size: 30px;
						}

						.line-tag {
							font-size: 22px;
							opacity: 0.47;
              font-family: PingFangSC;
						}

						.line-count {
							font-size:24px;
							color: #999999;
              font-family: PingFangSC;
						}
					}
				}
			}

			.cell-line-right {
				text-align: right;
				padding-top: 20px;

				.price {
					display: flex;
					justify-content: flex-end;
					color: #000000;
					font-size: 32px;
          font-family: PingFangSC-Medium;
				}

				.oriprice {
					display: flex;
					justify-content: flex-end;
					font-size:24px;
					color: #b6b4b4;
					text-decoration: line-through;
          font-family: PingFangSC;
				}
			}

			.readmore {
				display: flex;
				align-items: center;
				justify-content: center;
				font-size: 26px;
				font-family: PingFang SC;
				height:40px;
				color: #000010;
				opacity: 0.8;

				>span {
					margin-right: 6px;
				}
			}

			.orderfree {
				font-size: 26px;
				color: #000010;
				border-bottom: 1px solid #efefef;
				padding-top: 28px;
        padding-bottom: 20px;

				>.flex {
					padding: 12px 0;
					display: flex;
					justify-content: space-between;
          font-family: PingFangSC;

					>div {
						display: flex;
						justify-content: space-between;
						font-weight: bold;
					}
				}
			}

			.all {
				display: flex;
				justify-content: flex-end;
				align-items: center;
				height: 100px;
				font-size: 26px;
				color: #000000;
				.allprice {
					display: flex;
					align-items: center;
					margin-left: 32px;
					color: #42424d;
					div:nth-of-type(1) {
						margin-right: 8px;
					}
					div:nth-of-type(3) {
						font-size: 42px;
						margin-left: 8px;
						color: #010101;
            font-family: PingFangSC-Medium;
            margin-top: -5px;
					}
				}

				.cutprice {
					display: flex;
					align-items: center;
					div:nth-of-type(1) {
						margin-right: 8px;
            font-family: PingFangSC;
					}
          div:nth-of-type(2) {
            font-size: 22px;
            margin-right: 3px;
            font-family: PingFangSC-Medium;
            color: #fb4c58;
					}
          div:nth-of-type(3) {
            font-size: 30px;
            font-family: PingFangSC-Medium;
            color: #fb4c58;
            margin-top: -3px;
					}
				}
			}
		}

    .couponNum{
     color: #ff301e;
    }
    .couponNumRight{
       position: relative;
       top: 3px;
       left: 3px;
      color: #ff301e;
     }

		.bz {
			display: flex;
			align-items: center;
			justify-content: space-between;
			width: 710px;
			margin: 0 auto;
			height: 104px;
			line-height: 104px;
			background: #fff;
			font-size: 28px;
			border-radius: 20px;
			color: #000010;
			box-sizing: border-box;
			padding: 0 24px;
      font-family: PingFangSC;

			.left {
				color: #33333f;
			}

			.right {
				color: #7f7f87;
				::v-deep .van-icon {
					top: 5px;
				}
				>span {
					margin-right: 14px;
				}
			}
		}
	}

   .pop-title {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 117px;
            background-color: #f5f5f5;
            font-size: 34px;
            font-family:PingFangSC-Medium;
            font-weight: 500;
            color: #333333;
            line-height: 48px;
            text-align: center;
            padding-top: 40px;
            z-index: 1;
        }
    .coupon-pop {
        background-color: #f5f5f5;
        padding: 0 22px;
        height: 100%;
        overflow: scroll;
        .noCoupon{
          width: 95%;
          height: 80px;
          position: fixed;
          bottom: 35px;
          .noCouponBox{
            width: 80%;
            height: 80px;
            background-color: #eee;
            border: 1px solid #ededed;
            text-align: center;
            line-height: 80px;
            font-size: 30px;
            margin: 0 auto;
            border-radius: 8px;
          }
        }
        .pop-packet {
            font-size: 34px;
            font-family:PingFangSC-Medium;
            font-weight: 500;
            color: #333333;
            margin-left: 4px;
            padding-bottom: 8px;
        }
        .pop-card {
            width: 706px;
            height: 222px;
            background-color: #fff;
            padding-left: 22px;
            padding-right: 22px;
            // padding-top: 14px;
            box-sizing: border-box;
            margin-bottom: 16px;
            border-radius: 16px;
            .pop-card-top {
              position: relative;
              display: flex;
              align-items: center;
              justify-content: space-between;
              height: 148px;
              padding-top: 25px;
                .pop-card-topLeft,.pop-card-topRight {
                    display: flex;
                    align-items: center;
                }
              .pop-card-img {
                width: 70px;
                height: 70px;
                margin-right: 16px;
              }
              .pop-card-shop {
                  width: 360px;
                  font-size: 30px;
                  font-family:PingFangSC-Medium;
                  font-weight: 500;
                  color: #222222;

                  .pop-card-deadline{
                      font-size: 22px;
                      font-family: PingFangSC;
                      color: #666;
                      margin-top: 4px;
                  }
              }
              .pop-card-price {
                margin-right: 20px;
                .pop-card-amount {
                  font-size: 64px;
                  font-family: PingFangSC-Medium;
                  font-weight: 500;
                  color: #ff301e;
                  text-align: right;
                  >span:nth-of-type(1){
                    font-size: 26px;
                  }
                }
                .pop-card-limit {
                  font-size: 20px;
                  font-family: PingFangSC;
                  color: #666666;
                  margin-top: -15px;
                  transform: scale(.9);
                }
              }
              .pop-card-tip {
                  position: absolute;
                  left: 0;
                  top: 0;
                  width: 124px;
                  height: 36px;
                  background-image: url(https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/shoppingMall/index/tip3.png);
                  background-size: 100%;
                  font-size: 22px;
                  font-family:PingFangSC-Medium;
                  font-weight: 500;
                  color: #ffffff;
                  text-align: center;
              }
            }
            .pop-card-bottom {
              height: 73px;
              line-height: 73px;
              font-size: 20px;
              font-family: PingFangSC;
              color: #999999;
              border-top: 1px dashed #ededed;
            }
            // .will {
            //     background: linear-gradient(90deg,#ff1e29, #ff5a25);
            // }
            // .already {
            //     background: #d8d8d8;
            // }
        }
        .pop-card:last-child {
            margin-bottom: 34px;
        }
    }
    ::v-deep .van-popup__close-icon--top-right {
        top: 42px;
    }
    ::v-deep .van-popup__close-icon {
        color: #333;
    }

	.small {
		transform: scale(0.78);
		font-weight: normal;
	}

	.red {
		color: #fb4c58;
		font-size: 30px;
    font-family:PingFangSC-Medium;
    // font-weight: bold;
	}
</style>
