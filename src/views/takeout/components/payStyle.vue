<template>
  <div class="content">
    <div class="refund-left">
      <span>支付方式</span>
    </div>
    <div class="refund-right" @click="selPayStyle">
      <span>{{ paytips }}</span>
      <van-icon class="right_icon" name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/order/right.png" size="11px" />
    </div>

    <!-- 弹起支付方式 -->
    <van-popup
      v-model="show"
      round
      closeable
      position="bottom"
      :style="{
        width: '100%',
        maxHeight:'320px',
        minHeight:'220px',
      }"
    >
      <div class="box">
        <div class="title">选择支付方式</div>
        <van-radio-group v-model="radio" @change="payStatus">
          <div class="payment">

            <div v-for="(item,index) in paydata" :key="index" class="payment-line">
              <div class="line-left">
                <van-icon v-if="item.payStyle === 1||item.payStyle === 7" name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/pay/balance.png" size="23px" />
                <van-icon v-if="item.payStyle === 3" name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/pay/weixinpay.png" size="23px" />
                <van-icon v-if="item.payStyle === 2" name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/pay/alipay.png" size="23px" />
                <van-icon v-if="item.payStyle === 4" name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/pay/dragon.png" size="23px" />
                <van-icon v-if="item.payStyle === 9" name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/shop/goods/unipay.png" size="23px" />
                <span>{{ item.payStyleName==='二类户余额'?'余额':item.payStyleName }}</span>
              </div>
              <div class="line-right">
                <span v-if="item.payStyle === 1||item.payStyle === 7">￥ {{ balance }}</span>
                <van-radio :name="item.payStyle" icon-size="19px" checked-color="#5dcb4f" />
              </div>
            </div>

          </div>
        </van-radio-group>
      </div>
    </van-popup>
  </div>
</template>

<script>
import {
  getUserAccount
} from '@/api/takeout'
import {
  getPayStyle
} from '@/api/pay'
import {
  UserBalance
} from '@/api/my'
import { fundAccountQuery } from '@/api/bank'
export default {
  components: {},
  data() {
    return {
      paytips: '请选择支付方式',
      show: false,
      radio: '',
      balance: 0,
      sysPayList: '',
      paydata: '',
      payChannel: null
    }
  },
  watch: {
    radio(val, old) {
      if (val == 1) {
        this.paytips = '余额 ￥' + this.balance
        this.show = false
      } else if (val == 2) {
        this.paytips = '支付宝支付'
        this.show = false
      } else if (val == 3) {
        this.paytips = '微信支付'
        this.show = false
      } else if (val == 4) {
        this.paytips = '龙支付'
        this.show = false
      } else if (val == 7) { // 银联支付
        this.paytips = '余额 ￥' + this.balance
        this.show = false
      } else if (val == 9) { // 云闪付
        this.paytips = '云闪付'
        this.show = false
      }
    }
  },
  created() {
    this.getUserAccount()
    this.getPayStyle()
  },
  methods: {
    // 获取账户余额
    getUserAccount() {
      // poolId资金池id   现在为大区id
      getUserAccount(this.$store.state.market.marketData.marketSn).then(res => {
        if (res.status == 200) {
          this.payChannel = res.data.payChannel
          this.balance = res.data.balance == null ? 0 : res.data.balance
          this.$store.state.market.marketData.balance = res.data.balance == null ? 0 : res.data.balance

          // 转到确认订单按钮后验证
          // if (res.data.payChannel == 2) {
          //   // 检查是否开户成功
          //   this.fundAccountQuery()
          // }
        } else {
          this.$toast(res.message)
        }
      })
    },
    getBlance() {
      let data = this.$store.getters.getUserId
      UserBalance(data).then((res) => {
        if (res.status == 200) {
          if (res.data.length > 0) {
            this.getUserAccount()
          }
        } else {
          this.$toast(res.message)
        }
      })
    },
    // 选择支付方式
    payStatus(name) {
      this.$store.state.market.marketData.payradio = name
      if (this.balance != 0 && name == 1) {
        this.paytips = '余额 ￥' + this.balance
      }
    },
    selPayStyle() {
      if (this.paydata.length === 0) {
        this.$toast('暂无可用支付方式')
        return
      }
      this.show = true
    },
    // 获取支付方式
    getPayStyle() {
      getPayStyle({
        marketId: this.$store.state.market.marketData.marketId,
        orderType: this.$store.state.market.marketData.type == 10 ? 12 : 7,
        poolId: this.$store.getters.getRegionId
      }).then(res => {
        if (res.status == 200) {
          this.paydata = res.data
          if (this.paydata.length) {
            // 1余额，2支付宝，3微信，4龙支付
            if (this.paydata[0].payStyle == 2 || this.paydata[1].payStyle == 2 || this.paydata[2].payStyle == 2 || this.paydata[3].payStyle == 2) {
              this.radio = 2
              this.paytips = '支付宝支付'
              this.$store.state.market.marketData.payradio = '2'
            } else if (this.paydata[0].payStyle == 3 || this.paydata[1].payStyle == 3 || this.paydata[2].payStyle == 3 || this.paydata[3].payStyle == 3) {
              this.radio = 3
              this.paytips = '微信支付'
              this.$store.state.market.marketData.payradio = '3'
            } else if (this.paydata[0].payStyle == 1 || this.paydata[1].payStyle == 1 || this.paydata[2].payStyle == 1 || this.paydata[3].payStyle == 1) {
              if (this.payChannel == 2) {
                this.radio = 7
                this.$store.state.market.marketData.payradio = '7'
              } else {
                this.radio = 1
                this.$store.state.market.marketData.payradio = '1'
              }

              this.paytips = '余额 ￥' + this.balance
            } else if (this.paydata[0].payStyle == 4 || this.paydata[1].payStyle == 4 || this.paydata[2].payStyle == 4 || this.paydata[3].payStyle == 4) {
              this.radio = 4
              this.paytips = '龙支付'
              this.$store.state.market.marketData.payradio = '4'
            }
          }
          console.log(res.data)
        }
      })
    },
    // 检查开户
    fundAccountQuery() {
      let queryVO = {
        'bankType': 'UnionPay',
        'userId': this.$store.getters.getUserId
      }
      fundAccountQuery(queryVO).then(res => {
        if (res.data != null) {
          if (res.data.status != 2) {
            this.$dialog.alert({
              message: '请先完成开户流程！'
            }).then(() => {
              this.$router.push('/wallet')
            })
          }
        } else {
          this.$dialog.alert({
            message: '请先完成开户流程！'
          }).then(() => {
            this.$router.push('/wallet')
          })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
	.content {
		display: flex;
		justify-content: space-between;
		align-items: center;
		width: 710px;
		height: 104px;
		margin: 0 auto;
		background-color: #fff;
		border-radius: 20px;
		font-size: 30px;
		padding: 0 24px;
		box-sizing: border-box;
		color: #2c2c2c;
    margin-top: 12px;
    font-family: PingFangSC;
    .alipaybox{
      height: 200px;
      background-color: #fff;
    }
		.refund-left {
			color: #4c4c57;
		}

		.refund-right {
			color: #000010;

			>span {
				margin-right: 12px;
			}
			.van-icon {
				top: 1px;
			}
		}

		.box {
			.title {
				width: 100%;
				box-sizing: border-box;
				text-align: center;
				height: 125px;
				line-height: 125px;
				font-size: 32px;
				color: #333;
			}
		}
		.payment {
			margin: 0 40px  20px;
			.payment-line {
				display: flex;
				justify-content: space-between;
				align-items: center;
				font-size: 32px;
				color: #000010;
				height: 102px;
				line-height: 102px;
				.line-left {
					display: flex;
					align-items: center;
					>span {
						margin-left: 28px;
					}
          .alipay{
            margin-left: 24px;
            margin-top: 70px;
            .alipay_name{
              width: 200px;
              height: 40px;
            }
            .alipay_tj{
              width: 70px;
              height: 30px;
              font-size: 18px;
              text-align: center;
              line-height: 30px;
              color: #fff;
              border-radius: 8px;
              background-color: #ff976a;
              margin-top: -30px;
              position: relative;
              left: 150px;
            }
          }
          .alipay_tag{
            margin-top: 5px;
          }
          .alipay_tag2{
            margin-left: 15px;
          }
          .alipay_tips{
            width: 550px;
            height: 35px;
            line-height: 35px;
            border-top: 1px solid #eee;
            font-size: 25px;
            color: #999;
            padding-top: 5px;
            margin-top: -10px;
          }
				}
				.line-right {
					display: flex;
					align-items: center;
					>span {
						margin-right: 85px;
            color: #999;
					}
				}
				::v-deep .van-radio__icon .van-icon {
					border: 1.5px solid #5dcb4f;
				}
			}
		}
	}

	::v-deep .van-popup__close-icon {
		color: #000;
	}
</style>
