<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2022-09-20 13:56:27
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-11-24 11:05:54
-->
<template>
  <!-- 提交订单 -->
  <div class="content">
    <NavHeight bgc="#fff" />
    <SetMealTop />

    <SetMealGoods />

    <div style="height: 20px;" />
    <!-- 支付方式 -->
    <SetMealPayStyle />

    <setMealCart />

  </div>
</template>

<script>
import { SetMealTop, SetMealGoods, setMealCart } from './components'
import SetMealPayStyle from './components/setMealPayStyle'
import { checkInitPwd } from '@/api/takeout'
export default {
  components: {
    SetMealTop,
    SetMealGoods,
    SetMealPayStyle,
    setMealCart
  },
  data() {
    return {
    }
  },
  created() {
    document.body.scrollTop = 0
    document.documentElement.scrollTop = 0
    this.$store.state.market.marketData.marketSn = this.$route.query.marketSn
  },
  destroyed() {
    // 清空支付选择缓存
    this.$store.state.market.marketData.payradio = ''
  },
  methods: {
    checkInitPwd() { // 检查初始支付密码
      checkInitPwd(this.$store.getters.getUserId).then(res => {
        if (res.status == 200) {
          if (res.data == true) {
            this.$dialog.alert({
              message: '当前为初始支付密码，请尽快修改'
            }).then(() => {
              this.$router.push('/editPayPwd')
            })
          }
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
