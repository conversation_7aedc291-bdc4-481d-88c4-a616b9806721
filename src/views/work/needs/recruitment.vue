<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-04-02 10:08:31
 * @LastEditors: 孙立政
 * @LastEditTime: 2021-12-23 17:28:54
-->
<template>
  <div class="content">
    <NavHeight bgc="linear-gradient(45deg, #71d774 0%, #5ecc52 100%)" />
    <Top />
    <div class="wrap">
      <div class="cell-set">
        <div class="set-line">
          <van-field
            label="需求类型"
            placeholder="我要求职"
            input-align="right"
            readonly
          />
        </div>
        <div class="set-line">
          <van-field
            v-model="recru.jobType"
            label="职业类型"
            placeholder="请选择职业"
            input-align="right"
            right-icon="arrow"
            maxlength="20"
            readonly
            @click-input="careerChange()"
          />
        </div>
        <div class="set-line">
          <van-field
            v-model="resumeTips"
            label="简历"
            placeholder="请选择简历"
            input-align="right"
            right-icon="arrow"
            readonly
          />
          <input
            ref="input"
            type="file"
            name="upload_file"
            class="upload"
            @change="resumeChange()"
          >
        </div>
        <div class="set-line">
          <van-field
            v-model="recru.orderAddress"
            label="工作地点"
            placeholder="请输入工作地点"
            input-align="right"
            right-icon="arrow"
            maxlength="40"
          />
        </div>
        <div class="set-line">
          <van-field
            v-model="recru.salaryUnit"
            label="薪水单位"
            placeholder="请选择薪水单位"
            input-align="right"
            right-icon="arrow"
            readonly
            @click-input="salaryUnitChange()"
          />
        </div>
        <div class="set-line1">
          <div class="money">
            <span>薪水</span>
            <div class="set-line-right">
              <input
                v-model="recru.minPrice"
                type="text"
                placeholder="最低金额"
                maxlength="6"
                onkeyup="this.value=this.value.replace(/\D/g,'')"
                @blur="minChange"
              >
              <div class="line" />
              <input
                v-model="recru.maxPrice"
                type="text"
                placeholder="最高金额"
                maxlength="6"
                onkeyup="this.value=this.value.replace(/\D/g,'')"
                @blur="maxChange"
              >
              元
            </div>
          </div>
          <div class="tips">
            最低薪水不低于1600元/月、15元/时
          </div>
        </div>

        <div class="set-line area">
          <van-field
            v-model="recru.detailDesc"
            rows="8"
            autosize
            label=""
            type="textarea"
            maxlength="200"
            placeholder="如有其他需求，详细描述你想要的服务（至少10个字）"
            show-word-limit
          />
        </div>
      </div>
      <div class="btn">
        <van-button type="primary" @click="goNext">立即发布</van-button>
      </div>
    </div>
    <!-- 单位选择 -->
    <van-popup v-model="salaryUnitshow" position="bottom" :style="{}">
      <van-picker
        title=""
        show-toolbar
        :columns="columns"
        @confirm="salaryUnitConfirm"
        @cancel="salaryUnitCancel"
      />
    </van-popup>

    <!-- 职业选择 -->
    <van-popup v-model="careershow" position="bottom" :style="{}">
      <van-picker
        title=""
        show-toolbar
        :columns="columns1"
        @confirm="careerConfirm"
        @cancel="careerCancel"
      />
    </van-popup>
  </div>
</template>

<script>
import OSS from 'ali-oss'
import Top from './components/top'
import { postRecru, getUnit, getCareerType } from '@/api/work'
// import { upload } from '@/api/my'
export default {
  components: {
    Top
  },
  data() {
    return {
      recru: {
        jobType: '',
        orderAddress: '',
        detailDesc: '',
        salaryUnit: '',
        minPrice: '',
        maxPrice: '',
        resume: ''
      },
      resumeTips: '',
      salaryUnitshow: false,
      careershow: false,
      currentTime1: new Date(),
      minDate: new Date(),
      maxDate: new Date(2025, 10, 1),
      columns: [],
      columns1: [],
      typeData: []
    }
  },
  created() {
    this.getUnit1()
    this.getCareerType()
  },
  mounted() {},
  methods: {
    goNext() {
      if (this.recru.jobType == '') {
        this.$toast('请选择职业')
        return false
      }
      if (this.recru.resume == '') {
        this.$toast('请上传简历')
        return false
      }
      if (this.recru.orderAddress == '') {
        this.$toast('请输入工作地点')
        return false
      }
      if (this.recru.salaryUnit == '') {
        this.$toast('请选择薪水单位')
        return false
      }
      if (this.recru.minPrice == '' || this.recru.maxPrice == '') {
        this.$toast('薪水范围请填写完整')
        return false
      }
      var reg = /^\d+/
      if (reg.test(this.recru.minPrice) == '' || reg.test(this.recru.maxPrice) == '') {
        this.$toast('薪水范围请输入数字')
        return false
      }
      if (this.recru.detailDesc == '' || this.recru.detailDesc.length < 10) {
        this.$toast('详细信息最少输入10个字')
        return false
      }
      const isEmoji = str => /(\ud83c[\udf00-\udfff])|(\ud83d[\udc00-\ude4f\ude80-\udeff])|[\u2600-\u2B55]/g.test(str)
      if (isEmoji(this.recru.detailDesc)) {
        this.$toast('请勿输入表情')
        return false
      }
      this.okPress()
    },
    getUnit1() {
      let data = {
        categoryName: '招聘信息'
      }
      getUnit(data)
        .then((res) => {
          if (res.status == 200) {
            this.columns = res.data.content.split(',')
          }
        })
    },
    getCareerType() {
      getCareerType().then((res) => {
        this.typeData = res.data
        let newData1 = []
        this.typeData.map((item, index) => {
          if (item.parentId == 0) {
            item.children = []
            item.text = item.name
            newData1.push(item)
          }
        })
        this.typeData.map((item, index) => {
          newData1.map((item1, index1) => {
            if (item1.id == item.parentId) {
              item.text = item.name
              newData1[index1].children.push(item)
            }
          })
        })
        this.columns1 = newData1
      })
    },
    salaryUnitChange() {
      this.salaryUnitshow = true
    },
    salaryUnitConfirm(e) {
      console.log(e)
      this.recru.salaryUnit = e
      this.recru.minPrice = ''
      this.recru.maxPrice = ''
      this.salaryUnitshow = false
    },
    salaryUnitCancel() {
      this.salaryUnitshow = false
    },
    careerChange() {
      this.careershow = true
    },
    careerConfirm(e) {
      this.recru.jobType = e.join('-')
      this.careershow = false
    },
    careerCancel() {
      this.careershow = false
    },
    resumeChange() {
      var files = this.$refs.input.files
      this.uploadFile(files[0])
    },
    uploadFile(file) {
      let self = this
      this.$toast.loading({
        duration: 0, // 持续展示 toast
        forbidClick: true,
        message: '上传中'
      })
      var formData = new FormData()
      // 添加文件流
      formData.append('file', file)

      let client = new OSS({
        region: 'oss-cn-hangzhou',
        accessKeyId: 'LTAI4GHzemXAwKszXKiniT9s',
        accessKeySecret: '******************************',
        bucket: 'diandi-video'
      })
      let now = Date.parse(new Date())
      let names = now + file.name
      client.put('files/' + names, file)
        .then(function(res) {
          console.log(res.url)
          self.$toast('上传成功')
          self.resumeTips = '已上传'
          self.recru.resume = res.url
        })

      // upload(formData)
      //   .then((res) => {
      //     console.log(res.data)
      //     self.$toast.clear()
      //     if (res.status == 200) {
      //       self.$toast('上传成功')
      //       self.resumeTips = '已上传'
      //       self.recru.resume = res.data
      //     }
      //   })
    },
    minChange() {
      if (this.recru.salaryUnit == '') {
        this.$toast('请先选择薪水单位')
        this.recru.minPrice = ''
        return false
      }
      if (this.recru.salaryUnit == '月') {
        if (this.recru.minPrice < 1600) {
          this.$toast('最低薪水不低于1600元/月')
          this.recru.minPrice = ''
        }
      }
      if (this.recru.salaryUnit == '时') {
        if (this.recru.minPrice < 15) {
          this.$toast('最低薪水不低于15/时')
          this.recru.minPrice = ''
        }
      }
      if (
        this.recru.minPrice &&
        this.recru.maxPrice &&
        Number(this.recru.minPrice) > Number(this.recru.maxPrice)
      ) {
        this.$toast('最低薪水不得高于最高薪水')
        return false
      }
    },
    maxChange() {
      if (this.recru.salaryUnit == '') {
        this.$toast('请先选择薪水单位')
        this.recru.maxPrice = ''
        return false
      }

      if (
        this.recru.minPrice &&
        this.recru.maxPrice &&
        Number(this.recru.minPrice) > Number(this.recru.maxPrice)
      ) {
        this.$toast('最低薪水不得高于最高薪水')
        return false
      }
    },
    okPress() {
      let self = this
      this.$dialog
        .confirm({
          title: '确认要发布该需求？',
          message:
            '请确认您的发布不含色情违法、广告营销等垃圾信息，如多次发布垃圾信息，将会被封号处理哦',
          confirmButtonColor: '#6095F0'
        })
        .then(() => {
          self.recru.userId = self.$store.getters.getUserId
          self.recru.userName = localStorage.getItem('username')
          self.recru.telephone = localStorage.getItem('phone')
          self.recru.typeName = '我要求职'
          postRecru(self.recru)
            .then((res) => {
              if (res.status == 200) {
                self.$toast(res.message)
                // this.$router.push({
                //   path: '/work/appointment/myOrder'
                // })
                self.$router.push({
                  name: 'ExternalOrder',
                  query: {
                    type: 2
                  }
                })
              } else {
                self.$toast(res.message)
              }
            })
        })
    }
  }
}
</script>

<style scoped lang="scss">
.content::before {
  // 利用伪元素设置整个页面的背景色
  content: " ";
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: -100;
  min-height: 100%;
  background-color: #fff;
}
.content {
  .wrap {
    font-size: 28px;
    color: #5e5e5e;
    background-color: #fff;
    .cell-set .set-line:not(:last-child) {
      border-bottom: 1px solid #e8e9ea;
    }
    .cell-set:nth-child(2) .set-line:last-child {
      border-bottom: 1px solid #e8e9ea;
    }
    ::v-deep .van-cell__value--alone {
      background: #faf9f9;
      height: 200px;
      border-radius:16px;
      padding: 26px;
    }
    ::v-deep .van-field__control {
      box-sizing: border-box;
    }
    .cell-set {
      padding: 0 20px;
      background-color: #fff;

      .set-line {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 112px;
        ::v-deep .van-cell {
          padding-left: 0;
          padding-right: 0;
        }
      }
      .set-line1 {
        border-bottom: 1px solid #e8e9ea;
        .tips {
          font-size: 26px;
          color: #b2b2b7;
          margin-bottom: 15px;
        }
        .money {
          display: flex;
          align-items: center;
          justify-content: space-between;
          height: 112px;
          background-color: #fff;
        }
        .set-line-right {
          display: flex;
          align-items: center;
        }
        .line {
          width: 34px;
          height: 1px;
          background-color: #f6f6f6;
          margin: 0 15px;
        }
        input {
          width: 132px;
          height: 54px;
          background-color: #f6f6f6;
          border: none;
          border-radius: 28px;
          padding-left: 20px;
          padding-right: 10px;
        }
        input::placeholder {
          color: #c8c9cc;
        }
      }
      .area {
        margin-top: 48px;
        min-height: 430px;
      }
      ::v-deep .van-cell__value--alone {
        height: 430px;
      }
    }
    .btn {
      text-align: center;
      margin-top: 150px;
      padding-bottom: 50px;
    }
    .van-cell::after {
      border: none;
    }
    // 按钮
    ::v-deep .van-button--primary {
      width: 512px;
      border: 0;
      background: linear-gradient(45deg, #71d774 0%, #5ecc52 100%);
    }
    ::v-deep .van-button--normal {
      font-size: 32px;
      border-radius: 12px;
    }
    ::v-deep .van-radio--horizontal {
      margin-left: 52px;
    }
  }
  .upload {
    position: absolute;
    top: 0;
    right: 0;
    height: 112px;
    opacity: 0;
  }
}
</style>
