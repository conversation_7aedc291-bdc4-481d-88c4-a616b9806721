<template>
  <!-- 头部 -->
  <div class="content">
    <!-- 状态栏高度 -->
    <!-- <div class="status" :style="statusVar" /> -->
    <!-- <div class="statusOccupancy" :style="statusVar" /> -->
    <!-- 导航栏高度 -->
    <ul
      class="top-wrap  top-fixed"
      :style="styleVar"
    >
      <!-- 左侧 -->
      <li class="left">
        <van-icon
          class="fast"
          name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/work/back_white.png"
          size="15"
          @click="goback"
        />
      </li>
      <!-- 中间 -->
      <li class="center">
        <!-- 标题拦 -->
        <div
          class="title"
        >
          发布需求
        </div>
      </li>
      <!-- 右侧 -->
      <ul class="right" />
    </ul>
    <!-- 头部固定定位占位高度 -->
    <div class="headOccupancy" :style="styleVar" />
  </div>
</template>

<script>
export default {
  name: 'Mytop',
  props: {
    // 头部高度
    height: {
      type: Number,
      default: 1.14
    },
    statusColor: {
      type: String,
      default: '#fff'
    },
    // 导航栏字体颜色
    color: {
      type: String,
      default: '#fff'
    },
    // 背景颜色
    background: {
      type: String,
      default: 'green'
    },

    // 是否固定在顶部
    isFixed: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      keyword: ''
    }
  },
  computed: {
    statusVar() {
      return {
        '--top-status-height': this.$store.getters.getStatusHeight + 'px',
        '--top-status-color': this.statusColor
      }
    },
    styleVar() {
      console.log(this.$store.getters.getStatusHeight)

      return {
        '--top-height': this.height + 'px',
        '--top-color': this.color
      }
    }
  },
  mounted() {},
  methods: {
    goback() {
      this.$router.go(-1)
    }
  }
}
</script>

<style lang="scss" scoped>
.content {
  font-size: 32px;
}
.status {
  //状态栏
  position: fixed;
  left: 0;
  top: 0;
  z-index: 999;
  width: 100%;
  height: var(--top-status-height);
  background: linear-gradient(45deg, #71d774 0%, #5ecc52 100%);
}
.statusOccupancy {
  height: var(--top-status-height);
}
.top-wrap {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  height: 92px;
  box-sizing: border-box;
  color: var(--top-color);
  background: linear-gradient(45deg, #71d774 0%, #5ecc52 100%);
  padding: 0 16px 0 16px;
  font-family: PingFang SC;
}
.top-fixed {
  position: fixed;
  // top: 0;
  right: 0;
  left: 0;
  z-index: 1;
}
.top-border-bottom {
  border-bottom: 1px solid #5e5e5e;
}
.headOccupancy {
  width: 100%;
  height: 92px;
}
.left,
.right {
  width: calc((100% - var(--top-search-width)) / 2);
  display: flex;
  align-items: center;
  ::v-deep .van-icon:nth-child(1) {
    margin-right: 10px;
  }
  ::v-deep .van-icon {
  }
}
.right {
  justify-content: flex-end;
}
.center {
  position: relative;
  .search {
    width: var(--top-search-width);
    height: var(--top-search-height);
    line-height: var(--top-search-height);
    background:#fff;
    border-radius: var(--top-search-radius);
    padding-left: 62px;
    box-sizing: border-box;
    overflow: hidden;
    .search-input {
      border: none;
    }
  }
  .title {
    color: var(--top-title-color);
    font-size: var(--top-title-size);
  }

}
</style>
