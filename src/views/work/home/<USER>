<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-03-31 16:26:04
 * @LastEditors: 孙立政
 * @LastEditTime: 2023-03-06 13:35:48
-->
<template>
  <div class="content">
    <NavHeight bgc="#fff" />
    <Top />
    <div class="home-section1">
      <div>
        打造舒适生活
        <van-icon
          name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/work/orange_right_arrow.png"
          size="14"
        />
      </div>
      <ul>
        <li>
          <van-icon
            name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/work/icon3.png"
            size="16"
          />生活服务
        </li>
        <li>
          <van-icon
            name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/work/icon1.png"
            size="16"
          />上门准时
        </li>
        <li>
          <van-icon
            name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/work/icon2.png"
            size="16"
          />平台监控
        </li>
      </ul>
    </div>
    <div class="home-section2">
      <div class="section2-img1" @click="goClean()">
        <p>家庭保洁</p>
        <p>开启洁净生活</p>
      </div>
      <div v-if="false" class="section2-img3" @click="goRecru()">
        <p>招聘信息</p>
        <p>寻找合适岗位</p>
      </div>
      <div class="section2-img2" @click="goRepair()">
        <p>上门维修</p>
        <p>家电安全更健康</p>
      </div>

    </div>
    <!-- 发布需求 -->
    <div class="home-section3" @click="selectshow = true">
      <div class="section3-left">
        <p>发布需求</p>
        <p>需要什么服务说出来</p>
      </div>
      <div class="section3-right" />
    </div>
    <div class="home-section4">
      <van-swipe
        class="my-swipe"
        :autoplay="3000"
        indicator-color="#fff"
        touchable
      >
        <van-swipe-item v-for="(item, index) in bannerList" :key="index">
          <img :src="item">
        </van-swipe-item>
      </van-swipe>
    </div>
    <div v-if="recomList.length != 0" class="home-section5">
      <div class="section5-title">
        精选推荐
      </div>
      <ul>
        <li
          v-for="(item, index) in recomList"
          :key="index"
          @click="goDetail(item)"
        >
          <img :src="item.img" alt="" class="section5-img1">
          <div class="section5-desc">{{ item.itemName }}</div>
          <div class="section5-price">
            {{ item.itemPrice }}{{ item.priceUnit }}
          </div>
        </li>
      </ul>
    </div>
    <!-- 发布需求选择选择 -->
    <van-popup v-model="selectshow" position="bottom" :style="{}">
      <van-picker
        title=""
        show-toolbar
        :columns="columns"
        @confirm="confirm"
        @cancel="cancel"
      />
    </van-popup>
  </div>
</template>

<script>
import Top from './components/top'
import { getBanner, getRecommend } from '@/api/work'
export default {
  components: {
    Top
  },
  data() {
    return {
      selectshow: false,
      columns: ['家庭保洁', '上门维修'],
      bannerList: [],
      recomList: []
    }
  },
  created() {
    this.getBannerList()
    this.getRecommendAll()
  },
  mounted() {},
  methods: {
    getBannerList() {
      getBanner()
        .then((res) => {
          if (res.status == 200) {
            this.bannerList = res.data
          }
        })
    },
    getRecommendAll() {
      getRecommend()
        .then((res) => {
          // console.log(res)
          if (res.status == 200) {
            this.recomList = res.data
          }
        })
    },
    confirm(e) {
      console.log(e)
      let routerPath = ''
      let type = 1
      if (e == '家庭保洁') {
        routerPath = '/work/needs/clean'
      } else if (e == '上门维修') {
        routerPath = '/work/needs/clean'
        type = 2
      } else if (e == '招聘信息') {
        routerPath = '/work/needs/recruitment'
      }
      this.$router.push({
        path: routerPath,
        query: {
          type: type
        }
      })
    },
    cancel(e) {
      this.selectshow = false
    },
    goClean() {
      this.$router.push({
        path: '/work/list/listClean',
        query: {
          type: 1
        }
      })
    },
    goRepair() {
      this.$toast('即将上线，敬请期待')
      // this.$router.push({
      //   path: '/work/list/listClean',
      //   query: {
      //     type: 2
      //   }
      // })
    },
    goRecru() {
      this.$router.push({
        path: '/work/list/listRecru',
        query: {
          type: 2
        }
      })
    },
    goDetail(item) {
      if (item.categoryName == '招聘信息') {
        this.$router.push({
          path: '/work/list/listRecruDetail',
          query: {
            id: item.id
          }
        })
      } else {
        this.$router.push({
          path: '/work/list/listCleanDetail',
          query: {
            id: item.id,
            type: this.$route.query.type
          }
        })
      }
    }
  }
}
</script>

<style scoped lang="scss">
.content {
  .home-section1 {
    position: relative;
    z-index: -1;
    width: 100%;
    height: 220px;
    color: #db7f11;
    font-family: PingFangSC;
    font-size: 36px;
    background-image: url(https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/work/section1.png);
    background-size: 100% 100%;
    box-sizing: border-box;
    padding: 48px 0 0 20px;
    ul {
      display: flex;
      margin-top: 16px;
      li {
        display: flex;
        justify-content: center;
        font-size: 26px;
        margin-right: 22px;
        .van-icon {
          top:5px;
          margin-right: 5px;
        }
      }
    }
  }
  .home-section2 {
    width: 100%;
    height: 488px;
    background-color: #fff;
    margin-top: -28px;
    border-radius: 24px;
    margin-bottom: 16px;
    box-sizing: border-box;
    padding: 28px 20px 0;
    .section2-img1 {
      float: left;
      width: 322px;
      height: 436px;
      background-image: url(https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/work/bg1.png);
      background-size: 100% 100%;
      border-radius: 12px;
      overflow: hidden;
      margin-right: 30px;
    }
    .section2-img2 {
      width: 348px;
      height: 204px;
      background-image: url(https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/work/bg2.png);
      background-size: 100% 100%;
      border-radius: 12px;
      overflow: hidden;

    }
    .section2-img3 {
      width: 348px;
      height: 204px;
      background-image: url(https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/work/bg3.png);
      background-size: 100% 100%;
      border-radius: 12px;
      overflow: hidden;
      margin-bottom: 26px;
    }
    p:nth-of-type(1) {
      font-size: 36px;
      color: #323131;
      font-weight: 600;
      margin: 14px 0 8px 2px;
      margin-left: 20px;
    }
    p:nth-of-type(2) {
      color: #323131;
      font-size: 20px;
      font-weight: normal;
      margin-left: 22px;
    }
  }
  .home-section3 {
    display: flex;
    justify-content: space-between;
    width: 710px;
    height: 140px;
    background-color: #fff;
    border-radius: 14px;
    margin: 0 auto 16px;
    box-sizing: border-box;
    padding: 0 20px;
    .section3-right {
      width: 108px;
      height: 108px;
      background-image: url(https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/work/little_person.png);
      background-size: 100% 100%;
      margin-top: 10px;
      margin-right: 16px;
    }
    .section3-left {
      p:nth-of-type(1) {
        font-size: 36px;
        color: #323131;
        font-weight: 600;
        margin: 22px 0 8px 0;
      }
      p:nth-of-type(2) {
        color: #323131;
        font-size: 24px;
        font-weight: normal;
      }
    }
  }
  .home-section4 {
    width: 710px;
    height: 236px;
    // background-color: red;
    border-radius: 14px;
    margin: 0 auto 16px;
    .my-swipe {
      width: 710px;
      height: 236px;
      // background-color: red;
      border-radius: 14px;
      overflow: hidden;
      transform: translateY(0);
      margin: 0 auto;
      img {
        width: 100%;
        height: 100%;
        border-radius: 14px;
      }
    }

    ::v-deep .van-swipe {
      overflow: hidden !important;
    }
  }
  .home-section5 {
    width: 710px;
    background-color: #fff;
    border-radius: 14px;
    margin: 0 auto;
    box-sizing: border-box;
    padding: 32px 20px 0;
    .section5-title {
      font-size: 34px;
      color: #323131;
      margin-bottom: 28px;
    }
    ul {
      width: 710px;
      display: flex;
      flex-wrap: wrap;
    }
    li {
      flex: 1;
      .section5-img1 {
        width: 320px;
        height:210px;
        border-radius:12px;
        margin-bottom:28px;
        overflow: hidden;
      }
      .section5-desc {
        font-size:30px;
        color: #323131;
      }
      .section5-price {
        font-size: 28px;
        color: #fb636d;
      }
    }
    li:nth-of-type(-n + 2) {
      margin-bottom: 42px;
    }
  }
}
</style>
