<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-04-02 10:08:31
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-10-09 16:43:10
-->
<template>
  <div class="content">
    <NavHeight bgc="#5ecc52" />
    <Top message="预约下单" />
    <div class="wrap">
      <div class="cell-set">
        <div class="set-line">
          <van-field
            v-model="anOrder.orderAddress"
            label="选择服务地址"
            placeholder=""
            input-align="right"
            right-icon="arrow"
            readonly
            @click-input="addressChange()"
          />
        </div>
        <div class="set-line">
          <van-field
            v-model="anOrder.appointmentTime"
            label="服务时间"
            placeholder="请选择时间"
            input-align="right"
            right-icon="arrow"
            readonly
            @click-input="dateChange()"
          />
        </div>
        <!-- 维修类不显示保洁面积和服务人员 -->
        <div v-if="$route.query.type==1" class="set-line">
          <van-field
            v-model="anOrder.area"
            label="保洁面积"
            placeholder="请输入保洁面积"
            input-align="right"
            :border="false"
            type="digit"
            maxlength="6"
          />
          <span class="right">平方米</span>
        </div>
        <div v-if="$route.query.type==1" class="set-line">
          <van-field
            v-model="anOrder.number"
            label="服务人员"
            placeholder="请输入需要服务人员数量"
            input-align="right"
            :border="false"
            type="digit"
            maxlength="6"
          />
          <span style="margin-left:10px">人</span>
        </div>
        <div class="set-line" style="border-bottom:none">
          服务明细
        </div>
        <div class="set-line area">
          <van-field
            v-model="anOrder.detailDesc"
            rows="4"
            autosize
            type="textarea"
            maxlength="200"
            placeholder="如有其他需求，详细描述内容"
            show-word-limit
          />
        </div>
      </div>
    </div>
    <div class="btn">
      <div class="left">
        <span>预算金额</span>
        <span class="red">¥{{ price }}</span>
      </div>
      <van-button type="primary" @click="goNext">立即预约</van-button>
    </div>
    <!-- 时间选择器 -->
    <van-popup v-model="dateshow" position="bottom">
      <van-datetime-picker
        v-model="currentTime1"
        type="datehour"
        title="选择时间"
        :min-date="minDate"
        :max-date="maxDate"
        @confirm="dateConfirm1"
        @cancel="dateCancel1"
      />
    </van-popup>
    <!-- 选择地址弹框 -->
    <van-popup v-model="addressShow" position="bottom" round closeable :style="{ width: '100%', height: '350px' }">
      <div class="box">
        <div class="title">
          <div />
          <div>选择地址</div>
          <div class="addaddress" @click="goAddress">添加地址</div>
        </div>
        <div class="body">
          <van-radio-group v-model="radiovalue" @change="radioChange">
            <div class="address-pop" style="margin-top:5px;">
              <div v-for="(item, index) in temparr" :key="index" class="item">
                <div class="item-left">
                  <van-radio :name="index" icon-size="19" checked-color="#5dcb4f">
                    <div class="item-detail">
                      <!-- 最多可以输入18个字 -->
                      <div class="">{{ item.address | ellipsis(18) }}</div>
                      <div class="">
                        <span style="margin-right:13px">{{ item.username }}</span>
                        <span>{{ item.mobile }}</span>
                      </div>
                    </div>
                  </van-radio>
                </div>
              </div>
            </div>
          </van-radio-group>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script>
import Top from './components/top'
import { addressList } from '@/api/takeout'
import { formatDate } from '@/utils/date'
import { cleanAppoint } from '@/api/work'
export default {
  components: {
    Top
  },
  data() {
    return {
      anOrder: {
        appointmentTime: '',
        orderAddress: '',
        area: '',
        number: '',
        detailDesc: ''
      },
      dateshow: false,
      addressShow: false,
      currentTime1: new Date(),
      minDate: new Date(),
      maxDate: new Date(2025, 10, 1),
      type: '',
      temparr: [],
      radiovalue: ''
    }
  },
  created() {
    this.price = this.$route.query.itemPrice
    this.getAdrList()
  },
  mounted() {},
  methods: {
    goNext() {
      let self = this
      this.type = this.$route.query.type
      if (this.anOrder.appointmentTime == '') {
        this.$toast('请选择服务时间')
        return false
      }
      if (this.anOrder.orderAddress == '') {
        this.$toast('请选择服务地址')
        return false
      }
      if (this.anOrder.area == '' && this.type == 1) {
        this.$toast('请输入保洁面积')
        return false
      }
      if (this.anOrder.number == '' && this.type == 1) {
        this.$toast('请输入服务人数')
        return false
      }
      let data = {
        userId: this.$store.getters.getUserId,
        appointmentTime: this.anOrder.appointmentTime,
        orderAddress: this.anOrder.orderAddress,
        detailDesc: this.anOrder.detailDesc,
        itemPrice: this.$route.query.itemPrice,
        priceUnit: this.$route.query.priceUnit,
        telephone: localStorage.getItem('phone'),
        userName: localStorage.getItem('username'),
        itemId: this.$route.query.itemId
      }
      // this.$route.query.type==1?'家庭保洁':'上门维修'
      let type = this.type == 1 ? 'placeCleanOrder' : 'placeMaintainOrder'
      if (this.type == 1) {
        data.area = this.anOrder.area
        data.number = this.anOrder.number
      }
      cleanAppoint(data, type).then((res) => {
        console.log(res)
        if (res.status == 200) {
          self.$toast('预约成功')
          setTimeout(() => {
            self.$router.push({
              name: 'ExternalOrder',
              query: {
                type: 1
              }
            })
            // self.$router.push({
            //   name: 'MyEvalute'
            // })
          }, 1500)
        } else {
          self.$toast(res.message)
        }
      })
    },
    dateConfirm1(e) {
      this.anOrder.appointmentTime = formatDate(e, 'yyyy-MM-dd hh:00')
      this.currentTime1 = new Date()
      this.dateshow = false
    },
    dateCancel1(e) {
      this.currentTime1 = new Date()
      this.dateshow = false
    },
    dateChange() {
      this.dateshow = true
    },
    addressChange() {
      this.addressShow = true
    },
    radioChange(e) {
      console.log(e)
      console.log(this.temparr[e])
      this.anOrder.orderAddress =
        this.temparr[e].address +
        this.temparr[e].username +
        this.temparr[e].mobile
      this.addressShow = false
    },
    goAddress() {
      this.addressShow = false
      this.$router.push({
        name: 'AddressAdd'
      })
    },
    // 获取地址列表
    getAdrList() {
      let self = this
      let marketSn = 'SH800430'
      addressList(marketSn)
        .then(function(res) {
          if (res.status == 200) {
            self.temparr = res.data
            // self.temparr = res.data.withinDistance.concat(
            //   res.data.beyondDistance
            // )
          }
        })
    }
  }
}
</script>

<style scoped lang="scss">
.content::before {
  // 利用伪元素设置整个页面的背景色
  content: " ";
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: -100;
  min-height: 100%;
  background-color: #fff;
}
.content {
  .wrap {
    font-size:14px;
    color: #5e5e5e;
    background-color: #fff;
    .cell-set .set-line:not(:last-child) {
      border-bottom: 1px solid #e8e9ea;
    }
    .cell-set:nth-child(2) .set-line:last-child {
      border-bottom: 1px solid #e8e9ea;
    }
    ::v-deep .van-cell__value--alone {
        background:#faf9f9;
        height: 200px;
        border-radius: 16px;
        padding: 26px;
    }
    ::v-deep .van-field__control {
        box-sizing: border-box;
    }
    .cell-set {
      padding: 0 20px;
      background-color: #fff;

      .set-line {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 112px;
        background-color: #fff;
        .right {
          width: 100px;
          margin-left: 20px;
        }
        .set-line-right {
           display: flex;
        align-items: center;
        }
        input {
            width: 132px;
            height:54px;
            background-color: #F6F6F6;
            border: none;
            border-radius: 28px;
            padding-left:20px;
            padding-right:10px;
        }
        input::placeholder {
          color:#c8c9cc
        }
        .line {
            width: 34px;
            height: 1px;
            background-color: #F6F6F6;
            margin: 0 15px;
        }
        ::v-deep .van-cell {
          padding-left: 0;
          padding-right: 0;
        }

      }
     .area {
        margin-top:48px;
        min-height: 400px;
      }
      ::v-deep .van-cell__value--alone {
        height: 380px;
      }
    }
  }
    .btn {
        position: absolute;
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 710px;
        margin: 0 20px;
        bottom: 80px;
        font-size: 25px;
        .red {
            color: #FB636D;
            font-size:50px;
            margin-left:12px;
        }
    }

    // 按钮
    ::v-deep .van-button--primary {
      width: 300px;
      border: 0;
      background: linear-gradient(45deg, #71d774 0%, #5ecc52 100%);
    }
    ::v-deep .van-button--normal {
      font-size: 32px;
      border-radius: 12px;
    }
    .box {
		.title {
			position: absolute;
			top: 0;
			left: 0;
			z-index: 10;
			width: 100%;
			display: flex;
			justify-content: space-between;
			box-sizing: border-box;
			height: 104px;
			line-height: 104px;
			font-size: 32px;
			color: #000010;
			border-radius: 20px 20px 0 0;
			background: #fff;
            div {
				width: 33.3%;
				text-align: center;
			}
			.addaddress {
				font-size:26px;
				text-align: right;
				margin-right:20px;
			}
		}
		.body {
			width: 100%;
			height: 650px;
			overflow-y: auto;
      margin-top: 100px;
		}
		.close-icon {
			position: fixed;
			bottom: 650px;
			right: 40px;
			z-index: 10;
		}
		.address-pop {
			margin: 4px 42px 20px 18px;
			.item {
				display: flex;
				justify-content: space-between;
				align-items: center;
				font-size: 30px;
				color: #000010;
				height: 130px;
				.item-left {
					display: flex;
					align-items: center;
					.item-detail {
						margin-left: 22px;
						>div:nth-of-type(1) {
							font-family: PingFangSC;
							color: #33333f;
							margin-bottom: 4px;
						}
						>div:nth-of-type(2) {
							font-family: PingFang SC;
							font-size: 22px;
							color: #696969;
						}
					}
				}
				.item-right {
					display: flex;
					align-items: center;
				}
			}
		}
		.newAddr {
			position: absolute;
			bottom: 0;
			left: 0;
			width: 100%;
			background-color: #fff;
			color: #000010;
			font-size: 30px;
			padding: 0 0 22px;

			>div {
				display: flex;
				align-items: center;
				justify-content: center;
				width: 684px;
				height: 76px;
				line-height: 76px;
				background-color: #f5f5f5;
				border-radius: 10px;
				margin: 0 auto;
			}
		}

		::v-deep .van-radio__icon .van-icon {
			border: 1px solid #5dcb4f;
		}

		.righticon {
			position: relative;
			top: -2px;
		}
	}
}
</style>
