<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <PERSON>haoyu<PERSON>
 * @Date: 2021-04-06 19:31:28
 * @LastEditors: zhaoyuxin
 * @LastEditTime: 2021-06-05 17:37:43
-->
<template>
  <div class="content">
    <!-- <div class="status" :style="statusVar" /> -->
    <!-- <div class="statusOccupancy" :style="statusVar" /> -->
    <ul
      class="top-wrap  top-fixed"
    >
      <!-- 左侧 -->
      <li class="left">
        <van-icon
          class="fast"
          name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/work/back_black.png"
          size="13"
          @click="goback"
        />
      </li>
      <!-- 中间 -->
      <li class="center">
        <!-- 标题拦 -->
        <div>
          {{ message }}
        </div>
      </li>
      <!-- 右侧 -->
      <li class="right" />
    </ul>
    <div class="headOccupancy" />
  </div>
</template>

<script>
export default {
  props: {
    message: {
      type: String,
      default: ''
    }
  },
  data() {
    return {

    }
  },
  computed: {
    statusVar() {
      return {
        '--top-status-height': this.$store.getters.getStatusHeight + 'px'
      }
    }
  },
  created() {

  },
  mounted() {

  },
  methods: {
    goback() {
      this.$router.push({
        name: 'My'
      })
    }
  }
}
</script>

<style scoped lang="scss">
    .content {
         .status {
  //状态栏
        position: fixed;
        left: 0;
        top: 0;
        z-index: 999;
        width: 100%;
        height: var(--top-status-height);
          background-color: #f3f4f9;
}
.statusOccupancy {
  height: var(--top-status-height);
}
.top-wrap {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  height: 92px;
  box-sizing: border-box;
  color: var(--top-color);
   background-color: #f3f4f9;
  padding: 0 16px 0 16px;
  font-family: PingFang SC;
}
.top-fixed {
  position: fixed;
  // top: 0;
  right: 0;
  left: 0;
  z-index: 10;
}
.headOccupancy {
  width: 100%;
  height: 92px;
}
.navbar {
    display: flex;
    justify-content: space-between;
    width: 100%;
    position: fixed;
    top: 0;
    z-index: 2;
    background-color: #f3f4f9;
    .backimg {
        width: 20px;
        height: 31px;
        margin-left: 36px;
        margin-top: 29px;
    }
    height: 90px;
    line-height: 90px;
    font-size: 30px;
    font-weight: bold;

}
    }
    .center {
      font-size: 34px;
    }
</style>
