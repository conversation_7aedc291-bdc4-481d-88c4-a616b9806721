<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: z<PERSON><PERSON><PERSON>
 * @Date: 2021-03-31 19:04:48
 * @LastEditors: zhaoyuxin
 * @LastEditTime: 2021-06-05 17:49:20
-->
<template>
  <!-- 订单提示 -->
  <div class="content">
    <div v-if="detail.status==10">
      <div class="status">
        <span>新订单</span>
      </div>
      <div class="cancel-cell">
        <van-icon name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/work/icon_voice.png" size="15" style="margin:0 13px" />
        <span>商家超过1小时未接单，订单将自动取消</span>
      </div>
    </div>
    <div v-if="detail.status==20">
      <div class="status">
        <span>待服务商接单</span>
      </div>
      <div class="cancel-cell">
        <van-icon name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/work/icon_voice.png" size="15" style="margin:0 13px" />
        <span>商家超过1小时未接单，订单将自动取消</span>
      </div>
    </div>
    <div v-if="detail.status==30">
      <div class="status">
        <span>已接单</span>
      </div>
      <div class="cancel-cell">
        <van-icon name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/work/icon_voice.png" size="15" style="margin:0 13px" />
        <span>商家超过1小时未接单，订单将自动取消</span>
      </div>
    </div>
    <div v-if="detail.status==50">
      <div class="status">
        <span>订单已取消</span>
      </div>
      <div class="cancel-cell">
        <van-icon name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/work/icon_voice.png" size="15" style="margin:0 13px" />
        <span>您的订单已取消</span>
      </div>
    </div>
    <div v-if="detail.status==40">
      <div class="status">
        <span>订单已完成</span>
      </div>
      <div class="cancel-cell">
        <van-icon name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/work/icon_voice.png" size="15" style="margin:0 13px" />
        <span>感谢您对点滴平台的信任，期待您的再次光临。</span>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: { detail: { type: Object, default: function() { return {} } }},
  data() {
    return {
    }
  },
  mounted() {
  },
  methods: {
  }
}
</script>

<style lang="scss" scoped>
	.content {
		.status {
			font-size: 44px;
			font-family: PingFangSC;
			color: #000010;
			margin: 20px 0 30px 44px;
		}
		.cancel-cell {
			width: 710px;
			height: 76px;
			line-height: 76px;
			margin: 0 auto 12px;
			background-color: #fff;
			border-radius:20px;
			color: #4C4C57;
			font-size:28px;
		}
	}
</style>
