<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-04-06 16:43:42
 * @LastEditors: 孙立政
 * @LastEditTime: 2021-12-31 00:33:11
-->
<template>
  <div class="content">
    <NavHeight bgc="linear-gradient(45deg, #71d774 0%, #5ecc52 100%)" />
    <Top message="详情" />
    <div class="wrap">
      <div class="cell">
        <div class="cell-line">
          <span>订单状态</span>
          <span v-if="detail.status==10">新订单</span>
          <span v-if="detail.status==20">待接单</span>
          <span v-if="detail.status==30">已接单</span>
          <span v-if="detail.status==40">已完成</span>
          <span v-if="detail.status==50">已取消</span>
        </div>
        <div class="cell-line">
          <span>需求类型</span>
          <span>{{ detail.categoryName }}-{{ detail.typeName }}</span>
        </div>
        <div class="cell-line">
          <span>服务地址</span>
          <div style="width:250px;text-align:right">{{ detail.orderAddress }}</div>
        </div>
        <div v-if="detail.categoryName=='家庭保洁'||detail.categoryName=='上门维修'" class="cell-line">
          <span>服务时间</span>
          <span>{{ detail.appointmentTime }}</span>
        </div>
        <div class="cell-line">
          <span>预算金额</span>
          <span>{{ detail.minPrice }}-{{ detail.maxPrice }}/{{ detail.salaryUnit }}</span>
        </div>
        <div class="textarea">
          <span>需求明细</span>
          <div class="more">
            <div class="elipsis">{{ detail.detailDesc }}</div>
            <div class="imgList">
              <img v-for="(item,index) in JSON.parse(detail.demandImg)" :key="index" :src="item" alt="">
            </div>
          </div>
        </div>
        <div class="cell-line">
          <span>订单号</span>
          <span>{{ detail.orderNo }}</span>
        </div>
        <div class="cell-line">
          <span>下单时间</span>
          <span>{{ detail.orderTime }}</span>
        </div>
        <div v-if="detail.cancelTime" class="cell-line">
          <span>取消时间</span>
          <span>{{ detail.cancelTime }}</span>
        </div>
      </div>
      <!-- 只有在发布中才显示按钮 -->
      <div v-if="detail.status==10||detail.status==20||detail.status==30" class="btn">
        <van-button @click="clickBtn(detail,100)">取消预约</van-button>
        <van-button type="primary" @click="clickBtn(detail,200)">已完成</van-button>
      </div>
    </div>

  </div>
</template>

<script>
import Top from './components/top'
import { orderDetail, btnOrder } from '@/api/work'
export default {
  components: {
    Top
  },
  data() {
    return {
      detail: {}
    }
  },
  created() {
    this.getDetail()
  },
  mounted() {

  },
  methods: {
    getDetail() {
      let data = {
        type: this.$route.query.type,
        orderId: this.$route.query.orderId
      }
      orderDetail(data).then((res) => {
        if (res.status == 200) {
          this.detail = res.data
        }
      })
    },
    clickBtn(item, type) {
      console.log(item)
      let data = {
        userId: this.$store.getters.getUserId,
        orderId: item.orderId,
        type: 'cancel'
      }
      if (type == 100) { // 取消

      } else if (type == 200) { // 完成
        data.type = 'finish'
      }
      btnOrder(data).then((res) => {
        if (res.status == 200) {
          this.$toast(res.message)
          this.$router.push({
            name: 'ExternalOrder',
            query: {
              type: 1
            }
          })
          // this.$router.push({
          //   name: 'MyEvalute'
          // })
        } else {
          this.$toast(res.message)
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
.content::before {
  // 利用伪元素设置整个页面的背景色
  content: " ";
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: -100;
  min-height: 100%;
  background-color: #fff;
}
    .content {
        overflow-x: hidden;
         .wrap {
    font-size: 28px;
    color: #5e5e5e;
    background-color: #fff;
    padding-bottom:120px;
    .cell{
      padding: 0 20px;
      .cell-line {
          display: flex;
          justify-content: space-between;
          align-items: center;
          height: 116px;
         border-bottom: 1px solid #E8E9EA;
      }
      .textarea {
          display: flex;
          justify-content: space-between;
             margin-top: 36px;
             padding-bottom: 36px;
         border-bottom: 1px solid #E8E9EA;
         .more {
             width: 476px;
             .elipsis {
                text-overflow: -o-ellipsis-lastline;
                overflow: hidden;
                text-overflow: ellipsis;
                display: -webkit-box;
                -webkit-line-clamp:4;
                -webkit-box-orient: vertical;
             }
             .imgList {
                 margin-top: 36px;
                 img {
                     width: 130px;
                     height: 130px;
                     border-radius: 10px;
                     background-color: gray;
                     overflow: hidden;
                 }
                 img:nth-of-type(-n+2){
                     margin-right: 20px;
                 }
             }
         }
      }
    }
  }
  .btn {
        display: flex;
        align-items: center;
        margin: 0 58px;
        padding: 120px 0 0 0;
    }

    // 按钮
    ::v-deep .van-button--primary {
      width: 300px;
      border: 0;
      background: linear-gradient(45deg, #71d774 0%, #5ecc52 100%);
      margin-left: 32px;

    }
    ::v-deep .van-button--normal {
      width: 300px;
      border: 0;
     color: #fff;
      border-radius: 12px;
      background-color: #323131;
    }
    }
</style>
