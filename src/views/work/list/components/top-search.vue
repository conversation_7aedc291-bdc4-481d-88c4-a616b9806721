<template>
  <!-- 头部 -->
  <div class="content">
    <!-- 状态栏高度 -->
    <!-- <div class="status" :style="statusVar" /> -->
    <!-- <div class="statusOccupancy" :style="statusVar" /> -->
    <!-- 导航栏高度 -->
    <ul
      class="top-wrap  top-fixed"
      :style="styleVar"
    >
      <!-- 左侧 -->
      <li class="left">
        <van-icon
          class="fast"
          name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/common/arrowlbft.png"
          size="15"
          @click="goback"
        />
        <div class="left-text" style="margin-right:10px">{{ message }}</div>
      </li>
      <li class="center">
        <!-- 搜索框 -->
        <div
          class="search"
        >
          <van-search v-model="keyword" placeholder="请输入搜索内容" @search="goSearch" @clear="goClear" />
        </div>
        <!-- 标题拦 -->
      </li>
      <!-- 右侧 -->
      <ul class="right" />
    </ul>
    <!-- 头部固定定位占位高度 -->
    <div class="headOccupancy" :style="styleVar" />
  </div>
</template>

<script>
export default {
  name: 'Mytop',
  props: {
    message: {
      type: String,
      default: ''
    },
    // 头部高度
    height: {
      type: Number,
      default: 46
    },
    statusColor: {
      type: String,
      default: '#fff'
    },
    // 导航栏字体颜色
    color: {
      type: String,
      default: '#fff'
    },
    // 背景颜色
    background: {
      type: String,
      default: 'green'
    },

    // 是否固定在顶部
    isFixed: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      keyword: ''
    }
  },
  computed: {
    statusVar() {
      return {
        '--top-status-height': this.$store.getters.getStatusHeight + 'px',
        '--top-status-color': this.statusColor
      }
    },
    styleVar() {
      return {
        '--top-height': this.height + 'px',
        '--top-color': this.color
      }
    }
  },
  mounted() {},
  methods: {
    goback() {
      this.$router.go(-1)
    },
    goSearch() {
      // 搜索
      if (this.keyword == '') {
        this.$toast('请输入搜索内容')
        return false
      }
      this.$emit('func', this.keyword)
    },
    goClear() {
      this.$emit('func1', this.keyword)
    }
  }
}
</script>

<style lang="scss" scoped>
.content {
  font-size: 16px;
}
.status {
  //状态栏
  position: fixed;
  left: 0;
  top: 0;
  z-index: 999;
  width: 100%;
  height: var(--top-status-height);
  background: linear-gradient(45deg, #71d774 0%, #5ecc52 100%);
}
.statusOccupancy {
  height: var(--top-status-height);
}
.top-wrap {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  height: 92px;
  box-sizing: border-box;
  color: var(--top-color);
  background: linear-gradient(45deg, #71d774 0%, #5ecc52 100%);
  padding: 0 16px 0 16px;
  font-family: PingFang SC;
}
.top-fixed {
  position: fixed;
  right: 0;
  left: 0;
  z-index: 1;
}
.top-border-bottom {
  border-bottom: 1px solid #5e5e5e;
}
.headOccupancy {
  width: 100%;
  height: 92px;
}
.left,
.right {
  display: flex;
  align-items: center;
  ::v-deep .van-icon:nth-child(1) {
    margin-right: 10px;
  }
}
.right {
  justify-content: flex-end;
}
.center {
  position: relative;
  .search {
    ::v-deep .van-search {
		background-color: transparent;
		padding: 0;
		width: 442px;
		height: 60px;
		border-radius: 30px;
		overflow: hidden;
	}

	::v-deep .van-field__left-icon {
		color: #8e8e93;
	}

	::v-deep .van-cell__value--alone {
		color: #c4c4c7;
	}

  }
  input::placeholder {
    font-size: 13px;
    color: #c4c4c7;
  }
  .search-icon {
    position: absolute;
    left: 24px;
    top: 30px;
    transform: translateY(-47%);
    width: 16px;
    height: 24px;
  }
  ::v-deep .van-field__left-icon {
    color: #8e8e93;
  }
  ::v-deep .van-cell__value--alone {
    color: #c4c4c7;
  }
  input {
    caret-color: auto;
    display: block;
    color: #8e8e93;
  }

}
</style>
