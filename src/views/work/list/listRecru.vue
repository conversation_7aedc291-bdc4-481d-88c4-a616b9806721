<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-04-02 14:27:39
 * @LastEditors: 孙立政
 * @LastEditTime: 2021-12-13 20:13:18
-->
<template>
  <div class="content">
    <NavHeight bgc="linear-gradient(45deg, #71d774 0%, #5ecc52 100%)" />
    <top-search message="招聘信息" @func="getKeywords" @func1="goCancel" />
    <!-- <Top message="招聘信息"/> -->
    <div v-for="(item, index) in allList" :key="index" class="section">
      <div class="section-cell" @click="goDetail(item)">
        <div class="cell-title">
          <span>{{ item.itemName | ellipsis(14) }}</span>
          <span>{{ item.minSalary }}-{{ item.maxSalary }}{{ item.priceUnit }}</span>
        </div>
        <div class="cell-desc">
          <span>{{ item.quartersType }}</span>
          <!-- <span>{{item.quartersNature}}</span> -->
        </div>
        <ul class="cell-other">
          <li v-for="(item1,index1) in JSON.parse(item.quartersDemand)" :key="index1">{{ item1 }}</li>
        </ul>
      </div>
    </div>
    <van-empty v-if="allList.length==0" description="暂未有相关信息~" />
    <!-- 空数据 -->
    <!-- <Kong v-if="allList.length==0" /> -->
    <div class="addIcon" @click="addDemand">
      + 发布求职
    </div>
  </div>
</template>

<script>
import TopSearch from './components/top'
import { getRecruList, getRecruList1 } from '@/api/work'
// import Kong from '@/components/kong.vue'
export default {
  components: {
    TopSearch
    // Kong
  },
  data() {
    return {
      list: [
        { name: '深度保洁', icon: 1 },
        { name: '开荒保洁', icon: 1 },
        { name: '深度保洁', icon: 1 },
        { name: '深度保洁', icon: 1 },
        { name: '深度保洁', icon: 1 },
        { name: '深度保洁', icon: 1 },
        { name: '深度保洁', icon: 1 }
      ],
      allList: []
    }
  },
  created() {
    this.getRecruList()
  },
  mounted() {},
  methods: {
    goDetail(item) {
      this.$router.push({
        path: '/work/list/listRecruDetail',
        query: {
          id: item.id
        }
      })
    },
    getRecruList() {
      let data = {
        typeName: '招聘信息',
        pageNum: 1,
        pageSize: 10000
      }
      getRecruList(data).then((res) => {
        console.log(res)
        if (res.status == 200) {
          this.allList = res.data.data
        } else {
          this.$toast(res.message)
        }
      })
    },
    getKeywords(msg) {
      console.log(msg)
      let data = {
        typeName: '招聘信息',
        query: msg,
        pageNum: 1,
        pageSize: 10000
      }
      getRecruList1(data).then((res) => {
        console.log(res)
        if (res.status == 200) {
          this.allList = res.data.data
        } else {
          this.$toast(res.message)
        }
      })
    },
    goCancel() {
      this.getRecruList()
    },
    addDemand() {
      this.$router.push({
        path: '/work/needs/recruitment',
        query: {
          type: 1
        }
      })
    }
  }
}
</script>
<style scoped lang="scss">
.content {
  .addIcon{
    width: 230px;
    height: 90px;
    line-height: 90px;
    text-align: center;
    border-radius:50px;
    font-size: 30px;
    background-color: #23c981;
    color: #fff;
    position: fixed;
    bottom: 380px;
    right: 30px;
  }
  .section:last-child {
    margin-bottom: 30px;
  }
  .section {
    //   width: 100%;
    min-height: 150px;
    background-color: #fff;
    margin: 14px 20px 0;
    border-radius: 12px;
    box-sizing: border-box;
    .section-cell {
      padding: 22px;
      .cell-title {
        display: flex;
        justify-content: space-between;
        font-size: 34px;
        font-family: PingFangSC;
        margin-bottom: 14px;
        span:nth-of-type(2) {
          font-size: 32px;
        }
      }
      .cell-desc {
        font-size: 24px;
      }
      .cell-other {
        display: flex;
        flex-wrap: wrap;
        margin-top: 12px;
        font-size: 20px;
        color: #666;
        li {
          flex-shrink: 0;
          background-color: #f5f5f5;
          padding: 4px 16px;
          margin-right: 12px;
          margin-bottom: 12px;
          border-radius: 4px;
        }
      }
    }
  }
}
</style>
