<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-04-02 16:03:48
 * @LastEditors: 孙立政
 * @LastEditTime: 2021-12-13 16:47:40
-->
<template>
  <div class="content">
    <!-- 轮播图 -->
    <div>
      <van-sticky bg-color="rgba(0,0,0,.0);">
        <div
          :style="'margin-top:' + $store.getters.getStatusHeight + 'px'"
          class="top"
        >
          <van-icon
            name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/common/arrowlbft.png"
            color="#E7E7E6"
            size="22"
            style="float: left; margin-left: 15px"
            @click="goback()"
          />
        </div>
      </van-sticky>
      <!-- 轮播图 -->
      <van-swipe
        class="my-swipe"
        :autoplay="3000"
        indicator-color="#fff"
        touchable
      >
        <van-swipe-item v-for="(item,index) in JSON.parse(detail.img)" :key="index">
          <!-- <img :src="item"> -->
          <van-image
            width="100%"
            height="100%"
            fit="cover"
            :src="item"
          />
        </van-swipe-item>
      </van-swipe>
      <div class="section1">
        <p class="price">{{ detail.itemPrice }}/{{ detail.priceUnit }}</p>
        <p class="title">{{ detail.itemName }}</p>
        <p class="desc">
          {{ detail.itemDesc }}
        </p>
      </div>
      <div class="more">
        <div class="tabs">
          <van-tabs
            background="#F6F9FA"
            line-width="23px"
            title-inactive-color="#000010"
            title-active-color="#2C2C2C"
            @click="clickTab"
          >
            <van-tab v-model="active" title="服务">
              <div class="details" v-html="detail.serveDesc" />
            </van-tab>
            <van-tab v-model="active" title="详情">
              <div class="details" v-html="detail.imgDetail" />
            </van-tab>
          </van-tabs>
        </div>
        <div class="line" />
        <div class="other">
          <div v-if="detail.payReferences.length" class="primary">
            <p style="font-size:16px;margin:30px 0  15px 0">收费标准</p>
            <table>
              <tr>
                <td>收费项目</td>
                <td>项目明细</td>
                <td>价格</td>
              </tr>
              <tr v-for="(item1,index1) in detail.payReferences" :key="index1">
                <td>{{ item1.eventName }}</td>
                <td>{{ item1.eventDetails }}</td>
                <td class="red">￥{{ item1.eventPrice }}/{{ detail.priceUnit }}</td>
              </tr>
            </table>
          </div>
          <div v-if="detail.payReferences.length" class="line" />
          <div class="tips3">
            <p style="font-size:16px;margin:30px 0  15px 0">订购须知</p>
            <p class="color999" style="padding-bottom:15px">
              1、该项目由平台商家就近接单、规范服务；<br>
              2、如下单30分后无接单，建议取消订单从新下单；<br>
              3、商家接单后，取消订单请提前联系商家处理；<br>
              4、如师傅乱收费/请拒绝支付并申请取消订单；<br>
              5、为了保障您的权益，如有问题请联系平台客服。
            </p>
          </div>
        </div>
        <div class="detail" />
      </div>
    </div>
    <div class="btn">
      <van-button color="#323131" @click="goPhone(detail)">联系我们</van-button>
      <van-button type="primary" @click="goNext(detail)">立即预约</van-button>
    </div>
  </div>
</template>

<script>
import { getListDetail, getPhone } from '@/api/work'
export default {
  data() {
    return {
      active: 1,
      detail: {}
    }
  },
  created() {
    this.getListDetail()
  },
  mounted() {},
  methods: {
    getListDetail() {
      let data = {
        id: this.$route.query.id
      }
      getListDetail(data).then((res) => {
        console.log(res)
        if (res.status == 200) {
          this.detail = res.data
        } else {
          this.$toast(res.message)
        }
      })
    },
    goback() {
      this.$router.go(-1)
    },
    clickTab() {},
    goNext(detail) {
      console.log(detail)
      this.$router.push({
        path: '/work/appointment/anOrder',
        query: {
          itemId: detail.itemId,
          itemPrice: detail.itemPrice,
          priceUnit: detail.priceUnit,
          type: this.$route.query.type
        }
      })
    },
    goPhone(detail) {
      let data = {
        id: detail.itemId
      }
      getPhone(data).then((res) => {
        AlipayJSBridge.call('CallPhone', {
          phoneNum: res.data.facilitatorTel
        }, function(result) {})
      })
    }
  }
}
</script>

<style scoped lang="scss">
.content {
  ::v-deep .details{
    width: 710px;
    img{
      width: 100%;
    }
  }
  .top {
    position: absolute;
    z-index: 2;
  }
  .my-swipe {
    width: 100%;
    height: 490px;
    img {
      width: 100%;
      height: 100%;
    }
  }
  .section1 {
    background-color: #fff;
    padding: 20px;
    box-sizing: border-box;
    .title {
      font-weight: 600;
      font-size: 30px;
      margin-bottom: 4px;
    }
    .desc {
      font-size: 24px;
      color: #999999;
    }
    .price {
      font-size: 50px;
      color: #fb636d;
    }
  }
  .section2 {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 110px;
    background-color: #fff;
    margin: 12px 0;
    padding: 0 16px;
  }
  .more {
    background-color: #fff;
  }
  .tabs {
    font-size: 28px;
    color: #2c2c2c;
    background-color: #fff;

    ::v-deep .van-tabs__line {
      background-color: #37c204;
    }
    .van-tab__pane, .van-tab__pane-wrapper {
      margin-top: 15px;
      padding: 0 20px 0 20px ;
    }
  }
  .line {
    width: 100%;
    height: 15px;
    background-color: #FAF9F9;
    margin-top: 30px;
  }
  .other {
    font-size: 24px;
    .tips1 {
      font-family: PingFangSC;
      margin-top: 30px;
    }
    .blue {
      color: #6095f0;
      margin-top: 30px;
    }
    .tips3 {
      margin: 0 20px;
    }
    .color999 {
      color: #999;
      line-height: 40px;
    }
    .red {
      color: #fb636d;
    }
    .primary {
      margin: 0 20px;
      table {
        width: 100%;
        margin-top: 44px;
      }
      table td {
        width: 33%;
        text-align: center;
      }
    }
  }
  .detail {
      width: 710px;
      margin: 0 auto;
  }
  .btn {
      margin:78px  0;
      ::v-deep .van-button--primary {
      width: 352px;
      border: 0;
      background: linear-gradient(45deg, #71d774 0%, #5ecc52 100%);
    }
    ::v-deep .van-button--default {
        width: 258px;
        margin-left:64px;
        margin-right:12px;
    }
  }
}
</style>
