<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-04-02 14:28:38
 * @LastEditors: 孙立政
 * @LastEditTime: 2021-12-13 20:13:03
-->
<template>
  <div class="content">
    <NavHeight bgc="linear-gradient(45deg, #71d774 0%, #5ecc52 100%)" />
    <Top :message="message" />
    <!-- 分类 -->
    <div class="classify">
      <div v-if="secCateList.length" class="wrap">
        <div v-for="(item, index) in secCateList" :key="index">
          <div
            class="classify-item"
            :data-current="index"
            @click.stop="clickIdx($event, item)"
          >
            <div class="item-img" :data-current="index">
              <img
                :data-current="index"
                :src="
                  item.typeImg +
                    '?x-oss-process=image/resize,w_700/format,jpg/quality,q_50'
                "
                style="width: 44px; height: 44px;border-radius:22px"
              >
            </div>
            <div
              :data-current="index"
              :class="['name', currentTab == index ? 'greenbg' : '']"
            >
              {{ item.typeName }}
            </div>
          </div>
        </div>
      </div>
      <div v-else class="wrap-null" />
    </div>
    <div v-for="(item,index) in allList" :key="index" class="section">
      <div class="line" />
      <div class="section-cell" @click="goDetail(item)">
        <div class="cell-left">
          <img :src="item.itemImg" alt="">
        </div>
        <div class="cell-right">
          <p class="title">{{ item.itemName | ellipsis(15) }}</p>
          <p class="desc">{{ item.itemDesc | ellipsis(30) }}</p>
          <p class="price">{{ item.itemPrice }}/{{ item.priceUnit }}</p>
        </div>
      </div>
    </div>
    <!-- 空数据 -->
    <!-- <Kong v-if="allList.length==0" /> -->
    <van-empty v-if="allList.length==0" description="暂未有相关信息~" />
    <div class="addIcon" @click="addDemand">
      + 发布需求
    </div>
  </div>
</template>

<script>
import Top from './components/top'
import { secCate, getCleanList } from '@/api/work'
// import Kong from '@/components/kong.vue'
export default {
  components: {
    Top
    // Kong
  },
  data() {
    return {
      list: [
        { name: '深度保洁', icon: 1 },
        { name: '开荒保洁', icon: 1 },
        { name: '深度保洁', icon: 1 },
        { name: '深度保洁', icon: 1 },
        { name: '深度保洁', icon: 1 },
        { name: '深度保洁', icon: 1 },
        { name: '深度保洁', icon: 1 }
      ],
      currentTab: 0,
      message: '',
      secCateList: [],
      allList: []
    }
  },
  created() {
    if (this.$route.query.isMoudel == 1) {
      this.message = '家政服务'
    } else {
      this.message = this.$route.query.type == 1 ? '家庭保洁' : '上门维修'
    }

    this.getsecCate()
  },
  mounted() {},
  methods: {
    getsecCate() {
      let data = {
        categoryName: this.message === '家政服务' ? '家庭保洁' : this.message
      }
      secCate(data).then((res) => {
        console.log(res)
        if (res.status == 200) {
          this.secCateList = res.data
          this.getCleanList(0)
        }
      })
    },
    getCleanList(index) {
      let data = {
        typeName: this.secCateList[index].typeName,
        pageNum: 1,
        pageSize: 10000
      }
      getCleanList(data).then((res) => {
        console.log(res)
        if (res.status == 200) {
          this.allList = res.data.data
        } else {
          this.$toast(res.message)
        }
      })
    },
    clickIdx(e, item) {
      let current = e.target.dataset.current
      if (this.currentTab == current) {
        return false
      } else {
        this.currentTab = current
      }
      this.getCleanList(this.currentTab)
    },
    goDetail(item) {
      this.$router.push({
        path: '/work/list/listCleanDetail',
        query: {
          id: item.id,
          type: this.$route.query.type
        }
      })
    },
    addDemand() {
      this.$router.push({
        path: '/work/needs/clean',
        query: {
          type: 1
        }
      })
    }

  }
}
</script>

<style scoped lang="scss">
.content {
  .addIcon{
    width: 230px;
    height: 90px;
    line-height: 90px;
    text-align: center;
    border-radius:50px;
    font-size: 30px;
    background-color: #23c981;
    color: #fff;
    position: fixed;
    bottom: 380px;
    right: 30px;
  }
  .classify {
    width: 100%;
    background: linear-gradient(45deg, #71d774 0%, #5ecc52 100%);
    background-color: #fff;
    box-sizing: border-box;

    .wrap {
      display: flex;
      width: 100%;
      height: 198px;
      padding: 28px 40px 0;
      box-sizing: border-box;
      border-radius: 24px 24px 0 0;
      background-color: #fff;
      box-sizing: border-box;
      overflow-x: scroll;

      .classify-item {
        width: 126px;
        margin-right: 60px;
        flex-shrink: 0;
        text-align: center;
        .item-img {
          margin-bottom: 12px;
        }
        .name {
          font-size: 26px;
          color: #000010;
        }
      }
      .greenbg {
        height: 40px;
        line-height: 40px;
        color: #fff !important;
        text-align: center;
        border-radius:8px;
        background: linear-gradient(45deg, #70d773 0%, #5dcb4f 100%);
      }
    }
    .wrap-null {
      padding-top: 30px;
      background-color: #fff;
    }
  }
  .section {
      width: 100%;
      height: 228px;
      background-color: #fff;
      .line {
          height: 8px;
          background-color:#F3F4F9;
      }
      .section-cell {
          display: flex;
          margin: 26px 20px 0;
          .cell-left {
              width: 152px;
              height: 158px;
              border-radius: 8px;
              overflow: hidden;
              margin-right: 20px;
              img {
                width: 100%;
                height: 100%;
              }
          }
          .cell-right {
              width: 530px;
              .title {
                  font-size: 30px;
                  margin-bottom: 4px;
              }
              .desc {
                  font-size: 22px;
                  color:#4F4F4F ;
                  margin-bottom: 8px;

              }
              .price {
                  font-size: 24px;
                  color:#FB636D
              }
          }
      }
  }
}
</style>
