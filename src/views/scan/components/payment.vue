<template>
  <!-- 支付方式 -->
  <div class="content">
    <div class="wrap">
      <van-radio-group v-model="radiovalue">
        <div class="payment balce">
          <div v-for="(item,index) in payList" :key="index" class="payment-line" @click="radiovalue = item.payStyle">
            <div class="line-left">
              <van-icon v-if="item.payStyle === 1||item.payStyle === 7" name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/pay/balance.png" size="23px" />
              <van-icon v-if="item.payStyle === 3" name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/pay/weixinpay.png" size="23px" />
              <van-icon v-if="item.payStyle === 2" name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/pay/alipay.png" size="23px" />
              <van-icon v-if="item.payStyle === 4" name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/pay/dragon.png" size="23px" />
              <van-icon v-if="item.payStyle === 9" name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/shop/goods/unipay.png" size="23px" />
              <van-icon v-if="item.payStyle === 11" name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/pay/abc.png" size="23px" />
              <van-icon v-if="item.payStyle === 12" name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/logo/fshl.png" size="23px" />
              <span>{{ item.payStyleName==='二类户余额'?'余额':item.payStyleName }}</span>
            </div>
            <div class="line-right">
              <span v-if="item.payStyle === 1||item.payStyle === 7">￥ {{ balance }}</span>
              <van-radio :name="item.payStyle" icon-size="19px" checked-color="#5dcb4f" />
            </div>
          </div>

        </div>
      </van-radio-group>
    </div>
    <Loading :show="loadingShow" />
  </div>
</template>

<script>
import {
  checkInitPwd
} from '@/api/scan'

import Loading from '@/components/Loading'
export default {
  components: {
    Loading
  },
  props: {
    payList: {
      type: Array,
      default: function() {
        return []
      }
    },
    balance: { type: Number, default: 0 },
    payChannel: { type: Number, default: null }
  },
  data() {
    return {
      radiovalue: '',
      sysPayList: [],
      loadingShow: true
    }
  },
  watch: {
    radiovalue(val) {
      this.$store.state.scanPay.payradio = val
      if (val == 1 || val == 7) {
        this.checkInitPwd()
      }
    }
  },
  destroyed() {
    this.$store.state.scanPay.payradio = ''
  },
  mounted() {
    this.radiovalue = ''
    setTimeout(() => {
      this.loadingShow = false
    }, 1000)
  },
  methods: {
    // 检查初始支付密码
    checkInitPwd() {
      let self = this
      checkInitPwd(this.$store.getters.getUserId).then((res) => {
        if (res.status == 200) {
          if (res.data == true) {
            self.$dialog.alert({
              message: '当前为初始支付密码，请尽快修改'
            }).then(() => {
              self.$router.push('/editPayPwd')
            })
          }
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
	.content {
      .wrap {
        width: 710px;
        margin:auto
      }
			.payment {
        width: 710px;
        // height: 394px;
        background: #ffffff;
        border-radius: 16px;
        margin: auto;
        padding: 0 20px;
				.payment-line {
					display: flex;
					justify-content: space-between;
					align-items: center;
					font-size: 32px;
					color: #000010;
					height: 130px;
					line-height: 130px;

					.line-left {
						display: flex;
						align-items: center;

						>span {
              font-size: 32px;
              font-family: PingFangSC;
              color: #222222;
							margin-left: 24px;
              position: relative;
              top: 2px;
						}
					}

					.line-right {
						display: flex;
						align-items: center;

						>span {
							margin-right: 38px;
              font-size: 32px;
              font-family: PingFangSC;
              color: #999999;
						}
					}

					::v-deep .van-radio__icon .van-icon {
						border: 1.5px solid #cccccc;
					}

				}
			}
      .balce {
        width: 710px;
        background: #ffffff;
        border-radius: 16px;
        margin: auto;
        // height: 130px;
        margin-bottom: 20px;
      }
		}

</style>
