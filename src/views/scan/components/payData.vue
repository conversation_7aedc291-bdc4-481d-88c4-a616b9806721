<template>
  <!-- 支付数据 -->
  <div class="content">
    <div class="payData">
      <div class="payData-line">
        <div class="line-img">
          <img :src="datas.picture" alt="">
        </div>
        <div class="line-name">
          <div class="market-name">
            <div style="width: 220px;"> {{ datas.username }}</div>

            <van-tag
              v-if="datas.discount != 1 &&
                datas.discount != null &&
                datas.discount != 0
              "
              style="margin-left: 10px;"
              plain
              type="primary"
              color="#F9525E"
            >{{
              parseFloat((datas.discount * 10).toFixed(1)) + "折"
            }}</van-tag>
          </div>
          <div>
            <span class="region-name">{{ datas.regionName }}</span>
            <!-- <span class="region-name">遂昌县</span> -->
            <span class="line" />
            <span class="type">商户</span>
          </div>
        </div>
      </div>

      <!-- 含其他金额 -->
      <div v-if="payStatus">
        <div class="payData-line2">
          <div class="line2-left">
            <div>付款金额</div>
            <input
              v-model="payValue"
              type="number"
              placeholder="¥ 0.00"
              onkeyup="this.value = this.value.replace(/[^\d.]/g,'');"
              @blur="blurClick"
              @input="onClickInputKeyBoard()"
            >
          </div>
          <div class="line2-right">
            <div>
              <span>协议价</span>
              <!-- <span class="red">（不参与折扣）</span> -->
            </div>
            <input
              v-model="payValueElse"
              type="number"
              placeholder="¥0.00"
              @blur="blurClick"
              @input="onClickInputKeyBoard1()"
            >
          </div>
        </div>

        <div class="payData-line3">
          <div v-if="datas.marketType == 5" class="agreedPrice">
            <!-- <div class="agreedPrice"> -->
            <span>协议价</span>
            <van-switch v-model="payStatus" active-color="#45DB5E" size="25" />
          </div>
          <div class="totalpay">
            <span>实付：</span>
            <span class="yuan">¥</span>
            <span class="red">{{ sumNumber }}</span>
          </div>
        </div>
      </div>

      <!-- 不含其他金额 -->
      <div v-else>
        <div class="payData-line22">
          <div class="line2-left">
            <div>付款金额</div>
            <input
              v-model="payValue"
              type="number"
              placeholder="¥ 0.00"
              @blur="blurClick"
              @input="onClickInputKeyBoard()"
            >
          </div>
          <div class="line2-right">
            <div v-if="datas.marketType == 5" class="agreedPrice">
              <!-- <div class="agreedPrice"> -->
              <span class="mr">协议价</span>
              <van-switch v-model="payStatus" active-color="#45DB5E" size="25" />
            </div>
            <div class="totalpay">
              <span>实付: </span>
              <span class="yuan">¥</span>
              <span class="red"> {{ sumNumber }}</span>
            </div>
          </div>
        </div>
      </div>
      <!-- 优惠券 -->
      <Coupon v-if="isLoading" ref="restCoupon" />
    </div>

  </div>
</template>

<script>
import NP from 'number-precision'
import BigNumber from 'bignumber.js'
import Coupon from './coupon.vue'
import Maths from '@/utils/math.js'
export default {
  components: { Coupon },
  props: {
    datas: {
      type: Object,
      default: function() {
        return {}
      }
    }
  },
  data() {
    return {
      payStatus: false,
      payValue: '',
      payValueElse: '',
      isLoading: true
    }
  },
  computed: {
    sumNumber() {
      let self = this
      if (self.datas.discount == null) {
        let num = Number(self.payValue)
        num = num + Number(self.payValueElse)
        num = num.toFixed(2)
        if (self.payValue == 0.01) {
          num = 0.01
        }
        self.$store.state.scanPay.sum = NP.strip(num)
        if (this.$store.state.scanPay.coupon.couponId != null) {
          num = new Maths(num, this.$store.state.scanPay.coupon.preferentialAmount).minus()
        }
        if (this.$store.state.scanPay.coupon.couponId != null) {
          if (num >= this.$store.state.scanPay.coupon.useThreshold) {
            num = new Maths(num, this.$store.state.scanPay.coupon.preferentialAmount).minus()
            self.$store.state.scanPay.sum = num
          }
        }

        return NP.strip(num)
      } else {
        let num = 0
        let num1 = 0
        num1 = BigNumber(Number(self.payValue)).multipliedBy(
          self.datas.discount
        )
        num = BigNumber(num1).plus(Number(self.payValueElse))

        num = num.toFixed(2)
        if (self.payValue == 0.01) {
          num = 0.01
        }
        self.$store.state.scanPay.sum = NP.strip(num)
        if (this.$store.state.scanPay.coupon.couponId != null) {
          if (num >= this.$store.state.scanPay.coupon.useThreshold) {
            num = new Maths(num, this.$store.state.scanPay.coupon.preferentialAmount).minus()
            self.$store.state.scanPay.sum = num
          }
        }
        return NP.strip(num)
      }
    }
  },
  watch: {
    // 是否开始含其他金额
    payStatus(val) {
      if (val == false) {
        this.payValueElse = null
        this.$store.state.scanPay.payValueElse = ''
      }
    },
    payValue(val) {
      this.$refs.restCoupon.restCoupon(val)
      this.$store.state.scanPay.payValue = val
    },
    payValueElse(val) {
      this.$store.state.scanPay.payValueElse = val
    }
  },
  methods: {
    blurClick() {
      window.scroll(0, 0) // /页面滚动到顶部
    },
    // 限制小数输入位数
    onClickInputKeyBoard() {
      this.payValue = this.numberCheck(this.payValue)
    },
    onClickInputKeyBoard1() {
      this.payValueElse = this.numberCheck(this.payValueElse)
    },
    numberCheck(val) {
      var str = val
      var len1 = str.substr(0, 1)
      var len2 = str.substr(1, 1)
      if (str.length > 1 && len1 == 0 && len2 != '.') {
        str = str.substr(1, 1)
      }
      if (len1 == '.') {
        str = ''
      }
      if (str.indexOf('.') != -1) {
        var str_ = str.substr(str.indexOf('.') + 1)
        if (str_.indexOf('.') != -1) {
          str = str.substr(0, str.indexOf('.') + str_.indexOf('.') + 1)
        }
      }
      str = (str.match(/^\d*(\.?\d{0,2})/g)[0]) || null
      return str
    }
  }
}
</script>

<style lang="scss" scoped>
img {
  width: 100%;
  height: 100%;
}

.content {
  .payData {
    width: 710px;
    background: #ffffff;
    border-radius: 16px;
    box-sizing: border-box;
    overflow: hidden;
    margin: 20px auto;

    .payData-line {
      display: flex;
      padding: 42px 0 24px 20px;
      border-bottom: 1px solid #f4f4f4;

      .line-img {
        width: 108px;
        height: 108px;
        border-radius: 54px;
        overflow: hidden;
        background-color: #ccc;
        margin-right: 24px;
      }

      .line-name {
        color: #000010;

        .market-name {
          font-size: 40px;
          font-family: PingFangSC;
          color: #222222;
          margin-bottom: 10px;
        }

        >div {
          display: flex;
          align-items: center;
          font-size: 24px;
          // opacity: 0.7;

          .line {
            display: inline-block;
            margin: 0 18px;
            height: 24px;
            border: 1px solid #999;
          }

          .region-name,
          .type {
            font-size: 30px;
            font-family: PingFangSC;
            color: #999999;
          }
        }
      }
    }

    .payData-line2 {
      display: flex;
      justify-content: space-between;
      padding: 39px 0 24px 0;
      margin: 0 20px;
      font-size: 30px;
      font-family: PingFangSC;

      input {
        width: 250px;
        font-size: 44px;
        border: none;
      }

      .line2-left {
        display: flex;
        flex-direction: column;
        justify-content: space-between;

        input {
          margin-top: 26px;
        }

        input::placeholder {
          color: #999;
        }
      }

      .line2-right {
        input {
          margin-top: 26px;
        }

        input::placeholder {
          color: #999;
        }
      }
    }

    .payData-line22 {
      display: flex;
      justify-content: space-between;
      padding: 39px 0 24px 0;
      margin: 0 20px;
      font-size: 30px;
      font-family: PingFangSC;

      input {
        width: 250px;
        font-size: 44px;
        border: none;
      }

      .line2-left {
        display: flex;
        flex-direction: column;
        justify-content: space-between;

        input {
          margin-top: 26px;
        }

        input::placeholder {
          color: #999;
        }
      }

      .line2-right {
        display: flex;
        flex-direction: column;
        justify-content: space-between;

        .agreedPrice {
          display: flex;
          align-items: center;
        }

        .mr {
          margin-right: 24px;
        }

        input {
          margin-top: 26px;
        }

        input::placeholder {
          color: #ff301e;
        }
      }

      .totalpay {
        font-size: 28px;
        font-family: PingFangSC;
        color: #333333;
        display: flex;
        align-items: center;
      }
    }

    .payData-line3 {
      display: flex;
      flex-direction: column;
      align-items: flex-end;
      border-top: 1px solid #f4f4f4;
      padding-top: 39px;
      padding-right: 20px;
      padding-bottom: 24px;

      .agreedPrice {
        font-size: 30px;
        font-family: PingFangSC;
        color: #222222;
        display: flex;
        align-items: center;
        margin-bottom: 20px;

        >span {
          margin-right: 14px;
        }
      }

      .totalpay {
        font-size: 28px;
        font-family: PingFangSC;
        color: #333333;
        display: flex;
        align-items: center;
      }
    }
  }
}

.coupon {
  padding: 20px 20px;
  display: flex;
  justify-content: space-between;
  font-family: PingFangSC;
  font-size: 28px;

  .couponNum {
    color: #ff301e;
  }

  .couponNumRight {
    position: relative;
    top: 3px;
    left: 3px;
    color: #ff301e;
  }
}

.yuan {
  font-size: 38px;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  color: #ff301e;
  margin-right: 8px;
}

.red {
  color: #ff301e;
  font-size: 44px;
  font-family: PingFangSC-Medium;
  font-weight: 500;
}

.pop-title {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 117px;
  background-color: #f5f5f5;
  font-size: 34px;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  color: #333333;
  line-height: 48px;
  text-align: center;
  padding-top: 40px;
  z-index: 1;
}

.coupon-pop {
  background-color: #f5f5f5;
  width: 100%;
  height: 100%;
  overflow: scroll;
  margin: 0 auto;

  .pop-packet {
    font-size: 34px;
    font-family: PingFangSC-Medium;
    font-weight: 500;
    color: #333333;
    margin-left: 28px;
    padding-bottom: 18px;
  }

  .pop_card {
    width: 706px;
    height: 200px;
    background-color: #fff;
    border-radius: 16px;
    display: flex;
    justify-content: space-between;
    position: relative;
    margin: 0 auto;
    margin-bottom: 20px;

    .left {
      width: 180px;
      text-align: center;
      color: #FF301E;

      div:nth-of-type(1) {
        margin-top: 35px;
        font-family: PingFangSC-Medium;

        span:nth-of-type(1) {
          font-size: 26px;
        }

        span:nth-of-type(2) {
          font-size: 64px;
        }
      }

      div:nth-of-type(2) {
        font-size: 22px;
      }
    }

    .center {
      width: 400px;

      div:nth-of-type(1) {
        height: 35px;
        font-size: 35px;
        font-family: PingFangSC-Medium;
        color: #222222;
        margin-top: 40px;
      }

      div:nth-of-type(2) {
        height: 26px;
        font-size: 26px;
        color: #666666;
        margin-top: 13px;
        margin-bottom: 14px;
      }

      div:nth-of-type(3) {
        height: 23px;
        font-size: 22px;
        color: #999999;
      }
    }

    .right {
      width: 100px;
      text-align: center;
    }

    .coupon_tj {
      width: 64px;
      height: 40px;
      margin-top: 30px;
    }

    .coupon_radio {
      width: 40px;
      height: 20px;
      margin: 0 auto;
    }

    .coupon_radio_down {
      margin-top: 80px;
    }

    .coupon_zdlq {
      width: 200px;
      height: 42px;
      position: absolute;
      top: 0;
      left: 0;
    }

    .coupon_zbky {
      width: 130px;
      height: 42px;
      position: absolute;
      top: 0;
      right: 0;
    }

  }

  .pop_card_die {
    opacity: 0.4;
  }

  .coupon_bottom {
    width: 100%;
    position: fixed;
    bottom: 20px;

    .box {
      width: 678px;
      height: 94px;
      line-height: 94px;
      display: flex;
      margin: 0 auto;

      div:nth-of-type(1) {
        width: 486px;
        background: #0E0E0D;
        color: #fff;
        font-size: 26px;
        border-top-left-radius: 54px;
        border-bottom-left-radius: 54px;

        span:nth-of-type(1) {
          margin-left: 50px;
        }

        span:nth-of-type(2) {
          font-size: 32px;
        }
      }

      div:nth-of-type(2) {
        width: 192px;
        background: linear-gradient(90deg, #ff1e29, #ff5a25);
        color: #fff;
        font-size: 32px;
        font-family: PingFangSC-Medium;
        border-top-right-radius: 54px;
        border-bottom-right-radius: 54px;
        text-align: center;
      }
    }

  }

  .pop_card:last-child {
    margin-bottom: 34px;
  }
}

::v-deep .van-popup__close-icon--top-right {
  top: 42px;
}

::v-deep .van-popup__close-icon {
  color: #333;
}</style>
