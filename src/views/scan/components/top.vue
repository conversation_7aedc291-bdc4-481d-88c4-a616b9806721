<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2021-05-31 16:31:37
 * @LastEditors: zhaoyuxin
 * @LastEditTime: 2021-07-28 14:10:33
-->
<template>
  <div class="content">
    <div class="fixe">
      <van-nav-bar title="付款" left-text="" left-arrow :border="false">
        <template #left>
          <van-icon name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/user/icon/arrow-black.png" size="18" @click="goback" />
        </template>
      </van-nav-bar>
    </div>
    <div style="height:46px" />
  </div>
</template>

<script>
export default {
  components: {},
  data() {
    return {
      StatusHeight: localStorage.getItem('StatusHeight')
    }
  },
  mounted() {

  },
  methods: {
    goback() {
      this.$router.push('/index')
    }
  }
}
</script>

<style lang="scss" scoped>
	.content {
		.fixe{
			width: 100%;
			position: fixed;
			z-index: 9;
			background-color: #fff;
		}
		::v-deep .van-nav-bar__title {
			font-size: 36px;
      font-family: PingFangSC;
      color: #222222;
		}

	}
</style>
