<template>
  <div class="content">
    <div class="confirm">
      <van-button
        round
        block
        type="info"
        native-type="submit"
        @click="fastSetOrder"
      >提交</van-button>
    </div>
    <!-- 数字键盘 -->
    <van-popup
      v-model="show"
      round
      closeable
      :style="{
        width: '280px',
        height: '306px',
      }"
    >
      <div class="box">
        <div class="title">输入支付密码</div>
        <div style="" class="money">
          <span class="small">¥</span>{{ $store.state.scanPay.sum }}
        </div>
        <div class="balance">
          <div class="left">
            <van-icon name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/pay/balance.png" size="23" />
            <span>账户余额</span>
          </div>
          <div class="right">{{ ybalance }}元</div>
        </div>
        <van-password-input
          :value="password"
          info=""
          :length="6"
          :focused="showKeyboard"
          @focus="showKeyboard = true"
        />
      </div>
    </van-popup>
    <van-number-keyboard
      v-model="password"
      :show="showKeyboard"
      title="点滴安全键盘"
      z-index="9000"
      @blur="showKeyboard = false"
      @input="onInput"
      @delete="onDelete"
    />

    <!-- 验证码弹框 -->
    <van-popup v-model="codeLog" round :style="{ height: '130px', }">
      <div class="popBox">
        <van-field v-model="checkUnionPay.smsCode" center clearable :border="false" label="验证码" placeholder="请输入验证码" />
        <div class="popBtn">
          <div class="cancel" @click="codeLog = false">取消</div>
          <div class="confirm" @click="confirmFinishCheckUnionPay">确认</div>
        </div>
      </div>
    </van-popup>

    <!-- 安全键盘 -->
    <div v-if="false" id="app" @dblclick="() => {return false;}">
      <div class="cus-keyboard">
        <Keyboard ref="showKeyboardRef" :length="length" :default-val="defaultVal" :text.sync="password" :keyboard-type="1" />
      </div>
    </div>
  </div>
</template>

<script>
import { Pay, saveQrcodeOrder, getEncryptData } from '@/api/scan'
import { payLoading } from '@/api/takeout'
import { version } from '@/config/settings'
import { finishCheckUnionPay, fundAccountQuery } from '@/api/bank'
import { findUnionRcbAccount } from '@/api/bank/nsh'
import { fundAbcAccount } from '@/api/bank/abc'
import { Balance } from '@/api/scan'

import Keyboard from '@/components/Keyboard'
import { query } from '@/utils/sign'

import { DPay } from '@/modules'
// import Maths from '@/utils/math.js'
// import BigNumber from 'bignumber.js'

export default {
  components: { Keyboard },
  props: {
    type: { type: Number, default: 0 },
    receiveId: { type: Number, default: 0 },
    capitalAccountId: { type: Number, default: 0 },
    ybalance: { type: Number, default: 0 }
  },
  data() {
    return {
      show: false,
      password: '',
      showKeyboard: false,
      orderNo: '',
      clickNum: 0,
      codeLog: false,
      checkUnionPay: {
        smsId: '',
        smsCode: '',
        unionPayData: ''
      },
      defaultVal: '',
      length: 6
    }
  },
  watch: {
    password(val, old) {
      let self = this
      if (val.length >= 6) {
        this.$throttle(() => {
          self.saveQrcodeOrder()
        }, 1500)
      }
    },
    showKeyboard(val) {
      if (val === false) {
        // this.$refs.showKeyboardRef.hide()
        AlipayJSBridge.call('OffScreenshot', {}, function(result) {
          console.log(result)
        })
      }
    },
    show(val) {
      if (val === false) {
        // this.$refs.showKeyboardRef.hide()
        AlipayJSBridge.call('OffScreenshot', {}, function(result) {
          console.log(result)
        })
      }
    }
  },
  mounted() {},
  methods: {
    // 下单前检查
    fastSetOrder() {
      if (this.$store.state.scanPay.payradio == 7) {
        let data = {
          'userId': this.$store.getters.getUserId,
          'receiveId': this.receiveId
        }
        Balance(data).then((res) => {
          if (res.status == 200) {
            if (res.data.payChannel == 2) {
              // 检查是否开户成功
              if (this.$route.query.regionId == 3) {
                this.fundAccountLqQuery()
              } else if (this.$route.query.regionId == 1) {
                this.fundAccountQuery()
              } else if (this.$route.query.regionId == 6 || this.$route.query.regionId == 7) {
                this.jnFundAccountQuery()
              }
            } else {
              this.submit()
            }
          }
        })
      } else {
        this.submit()
      }
    },
    // 遂昌检查开户
    fundAccountQuery() {
      let queryVO = {
        'bankType': 'UnionPay',
        'userId': this.$store.getters.getUserId
      }
      fundAccountQuery(queryVO).then(res => {
        if (res.data != null) {
          if (res.data.status != 2) {
            this.$dialog.confirm({
              message: '请先完成开户流程！',
              confirmButtonText: '去开通',
              cancelButtonText: '暂不开通'
            }).then(() => {
              this.$router.push('/wallet')
            })
          } else {
            this.submit()
          }
        } else {
          this.$dialog.confirm({
            message: '请先完成开户流程！',
            confirmButtonText: '去开通',
            cancelButtonText: '暂不开通'
          }).then(() => {
            this.$router.push('/wallet')
          })
        }
      })
    },
    // 龙泉检查开户
    fundAccountLqQuery() {
      let data = {
        'userId': this.$store.getters.getUserId,
        'capitalAccountId': this.capitalAccountId
      }
      findUnionRcbAccount(data).then(res => {
        if (res.status == 200) {
          if (res.data != null) {
            if (res.data.status != 2) {
              this.$dialog.confirm({
                message: '请先完成开户流程！',
                confirmButtonText: '去开通',
                cancelButtonText: '暂不开通'
              }).then(() => {
                this.$router.push('/wallet')
              })
            } else {
              this.submit()
            }
          } else {
            this.$dialog.confirm({
              message: '请先完成开户流程！',
              confirmButtonText: '去开通',
              cancelButtonText: '暂不开通'
            }).then(() => {
              this.$router.push('/wallet')
            })
          }
        }
      })
    },
    // 景宁
    jnFundAccountQuery() {
      let queryVO = {
        'capitalAccountId': this.capitalAccountId,
        'userId': this.$store.getters.getUserId
      }
      fundAbcAccount(queryVO).then(res => {
        if (res.data != null) {
          if (res.data.status != 4) {
            this.$dialog.confirm({
              message: '请先完成开户流程！',
              confirmButtonText: '去开通',
              cancelButtonText: '暂不开通'
            }).then(() => {
              this.$router.push('/wallet')
            })
          } else {
            this.submit()
          }
        } else {
          this.$dialog.confirm({
            message: '请先完成开户流程！',
            confirmButtonText: '去开通',
            cancelButtonText: '暂不开通'
          }).then(() => {
            this.$router.push('/wallet')
          })
        }
      })
    },
    // 下单
    saveQrcodeOrder() {
      let amount = this.$store.state.scanPay.payValue
      // if (this.$store.state.scanPay.discount != null) {
      //   amount = BigNumber(Number(amount)).multipliedBy(
      //     this.$store.state.scanPay.discount
      //   )
      // }
      // if (this.$store.state.scanPay.coupon.couponId != null) {
      //   amount = new Maths(amount, this.$store.state.scanPay.coupon.preferentialAmount).minus()
      // }

      let data = {
        type: this.type,
        payPassword: this.password,
        amount:
        amount == ''
          ? 0
          : amount,
        noDiscountAmount:
          this.$store.state.scanPay.payValueElse == ''
            ? 0
            : this.$store.state.scanPay.payValueElse,
        receiveId: this.receiveId,
        capitalAccountId: this.capitalAccountId,
        paymentType: this.$store.state.scanPay.payradio,
        qrCode: this.$route.query.code,
        couponsId: this.$store.state.scanPay.coupon.couponId != null ? this.$store.state.scanPay.coupon.couponId : ''
      }
      this.$toast.loading({
        duration: 0,
        forbidClick: true,
        message: '下单中'
      })
      saveQrcodeOrder(data).then(res => {
        if (res.status == 200) {
          this.orderNo = res.data.orderNo
          this.pay()
        } else {
          this.password = ''
        }
      })
    },

    submit() {
      let self = this
      self.password = ''
      if (this.$store.state.scanPay.sum > 99999) {
        this.$toast('超过最大支付金额99999，请修改')
        return false
      }
      if (this.$store.state.scanPay.sum < 0.01) {
        this.$toast('支付金额必须大于0.01元')
        return false
      }
      if (this.$store.state.scanPay.payradio == '') {
        this.$toast('选择支付方式')
        return false
      }
      // 如果支付方式是余额支付则
      if (this.$store.state.scanPay.payradio == 1 || this.$store.state.scanPay.payradio == 7) {
        if (this.ybalance <= 0) {
          this.$toast('余额不足')
        } else {
          this.showKeyboard = true
          // this.$refs.showKeyboardRef.show()
          this.show = true
          AlipayJSBridge.call('OnScreenshot', {}, function(result) {
            console.log(result)
          })
        }
      } else {
        this.$throttle(() => {
          self.saveQrcodeOrder()
        }, 3000)
      }
    },
    pay() {
      let self = this
      let Base64 = require('js-base64').Base64
      let data = {
        type: self.type,
        amount:
          self.$store.state.scanPay.payValue == ''
            ? 0
            : self.$store.state.scanPay.payValue,
        noDiscountAmount:
          self.$store.state.scanPay.payValueElse == ''
            ? 0
            : self.$store.state.scanPay.payValueElse,
        receiveId: self.receiveId,
        capitalAccountId: self.capitalAccountId,
        paymentType: self.$store.state.scanPay.payradio,
        payPassword: Base64.encode(self.password),
        terminalSysVer: version, // 当前版本号,
        // eslint-disable-next-line no-undef
        clientIp: '127.0.0.1',
        'deviceId': localStorage.getItem('deviceId') ? localStorage.getItem('deviceId') : 'test',
        longitude: this.$store.getters.getLocation.longitude,
        latitude: this.$store.getters.getLocation.latitude,
        qrCode: this.$route.query.code,
        orderNo: this.orderNo
      }
      self.$toast.loading({
        duration: 0,
        forbidClick: true,
        message: '支付中'
      })

      // 加签
      if (data.noDiscountAmount === null) {
        delete data.noDiscountAmount
      }
      if (data.payPassword === null || data.payPassword === '') {
        delete data.payPassword
      }
      data.sign = query(data)

      Pay(data)
        .then((res) => {
          self.$toast.clear()
          if (res.status == 200) {
            // 余额支付
            if (self.$store.state.scanPay.payradio == 1 || self.$store.state.scanPay.payradio == 7) {
              this.showKeyboard = false
              // this.$refs.showKeyboardRef.hide()
              this.show = false
              this.password = ''
              // 强校验
              if (self.$store.state.scanPay.payradio == 7 && res.data.thirdPartPayData.resultCode == '00018') {
                this.checkUnionPay.smsCode = ''
                this.$toast('交易存在风险，请输入银联验证码进行确认')
                this.checkUnionPay.smsId = res.data.thirdPartPayData.smsId
                this.checkUnionPay.unionPayData = res.data.thirdPartPayData.unionPayData
                this.codeLog = true

                return
              }
              // 交易中
              if (self.$store.state.scanPay.payradio == 7 && res.data.thirdPartPayData.resultCode == '00004') {
                self.orderNo = res.data.orderNo
                const timerccb = window.setInterval(() => {
                  self.appPay()
                }, 2000)

                this.$toast.loading({
                  duration: 3000,
                  forbidClick: true,
                  message: '交易处理中...'
                })

                this.$once('hook:beforeDestroy', () => {
                  window.clearInterval(timerccb)
                })
                return
              }

              self.$toast('支付成功')
              this.showKeyboard = false
              this.show = false
              self.clear()
              // 跳转到支付成功的回调缺页面
              this.$store.state.tabbar.index = 2
              this.$router.push({
                name: 'Order'
              })
            } else if (self.$store.state.scanPay.payradio == 2) {
              // 支付宝支付
              DPay.getPay(self.$store.state.scanPay.payradio, res.data.thirdPartPayData.qrUrl, 0, res.data.orderNo)
            } else if (self.$store.state.scanPay.payradio == 3) {
              // 微信支付
              DPay.getPay(self.$store.state.scanPay.payradio, res.data.thirdPartPayData.pyTrnNo, 0, res.data.orderNo)
            } else if (self.$store.state.scanPay.payradio == 9) {
              // 云闪付
              DPay.getPay(self.$store.state.scanPay.payradio, res.data.thirdPartPayData.qrUrl, 0, res.data.orderNo)
            } else if (self.$store.state.scanPay.payradio == 11) {
              let urls = new URL(res.data.thirdPartPayData.cshdkUrl)

              let params = new URLSearchParams(urls.search)

              let tokenid = params.get('TOKEN')

              // 农行
              DPay.getPay(self.$store.state.scanPay.payradio, tokenid, 0, res.data.orderNo)
            } else if (self.$store.state.scanPay.payradio == 12) {
              self.orderNo = res.data.orderNo
              setTimeout(() => {
                self.okPayMess()
              }, 4000)
              self.getEncryptData(res.data.thirdPartPayData.cshdkUrl)
            }
          } else {
            self.$toast(res.message)
            self.password = ''
          }
        })
    },
    // 获取APP支付加密信息
    getEncryptData(cshdkUrl) {
      var u = navigator.userAgent
      var isiOS = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/) // ios终端
      getEncryptData({
        'order': cshdkUrl,
        'bundleId': isiOS ? 'com.randao.client' : 'plus.H57B9E083',
        'scheme': isiOS ? 'diandi' : 'plus.H57B9E083',
        'version': '1.0.0',
        'method': 'APP'
      }
      ).then(res => {
        if (res.status == 200) {
          console.log(res)
          AlipayJSBridge.call('startNsh', {
            openurl: res.data
          }, function(result) {})
        }
      })
    },
    // 清空
    clear() {
      this.$store.state.scanPay.sum = ''
      this.$store.state.scanPay.payValue = ''
      this.$store.state.scanPay.payValueElse = ''
      this.$store.state.scanPay.payradio = '1'
    },
    //   确认支付提示
    okPayMess() {
      let self = this
      this.$dialog
        .confirm({
          title: '是否支付完成？',
          message: ''
        })
        .then(() => {
          self.appPay()
        })
        .catch(() => {
          self.appPay()
        })
    },
    // 加查支付
    appPay() {
      let self = this
      payLoading(self.orderNo)
        .then(function(res) {
          if (res.status == 200) {
            if (res.data == true) {
              self.$toast('支付成功')
              self.$store.state.tabbar.index = 0
              self.$router.push({ name: 'Order' })
            } else {
              self.$toast('支付失败')
            }
          } else {
            self.$toast(res.message)
          }
        })
    },
    onInput(value) {
      //   Toast(value);
    },
    onDelete() {
      //   Toast('删除');
    },
    // 强校验
    confirmFinishCheckUnionPay() {
      finishCheckUnionPay(this.checkUnionPay).then(res => {
        if (res.status == 200) {
          if (res.data.resultCode == '00000') {
            this.$toast('支付成功')
            this.$store.state.tabbar.index = 2
            this.$router.push({
              name: 'Order'
            })
          } else {
            this.$toast('支付失败')
          }
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.content {
  .confirm {
    ::v-deep .van-button--block {
      width: 452px;
      height: 88px;
      font-size: 32px;
      font-family:PingFangSC-Medium;
      margin: 100px auto 0;
    }

    ::v-deep .van-button--round {
      border-radius: 8px;
      background: linear-gradient(45deg, #71d774 0%, #5ecc52 100%);
      border: 1px solid #5ecc52;
    }
  }

  .box {
    .title {
      text-align: center;
      width: 100%;
      box-sizing: border-box;
      font-size: 34px;
      font-family: PingFangSC;
      color: #222222;
      padding: 56px 0 48px 0;
    }
    .money {
      text-align:center;
      font-size:60px;
      margin-bottom:79px;
      font-family: PingFangSC;
      color: #222222;
    }
    .small {
      font-size: 36px;
      font-family: PingFangSC-Medium;
      font-weight: 500;
      color: #222222;
    }
    .balance {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin: 0 50px 48px;
      font-size: 34px;
      font-family: PingFangSC;
      .left {
        display: flex;
      justify-content: space-between;
      align-items: center;
      >span {
        margin-left: 10px;
      }
      }
      .right {
        font-size: 32px;
        color: #999999;
      }
    }
  }
}

::v-deep .van-popup__close-icon {
  font-size:30px;
}

::v-deep .van-popup__close-icon--top-right {
   top: 63px;
}

::v-deep[class*="van-hairline"]::after {
  border-color: #d5d5d5;
}

::v-deep .van-popup--center.van-popup--round {
  border-radius: 16px;
}

::v-deep .van-popup--center {
  top: 35%;
}
  .popBox {
  position: relative;
  width: 561px;
  height: 32px;
  padding-top:48px;
  box-sizing: border-box;

  .title {
    color: #7f7f87;
    font-size: 30px;
    padding-left: 42px;
  }

  ::v-deep .van-field__label{
    width: 100px;
    margin-right: 0;
  }
}
.popBtn {
  position: fixed;
  left: 0;
  bottom: 0px;
  display: flex;
  width: 561px;
  height: 97px;
  line-height: 97px;
  font-size: 32px;
  justify-content: space-between;
  border-top: 1px solid #cfcece;
  text-align: center;
  .cancel {
    width: 50%;
    border-right: 1px solid #cfcece;
    color: #999999;
  }
  .confirm {
    width: 50%;
    color: #6095f0;
  }
  }

</style>
