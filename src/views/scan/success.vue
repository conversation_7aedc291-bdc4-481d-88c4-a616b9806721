<template>
  <div class="home">
    <NavHeight bgc="#fff" />
    <div class="top" :style="'height:' + $store.getters.getStatusHeight + 'px'" />
    <van-nav-bar left-arrow>
      <template #left>
        <van-icon name="arrow-left" size="21" color="#000" @click="goback" />
      </template>
      <template #title>
        <span class="top-title">支付成功</span>
      </template>
    </van-nav-bar>

    <div class="center">
      <div>
        <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/user/pay/successpay.png" alt="">
      </div>
      <div class="money">
        <span class="symbol">￥</span>
        <span class="num">{{ $route.query.totalPay }}</span>
      </div>
    </div>

    <div class="order">
      <div>
        <span>支付方式：</span>
        <!-- <span>余额支付</span> -->
        <van-tag round type="primary" color="#FF7807" size="medium">余额支付</van-tag>
      </div>
      <div>
        <span>交易单号：</span>
        <span class="num">{{ $route.query.orderNo }}</span>
      </div>
      <div>
        <span>交易时间：</span>
        <span class="num">{{ $route.query.payTime }}</span>
      </div>
    </div>

    <div>
      <div class="back" @click="goback">
        返回
      </div>
      <div class="lookOrder" @click="goOrder">
        查看订单
      </div>
    </div>

  </div>
</template>

<script>
export default {
  components: {},
  data() {
    return {

    }
  },
  created() {},
  methods: {
    goback() {
      this.$router.push('/')
    },
    goOrder() {
      this.$router.push({ name: 'Order' })
    }
  }
}
</script>

<style lang="scss" scoped>
	.home {
		height: 100vh;
		background-color: #fff;

		.top {
			background-color: #fff;
		}
		.top-title{
			font-size: 0.34rem;
		}
		.center{
			width: 5.13rem;
			margin: 0 auto;
			text-align: center;
			border-bottom: 1px solid #E5E5E5;
			img{
				width:0.8rem;
				height:0.8rem;
				margin-top: 1.12rem;
			}
			.money{
				margin-top: 0.4rem;
				margin-bottom: 0.35rem;
			}
			.symbol{
				font-size: 0.5rem;
				margin-left: -0.3rem;
				position: relative;
				top: -0.08rem;
			}
			.num{
				font-size: 0.8rem;
			}
		}
		.order{
			width: 5.6rem;
			margin: 0 auto;
			margin-top: 0.86rem;

			div{
				margin-bottom: 0.2rem;
				span:nth-child(1){
					font-family: PingFang SC;
					font-size:0.28rem;
					font-weight:400;
					color: #333333;
				}
				.num{
					font-size: 24px;
				}
			}
		}
		.back{
			width: 5.6rem;
			height: 0.84rem;
			margin: 0 auto;
			text-align: center;
			background: linear-gradient(60deg, #71D774 0%, #5ECC52 100%);
			border: 2px solid #71D774;
			line-height: 0.84rem;
			color:#fff;
			font-weight: bold;
			border-radius: 0.12rem;
			margin-top: 0.64rem;
			font-size: 24px;
		}
		.lookOrder{
			width: 5.6rem;
			height: 0.8rem;
			margin: 0 auto;
			text-align: center;
			line-height: 0.8rem;
			color:#333333;
			border: 2px solid #999999;
			font-weight: bold;
			border-radius: 0.12rem;
			margin-top: 0.32rem;
			font-size: 24px;
		}
	}
</style>
