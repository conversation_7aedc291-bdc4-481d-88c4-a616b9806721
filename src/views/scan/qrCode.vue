<template>
  <div class="home">
    <NavHeight bgi="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/pay/codebj.png" />
    <div class="top">
      <!-- 头部 -->
      <div class="back">
        <van-icon
          class="bicon"
          name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/common/arrowlbft.png"
          size="18"
          @click="goback"
        />
      </div>
      <!-- 头像 -->
      <div class="avi">
        <van-image round width="75px" height="75px" :src="headImg" />
      </div>
      <!-- 付款码 -->
      <div class="qrcode">
        <div class="top_msg">
          <van-icon
            class="msgImg"
            name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/index/codeblack.png"
            size="18"
          />
          向商家付款
        </div>
        <div class="code">

          <qrcode :value="encodeStr" :options="qroptions" />
        </div>
        <div class="msg" @click="onRest()">
          每30秒自动更新一次,请在店消费时使用
          <span class="textRest" style="color: #000000;"><van-icon name="replay" class="rsticon" />刷新</span>
        </div>
        <div class="billList">
          <div class="billName">
            <van-icon class="msgImg" name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/pay/balance.png" size="18" />
            {{ regionName }}
            <span>(￥{{ blance }})</span>
          </div>
          <div v-if="columns.length!=0" class="billNext" @click="billShow = true">
            更换
          </div>
        </div>
      </div>
      <div v-if="false" class="scan">
        <div class="sacn_cent" @click="toHsScanCode">
          <div class="scanName">
            <van-icon name="scan" size="18" class="icon" />
            扫码支付
          </div>
          <div class="scanNext">
            <van-icon name="arrow" size="18" class="icon" />
          </div>
        </div>
      </div>
    </div>

    <!-- 钱包切换 -->
    <van-popup v-model="billShow" position="bottom">
      <van-picker show-toolbar :columns="columns" value-key="regionName" @confirm="onConfirm" @cancel="onCancel" />
    </van-popup>

    <div class="keyboard">
      <!-- 密码输入框 -->
      <van-popup v-model="showKeyboard" :close-on-click-overlay="false" round closeable :style="{ width: '300px', height: '200px' }" @click-close-icon="outClose">
        <div class="box">
          <div class="title">
            请输入支付密码
          </div>
          <div class="info">输入支付密码，以验证身份</div>
          <van-password-input :value="pwd" :focused="showKeyboard" class="inEt" @focus="showKeyboard = true" />
        </div>
      </van-popup>
      <!-- 数字键盘 -->
      <van-number-keyboard v-model="pwd" :show="showKeyboard" z-index="9000" />
    </div>

    <Loading :show="loadingShow" />
  </div>
</template>

<script>
import Loading from '@/components/Loading'
import {
  UserInfo,
  UserBalance
} from '@/api/my'
import {
  getPayment,
  getScan, checkOrderStatus
} from '@/api/scan'
import { checkInitPwd } from '@/api/takeout'
export default {
  components: {
    Loading
  },
  data() {
    return {
      billShow: false,
      columns: [],
      headImg: '',
      blance: 0,
      regionName: '',
      qroptions: {
        width: 200
      },
      pwd: '',
      showKeyboard: false,
      capitalAccountId: '',
      encodeStr: 'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/scan/delcode.png',
      tomerNum: 0,
      blanceIndex: 0,
      checkData: {
        encode: '',
        time: ''
      },
      checkNum: 0,
      loadingShow: true
    }
  },
  watch: {
    pwd(val) {
      if (val.length >= 6) {
        let data = {
          'payPwd': val,
          'capitalAccountId': this.capitalAccountId
        }
        this.getPayment(data)
      }
    },
    showKeyboard(val) {
      if (val == false) {
        this.pwd = ''
      }
    }
  },
  created() {
    this.checkInitPwd()
  },
  mounted() {
    AlipayJSBridge.call('OnScreenshot', {}, function(result) {
      console.log(result)
    })
  },
  destroyed() {
    AlipayJSBridge.call('OffScreenshot', {}, function(result) {
      console.log(result)
    })
  },
  methods: {
    // 获取账户信息
    getUserInfo() {
      let self = this
      let data = this.$store.getters.getUserId
      UserInfo(data).then((res) => {
        if (res.status == 200) {
          self.headImg = res.data.headImg
          // 获取用户余额
          self.getBalance()
        } else if (res.status == 401) {
          self.$store.state.userHeadImg = ''
          self.token = ''
        }
      })
    },
    // 获取钱包余额
    getBalance() {
      let self = this
      let data = this.$store.getters.getUserId
      UserBalance(data).then((res) => {
        self.loadingShow = false
        if (res.status == 200) {
          if (res.data.length == 0) {
            self.blance = 0
            this.$toast.fail('暂无资金账户')
            setTimeout(() => {
              self.$router.push('/')
            }, 2000)
          } else {
            let acc = res.data[0]
            res.data.map(item => {
              if (item.balance != 0) {
                acc = item
              }
            })
            self.blance = acc.balance
            self.regionName = acc.regionName
            self.columns = res.data
            self.capitalAccountId = acc.capitalAccountId
            // 请求付款码
            if (localStorage.getItem('payToken') == null) {
              this.showKeyboard = true
            } else {
              let data = {
                'payToken': localStorage.getItem('payToken'),
                'capitalAccountId': this.capitalAccountId
              }
              this.getPayment(data)
            }
          }
        }
      })
    },
    // 刷新钱包余额
    restMoney() {
      let self = this
      let data = this.$store.getters.getUserId
      UserBalance(data).then((res) => {
        if (res.status == 200) {
          if (res.data.length == 0) {
            self.blance = 0
            this.$toast.fail('暂无资金账户')
            setTimeout(() => {
              self.$router.push('/')
            }, 2000)
          } else {
            let acc = res.data[this.blanceIndex]
            self.blance = acc.balance
            self.regionName = acc.regionName
            self.columns = res.data
            self.capitalAccountId = acc.capitalAccountId
          }
        }
      })
    },
    outClose() {
      this.$store.state.tabbar.index = 0
      // console.log(this.$router.history.current.path)
      this.$router.push('/index')
    },
    // 手动刷新
    onRest() {
      this.tomerNum = 10
      this.restCode()
    },
    // 刷新二维码
    restCode() {
      if (localStorage.getItem('payToken') == null) {
        this.showKeyboard = true
      } else {
        let data = {
          'payToken': localStorage.getItem('payToken'),
          'capitalAccountId': this.capitalAccountId
        }
        this.getPayment(data)
        this.restMoney()
      }
    },
    // 获取付款码
    getPayment(row) {
      let self = this
      this.$toast.loading({
        duration: 0, // 持续展示 toast
        forbidClick: true,
        message: '请求中'
      })
      getPayment(row).then((res) => {
        this.$toast.clear()
        if (res.status == 200) {
          if (res.data != null && res.data.payToken != null) {
            localStorage.setItem('payToken', res.data.payToken)
          }

          this.encodeStr = res.data.encodeStr
          this.showKeyboard = false
          this.checkData.encode = localStorage.getItem('userId')

          var time = new Date()
          time = time.getFullYear() + '-' + (time.getMonth() + 1) + '-' + time.getDate() + ' ' + time.getHours() + ':' + time.getMinutes() + ':' + time.getSeconds()
          this.checkData.time = time
          this.checkOrderStatus()

          // 定时刷新
          if (this.tomerNum == 0) {
            const timer = window.setTimeout(() => {
              self.restCode()
            }, 30000)
            this.$once('hook:beforeDestroy', () => {
              window.clearTimeout(timer)
            })
          } else {
            this.tomerNum = 0
            // let highestTimeoutId = setTimeout(';')
            // for (var i = 0; i < highestTimeoutId; i++) {
            //   clearTimeout(i)
            // }
            self.restCode()
          }
        } else if (res.status == 10030) {
          // 余额不足
          this.encodeStr = res.message
          this.$toast.fail(res.message)
          this.showKeyboard = false
        } else if (res.status == 10010) {
          // 支付密码确认
          this.pwd = ''
          this.$toast.fail(res.message)
          this.showKeyboard = true
        } else if (res.status == 10011) {
          this.pwd = ''
          // 支付密码确认
          this.$toast.fail(res.message)
          this.showKeyboard = true
        } else if (res.status == 1001) {
          this.showKeyboard = false
          // 重置密码

          this.$dialog.alert({
            message: '请先重置密码'
          }).then(() => {
            self.$router.push('/editPayPwd')
          })
        } else {
          this.$toast.fail(res.message)
        }
      })
    },
    // 轮询是否支付成功
    checkOrderStatus() {
      let self = this
      const timers = window.setInterval(() => {
        self.checkNum = self.checkNum + 1
        if (self.checkNum > 25) {
          window.clearInterval(timers)
          return
        }
        checkOrderStatus(self.checkData).then(res => {
          if (res.message == 'success') {
            // self.$toast('支付成功');
            self.$router.push({
              path: '/scan/success',
              query: {
                orderNo: res.data.orderNo,
                totalPay: res.data.totalPay,
                payTime: res.data.payTime
              }
            })
          }
        })
      }, 1000)
      this.$once('hook:beforeDestroy', () => {
        window.clearInterval(timers)
      })
    },
    GetQueryString(name, code) {
      var reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)')
      let newurl = code.split('\?')
      newurl = '?' + newurl[1]
      var r = newurl.substr(1).match(reg)
      if (r != null) return unescape(r[2])
      return null
    },
    // 扫码(做好大区区分)
    toHsScanCode() {
      let self = this
      if (this.$store.getters.getUserId == null) {
        this.$router.push({ name: 'wxLogin2' })
      } else {
        AlipayJSBridge.call('QrCode', {}, function(result) { // 解析扫码数据
          let code = result.qr_code_url
          if (code.slice(0, 4) == 'http') {
            code = this.GetQueryString('code', result.qr_code_url)
          }
          let data = {
            code: code
          }
          getScan(data).then((res) => {
            if (res.status == 200) {
              self.$router.push({
                path: '/scan/index',
                query: {
                  code: code
                }
              })
            } else {
              self.$toast(res.message)
            }
          })
        })
      }
    },
    onConfirm(value, index) {
      this.blance = value.balance
      this.regionName = value.regionName
      this.capitalAccountId = value.capitalAccountId
      this.blanceIndex = index
      this.billShow = false
      if (localStorage.getItem('payToken') == null) {
        this.showKeyboard = true
      } else {
        let data = {
          'payToken': localStorage.getItem('payToken'),
          'capitalAccountId': this.capitalAccountId
        }
        this.tomerNum++
        this.getPayment(data)
      }
    },
    onCancel() {
      this.billShow = false
      console.log('取消')
    },
    goback() {
      this.$router.push('/index')
    },
    checkInitPwd() { // 检查初始支付密码
      let self = this
      checkInitPwd(this.$store.getters.getUserId).then(res => {
        if (res.status == 200) {
          if (res.data == true) {
            self.$dialog.alert({
              message: '当前为初始支付密码，请尽快修改'
            }).then(() => {
              self.$router.push('/editPayPwd')
            })
          } else {
            self.getUserInfo()
          }
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
	.home {
		height: 100vh;
		background-color: #fff;

		.top {
			width: 100%;
			height: 550px;
			background-image: url(https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/pay/codebj.png);
			background-size: 100%;
			padding-top: 28px;
			border-radius: 0 0 10px  10px;
		}

		.back {
			height: 80px;
			line-height: 80px;

			.bicon {
				margin-left: 30px;
			}
		}

		.avi {
			width: 150px;
			height: 150px;
			margin: 0 auto;
			position: relative;

			img {
				width: 150px;
				height: 150px;
				border-radius: 50%;
				box-shadow: 0 2px 4px rgba(0, 0, 0, .12), 0 0 6px rgba(0, 0, 0, .04);
			}
		}

		.box {
			text-align: center;
			.title {
				margin-top: 35px;
        font-size: 38px;
			}

			.info {
				font-size: 20px;
				margin-top: 30px;
			}
			.inEt{
				margin-top: 50px;
			}
		}

		.qrcode {
			width: 86%;
			min-height: 780px;
			height: auto;
			margin: 0 auto;
			border-radius: 10px;
			box-shadow: 0 2px 4px rgba(0, 0, 0, .12), 0 0 6px rgba(0, 0, 0, .04);
			margin-top: -.60px;
			overflow: hidden;
			background-color: #fff;

			.code {
				text-align: center;
				margin-top: 30px;

				.faceImg {
					width: 350px;
					height: 370px;
				}
			}

			.top_msg {
				text-align: center;
				margin-top: 110px;
        font-size: 36px;
				.msgImg {
					position: relative;
					top: 5px;
				}
			}

			.msg {
				text-align: center;
				margin-top:35px;
				color: #999;
				font-size: 25px;
				.testRest{
					margin-left: 5px;
					font-weight: bold;
					color: #000000;
				}
			}

			.billList {
				width: 93%;
				height: 100px;
				line-height: 100px;
				margin: 0 auto;
				margin-top: 50px;
				display: flex;
				justify-content: space-between;
				border-top: 1px solid #E0E0E0;

				.billName {
          display: flex;
          align-items: center;
					width: 80%;
          font-size: 30px;

					.msgImg {
						position: relative;
						top: .5px;
					}
				}

				.billNext {
					width: 20%;
					text-align: right;
					color: #67C23A;
          font-size: 30px;
				}
			}
		}

		.scan {
			width: 86%;
			height: 100px;
			line-height: 100px;
			border-radius: .10px;
			box-shadow: 0 2px 4px rgba(0, 0, 0, .12), 0 0 6px rgba(0, 0, 0, .04);

			margin: 0 auto;
			margin-top: .30px;
      font-size: 30px;

			.sacn_cent {
				width: 93%;
				display: flex;
				justify-content: space-between;
				margin: 0 auto;

				.scanName {
					width: 80%;

					.msgImg {
						position: relative;
						top: 5px;
					}
				}

				.scanNext {
					text-align: right;
					width: 20%;
				}

				.icon {
					position: relative;
					top: 7px;
				}
			}

		}

		.keyboard{
			::v-deep .van-popup--center {
			top: 35%;
			}
		}
		.rsticon{
			position: relative;
			top: 3px;
		}

	}
</style>
