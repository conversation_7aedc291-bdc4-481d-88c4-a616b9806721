<template>
  <div class="content">
    <NavHeight bgc="#fff" />
    <!-- 头部 -->
    <Top />
    <!-- 商户数据 -->
    <PayData v-if="isLoading" :datas="datas" />
    <!-- 支付方式 -->
    <Payment :pay-channel="payChannel" :pay-list="payList" :balance="balance" />
    <!-- 确认支付 -->
    <Confirm :ybalance="balance" :type="type" :receive-id="receiveId" :capital-account-id="capitalAccountId" />
    <!-- 默认密码修改 -->
  </div>
</template>

<script>
import Top from './components/top'
import Payment from './components/payment'
import PayData from './components/payData.vue'
import Confirm from './components/confirm.vue'
import {
  ScanCode,
  checkInitPwd,
  ScanCodeLq,
  Balance
} from '@/api/scan'
// import { findByRegionIdAndUserId } from '@/api/takeout'
import { fundAccountQuery } from '@/api/bank'
import { getPayStyle } from '@/api/pay'
import { findUnionRcbAccount } from '@/api/bank/nsh'

export default {
  components: {
    Top,
    Payment,
    PayData,
    Confirm
  },
  data() {
    return {
      code: '',
      datas: {},
      payList: [],
      balance: 0,
      type: 0,
      receiveId: 0,
      capitalAccountId: 0,
      payChannel: null,
      isLoading: false
    }
  },
  created() {
    this.$store.state.scanPay.coupon = {
      couponId: null
    }
    this.code = this.$route.query.code
  },
  mounted() {
    this.getScanCode()
    // 暂时关闭支付密码
    // this.checkInitPwd()
    this.$store.state.scanPay.payradio = ''
    this.$store.state.scanPay.payValue = null
    this.$store.state.scanPay.payValueElse = null
  },
  destroyed() {
    this.$store.state.scanPay.payradio = ''
    AlipayJSBridge.call('OffScreenshot', {}, function(result) {
      console.log(result)
    })
  },
  methods: {
    getScanCode() {
      let self = this
      if (this.$route.query.regionId == 1 || this.$route.query.regionId == 6 || this.$route.query.regionId == 7) {
        ScanCode(this.code)
          .then((res) => {
            if (res.status == 200) {
              self.datas = res.data
              self.type = self.datas.type
              self.receiveId = self.datas.receiveId
              self.$store.state.scanPay.receiveId = self.datas.receiveId

              // 扫码成功，查询余额
              self.getBalance()
              self.getPayStyle()
              self.isLoading = true
            }
          })
      } else { // ScanCodeLq
        ScanCodeLq(this.code)
          .then((res) => {
            if (res.status == 200) {
              self.datas = res.data
              self.type = self.datas.type
              self.receiveId = self.datas.receiveId
              self.$store.state.scanPay.receiveId = self.datas.receiveId
              self.$store.state.scanPay.discount = self.datas.discount
              //   扫码成功，查询余额
              self.getBalance()
              self.getPayStyle()
              self.isLoading = true
            }
          })
      }
    },
    // 获取支付方式
    getPayStyle() {
      getPayStyle({
        marketId: this.receiveId,
        orderType: 3,
        poolId: this.$route.query.regionId
      }).then(res => {
        if (res.status == 200) {
          this.payList = res.data
        }
      })
    },
    // 获取余额
    getBalance() {
      let self = this
      let data = {
        'userId': this.$store.getters.getUserId,
        'receiveId': self.receiveId
      }
      Balance(data)
        .then((res) => {
          if (res.status == 200) {
            self.balance = res.data.balance == null ? 0 : res.data.balance
            self.capitalAccountId = res.data.capitalAccountId
            this.payChannel = res.data.payChannel
            if (res.data.payChannel == 2) {
              // 检查是否开户成功
              // this.fundAccountQuery()
              if (this.$store.getters.getRegionId == 3) {
                this.findUnionRcbAccount(self.capitalAccountId)
              } else {
                // this.fundAccountQuery()
              }
            }
          } else {
            self.$toast(res.message)
          }
        })

      // let data = {
      //   'userId': this.$store.getters.getUserId,
      //   'regionId': this.$store.getters.getRegionId
      // }
      // findByRegionIdAndUserId(data)
      //   .then((res) => {
      //     if (res.status == 200) {
      //       self.balance = res.data.balance == null ? 0 : res.data.balance
      //       self.capitalAccountId = res.data.capitalAccountId
      //       this.payChannel = res.data.payChannel
      //       if (res.data.payChannel == 2) {
      //         // 检查是否开户成功
      //         // this.fundAccountQuery()
      //         if (this.$store.getters.getRegionId == 3) {
      //           this.findUnionRcbAccount(self.capitalAccountId)
      //         } else {
      //           this.fundAccountQuery()
      //         }
      //       }
      //     } else {
      //       self.$toast(res.message)
      //     }
      //   })
    },
    findUnionRcbAccount(val) {
      let data = {
        'userId': this.$store.getters.getUserId,
        'capitalAccountId': val
      }
      findUnionRcbAccount(data).then(res => {
        if (res.status == 200) {
          if (res.data == null) {
            this.nshType = 1
            return
          }
          if (res.data.status == 1) {
            this.nshType = 2
          } else {
            this.nshType = 3
          }
        }
      })
    },
    // 检查初始支付密码
    checkInitPwd() {
      let self = this
      checkInitPwd(this.$store.getters.getUserId).then((res) => {
        if (res.status == 200) {
          if (res.data == true) {
            self.$dialog.alert({
              message: '当前为初始支付密码，请尽快修改'
            }).then(() => {
              self.$router.push('/editPayPwd')
            })
          }
        }
      })
    },
    // 检查开户
    fundAccountQuery() {
      let queryVO = {
        'bankType': 'UnionPay',
        'userId': this.$store.getters.getUserId
      }
      fundAccountQuery(queryVO).then(res => {
        if (res.data != null) {
          if (res.data.status != 2) {
            this.$dialog.confirm({
              message: '请先完成开户流程！',
              confirmButtonText: '去开通',
              cancelButtonText: '暂不开通'
            }).then(() => {
              this.$router.push('/wallet')
            })
          }
        } else {
          this.$dialog.confirm({
            message: '请先完成开户流程！',
            confirmButtonText: '去开通',
            cancelButtonText: '暂不开通'
          }).then(() => {
            this.$router.push('/wallet')
          })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
