<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2021-10-27 10:29:43
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-02-07 15:35:07
-->
<template>
  <div class="home">
    <NavHeight bgc="#fff" />
    <Top />
    <div class="box" :style="styleVar">
      <iframe
        ref="vueIframe"
        class="iframe"
        :src="url"
        frameborder="0"
        scrolling="auto"
        @load="loaded"
      />
    </div>

  </div>
</template>

<script>
import Top from './components/top.vue'
export default {
  components: {
    Top
  },
  data() {
    return {
      url: this.$route.query.url,
      resultData: {
        token: localStorage.getItem('token')
      }
    }
  },
  computed: {
    styleVar() {
      return {
        '---nav-height': this.$store.getters.getStatusHeight + 'px'
      }
    }
  },
  mounted() {
    if (this.$route.query.type == 1) {
      this.iframeWin = this.$refs.vueIframe.contentWindow
    }
  },
  methods: {
    loaded() {
      if (this.$route.query.type == 1) {
        this.iframeWin.postMessage(this.resultData, '*')
      }
    }
  }
}
</script>

<style scoped lang="scss">
.home{
    .box{
        width: 100%;
        background-color: #fff;
        position: absolute;
        top: calc(100px + var(---nav-height));
        bottom: 0px;
        left: 0px;
    }
    .iframe{
      width: 100%;
      height: 100%;
    }
}
</style>
