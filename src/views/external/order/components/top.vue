<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-04-22 18:28:34
 * @LastEditors: 孙立政
 * @LastEditTime: 2021-12-09 09:45:34
-->
<template>
  <div class="content">
    <div class="statusTop">
      <van-nav-bar title="" left-text="" left-arrow>
        <template #title>
          <div>{{ message }}</div>
        </template>
        <template #left>
          <van-icon
            name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/common/arrowlwft.png"
            size="18"
            @click="goback"
          />
        </template>
        <template #right />
      </van-nav-bar>
    </div>
    <div style="height: 46px" />
  </div>
</template>

<script>
export default {
  components: {},
  props: {
    message: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
    }
  },
  mounted() {},
  methods: {
    goback() {
      this.$router.go(-1)
    }
  }
}
</script>

<style lang="scss" scoped>
.content {
  .statusTop {
    width: 100%;
    position: fixed;
    // top: 0;
    left: 0;
    z-index: 1;
    background-color: #fff;
  }

  ::v-deep .van-nav-bar__title {
    font-size: 34px;
    font-family: 萍方-简;
    color: #000010;
  }

  ::v-deep .van-nav-bar {
    background: none;
  }

  ::v-deep .van-nav-bar__right {
    font-size: 30px;
  }
}
</style>
