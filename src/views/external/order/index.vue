<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2021-12-09 09:39:37
 * @LastEditors: 孙立政
 * @LastEditTime: 2021-12-30 15:40:31
-->
<template>
  <div class="home">
    <NavHeight bgc="#fff" />
    <Top message="我的发布/预约" />
    <div class="nav">
      <div :class="actievIndex==0?'actiev':''" @click="goTab(0)">房屋租赁</div>
      <div :class="actievIndex==1?'actiev':''" @click="goTab(1)">家政服务</div>
      <div :class="actievIndex==2?'actiev':''" @click="goTab(2)">本地招聘</div>
    </div>
    <div class="box" :style="styleVar">
      <iframe
        v-show="actievIndex == 0"
        id="iframe"
        ref="vueIframe"
        :src="url"
        frameborder="0"
        scrolling="auto"
        @load="loaded"
      />
      <myOrder v-show="actievIndex == 1" class="ordercss" :style="styleVar" />
      <myOrder2 v-show="actievIndex == 2" class="ordercss" :style="styleVar" />
    </div>
  </div>
</template>

<script>
import { baseUrlOpen } from '@/config/settings'
import Top from './components/top'
import myOrder from '@/views/work/appointment/myOrder'
import myOrder2 from '@/views/work/appointment/myOrder2'
export default {
  components: {
    Top, myOrder, myOrder2
  },
  data() {
    return {
      url: baseUrlOpen + '/house/#/wodefabuA_1',
      actievIndex: 0,
      resultData: {
        token: localStorage.getItem('token')
      }
    }
  },
  computed: {
    styleVar() {
      return {
        '---nav-height': this.$store.getters.getStatusHeight + 'px'
      }
    }
  },
  created() {
    this.actievIndex = this.$route.query.type
  },
  mounted() {
    this.iframeWin = this.$refs.vueIframe.contentWindow
  },
  methods: {
    goTab(type) {
      this.actievIndex = type
    },
    loaded() {
      this.iframeWin.postMessage(this.resultData, '*')
    }
  }
}
</script>

<style scoped lang="scss">
.nav{
  width: 93%;
  margin: 0 auto;
  margin-top: 25px;
  display: flex;
  justify-content: space-between;
  div{
    width: 200px;
    height: 64px;
    line-height: 64px;
    text-align: center;
    font-size: 26px;
    background: #ecedf0;
    border-radius: 8px;
  }
  .actiev{
    background: #6bd369;
    color: #fff;
  }
}
.box{
    width: 100%;
    // background-color: #fff;
    overflow-y: auto;
    position: absolute;
    top: calc(220px + var(---nav-height));
    bottom: 0px;
    left: 0px;
    .ordercss{
      margin-top: -80px;
    }
}
#iframe{
    width: 100%;
    height: 100%
}
</style>
