<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2022-01-12 22:27:07
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-01-14 11:12:51
-->
<template>
  <div class="home">
    <NavHeight bgc="#fff" />
    <Top />
    <div v-if="$route.query.type == 1">
      <div class="bj1" />
      <div class="res">
        <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/external/ccb/res4.png" alt="">
      </div>
      <div class="btn" @click="goTo(1)">
        <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/external/ccb/btn1.png" alt="">
      </div>
    </div>
    <div v-if="$route.query.type == 2">
      <div class="bj2" />
      <div class="res">
        <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/external/ccb/res2.png" alt="">
      </div>
      <div class="btn">
        <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/external/ccb/btn2.png" alt="">
      </div>
    </div>
    <div v-if="$route.query.type == 3">
      <div class="bj3" />
      <div class="res">
        <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/external/ccb/res3.png" alt="">
      </div>
    </div>
  </div>
</template>

<script>
import Top from './components/top.vue'
export default {
  components: {
    Top
  },
  data() {
    return {

    }
  },
  created() {

  },
  mounted() {

  },
  methods: {
    goTo(val) {
      if (val == 1) {
        this.$router.push('/')
      } else if (val == 2) {
        this.$router.push({
          name: 'Webview',
          query: {
            url: 'https://yunbusiness.ccb.com/gbchannel/e_report/outercoupon/#/CustomerActivity/20210525010000000270/ZF0000827982x',
            type: 2,
            message: '注册拿好礼'
          }
        })
      }
    }
  }
}
</script>

<style scoped lang="scss">
    .home {
        img{
            width: 100%;
            height: 100%;
        }
        .bj1{
            width: 100%;
            height: 238px;
            background-image: url(https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/external/ccb/bj1.png);
            background-size: 100% 100%;
            background-repeat: no-repeat;
        }
        .bj2{
            width: 100%;
            height: 238px;
            background-image: url(https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/external/ccb/bj2.png);
            background-size: 100% 100%;
            background-repeat: no-repeat;
        }
        .bj3{
            width: 100%;
            height: 238px;
            background-image: url(https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/external/ccb/bj3.png);
            background-size: 100% 100%;
            background-repeat: no-repeat;
        }
        .res{
            width: 690px;
            margin: 0 auto;
            margin-top: -180px;
        }
        .btn{
            width: 646px;
            margin: 0 auto;
            margin-top: 90px;
        }
    }
</style>
