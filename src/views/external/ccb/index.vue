<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2022-01-12 21:54:09
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-01-13 16:32:08
-->
<template>
  <div class="home">
    <NavHeight bgc="#fff" />
    <Top />
    <div class="box">
      <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/external/ccb/index1.png" alt="">
    </div>
    <div class="bottom">
      <div class="form">
        <div>您的姓名：</div>
        <div class="input">
          <van-field v-model="value" maxlength="4" placeholder="真实姓名才可以报名成功哦" />
        </div>
      </div>
      <div v-if="isMsg" class="ref">
        <van-icon name="info-o" />
        <div>
          请输入正确姓名
        </div>
      </div>
      <div class="btn" @click="updateUserName">
        <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/external/ccb/index3.png" alt="">
      </div>
    </div>
  </div>
</template>

<script>
import Top from './components/top.vue'
import { updateUserName } from '@/api/external/bankActieve'
import { isChina } from '@/utils/validate'
export default {
  components: {
    Top
  },
  data() {
    return {
      value: '',
      isMsg: false
    }
  },
  created() {

  },
  mounted() {

  },
  methods: {
    updateUserName() {
      if (this.value.length < 2 || this.value.length > 4) {
        this.$toast('请正确填写姓名')
        this.isMsg = true
        return
      }
      if (this.value == '') {
        this.$toast('请正确填写姓名')
        this.isMsg = true
        return
      }
      if (!isChina(this.value)) {
        this.$toast('请正确填写姓名')
        this.isMsg = true
        return
      }
      updateUserName(this.value).then(res => {
        if (res.status == 200) {
          this.$toast('操作成功')
          this.$router.push({
            name: 'CcbResult',
            query: {
              type: 1
            }
          })
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
    .home {
        height: 100vh;
        background-image: url(https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/external/ccb/index2.png);
        background-size: 100% 100%;
        background-repeat: no-repeat;
        overflow-y: hidden;
        .box{
            width: 100%;
            height: 960px;
            img{
                width: 100%;
                height: 100%;
            }
        }
        .bottom{
            width: 100%;
            height: 500px;
            background-image: url(https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/external/ccb/index2.png);
            background-size: 100% 100%;
            background-repeat: no-repeat;
            .form{
                width: 610px;
                margin: 0 auto;
                display: flex;
                color: #FFFFFF;
                font-size: 34px;
                height: 98px;
                line-height: 98px;
                margin-top: 50px;
                .input{
                    width: 440px;
                    height: 98px;
                    background: #ffffff;
                    border-radius: 10px;
                    margin-left: 10px;
                    .van-cell{
                        border-radius: 10px;
                        margin-top: 8px;
                        ::v-deep input::-webkit-input-placeholder{
                            font-size:34px;
                        }
                    }
                }

            }
            .ref{
              color: #ffffff;
              font-size: 26px;
              margin-left: 220px;
              margin-top: 20px;
              display: flex;
              div{
                margin-left: 10px;
                margin-top: -5px;
              }
            }
            .btn{
                width: 646px;
                margin: 0 auto;
                margin-top: 62px;
                img{
                    width: 100%;
                    height: 100%;
                }
            }
        }
    }
</style>
