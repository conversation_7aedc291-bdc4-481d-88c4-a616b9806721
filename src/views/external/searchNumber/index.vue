<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2021-11-15 14:35:14
 * @LastEditors: 孙立政
 * @LastEditTime: 2021-12-30 21:52:11
-->
<template>
  <div class="home">
    <NavHeight bgc="#2B72F6" />
    <div class="top">
      <div class="nav">
        <div @click="goBack">
          <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/user/classify/arrow-left-white.png" alt="">
        </div>
        <div>便民导航</div>
        <div />
      </div>
      <div class="search">
        <van-search v-model="keyword" clear left-icon="" placeholder="输入企业单位名称" show-action @search="goSearch">
          <template #action>
            <div class="searchBtn" @click.stop="goSearch">搜索</div>
          </template>
        </van-search>
      </div>

    </div>

    <ul class="list">
      <li v-for="item in itemList" :key="item.id" class="item">
        <div class="itemLeft">
          <div class="itemTitle">{{ item.companyName }}</div>
          <div class="itemMsg">
            <div class="itemMsgLeft">工作时间：</div>
            <div class="itemMsgRight">{{ item.workTime==''?'--':item.workTime }}</div>
          </div>
        </div>
        <div class="itemRight" @click="goToMap(item)">
          <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/external/telphone/lamap.png" alt="">
          导航
        </div>
        <div class="itemRight" @click="onOverlay(item)">
          <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/external/telphone/lphone.png" alt="">
          电话
        </div>
      </li>
      <div class="list-end">
        没有更多了~
      </div>
      <van-empty v-if="itemList.length == 0" description="暂未查询到相关结果" />
    </ul>

    <!-- 拨号弹出 -->
    <van-overlay :show="overlay" z-index="999" @click="overlay = false">
      <div class="wrapper">
        <van-popup v-model="overlay" :overlay="false" position="bottom">
          <div class="item" @click.stop="goTel">{{ phone }}</div>
          <div class="item" @click.stop="overlay = false">取消</div>
        </van-popup>
      </div>
    </van-overlay>
    <!-- 空状态 -->
    <div v-if="false" class="null">
      <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/external/null.png" alt="">
    </div>

    <!-- 拨号后弹出 -->
    <van-overlay :show="overlayTelOver" z-index="999">
      <div class="telOver">
        <div class="top">
          <div class="title" />
          <div class="item" @click="updateNumberMarking('空号')">空号</div>
          <div class="item" @click="updateNumberMarking('号码和公司不一致')">号码和公司不一致</div>
          <div class="item" @click="updateNumberMarking('推销/诈骗/不安全号码')">推销/诈骗/不安全号码</div>
        </div>
        <div class="btn" @click="goTel()">
          <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/external/btn.png" alt="">
        </div>
        <div class="close" @click="overlayTelOver = false">
          <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/external/close.png" alt="">
        </div>
      </div>
    </van-overlay>

  </div>
</template>

<script>
import { findAll, updateCallsById, updateNumberMarking } from '@/api/external/searchNumber'
// var sensors = window['sensorsDataAnalytic201505']
export default {
  data() {
    return {
      overlay: false,
      overlayTelOver: false,
      keyword: '',
      phone: '',
      query: {
        'phone': '', // 手机号精确查询
        'companyName': '', // 单位名称模糊查询
        'regionId': this.$store.getters.getRegionId, // 大区id
        'pageNum': 1, // 分页
        'pageSize': 10000
      },
      itemList: [],
      phoneId: null
    }
  },
  watch: {
    keyword: function(val) {
      if (val == '') {
        this.query.phone = ''
        this.query.companyName = ''
      }
    }
  },
  created() {
    this.findAll()
  },
  methods: {
    goBack() {
      this.$router.go(-1)
    },
    findAll() {
      this.$toast.loading({
        message: '',
        duration: 0,
        forbidClick: true
      })
      findAll(this.query).then(res => {
        this.$toast.clear()
        if (res.status == 200) {
          this.itemList = res.data.list
        }
      })
    },
    // 搜索
    goSearch() {
      let regPone = null
      let mobile = /^1(3|4|5|6|7|8|9)\d{9}$/ // 最新16手机正则
      let tel = /^(0[0-9]{2,3}\-)([2-9][0-9]{4,7})+(\-[0-9]{1,4})?$/ // 座机
      if (this.keyword.charAt(0) == 0) { // charAt查找第一个字符方法，用来判断输入的是座机还是手机号
        regPone = tel
      } else {
        regPone = mobile
      }
      if (regPone.test(this.keyword)) {
        this.query.phone = this.keyword
        this.findAll()
      } else {
        this.query.companyName = this.keyword
        this.findAll()
      }

      // sensors.track('BuyProduct', {
      //   customData: 'search',
      //   custom: 1
      // })
    },
    // 打开谈话旷
    onOverlay(item) {
      // this.overlay = true
      this.phone = item.phone
      this.phoneId = item.id
      setTimeout(() => {
        this.overlayTelOver = true
      }, 3000)
      AlipayJSBridge.call('CallPhone', {
        phoneNum: this.phone
      }, function(result) {})
      // this.goTel()
    },
    // 打电话
    goTel() {
      // 'https://m.amap.com/search/mapview/keywords=' + this.data.detailedAddress + '&cluster_state=5&pagenum=1'
      updateCallsById(this.phoneId).then(res => {
        this.overlayTelOver = false
      })
      // sensors.track('BuyProduct', {
      //   customData: 'search-ok',
      //   custom: 1
      // })
    },
    goToMap(item) {
      this.$router.push({
        name: 'Webview',
        query: {
          url: 'https://m.amap.com/search/mapview/keywords=' + item.companyName + '&cluster_state=5&pagenum=1',
          type: 2,
          message: '便民导航'
        }
      })
    },
    // 标记
    updateNumberMarking(val) {
      let data = {
        id: this.phoneId,
        markReason: val
      }
      updateNumberMarking(data).then(res => {
        if (res.status == 200) {
          this.$toast('操作成功')
          this.overlayTelOver = false
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
.home {

    .top{
        width: 100%;
        height: 248px;
        position:relative;
        background-color: #2B72F6;
        .nav{
            display: flex;
            justify-content: space-between;
            text-align: center;
            div{
                width: 33%;
                font-size: 36px;
                color: #ffffff;
            }
            img{
                width: 40px;
                height: 40px;
                float: left;
                margin-left: 35px;
                margin-top: 5px;
            }
        }
        .search{
            width: 706px;
            height: 88px;
            background: #ffffff;
            border-radius: 4px;
            margin: 0 auto;
            margin-top: 44px;
            overflow: hidden;
            .van-search{
               padding:0px;
               margin-top: 12px;
            }
            .van-search__content{
                background-color: #fff;
                .van-cell{
                    font-size: 34px;
                }
            }
            .searchBtn{
                width: 108px;
                height: 68px;
                background: #39cf3f;
                border-radius: 4px;
                color: #fff;
                font-size: 30px;
                text-align: center;
                line-height: 68px;
            }
        }
    }
    .list{
        width: 100%;
        position:absolute;
        top:278px;
        bottom:0;
        overflow-y: auto;
        background-color: #fff;
        .item{
            width: 680px;
            min-height: 155px;
            margin: 0 auto;
            border-bottom: 1px solid #F4F4F4;
            display: flex;
            .itemLeft{
                width: 597px;
                .itemTitle{
                    font-size: 32px;
                    font-family: PingFangSC;
                    color: #333333;
                    margin-top: 32px;
                    word-break: break-all;
                }
                .itemMsg{
                  height: 50px;
                  line-height: 50px;
                    font-size: 26px;
                    color: #999999;
                    display: flex;
                    margin-top: 13px;
                    .itemMsgLeft{
                        width: 23%;
                    }
                    .itemMsgRight{
                        width: 75%;
                    }
                }
            }
            .itemRight{
                width: 105px;
                text-align: center;
                img{
                    width: 38px;
                    height: 38px;
                    margin-top: 50px;
                    display: block;
                    margin: 0 auto;
                    margin-top: 43px;
                    margin-bottom: 10px;
                }
                font-size: 20px;
                color: #bbbbbb;
            }
        }
        .list-end{
          text-align: center;
          font-size: 32px;
          height: 100px;
          line-height: 100px;
          color: #999;
        }
    }
    .wrapper{
        width: 100%;
        position: fixed;
        bottom: 30px;
        .item{
            width: 710px;
            height: 100px;
            margin: 0 auto;
            background: #ffffff;
            border-radius: 24px;
            color: #3488FF;
            line-height: 100px;
            text-align: center;
            margin-bottom: 20px;
            font-size: 36px;
        }
        .van-popup{
            background-color: rgba(0,0,0,0);
        }
    }
    .null{
        text-align: center;
        img{
            width: 336px;
            height: 282px;
            margin-top: 170px;
        }
    }
    .telOver{
        // display: flex;
        // align-items: center;
        // justify-content: center;
        width: 602px;
        height: 100%;
        text-align: center;
        margin: 0 auto;
        margin-top: 30%;
        .top{
            width: 602px;
            height: 672px;
            background-image: url(https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/external/top.png);
            background-size: 100% 100%;
            overflow: hidden;
            .title{
                width: 480px;
                margin: 0 auto;
                margin-top: 235px;
                font-size: 30px;
                color: #333333;
            }
            .item{
                width: 480px;
                height: 84px;
                border: 2px solid #cbcbcb;
                border-radius: 44px;
                font-size: 32px;
                color: #666666;
                line-height: 84px;
                margin: 0 auto;
                margin-top: 24px;
            }
        }
        .btn{
            margin-top: 16px;
            img{
                width: 602px;
                height: 104px;
            }
        }
        .close{
            margin-top: 53px;
            img{
                width: 216px;
                height: 216px;
            }
        }
    }
}
</style>
