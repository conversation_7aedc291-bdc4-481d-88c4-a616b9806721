<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2021-12-09 16:57:09
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-12-08 13:55:45
-->
<template>
  <div class="homes">
    <NavHeight bgc="#fff" />
    <Top message="本地生活" />
    <div>
      <ul class="classifyD">
        <li
          v-for="(item, index) in listD"
          :key="index"
          @click="goToD(item, index)"
        >
          <div>
            <img :src="item.icon" alt="">
          </div>
          <div class="iconName">{{ item.name }}</div>
        </li>
        <li />
      </ul>
    </div>
    <div class="ad">
      <div class="title">
        <!-- 为你推荐 -->
        为你推荐
      </div>
      <!-- ccb广告 -->
      <CCB v-if="true" />
      <div class="adImgs">
        <img
          src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/open/index/ad/yad1.jpg"
          alt=""
          @click="goToD('', 8)"
        >
        <img
          src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/open/index/ad/yad2.jpg"
          alt=""
          @click="goToD('', 1)"
        >
      </div>
    </div>
  </div>
</template>

<script>
import { baseUrlOpen } from '@/config/settings'
import { activityNo } from '@/config/die'
import Top from './components/top'
// ccb广告
import CCB from '@/modules/CCB/index.vue'
export default {
  components: {
    Top, CCB
  },
  data() {
    return {
      listD: [
        // {
        //   icon:
        //     'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/open/index/icon/icon1.png',
        //   name: '点滴超市'
        // },
        {
          icon:
            'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/home/<USER>/classify/sc.png',
          name: '拼团'
        },
        {
          icon:
            'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/open/index/icon/icon2.png',
          name: '助农集市'
        },
        {
          icon:
            'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/open/index/icon/icon3.png',
          name: '智慧影院'
        },
        {
          icon:
            'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/open/index/icon/icon4.png',
          name: '智慧缴费'
        },
        {
          icon:
            'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/open/index/icon/icon5.png',
          name: '房屋租赁'
        },
        {
          icon:
            'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/open/index/icon/icon6.png',
          name: '家政服务'
        },
        // {
        //   icon:
        //     'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/open/index/icon/icon7.png',
        //   name: '本地招聘'
        // },
        {
          icon:
            'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/open/index/icon/icon8.png',
          name: '便民导航'
        }
      ]
    }
  },
  methods: {
    goToD(item, index) {
      // let userID = this.$store.getters.getUserId
      if (index == 0) {
        // if (userID != 3402 && userID != 11485 && userID != 11721 && userID != 11949 && userID != 18302 && userID != 3393) {
        //   this.$toast('即将上线，敬请期待')
        //   return
        // }
        // this.$router.push({
        //   path: '/Classify',
        //   query: {
        //     id: 73
        //     // id: 62
        //   }
        // })
        this.$router.push({
          name: 'MarketPt',
          query: {
            activityNo: activityNo
          }
        })
      } else if (index == 8) {
        this.$router.push({
          path: '/Classify',
          query: {
            id: 73
          }
        })
      } else if (index == 1) {
        // if (userID != 3402 && userID != 11485 && userID != 11721 && userID != 11949 && userID != 18302 && userID != 3393) {
        //   this.$toast('即将上线，敬请期待')
        //   return
        // }
        this.$store.state.cart.cartData[0].goodsList = []
        this.$store.state.market.marketData.remark = ''
        this.$router.push({
          name: 'ShoppingMallIndex',
          query: {
            id: 1138
            // id: 880
          }
        })
      } else if (index == 3) {
        this.$router.push({
          name: 'Webview',
          query: {
            url: 'https://cloud.life.ccb.com/live/paymentManage_wechat.jhtml',
            type: 2,
            message: '智慧缴费'
          }
        })
      } else if (index == 4) {
        this.$router.push({
          name: 'Webview',
          query: {
            url: baseUrlOpen + '/house/',
            type: 1,
            message: '房屋租赁'
          }
        })
      } else if (index == 5) {
        this.$router.push({
          path: '/work/list/listClean',
          query: {
            type: 1,
            isMoudel: 1
          }
        })
      } else if (index == 6) {
        // this.$router.push({
        //   path: '/work/list/listRecru',
        //   query: {
        //     type: 2,
        //     isMoudel: 2
        //   }
        // })
        this.$router.push({ name: 'SearchNumber' })
      } else if (index == 7) {
        this.$router.push({ name: 'SearchNumber' })
      } else if (index == 2) {
        this.$router.push({
          name: 'Webview',
          query: {
            url: 'https://m.qianzhu8.com/cinema/main/movie?platformId=10390',
            type: 2,
            message: '智慧影院'
          }
        })
      }
    }
  }
}
</script>

<style scoped lang="scss">
.homes {
  .classifyD {
    width: 690px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    margin-top: 21px;
    background-color: #fff;
    padding: 10px;
    padding-top: 30px;
    border-radius: 16px;
    li {
      width: 25%;
      flex-shrink: 0;
      text-align: center;
      margin-bottom: 25px;
      img {
        width: 92px;
        height: 92px;
      }
      font-size: 24px;
      font-family: PingFangSC;
      font-weight: 400;
      color: #333333;
      .iconName {
        margin-top: 10px;
      }
    }
  }
  .ad {
    width: 710px;
    margin: 0 auto;
    background-color: #fff;
    margin-top: 32px;
    border-radius: 16px;
    padding: 20px;
    .title {
      font-size: 36px;
      font-family: PingFangSC-Medium;
      margin-bottom: 23px;
    }
    .adImgs {
      text-align: center;
      img {
        width: 670px;
        height: 240px;
        margin-bottom: 30px;
      }
    }
  }
}
</style>
