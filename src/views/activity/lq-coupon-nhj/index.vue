<!--
 * @Descripttion: 龙泉优惠券
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2021-07-07 11:29:51
 * @LastEditors: 孙立政
 * @LastEditTime: 2023-08-11 17:08:02
-->
<template>
  <div class="home">
    <div class="back">
      <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/advertisement/back.png" alt="" @click="goBack">
    </div>
    <div v-if="isLoading">
      <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/home/<USER>/activity/20240130/nhuod0_01.png" alt="">
      <div class="count">
        <div v-if="endTime > 0" class="count_down">
          <span style="margin-left: 3px;">倒计时：</span>

          <van-count-down :time="seconds">
            <template #default="timeData">
              <div class="count_down_timeData">
                <span class="block">{{ formDate(timeData.hours + timeData.days * 24) }}</span>
                <span class="colon">:</span>
                <span class="block">{{ formDate(timeData.minutes) }}</span>
                <span class="colon">:</span>
                <span class="block">{{ formDate(timeData.seconds) }}</span>
              </div>
            </template>
          </van-count-down>

        </div>
        <div v-if="endTime <= 0" class="count_down">
          <span style="margin-left: 10px;">活 动 进 行 中</span>
        </div>

        <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/home/<USER>/activity/20240130/nhuod0_02.png" alt="">
      </div>
      <div class="coupon_list">
        <div v-for="(item,index) in couponList" :key="item.id" @click="pick(item)">
          <img v-if="index === 0 && item.quantity > 0" src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/home/<USER>/activity/20240130/nhuod0_03.jpg" alt="">
          <img v-if="index === 1 && item.quantity > 0" src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/home/<USER>/activity/20240130/nhuod0_04.jpg" alt="">
          <img v-if="index === 2 && item.quantity > 0" src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/home/<USER>/activity/20240130/nhuod0_05.jpg" alt="">
          <img v-if="index === 0 && item.quantity <= 0" class="coupon_hidden" src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/home/<USER>/activity/20240130/null_01.png" alt="">
          <img v-if="index === 1 && item.quantity <= 0" class="coupon_hidden" src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/home/<USER>/activity/20240130/null_02.png" alt="">
          <img v-if="index === 2 && item.quantity <= 0" class="coupon_hidden" src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/home/<USER>/activity/20240130/null_03.png" alt="">
        </div>
      </div>
      <div class="coupon_rule">
        <div class="rule_box">
          <div>活动规则 :</div>
          <div v-for="(item,index) in coupon_rules" :key="index">{{ item }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import logger from '@/utils/aliLog'
import { couponPick } from '@/api/coupon'
import { getPhpConfig } from '@/api/index'
import confetti from 'canvas-confetti'
export default {
  components: {},
  data() {
    return {
      time: 30 * 60 * 60 * 1000,
      goodsData: [],
      seconds: 0,
      isLoading: false,
      isShowOne: true,
      couponList: {},
      coupon_rules: [],
      endTime: 0
    }
  },
  created() {
    this.getPhpConfig()
  },
  mounted() {
    this.isLoading = true
    logger.sum('龙泉-新年活动')
  },
  methods: {
    // 获取php配置
    getPhpConfig() {
      let self = this
      getPhpConfig().then(res => {
        if (res.status === 200) {
          console.log(res.data)
          this.couponList = res.data.coupon
          this.coupon_rules = res.data.coupon_rules
          this.endTime = res.data.start_time
          this.seconds = res.data.start_time * 1000

          setInterval(() => {
            self.endTime = self.endTime - 1
          }, 1000)
        }
      })
    },
    formatNum(num) {
      return num.replace(/\.00/, '')
    },
    pick(item) {
      logger.sum('龙泉-新年活动-优惠券点击-' + item.id)
      if (this.endTime > 0) {
        this.$toast('活动暂未开始')
        return
      }
      if (item.quantity <= 0) {
        this.$toast('优惠券已领完')
        return
      }
      let self = this
      this.$toast.loading({
        duration: 0,
        forbidClick: true,
        message: ''
      })
      couponPick(item.id).then(res => {
        if (res.status === 200) {
          this.$toast('成功领取1张')

          confetti({
            particleCount: 100,
            spread: 70,
            origin: { y: 0.6 }
          })
        } else {
          self.$toast.clear()
          if (res.message == '今天领取次数已达到上限') {
            this.$toast('您已领取过该优惠券')
          }
          if (res.message == '用户领取优惠券失败：{"message":"优惠券已结束发放","status":500}') {
            this.$toast('优惠券已结束发放')
          }
          if (res.message == '用户领取优惠券失败：{\"message\":\"还没到优惠券领取时间，请等待正式发放后再领取\",\"status\":500}') {
            this.$toast('还没到优惠券领取时间，请等待正式发放后再领取')
          }
          if (res.message == '用户领取优惠券失败：{\"message\":\"今天领取次数已达到上限\",\"status\":500}') {
            this.$toast('今天领取次数已达到上限')
          }
        }
        setTimeout(() => {
          self.$toast.clear()
        }, 1000)
      })
    },
    formDate(num) {
      // 小于10补0
      return num < 10 ? '0' + num : num
    },
    goBack() {
      this.$router.go(-1)
    }
  }
}
</script>

<style scoped lang="scss">
.home {
  width: 100%;
  min-height: 100vh;
  img {
    width: 100%;
    display: block;
  }

  .back {
    width: 100%;
    position: fixed;
    top: 65px;
    left: 38px;

    img {
      width: 66px;
      height: 66px;
    }
  }

  .count {
    position: relative;

    .count_down {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 50px;
      color: #ffe95c;
      font-weight: bold;
      padding-top: 5px;
      font-style: italic;

      .block {
        width: 70px;
        height: 80px;
        border-radius: 10px;
        font-size: 50px;
        color: #ffe95c;
        font-style: italic;
      }

      .count_down_timeData {
        padding-top: 8px;
        margin-left: 10px;
      }

      .colon {
        font-size: 50px;
        color: #ffe95c;
      }
    }
  }
  .coupon_list{
    width: 100%;
    overflow: hidden;
    .coupon_item{
      //https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/active/lq-coupon/v2/item.png
      width: 76%;
      height: 170px;
      background-image: url(https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/active/lq-coupon/v2/item.png);
      background-size: 100% 100%;
      background-repeat: no-repeat;
      margin: 0 auto;
      margin-bottom: 20px;
      position: relative;
      .coupon_hidden{
        width: 100%;
        height: 96%;
        position: absolute;
        top: -1px;
        left: 0;
      }
      .item_info{
        margin-left: 55px;
        overflow: hidden;
      }
      .item_price{
        display: flex;
        font-size: 15px;
        margin-left: 160px;
        margin-top: 40px;
        .item_price_icon{
          height: 20px;
          line-height: 20px;
          font-size: 25px;
          color: #000;
          margin-top: 35px;
        }
        .item_price_num{
          height: 30px;
          line-height: 30px;
          font-size: 75px;
          color: #ff822a;
          margin-top: 10px;
          font-weight: bold;
          font-family: PingFangSC-Medium;
          margin-right: 10px;
        }
        .item_price_msg{
          width: 200px;
          height: 30px;
          font-size: 25px;
          color: #333;
          margin-top: 10px;
          font-weight: bold;
          font-family: PingFangSC-Medium;
          text-align: left;
        }
      }
      .item_rule{
        width: 200px;
        height: 30px;
        line-height: 32px;
        font-size: 18px;
        color: #fff;
        background-color: #ff8917;
        border-radius: 6px;
        text-align: center;
        margin-top: 10px;
        margin-left: 200px;
      }
    }
  }
  .coupon_rule{
    width: 100%;
    height: 400px;
    background-image: url(https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/home/<USER>/activity/20240130/nhuod0_06.png);
    background-size: 100% 100%;
    background-repeat: no-repeat;
    overflow: hidden;
    .rule_box{
      width: 79%;
      margin: 0 auto;
      font-size: 26px;
      color: #fff;
      margin-top: 50px;
    }
  }
}
</style>
