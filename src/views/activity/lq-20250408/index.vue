<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2021-07-07 11:29:51
 * @LastEditors: 孙立政
 * @LastEditTime: 2023-08-22 09:43:26
-->
<template>
  <div class="home">
    <div class="back">
      <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/advertisement/back.png" alt="" @click="goBack">
    </div>
    <div>
      <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/active/lq-20250408/1.jpg" alt="">
      <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/active/lq-20250408/2.jpg" alt="" @click="goShop(126)">
      <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/active/lq-20250408/3.jpg" alt="" @click="goShop(125)">
      <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/active/lq-20250408/4.jpg" alt="" @click="goShop(123)">
      <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/active/lq-20250408/5.jpg" alt="" @click="goShop(121)">
      <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/active/lq-20250408/6.jpg" alt="" @click="goShop(142)">
      <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/active/lq-20250408/7.jpg" alt="" @click="goShop(143)">
      <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/active/lq-20250408/8.jpg" alt="" @click="goShop(145)">
      <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/active/lq-20250408/9.jpg" alt="" @click="goShop(144)">
      <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/active/lq-20250408/10.jpg" alt="" @click="goShop(138)">
      <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/active/lq-20250408/11.jpg" alt="" @click="goShop(141)">
      <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/active/lq-20250408/12.jpg" alt="" @click="goShop(140)">
      <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/active/lq-20250408/13.jpg" alt="" @click="goShop(139)">

    </div>
  </div>
</template>

<script>
export default {
  components: {},
  data() {
    return {
      goodsData: []
    }
  },
  created() {

  },
  mounted() {
    this.$store.state.cart.cartData[0].goodsList = []
    this.$store.state.market.marketData.remark = ''
  },
  methods: {
    // 去店铺
    goShop(item) {
      this.$router.push({
        name: 'MarketDetails',
        query: {
          id: item
        }
      })
    },
    test(val) {
      console.log(val)
    },
    goBack() {
      this.$router.go(-1)
    }
  }
}
</script>

<style scoped lang="scss">
    .home {
      width: 100%;
      img {
        width: 100%;
        display: block;
      }
      .back{
        width: 100%;
        position: fixed;
        top: 65px;
        left: 38px;
        img{
          width: 66px;
          height: 66px;
        }
      }
      // .fixet{
      //   position: relative
      // }
    }
</style>
