<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2021-07-07 11:29:51
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-12-13 09:31:42
-->
<template>
  <div class="home">
    <div class="back">
      <img
        src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/advertisement/back.png"
        alt=""
        @click="goBack"
      >
    </div>

    <div class="activity_top_img">
      <img
        src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/active/nhj-sc/a-1.jpg"
        alt=""
      >
    </div>
    <div class="activity_body">
      <div class="activity_title_two">
        <img
          src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/active/winter/img3.png"
          alt=""
        >
      </div>
      <div class="activity_group_list">
        <div class="item_fast">
          <div class="box" @click="goGroupShop(group[0].marketId)">
            <div class="goods_img">
              <img
                :src="group[0].headPicture"
                alt=""
              >
            </div>
            <div class="flex_right">
              <div class="goods_name">
                {{ group[0].goodsName }}
              </div>
              <div class="goods_price">
                <div>￥</div>
                <div>{{ group[0].price }}</div>
                <div>原价：{{ group[0].oriPrice }}</div>
              </div>
              <div class="goods_btn">
                <img
                  src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/active/winter/img4.png"
                  alt=""
                >
              </div>
            </div>
          </div>
        </div>

        <div class="item_list">
          <div class="box" @click="goGroupShop(group[1].marketId)">
            <div class="goods_img">
              <img
                :src="group[1].headPicture"
                alt=""
              >
            </div>
            <div class="flex_right">
              <div class="goods_name">
                {{ group[1].goodsName }}
              </div>
              <div class="goods_price">
                <div>￥</div>
                <div>{{ group[1].price }}</div>
                <div>原价：{{ group[1].oriPrice }}</div>
              </div>
              <div class="goods_btn">
                <img
                  src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/active/winter/img4.png"
                  alt=""
                >
              </div>
            </div>
          </div>
          <div class="box" @click="goGroupShop(group[2].marketId)">
            <div class="goods_img">
              <img
                :src="group[2].headPicture"
                alt=""
              >
            </div>
            <div class="flex_right">
              <div class="goods_name">
                {{ group[2].goodsName }}
              </div>
              <div class="goods_price">
                <div>￥</div>
                <div>{{ group[2].price }}</div>
                <div>原价：{{ group[2].oriPrice }}</div>
              </div>
              <div class="goods_btn">
                <img
                  src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/active/winter/img4.png"
                  alt=""
                >
              </div>
            </div>
          </div>
        </div>

        <div class="item_list">
          <div class="box" @click="goGroupShop(group[3].marketId)">
            <div class="goods_img">
              <img
                :src="group[3].headPicture"
                alt=""
              >
            </div>
            <div class="flex_right">
              <div class="goods_name">
                {{ group[3].goodsName }}
              </div>
              <div class="goods_price">
                <div>￥</div>
                <div>{{ group[3].price }}</div>
                <div>原价：{{ group[3].oriPrice }}</div>
              </div>
              <div class="goods_btn">
                <img
                  src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/active/winter/img4.png"
                  alt=""
                >
              </div>
            </div>
          </div>
          <div class="box" @click="goGroupShop(group[4].marketId)">
            <div class="goods_img">
              <img
                :src="group[4].headPicture"
                alt=""
              >
            </div>
            <div class="flex_right">
              <div class="goods_name">
                {{ group[4].goodsName }}
              </div>
              <div class="goods_price">
                <div>￥</div>
                <div>{{ group[4].price }}</div>
                <div>原价：{{ group[4].oriPrice }}</div>
              </div>
              <div class="goods_btn">
                <img
                  src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/active/winter/img4.png"
                  alt=""
                >
              </div>
            </div>
          </div>
        </div>

        <!-- <div class="item_list">
          <div class="box" @click="goGroupShop(group[5].marketId)">
            <div class="goods_img">
              <img
                :src="group[5].headPicture"
                alt=""
              >
            </div>
            <div class="flex_right">
              <div class="goods_name">
                {{ group[5].goodsName }}
              </div>
              <div class="goods_price">
                <div>￥</div>
                <div>{{ group[5].price }}</div>
                <div>原价：{{ group[5].oriPrice }}</div>
              </div>
              <div class="goods_btn">
                <img
                  src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/active/winter/img4.png"
                  alt=""
                >
              </div>
            </div>
          </div>
          <div class="box" @click="goGroupShop(group[6].marketId)">
            <div class="goods_img">
              <img
                :src="group[6].headPicture"
                alt=""
              >
            </div>
            <div class="flex_right">
              <div class="goods_name">
                {{ group[6].goodsName }}
              </div>
              <div class="goods_price">
                <div>￥</div>
                <div>{{ group[6].price }}</div>
                <div>原价：{{ group[6].oriPrice }}</div>
              </div>
              <div class="goods_btn">
                <img
                  src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/active/winter/img4.png"
                  alt=""
                >
              </div>
            </div>
          </div>
        </div>

        <div class="item_list">
          <div class="box" @click="goGroupShop(group[7].marketId)">
            <div class="goods_img">
              <img
                :src="group[7].headPicture"
                alt=""
              >
            </div>
            <div class="flex_right">
              <div class="goods_name">
                {{ group[7].goodsName }}
              </div>
              <div class="goods_price">
                <div>￥</div>
                <div>{{ group[7].price }}</div>
                <div>原价：{{ group[7].oriPrice }}</div>
              </div>
              <div class="goods_btn">
                <img
                  src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/active/winter/img4.png"
                  alt=""
                >
              </div>
            </div>
          </div>
          <div class="box" @click="goGroupShop(group[8].marketId)">
            <div class="goods_img">
              <img
                :src="group[8].headPicture"
                alt=""
              >
            </div>
            <div class="flex_right">
              <div class="goods_name">
                {{ group[8].goodsName }}
              </div>
              <div class="goods_price">
                <div>￥</div>
                <div>{{ group[8].price }}</div>
                <div>原价：{{ group[8].oriPrice }}</div>
              </div>
              <div class="goods_btn">
                <img
                  src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/active/winter/img4.png"
                  alt=""
                >
              </div>
            </div>
          </div>
        </div> -->
      </div>
      <div class="activity_title_one">
        <img
          src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/active/winter/img2.png"
          alt=""
        >
      </div>
      <div class="activity_takeout_list">
        <div v-for="(item,index) in takeout" :key="index" class="item" @click="goTakeShop(item.marketId)">
          <div class="goods_img">
            <img
              :src="item.cover"
              alt=""
            >
          </div>
          <div class="flex_right">
            <div class="goods_name">
              {{ item.goodsName }}
            </div>
            <div class="goods_tag">
              <div>劲道好吃</div>
              <div>浓香美味</div>
            </div>
            <div class="goods_price">
              <div>￥</div>
              <div>{{ item.price }}</div>
              <!-- <div>原价：{{ item.oriPrice }}</div> -->
            </div>
            <div class="goods_btn">
              <img
                src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/active/winter/img4.png"
                alt=""
              >
            </div>
          </div>
        </div>
      </div>

      <div style="height: 60px;" />
    </div>

  </div>
</template>

<script>
import Data from './data.js'
export default {
  components: {},
  data() {
    return {
      takeout: Data.takeout,
      group: Data.group
    }
  },
  created() {},
  mounted() {
    console.log(Data)
  },
  methods: {
    goTakeShop(id) {
      this.$router.push({
        name: 'Shop',
        query: {
          id: id
        }
      })
    },
    goGroupShop(id) {
      this.$router.push({
        name: 'SetMeal',
        query: {
          id: id,
          type: 2
        }
      })
    },
    goBack() {
      this.$router.go(-1)
    }
  }
}
</script>

<style scoped lang="scss">
.home {
  img {
    width: 100%;
  }
  .back {
    width: 100%;
    position: fixed;
    top: 65px;
    left: 38px;
    img {
      width: 66px;
      height: 66px;
    }
  }
  .activity_top_img {
    display: flex;
  }
  .activity_body {
    width: 100%;
    min-height: 1000px;
    // background-color: #a81418;
    background-image: url(https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/active/nhj-sc/a-2-1.jpg);
    background-size: 100% 100%;
    background-repeat: no-repeat;
    .activity_title_one {
      text-align: center;
      margin-top: 44px;
      margin-bottom: 10px;
      img {
        width: 574px;
        height: 100px;
      }
    }
    .activity_title_two {
      text-align: center;
      margin-bottom: 20px;
      img {
        width: 574px;
        height: 100px;
      }
    }
    .activity_takeout_list {
      .item {
        width: 682px;
        min-height: 276px;
        background: #fffee5;
        border: 4px solid #eabb6b;
        border-radius: 16px;
        margin: 0 auto;
        padding: 18px;
        display: flex;
        margin-bottom: 23px;
        .flex_right {
          width: 306px;
          margin-left: 32px;
        }
        .goods_img {
          width: 252px;
          height: 230px;
          img {
            width: 252px;
            height: 230px;
            border-radius: 12px;
            object-fit: cover;
          }
        }
        .goods_name {
          font-weight: 700;
          font-size: 34px;
          color: #ff0000;
        }
        .goods_tag {
          display: flex;
          height: 40px;
          margin-top: 10px;
          div {
            width: 130px;
            height: 40px;
            border: 1px solid #ff0000;
            border-radius: 21px;
            font-weight: 400;
            font-size: 26px;
            color: #ff0000;
            text-align: center;
            line-height: 40px;
            margin-right: 8px;
          }
        }
        .goods_price {
          display: flex;
          height: 70px;
          margin-top: 10px;
          line-height: 70px;
          div:nth-child(1) {
            font-size: 26px;
            color: #ff0000;
            line-height: 85px;
          }
          div:nth-child(2) {
            font-size: 54px;
            font-weight: 700;
            margin-left: 1px;
            color: #ff0000;
          }
          div:nth-child(3) {
            color: #333333;
            font-weight: 400;
            text-decoration: line-through;
            margin-left: 20px;
            font-size: 24px;
            line-height: 85px;
          }
        }
        .goods_btn {
          width: 174px;
          height: 52px;
          display: flex;
          img {
            width: 174px;
            height: 52px;
          }
        }
      }
    }
    .activity_group_list {
        margin-top: 20px;
      .item_fast {
        width: 710px;
        min-height: 560px;
        background: #e59071;
        border-radius: 16px;
        margin: 0 auto;
        padding: 23px;
        .box{
            border-radius: 16px;
            background: #fffee5;
            padding: 18px;
        }
        .goods_img {
            width: 626px;
            height: 351px;
            img {
                width: 626px;
                height: 351px;
                border-radius: 12px;
                object-fit: cover;
            }
        }
        .goods_name {
          font-weight: 700;
          font-size: 34px;
          color: #ff0000;
          margin-top: 12px;
        }
        .goods_price {
          display: flex;
          height: 70px;
          line-height: 70px;
          div:nth-child(1) {
            font-size: 26px;
            color: #ff0000;
            line-height: 85px;
          }
          div:nth-child(2) {
            font-size: 54px;
            font-weight: 700;
            margin-left: 1px;
            color: #ff0000;
          }
          div:nth-child(3) {
            color: #333333;
            font-weight: 400;
            text-decoration: line-through;
            margin-left: 20px;
            font-size: 24px;
            line-height: 85px;
          }
        }
        .goods_btn {
          width: 174px;
          height: 52px;
          display: flex;
          img {
            width: 174px;
            height: 52px;
          }
          float: right;
          margin-top: -60px;
        }
      }
      .item_list{
        width: 710px;
        min-height: 502px;
        background: #e59071;
        border-radius: 16px;
        margin: 0 auto;
        padding: 22px;
        display: flex;
        justify-content: space-between;
        margin-top: 28px;
        .box{
            width: 320px;
            border-radius: 16px;
            background: #fffee5;
            padding: 14px;
        }
        .goods_img {
            width: 292px;
            height: 234px;
            img {
                width: 292px;
            height: 234px;
                border-radius: 12px;
                object-fit: cover;
            }
        }
        .goods_name {
          font-weight: 700;
          font-size: 34px;
          color: #ff0000;
          margin-top: 12px;
        }
        .goods_price {
          display: flex;
          height: 70px;
          line-height: 70px;
          div:nth-child(1) {
            font-size: 26px;
            color: #ff0000;
            line-height: 85px;
          }
          div:nth-child(2) {
            font-size: 54px;
            font-weight: 700;
            margin-left: 1px;
            color: #ff0000;
          }
          div:nth-child(3) {
            color: #333333;
            font-weight: 400;
            text-decoration: line-through;
            margin-left: 20px;
            font-size: 24px;
            line-height: 85px;
          }
        }
        .goods_btn {
          width: 174px;
          height: 52px;
          display: flex;
          img {
            width: 174px;
            height: 52px;
          }
        }
      }
    }
  }
}
</style>
