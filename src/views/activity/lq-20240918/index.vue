<!--
 * @Descripttion: 龙泉
-->
<template>
  <div class="home">
    <NavHeight bgc="#fff" />
    <div class="statusTop">
      <van-nav-bar title="" left-text="" left-arrow>
        <template #title>
          <div>城北好物</div>
        </template>
        <template #left>
          <van-icon
            name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/common/arrowlwft.png"
            size="18"
            @click="goBack"
          />
        </template>
        <template #right>
          <div class="right_text" @click="goRules">我的商城订单</div>
          <!-- <span v-if="current != 0" @click="dateShow = true">{{ time }}</span> -->
        </template>
      </van-nav-bar>
    </div>
    <!-- <div class="back">
      <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/advertisement/back.png" alt="" @click="goBack">
    </div> -->
    <div>
      <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/home/<USER>/activity/20240918/top.jpg" alt="">
      <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/home/<USER>/activity/20240918/top.jpg" alt="">
      <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/home/<USER>/activity/20240918/1.jpg?a=1" alt="">
      <img
        src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/home/<USER>/activity/20240918/2.jpg?a=2"
        alt=""
        @click="goCart(1)"
      >
      <img
        src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/home/<USER>/activity/20240918/3.jpg?a=3"
        alt=""
        @click="goCart(0)"
      >
      <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/home/<USER>/activity/20240918/4.jpg?a=1" alt="">
      <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/home/<USER>/activity/20240918/5.jpg?a=1" alt="">
      <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/home/<USER>/activity/20240918/6.jpg?a=1" alt="">
      <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/home/<USER>/activity/20240918/7.jpg?a=1" alt="">
      <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/home/<USER>/activity/20240918/8.jpg?a=1" alt="">
      <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/home/<USER>/activity/20240918/9.jpg?a=1" alt="">

    </div>
  </div>
</template>

<script>
import logger from '@/utils/aliLog'
import { goodsShelvesDetails } from '@/api/market/home'
export default {
  components: {},
  data() {
    return {
      detail: []
    }
  },
  created() {
    this.detail = []
    this.goodsShelvesDetails()
  },
  mounted() {
    logger.sum('龙泉-banner-20240918')
  },
  methods: {
    goRules() {
      this.$router.push({
        name: 'MarketOrder'
      })
    },
    goCart(index) {
      console.log(this.detail)
      this.$store.state.mall.distributionInfo = []
      // 记录配送方式
      let distri = this.detail[index].distributionInfo
      for (let i = 0; i < distri.length; i++) {
        this.$store.state.mall.distributionInfo.push(distri[i])
        if (distri[i].distributionType == 2) {
          this.$store.state.mall.ztData = distri[i]
        }
      }
      // console.log(this.detail[index])
      // 商品信息
      let goodsData = {
        goodsId: this.detail[index].id,
        goodsName: this.detail[index].title,
        pictureUrl: this.detail[index].cover,
        pintuanPrice: this.detail[index].price,
        originPrice: this.detail[index].price,
        inventory: this.detail[index].inventory
      }

      this.$store.state.mall.goodsData = goodsData

      // 下单数据
      this.$store.state.mall.creatForm.mallGoods = [
        {
          skuId: this.detail[index].skuId,
          skuQuantity: 1
        }
      ]
      this.$store.state.mall.creatForm.marketId = this.detail[index].mallMarketId
      this.$store.state.mall.creatForm.poolId = 1

      let distribution = this.detail[index].distributionInfo
      for (let i = 0; i < distribution.length; i++) {
        if (distribution[i].distributionType == 1) {
          this.$store.state.mall.goodsData.freightFee = distribution[i].basicFreight
        }
      }

      this.$router.push({
        name: 'MarketCart',
        query: {
          goodsId: this.detail[index].skuId,
          marketId: this.detail[index].mallMarketId,
          poolId: this.detail[index].poolId
        }
      })
    },
    // 获取详情
    async goodsShelvesDetails() {
      const res1 = await goodsShelvesDetails({
        'goodsId': 112,
        'latitude': this.$store.getters.getLocation.latitude,
        'longitude': this.$store.getters.getLocation.longitude
      })
      if (res1.status == 200) {
        this.detail.push(res1.data)
      }
      const res2 = await goodsShelvesDetails({
        'goodsId': 113,
        'latitude': this.$store.getters.getLocation.latitude,
        'longitude': this.$store.getters.getLocation.longitude
      })
      if (res2.status == 200) {
        this.detail.push(res2.data)
      }
    },
    goBack() {
      this.$router.go(-1)
    }
  }
}
</script>

<style scoped lang="scss">
.home {
  width: 100%;

  .statusTop {
    width: 100%;
    height: 92px;
    position: fixed;
    left: 0;
    z-index: 1;
    background-color: #fff;
  }

  img {
    width: 100%;
    display: block;
  }

  .back {
    width: 100%;
    position: fixed;
    top: 65px;
    left: 38px;

    img {
      width: 66px;
      height: 66px;
    }
  }

  .right_text{
    color: #169D1B;
  }
}
</style>
