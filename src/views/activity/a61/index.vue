<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2021-07-07 11:29:51
 * @LastEditors: 孙立政
 * @LastEditTime: 2023-05-30 17:26:15
-->
<template>
  <div class="home">
    <div class="back">
      <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/advertisement/back.png" alt="" @click="goBack">
    </div>
    <div>
      <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/active/a61/1.png" alt="">
      <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/active/sc-20240527/1.png" alt="" @click="goGroupShop(1540)">
      <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/active/sc-20240527/2.png" alt="" @click="goGroupShop(167)">
      <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/active/sc-20240527/3.png" alt="" @click="goTakeShop(1540)">

    </div>
  </div>
</template>

<script>
export default {
  components: {},
  data() {
    return {
      goodsData: []
    }
  },
  created() {

  },
  mounted() {
    this.$store.state.cart.cartData[0].goodsList = []
    this.$store.state.market.marketData.remark = ''
  },
  methods: {
    //
    goGroupShop(id) {
      this.$router.push({
        name: 'SetMeal',
        query: {
          id: id,
          type: 2
        }
      })
    },
    goTakeShop(id) {
      this.$router.push({
        name: 'Shop',
        query: {
          id: id
        }
      })
    },
    goBack() {
      this.$router.go(-1)
    }
  }
}
</script>

<style scoped lang="scss">
    .home {
      width: 100%;
      img {
        width: 100%;
        display: block;
      }
      .back{
        width: 100%;
        position: fixed;
        top: 65px;
        left: 38px;
        img{
          width: 66px;
          height: 66px;
        }
      }
      // .fixet{
      //   position: relative
      // }
      .goods_list{
        width: 100%;
        height: 350px;
        position: relative;
        top: -20px;
        z-index: 2;
        margin-top: -350px;
        display: flex;
        div{
          width: 50%;
        }
      }
    }
</style>
