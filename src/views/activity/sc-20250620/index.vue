<template>
  <div class="event-page">
    <!-- Header with background image -->
    <div class="hero-section">
      <div class="hero-background" />
      <div class="hero-overlay" />
    </div>

    <!-- Event card -->
    <div class="event-card">
      <div class="event-poster">
        <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/home/<USER>/activity/20250620/bj.jpg" alt="哪吒脑海">
      </div>

      <div class="event-info">
        <h1 class="event-title">7月2号 儿童剧《哪吒脑海》</h1>

        <div class="view-all">
          <span>遂昌站</span>
        </div>

        <div class="price-section">
          <span class="price">¥58 - 198</span>
          <span class="discount-tag">满减</span>
        </div>
      </div>
    </div>

    <!-- Event details -->
    <div class="event-details">
      <div class="detail-row">
        <span class="label">时间</span>
        <div class="detail-content">
          <div class="datetime">2025-07-02 19:00 星期三</div>
          <div class="duration">约75分钟，具体以现场为准</div>
        </div>
      </div>

      <div class="detail-row">
        <span class="label">地址</span>
        <div class="detail-content">
          <div class="venue">汤显祖大剧院</div>
          <div class="address">浙江省丽水市遂昌县S51遂昌凯兴开元大酒店东北侧约100米</div>
        </div>
      </div>

    </div>

    <!-- Features -->
    <div class="features">
      <div class="feature-item">
        <i class="icon">✓</i>
        <span>支持选座</span>
      </div>
      <div class="feature-item">
        <i class="icon">⚠</i>
        <span>不可退</span>
      </div>
      <div class="feature-item">
        <i class="icon">📱</i>
        <span>电子票</span>
      </div>
    </div>

    <!-- Tabs -->
    <div class="tabs">
      <div class="tab" :class="{ active: activeTab === 'details' }" @click="activeTab = 'details'">详情</div>
      <div class="tab" :class="{ active: activeTab === 'notice' }" @click="activeTab = 'notice'">须知</div>
    </div>

    <!-- Event description -->
    <div v-if="activeTab === 'details'" class="event-description">
      <h3>详情介绍</h3>
      <div class="description-image">
        <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/home/<USER>/activity/20250620/xq-ys.png" alt="活动详情">
      </div>
    </div>

    <!-- Notice content -->
    <div v-if="activeTab === 'notice'" class="notice-content">
      <div class="notice-section">
        <h3>观看须知</h3>
        <div class="notice-item">
          <span class="notice-number">1.</span>
          <div class="notice-text">
            <strong>观演公约：</strong>请文明观演，请勿嬉戏打闹，禁止跨层跨区就坐
          </div>
        </div>
        <div class="notice-item">
          <span class="notice-number">2.</span>
          <div class="notice-text">
            <strong>禁止携带物品：</strong>由于安保和版权的原因，大多数演出、展览及比赛场所禁止携带食品、饮料、专业摄录设备、打火机等物品请您注意现场工作人员和广播的提示，予以配合
          </div>
        </div>
        <div class="notice-item">
          <span class="notice-number">3.</span>
          <div class="notice-text">
            <strong>寄存说明：</strong>无寄存处，请自行保管携带物品，谨防贵重物品丢失
          </div>
        </div>
      </div>

      <div class="notice-section">
        <h3>限制购票说明</h3>
        <div class="notice-item">
          <span class="notice-number">1.</span>
          <div class="notice-text">
            <strong>仔细检查：</strong>请核对好场次及座位后再下单
          </div>
        </div>
      </div>

      <div class="notice-section">
        <h3>退换政策说明</h3>
        <div class="notice-item">
          <span class="notice-number">1.</span>
          <div class="notice-text">
            票品为有价证券，非普通商品，其背后承载的文化服务具有时效性，稀缺性等特征，不支持退换。
          </div>
        </div>
        <div class="notice-item">
          <span class="notice-number">2.</span>
          <div class="notice-text">
            由于票品需要占座的特殊性，下单成功后无法支持退换。
          </div>
        </div>
      </div>

      <div class="notice-section">
        <h3>入场方式</h3>
        <div class="notice-item">
          <span class="notice-number">1.</span>
          <div class="notice-text">
            <strong>入场规则：</strong>须携带电子门票验票入场。
          </div>
        </div>
        <div class="notice-item">
          <span class="notice-number">2.</span>
          <div class="notice-text">
            <strong>入场时间：</strong>请于演出前约30分钟入场。
          </div>
        </div>
      </div>
    </div>

    <!-- Bottom action bar -->
    <div class="bottom-bar">
      <button class="btn-outline">订单</button>
      <button class="btn-outline">客服</button>
      <button class="btn-primary" @click="showPopup = true">立即订票</button>
    </div>

    <van-popup v-model="showPopup" position="bottom" closeable round>
      <div>
        <div>158票价</div>
        <div>98票价</div>
        <div>58票价</div>
      </div>
    </van-popup>
  </div>
</template>

<script>
export default {
  name: 'EventPage',
  data() {
    return {
      activeTab: 'details',
      showPopup: false
    }
  },
  methods: {
    goBack() {
      // Navigation logic
    },
    shareEvent() {
      // Share logic
    },
    bookTicket() {
      // Booking logic
    }
  }
}
</script>

<style scoped lang="scss">
.event-page {
  min-height: 100vh;
  background-color: #f5f5f5;
  position: relative;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.hero-section {
  position: relative;
  height: 560px;
  overflow: hidden;
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url('https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/home/<USER>/activity/20250620/bj.jpg');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

.hero-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to bottom, rgba(0,0,0,0.3), rgba(0,0,0,0.6));
}

.event-card {
  position: relative;
  margin: -120px 32px 0;
  background: white;
  border-radius: 32px;
  padding: 32px;
  box-shadow: 0 8px 40px rgba(0,0,0,0.1);
  display: flex;
  gap: 32px;
  z-index: 10;
}

.event-poster {
  width: 200px;
  height: 260px;
  border-radius: 16px;
  overflow: hidden;
  flex-shrink: 0;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.event-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.event-title {
  font-size: 32px;
  font-weight: 600;
  color: #333;
  margin: 0;
  line-height: 1.3;
}

.price-section {
  display: flex;
  align-items: center;
  gap: 16px;
  margin: 8px 0;
}

.price {
  color: #ff4444;
  font-size: 42px;
  font-weight: 600;
}

.discount-tag {
  background: #ff4444;
  color: white;
  padding: 4px 12px;
  border-radius: 8px;
  font-size: 20px;
}

.view-all {
  display: flex;
  align-items: center;
  color: #999;
  font-size: 24px;
  margin-top: auto;
}

.arrow-right {
  margin-left: 8px;
  color: #ccc;
}

.event-details {
  background: white;
  margin: 22px;
  border-radius: 24px;
  overflow: hidden;
}

.detail-row {
  display: flex;
  padding: 32px;
  border-bottom: 1px solid #f0f0f0;
  align-items: flex-start;

  &:last-child {
    border-bottom: none;
  }
}

.label {
  color: #999;
  font-size: 28px;
  width: 120px;
  flex-shrink: 0;
}

.detail-content {
  flex: 1;
  margin-left: 24px;
  position: relative;
}

.datetime {
  font-size: 28px;
  color: #333;
  font-weight: 500;
}

.duration {
  font-size: 24px;
  color: #999;
  margin-top: 4px;
}

.venue {
  font-size: 28px;
  color: #333;
  font-weight: 500;
}

.address {
  font-size: 24px;
  color: #999;
  margin-top: 4px;
  line-height: 1.3;
}

.features {
  background: white;
  margin: 0 22px 32px;
  border-radius: 24px;
  padding: 32px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 48px;
  position: relative;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 28px;
  color: #333;
  padding-right: 10px;
}

.icon {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.tabs {
  background: white;
  margin: 0 22px 32px;
  border-radius: 24px;
  display: flex;
  padding: 8px;
}

.tab {
  flex: 1;
  text-align: center;
  padding: 24px;
  font-size: 28px;
  border-radius: 16px;
  cursor: pointer;
  transition: all 0.3s;

  &.active {
    background: #ff4444;
    color: white;
    font-weight: 500;
  }

  &:not(.active) {
    color: #666;

    &:hover {
      background: #f8f8f8;
    }
  }
}

.event-description {
  background: white;
  margin: 0 22px 200px;
  border-radius: 24px;
  padding: 32px;

  h3 {
    margin: 0 0 22px 0;
    font-size: 32px;
    color: #333;
  }
}

.description-image {
  img {
    width: 100%;
    border-radius: 16px;
  }
}

.notice-content {
  background: white;
  margin: 0 32px 200px;
  border-radius: 24px;
  padding: 32px;
}

.notice-section {
  margin-bottom: 48px;

  &:last-child {
    margin-bottom: 0;
  }

  h3 {
    margin: 0 0 32px 0;
    font-size: 32px;
    color: #333;
    font-weight: 600;
  }
}

.notice-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 24px;

  &:last-child {
    margin-bottom: 0;
  }
}

.notice-number {
  color: #666;
  font-size: 26px;
  font-weight: 500;
  margin-right: 16px;
  flex-shrink: 0;
  line-height: 1.4;
}

.notice-text {
  flex: 1;
  font-size: 26px;
  line-height: 1.5;
  color: #333;

  strong {
    font-weight: 600;
    color: #333;
  }
}

.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 24px 32px 40px;
  display: flex;
  gap: 24px;
  box-shadow: 0 -4px 20px rgba(0,0,0,0.1);
  align-items: center;
}

.btn-outline {
  background: transparent;
  border: 1px solid #ddd;
  color: #666;
  padding: 20px 32px;
  border-radius: 16px;
  font-size: 28px;
  cursor: pointer;
}

.btn-favorite {
  background: transparent;
  border: none;
  font-size: 40px;
  color: #ccc;
  cursor: pointer;
  padding: 16px;
}

.btn-primary {
  background: #ff4444;
  color: white;
  border: none;
  padding: 24px 48px;
  border-radius: 16px;
  font-size: 32px;
  font-weight: 500;
  flex: 1;
  cursor: pointer;
}

// Responsive adjustments
@media (max-width: 750px) {
  .event-card {
    margin: -100px 24px 0;
    padding: 24px;
  }

  .event-poster {
    width: 160px;
    height: 200px;
  }

  .event-title {
    font-size: 28px;
  }

  .price {
    font-size: 32px;
  }
}
</style>
