<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2021-07-07 11:29:51
 * @LastEditors: 孙立政
 * @LastEditTime: 2023-06-21 11:03:46
-->
<template>
  <div class="home">
    <div class="back">
      <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/advertisement/back.png" alt="" @click="goBack">
    </div>
    <div>
      <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/active/sc-20240606/1.jpg" alt="">
      <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/active/sc-20240606/2.jpg" alt="" @click="goShop2(689)">
      <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/active/sc-20240606/3.jpg" alt="" @click="goShop2(689)">
      <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/active/sc-20240606/4.jpg" alt="" @click="goShop2(689)">

    </div>

  </div>
</template>

<script>
export default {
  components: {},
  data() {
    return {
      goodsData: []
    }
  },
  methods: {
    goShop2(val) {
      this.$router.push({
        name: 'SetMeal',
        query: {
          id: val,
          type: 2
        }
      })
    },
    goBack() {
      this.$router.go(-1)
    }
  }
}
</script>

<style scoped lang="scss">
    .home {
      width: 100%;
      img {
        width: 100%;
        display: block;
      }
      .back{
        width: 100%;
        position: fixed;
        top: 65px;
        left: 38px;
        img{
          width: 66px;
          height: 66px;
        }
      }
      // .fixet{
      //   position: relative
      // }
      .goods_list{
        width: 100%;
        height: 350px;
        position: relative;
        top: -20px;
        z-index: 2;
        margin-top: -350px;
        display: flex;
        div{
          width: 50%;
        }
      }
    }
</style>
