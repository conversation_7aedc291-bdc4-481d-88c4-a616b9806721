<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2021-07-07 11:29:51
 * @LastEditors: 孙立政
 * @LastEditTime: 2023-03-07 09:45:23
-->
<template>
  <div class="home">
    <div class="back">
      <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/advertisement/back.png" alt="" @click="goBack">
    </div>
    <div>
      <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/home/<USER>/activity/********/********.png" alt="" @click="goTo()">

    </div>

  </div>
</template>

<script>
import { rcbHomePage, findUnionRcbAccount } from '@/api/bank/nsh'
import logger from '@/utils/aliLog'
import { capitalist } from '@/api/my'
export default {
  components: {},
  data() {
    return {
      goodsData: [],
      capitalAccountId: ''
    }
  },
  created() {
    AlipayJSBridge.call('GetNsh', this.form, function(result) {
      console.log(result)
    })
  },
  mounted() {
    this.getcapitalist()
    logger.sum('龙泉-banner')
  },
  methods: {
    goTo() {
      this.findUnionRcbAccount()
      // this.$router.push({
      //   name: 'My'
      // })
    },
    goBack() {
      this.$router.go(-1)
    },
    //
    getcapitalist() {
      let data = this.$store.getters.getUserId
      capitalist(data).then((res) => {
        if (res.status == 200) {
          for (let index = 0; index < res.data.length; index++) {
            if (res.data[index].capitalPoolId == 3) {
              this.capitalAccountId = res.data[index].capitalAccountId
            }
          }
        }
      })
    },
    // 查询农商
    findUnionRcbAccount() {
      let data = {
        'userId': this.$store.getters.getUserId,
        'capitalAccountId': this.capitalAccountId
      }
      findUnionRcbAccount(data).then(res => {
        if (res.status == 200) {
          if (res.data.status === 2) {
            this.clickNshOn()
          } else {
            this.$router.push({
              name: 'My'
            })
          }
        }
      })
    },
    // 打开农商sdk
    clickNshOn() {
      rcbHomePage(this.$store.getters.getUserId + '/1').then(res => {
        if (res.data == null) {
          this.$toast('开户异常，请重新开户')
          return
        }
        let data = {
          redirectUrl: res.data.redirectUrl
        }
        AlipayJSBridge.call('NSBBizvoke', data, function(result) {
          console.log('---------农商行----------')
          console.log(result)
          console.log('---------end----------')
        })
      })
    }
  }
}
</script>

<style scoped lang="scss">
    .home {
      width: 100%;
      img {
        width: 100%;
        display: block;
      }
      .back{
        width: 100px;
        height: 100px;
        position: fixed;
        top: 65px;
        left: 38px;
        img{
          width: 66px;
          height: 66px;
        }
      }
    }
</style>
