<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2021-07-07 11:29:51
 * @LastEditors: 孙立政
 * @LastEditTime: 2023-02-14 16:19:06
-->
<template>
  <div class="home">
    <div class="back">
      <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/advertisement/back.png" alt="" @click="goBack">
    </div>
    <div>
      <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/home/<USER>/banner/20250303/1.png" alt="">
      <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/home/<USER>/banner/20250303/2.png" alt="" @click="goShop(689)">
      <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/home/<USER>/banner/20250303/3.png" alt="" @click="goShop(689)">
      <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/home/<USER>/banner/20250303/4.png" alt="" @click="goShop(2302)">
    </div>

  </div>
</template>

<script>
export default {
  components: {},
  data() {
    return {
    }
  },
  created() {

  },
  mounted() {},
  methods: {
    // 去店铺
    goShop(item) {
      this.$router.push({
        name: 'SetMeal',
        query: {
          id: item,
          type: 2
        }
      })
    },
    test(val) {
      console.log(val)
    },
    goBack() {
      this.$router.go(-1)
    }
  }
}
</script>

<style scoped lang="scss">
    .home {
      width: 100%;
      img {
        width: 100%;
        display: block;
      }
      .back{
        width: 100%;
        position: fixed;
        top: 65px;
        left: 38px;
        img{
          width: 66px;
          height: 66px;
        }
      }
      // .fixet{
      //   position: relative
      // }
      .goods_list{
        width: 100%;
        height: 350px;
        position: relative;
        top: -20px;
        z-index: 2;
        margin-top: -350px;
        display: flex;
        div{
          width: 50%;
        }
      }
    }
</style>
