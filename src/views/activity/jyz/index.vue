<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2021-07-07 11:29:51
 * @LastEditors: 孙立政
 * @LastEditTime: 2023-06-20 16:53:12
-->
<template>
  <div class="home">
    <div class="back">
      <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/advertisement/back.png" alt="" @click="goBack">
    </div>
    <div>
      <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/active/jyz/1.png" alt="">
      <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/active/jyz/2.png" alt="" @click="goGroupShop(1665)">
      <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/active/jyz/3.png" alt="" @click="goGroupShop(1668)">
      <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/active/jyz/4.png" alt="" @click="goGroupShop(1669)">
    </div>
  </div>
</template>

<script>
export default {
  components: {},
  data() {
    return {}
  },
  created() {

  },
  mounted() {
    this.$store.state.cart.cartData[0].goodsList = []
    this.$store.state.market.marketData.remark = ''
  },
  methods: {
    // 去店铺
    goGroupShop(id) {
      this.$router.push({
        name: 'FineFoodShopIndex',
        query: {
          id: id
        }
      })
    },
    goBack() {
      this.$router.go(-1)
    }
  }
}
</script>

<style scoped lang="scss">
    .home {
      width: 100%;
      img {
        width: 100%;
        display: block;
      }
      .back{
        width: 100%;
        position: fixed;
        top: 65px;
        left: 38px;
        img{
          width: 66px;
          height: 66px;
        }
      }
    }
</style>
