<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2021-07-07 11:29:51
 * @LastEditors: 孙立政
 * @LastEditTime: 2023-08-22 09:43:26
-->
<template>
  <div class="home">
    <div class="back">
      <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/advertisement/back.png" alt="" @click="goBack">
    </div>
    <div>
      <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/active/sc-20240520/d/1.png" alt="">
      <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/active/sc-20240520/d/2.png" alt="" @click="goShop(865)">
      <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/active/sc-20240520/d/5.png" alt="" @click="goShop(2188)">
      <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/active/sc-20240520/d/4.png" alt="">

    </div>
  </div>
</template>

<script>
export default {
  components: {},
  data() {
    return {
      goodsData: []
    }
  },
  created() {

  },
  mounted() {
    this.$store.state.cart.cartData[0].goodsList = []
    this.$store.state.market.marketData.remark = ''
  },
  methods: {
    // 去店铺
    goShop(item) {
      this.$router.push({
        name: 'SetMeal',
        query: {
          id: item,
          type: 2
        }
      })
    },
    test(val) {
      console.log(val)
    },
    goBack() {
      this.$router.go(-1)
    }
  }
}
</script>

<style scoped lang="scss">
    .home {
      width: 100%;
      img {
        width: 100%;
        display: block;
      }
      .back{
        width: 100%;
        position: fixed;
        top: 65px;
        left: 38px;
        img{
          width: 66px;
          height: 66px;
        }
      }
      // .fixet{
      //   position: relative
      // }
    }
</style>
