<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2021-07-07 11:29:51
 * @LastEditors: 孙立政
 * @LastEditTime: 2023-04-04 10:10:25
-->
<template>
  <div class="home">
    <div class="back">
      <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/advertisement/back.png" alt="" @click="goBack">
    </div>
    <div>
      <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/active/qmj/20240402/1.jpg?a=1" alt="">
      <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/active/qmj/20240402/2.jpg" alt="" @click="goShop3(1540)">
      <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/active/qmj/20240402/3.jpg" alt="" @click="goShop3(2053)">
      <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/active/qmj/20240402/4.jpg" alt="" @click="goShop3(2053)">
      <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/active/qmj/20240402/5.jpg" alt="" @click="goShop3(2053)">

    </div>

  </div>
</template>

<script>
export default {
  components: {},
  data() {
    return {
      goodsData: [
        {
          marketId: 167,
          type: 3,
          marketSn: 'SH450804',
          marketName: '卡莎蜜亚（县府店）',
          agentPostFee: null,
          pic: 'https://shenghuofw.oss-cn-beijing.aliyuncs.com/765f3150abcd42a38cd00ae615d66579.jpg',
          distance: null,
          isSelfMention: false,
          latitude: 28.589733,
          longitude: 119.276597,
          skuId: 49297,
          goodsId: 14291,
          tagId: 1953,
          leastCopies: 1,
          attrs: null,
          cover: 'https://diandi-video.oss-cn-hangzhou.aliyuncs.com/img/1675928251000goods',
          difference: '默认',
          numberOfPackages: 1,
          stock: 999,
          goodsName: '镭射爱心手提蛋糕(情人节限定)',
          price: 78,
          oriPrice: 88,
          packPrice: 0,
          distributionMode: 1
        }
      ]
    }
  },
  created() {

  },
  mounted() {
    this.$store.state.cart.cartData[0].goodsList = []
    this.$store.state.market.marketData.remark = ''
  },
  methods: {
    // 去购物车
    goShop(item) {
      this.$store.state.market.marketData.marketId = item.marketId
      this.$store.state.market.marketData.type = item.type
      this.$store.state.market.marketData.marketSn = item.marketSn
      this.$store.state.market.marketData.marketName = item.marketName
      this.$store.state.market.marketData.agentPostFee = item.agentPostFee
      this.$store.state.market.marketData.pic = item.pic
      this.$store.state.market.marketData.address = item.address
      this.$store.state.market.marketData.distance = item.distance
      this.$store.state.market.marketData.isSelfMention = item.isSelfMention

      this.$store.state.market.marketData.map.latitude = item.latitude
      this.$store.state.market.marketData.map.longitude = item.longitude
      this.$store.state.market.marketData.map.marketName = item.marketName
      let cart = this.$store.state.cart.cartData[0].goodsList
      // let newCart = this.$store.state.cart.cartData[0].goodsList
      let cartList = {}
      cartList.skuId = item.skuId
      cartList.goodsId = item.goodsId
      cartList.tagId = item.tagId
      cartList.leastCopies = item.leastCopies // 最少购买份数
      cartList.attrs = item.attrs
      cartList.cover = item.cover
      cartList.difference = item.difference
      cartList.numberOfPackages = item.numberOfPackages
      cartList.stock = item.stock
      cartList.goodsName = item.goodsName
      // 记录价格
      cartList.price = {}
      cartList.price.price = item.price
      cartList.price.oriPrice = item.oriPrice
      cartList.price.packPrice = item.packPrice

      cartList.distributionMode = item.distributionMode

      cart.push(cartList)

      this.$router.push({
        path: '/submitOrder'
      })
    },
    goShop2(val) {
      this.$router.push({
        name: 'Shop',
        query: {
          id: val
        }
      })
    },
    goShop3(val) {
      this.$router.push({
        name: 'SetMeal',
        query: {
          id: val,
          type: 2
        }
      })
    },
    goBack() {
      this.$router.go(-1)
    }
  }
}
</script>

<style scoped lang="scss">
    .home {
      width: 100%;
      img {
        width: 100%;
        display: block;
      }
      .back{
        width: 100%;
        position: fixed;
        top: 65px;
        left: 38px;
        img{
          width: 66px;
          height: 66px;
        }
      }
      // .fixet{
      //   position: relative
      // }
      .goods_list{
        width: 100%;
        height: 350px;
        position: relative;
        top: -20px;
        z-index: 2;
        margin-top: -350px;
        display: flex;
        div{
          width: 50%;
        }
      }
    }
</style>
