<!--
 * @Descripttion: 龙泉农商行贷款广告
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2021-07-07 11:29:51
 * @LastEditors: 孙立政
 * @LastEditTime: 2023-05-30 17:26:15
-->
<template>
  <div class="home">
    <div class="back">
      <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/advertisement/back.png" alt="" @click="goBack">
    </div>
    <div>
      <img src="https://shenghuofw.oss-cn-beijing.aliyuncs.com/b21b917568d04df1bc9c43d62b4a9ebd.jpg" alt="">
    </div>
  </div>
</template>

<script>
export default {
  components: {},
  data() {
    return {
      goodsData: []
    }
  },
  created() {

  },
  mounted() { },
  methods: {
    goBack() {
      this.$router.go(-1)
    }
  }
}
</script>

<style scoped lang="scss">
    .home {
      width: 100%;
      img {
        width: 100%;
        display: block;
      }
      .back{
        width: 100%;
        position: fixed;
        top: 65px;
        left: 38px;
        img{
          width: 66px;
          height: 66px;
        }
      }
    }
</style>
