<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2021-07-07 11:29:51
 * @LastEditors: 孙立政
 * @LastEditTime: 2023-08-22 09:43:26
-->
<template>
  <div class="home">
    <div class="back">
      <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/advertisement/back.png" alt="" @click="goBack">
    </div>
    <div>
      <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/home/<USER>/activity/20240809/a/1.png" alt="">
      <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/home/<USER>/activity/20240809/a/2.png" alt="" @click="goShop(533)">
      <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/home/<USER>/activity/20240809/a/3.png" alt="" @click="goShop(533)">
      <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/home/<USER>/activity/20240809/a/4.png" alt="" @click="goShop(533)">

    </div>

    <div class="goods_list">
      <div @click="goShop(1540)" />
      <div @click="goShop(1540)" />
    </div>
  </div>
</template>

<script>
export default {
  components: {},
  data() {
    return {
      goodsData: []
    }
  },
  created() {

  },
  mounted() {
    this.$store.state.cart.cartData[0].goodsList = []
    this.$store.state.market.marketData.remark = ''
  },
  methods: {
    // 去店铺
    goShop(item) {
      this.$router.push({
        name: 'Shop',
        query: {
          id: item
        }
      })
    },
    test(val) {
      console.log(val)
    },
    goBack() {
      this.$router.go(-1)
    }
  }
}
</script>

<style scoped lang="scss">
    .home {
      width: 100%;
      img {
        width: 100%;
        display: block;
      }
      .back{
        width: 100%;
        position: fixed;
        top: 65px;
        left: 38px;
        img{
          width: 66px;
          height: 66px;
        }
      }
      // .fixet{
      //   position: relative
      // }
      .goods_list{
        width: 100%;
        height: 350px;
        position: relative;
        top: -20px;
        z-index: 2;
        margin-top: -350px;
        display: flex;
        div{
          width: 50%;
        }
      }
    }
</style>
