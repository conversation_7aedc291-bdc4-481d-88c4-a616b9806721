<!--
 * @Author: your name
 * @Date: 2022-04-28 11:12:01
 * @LastEditTime: 2022-06-25 03:11:00
 * @LastEditors: 孙立政
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: /newUser/src/views/market/index.vue
-->
<template>
  <div class="home">
    <NavHeight bgc="#fff" />
    <Top message="拼团" :is-right="false" :is-left="true" />
    <van-skeleton v-for="i in 5" v-show="loadingShow" :key="i" title avatar :row="3" />
    <div v-for="item in list" v-show="item.activityStatus!==1" :key="item.id" @click="goGoods(item)">
      <GoodsCard :item="item" />
      <GoodsCard v-if="false" :item="test" />
    </div>
    <div v-if="nullStatus" class="emptyNull">
      <van-empty
        class="custom-image"
        image="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/empty/goodsnull2.png"
        description="商城正在为您寻找宝贝~"
        image-size="210"
      />
    </div>
    <div style="height:100px" />

    <!-- 加载状态 -->
    <Loading :show="loadingShow" />
  </div>
</template>

<script>
import Top from './components/Tops.vue'
import GoodsCard from './components/GoodsCard'
import Loading from '@/components/Loading/index'
import { getActivityList } from '../../api/market'
export default {
  components: {
    GoodsCard, Top, Loading
  },
  data() {
    return {
      query: {
        pageNum: 1,
        pageSize: 100,
        // activityNo: '93143041'
        activityNo: this.$route.query.activityNo
      },
      list: [],
      loadingShow: true,
      test: {
        pictureUrl: 'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/market/test/1.png',
        goodsName: '新鲜红石榴新鲜红石榴新鲜红石',
        freightFee: 0,
        pintuanUnitAmount: 2,
        pintuanPrice: 1.8,
        originPrice: 3.6,
        groupUserHeadPictureUrlList: [
          'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/market/test/1.png',
          'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/market/test/2.png',
          'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/market/test/2.png',
          'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/market/test/1.png',
          'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/market/test/2.png',
          'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/market/test/2.png',
          'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/market/test/2.png',
          'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/market/test/1.png',
          'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/market/test/2.png',
          'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/market/test/2.png',
          'https://diandi-video.oss-cn-hangzhou.aliyuncs.com/face/16360964690001636096469604.png?x-oss-process=image/resize,w_700/format,jpg/quality,q_30'
        ],
        pintuanUserAmount: 1256
      },
      nullStatus: false
    }
  },
  created() {
    this.getActivityList()
    this.$store.state.Index.bannerIndex = 1
    this.$store.state.tabbar.index = 1
    document.body.scrollTop = 0
    document.documentElement.scrollTop = 0
  },
  methods: {
    // 获取活动列表
    getActivityList() {
      getActivityList(this.query).then(res => {
        this.loadingShow = false
        if (res.status == 200) {
          this.list = res.list
          if (res.list.length == 0) {
            this.nullStatus = true
          }
        }
      })
    },
    goGoods(item) {
      if (item.activityStatus === 3) {
        return
      }
      this.$router.push({
        name: 'MarketPtDetails',
        query: {
          id: item.activityJoinNo,
          goodsId: item.goodsId,
          activityNo: item.activityNo,
          activityJoinNo: item.activityJoinNo
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.home {
  .custom-image{
    width: 560px;
    height: 300px;
    margin: 0 auto;

    ::v-deep .van-empty__description{
      color: #333333;
      font-size: 32px;
      font-family: PingFangSC-Medium;
    }
    ::v-deep .van-empty__image img {
      width: 420px !important;
      height: 300px !important;
    }
  }
  .emptyNull{
    width: 100%;
    padding-top: 250px;
    margin-top: 20px;
  }
}
</style>
