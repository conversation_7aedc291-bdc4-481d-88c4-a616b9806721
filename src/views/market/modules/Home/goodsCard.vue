<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2022-06-16 11:10:30
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-12-27 18:10:55
-->
<template>
  <div class="goods-card" @click="goGoodsDetails(item)">
    <div class="goods-image-container">
      <img :src="item.cover" alt="" class="goods-image">

      <!-- 销量标签 -->
      <div v-if="item.sales > 0" class="sales-badge">
        {{ item.sales }}人已购买
      </div>

      <!-- 活动标签 -->
      <div v-if="item.isActivity" class="activity-badge">
        <span class="activity-text">活动</span>
      </div>

      <!-- 渐变遮罩 -->
      <div class="image-overlay" />
    </div>

    <div class="goods-content">
      <div class="goods-title">
        <img
          v-if="item.isActivity"
          class="activity-icon"
          src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/market/market/home/<USER>"
          alt=""
        >
        <span class="title-text">{{ item.title }}</span>
      </div>

      <!-- 标签区域 -->
      <div v-if="item.mallGoodsActivityInfo != null" class="goods-tags">
        <span class="tag tag-limited">限时</span>
        <span class="tag tag-hot">热卖</span>
      </div>

      <!-- 价格和购买按钮 -->
      <div class="goods-footer">
        <div class="price-container">
          <div class="current-price">
            <span class="currency">¥</span>
            <span
              v-if="
                item.mallGoodsActivityInfo != null &&
                  item.mallGoodsActivityInfo.activityType == 2
              "
              class="price"
            >{{ item.mallGoodsActivityInfo.activityPrice }}</span>
            <span v-else class="price">{{ item.price }}</span>
          </div>
          <div
            v-if="
              item.mallGoodsActivityInfo != null &&
                item.mallGoodsActivityInfo.activityType == 2
            "
            class="original-price"
          >
            ¥{{ item.price }}
          </div>
        </div>

        <div class="buy-button">
          <svg
            class="buy-icon"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M3 3H5L5.4 5M7 13H17L21 5H5.4M7 13L5.4 5M7 13L4.7 15.3C4.3 15.7 4.6 16.5 5.1 16.5H17M17 13V17C17 18.1 16.1 19 15 19H9C7.9 19 7 18.1 7 17V13M9 21C9.6 21 10 20.6 10 20S9.6 19 9 19 8 19.4 8 20 8.4 21 9 21ZM20 21C20.6 21 21 20.6 21 20S20.6 19 20 19 19 19.4 19 20 19.4 21 20 21Z"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </svg>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    test: {
      type: Number,
      default: 0
    },
    item: {
      type: Object,
      default: function() {
        return {}
      }
    }
  },

  data() {
    return {}
  },
  created() {},
  methods: {
    goGoodsDetails(item) {
      if (item.isActivity) {
        this.$router.push({
          name: 'MarketPtDetails',
          query: {
            id: item.mallGoodsActivityInfo.activityJoinNo,
            goodsId: item.id,
            activityNo: item.mallGoodsActivityInfo.activityNo,
            activityJoinNo: item.mallGoodsActivityInfo.activityJoinNo
          }
        })
        return
      }
      this.$router.push({
        name: 'MarketDetails',
        query: {
          id: this.item.id
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
.goods-card {
  width: 354px;
  background: #ffffff;
  border-radius: 20px;
  margin-bottom: 24px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
  cursor: pointer;
  position: relative;

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
      135deg,
      rgba(255, 255, 255, 0.1) 0%,
      rgba(255, 255, 255, 0) 100%
    );
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
    z-index: 1;
  }

  &:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);

    &::before {
      opacity: 1;
    }

    .goods-image {
      transform: scale(1.1);
    }

    .buy-button {
      background: linear-gradient(135deg, #ff6b6b 0%, #ff5722 100%);
      transform: scale(1.1);
    }
  }

  &:active {
    transform: translateY(-4px) scale(0.98);
  }
}

.goods-image-container {
  position: relative;
  width: 100%;
  height: 354px;
  overflow: hidden;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);

  .goods-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.6s cubic-bezier(0.25, 0.8, 0.25, 1);
  }

  .image-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 60px;
    background: linear-gradient(
      to top,
      rgba(0, 0, 0, 0.3) 0%,
      transparent 100%
    );
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  &:hover .image-overlay {
    opacity: 1;
  }

  .sales-badge {
    position: absolute;
    left: 16px;
    bottom: 16px;
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 8px 12px;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    font-size: 24px;
    font-weight: 500;
    color: #333;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 2;

    .sales-icon {
      width: 16px;
      height: 16px;
      color: #ffd700;
    }
  }

  .activity-badge {
    position: absolute;
    top: 16px;
    right: 16px;
    z-index: 2;

    .activity-text {
      display: inline-block;
      padding: 6px 16px;
      background: linear-gradient(135deg, #ff6b6b 0%, #ff5722 100%);
      color: white;
      font-size: 22px;
      font-weight: 600;
      border-radius: 20px;
      box-shadow: 0 4px 12px rgba(255, 107, 107, 0.4);
      animation: pulse 2s infinite;
    }
  }
}

@keyframes pulse {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

.goods-content {
  padding: 20px 18px 24px;
}

.goods-title {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  margin-bottom: 12px;

  .activity-icon {
    width: 60px;
    height: 28px;
    flex-shrink: 0;
    margin-top: 2px;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
  }

  .title-text {
    font-size: 28px;
    font-weight: 600;
    color: #2c3e50;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    letter-spacing: 0.3px;
  }
}

.goods-tags {
  display: flex;
  gap: 8px;
  margin-bottom: 16px;
  flex-wrap: wrap;

  .tag {
    padding: 4px 12px;
    font-size: 20px;
    font-weight: 500;
    border-radius: 12px;
    transition: all 0.3s ease;

    &.tag-limited {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
    }

    &.tag-hot {
      background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
      color: white;
    }

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    }
  }
}

.goods-footer {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  margin-top: auto;
}

.price-container {
  flex: 1;

  .current-price {
    display: flex;
    align-items: baseline;
    gap: 2px;

    .currency {
      font-size: 24px;
      font-weight: 600;
      color: #e74c3c;
    }

    .price {
      font-size: 36px;
      font-weight: 700;
      color: #e74c3c;
      letter-spacing: -0.5px;
    }
  }

  .original-price {
    font-size: 22px;
    color: #95a5a6;
    text-decoration: line-through;
    margin-top: 4px;
  }
}

.buy-button {
  width: 56px;
  height: 56px;
  background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  box-shadow: 0 4px 16px rgba(116, 185, 255, 0.4);

  .buy-icon {
    width: 24px;
    height: 24px;
    color: white;
  }

  &:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 20px rgba(116, 185, 255, 0.6);
  }

  &:active {
    transform: scale(0.95);
  }
}
</style>
