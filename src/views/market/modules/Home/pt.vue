<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2022-06-16 10:22:21
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-08-16 17:37:28
-->
<template>
  <div class="home">
    <div v-if="ptData.length>0" class="d_pt">
      <div class="d_pt_time">
        <div class="d_pt_time_bj" />

        <van-count-down :time="day_limit()">
          <template #default="timeData">
            <div class="count_down">
              <div class="block">{{ timeData.hours }}</div>
              <div class="block">{{ timeData.minutes }}</div>
              <div class="block">{{ timeData.seconds }}</div>
            </div>
          </template>
        </van-count-down>

      </div>
      <div class="d_pt_people">
        <div>
          <img class="ptanmint" src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/market/market/home/<USER>" alt="">
        </div>
        <div @click="goPt">
          <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/market/market/home/<USER>" alt="">
        </div>
      </div>
      <div class="d_pt_list">
        <div class="d_pt_box">
          <div v-for="item in ptData" :key="item.activityJoinNo+'pts'" @click="goMarketDetil(item)">
            <PtGoods :item="item" />
          </div>

        </div>
      </div>
    </div>
  </div>
</template>

<script>
import PtGoods from './pt-goods.vue'
import { pointActivityDisplay } from '@/api/market/home'
import { activityNo } from '@/config/die'
export default {
  components: {
    PtGoods
  },
  data() {
    return {
      activityNo: activityNo,
      ptData: [],
      time: new Date(new Date(new Date().toLocaleDateString()).getTime() + 24 * 60 * 60 * 1000 - 1)
    }
  },
  created() {
    this.mallPtGoodsShowList()
  },
  methods: {
    mallPtGoodsShowList() {
      pointActivityDisplay({ activityNo: this.activityNo }).then(res => {
        if (res.status == 200) {
          this.ptData = res.data.joinDetailList
        }
      })
    },
    goMarketDetil(item) {
      if (item.activityStatus == 3) {
        return
      }
      this.$router.push({
        name: 'MarketPtDetails',
        query: {
          id: item.activityJoinNo,
          goodsId: item.goodsId,
          activityNo: this.activityNo,
          activityJoinNo: item.activityJoinNo
        }
      })
    },
    goPt() {
      this.$router.push({
        name: 'MarketPt',
        query: {
          activityNo: this.activityNo
        }
      })
    },
    day_limit() {
      var dayjs = require('dayjs')
      var date1 = new Date() // 开始时间，当前时间
      var date2 = dayjs(dayjs().format('YYYY-MM-DD') + ' 23:59:59').valueOf()
      var date3 = date2 - date1.getTime() // 时间差的毫秒数
      return date3
    }
  }
}
</script>

<style scoped lang="scss">
    .home {
        .d_pt{
            width: 722px;
            height: 630px;
            background-image: url(https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/market/market/home/<USER>
            background-size: 100% 100%;
            margin: 0 auto;
            margin-top: 30px;
            overflow: hidden;
            .d_pt_time{
                width: 406px;
                height: 58px;
                margin: 0 auto;
                margin-top: 8px;
                display: flex;
                justify-content: space-between;
                .d_pt_time_bj{
                  width: 218px;
                  height: 58px;
                  background-image: url(https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/market/market/home/<USER>
                  background-size: 100% 100%;
                }
                .count_down{
                  width: 172px;
                  height: 44px;
                  background-image: url(https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/market/market/home/<USER>
                  background-size: 100% 100%;
                  display: flex;
                  font-size: 30px;
                  justify-content: space-between;
                  margin-top: 6px;
                  div{
                    width: 43px;
                    height: 44px;
                    line-height: 50px;
                    text-align: center;
                    color: #5E310A;
                    font-family: PingFangSC-Medium;
                  }
                }
            }
            .d_pt_people{
                display: flex;
                width: 685px;
                justify-content: space-between;
                height: 48px;
                font-size: 30px;
                margin: 0 auto;
                margin-top: 25px;
                img{
                    width: 148px;
                    height: 48px;
                }
                .ptanmint{
                  width: 368px;
                    height: 42px;
                }
            }
            .d_pt_list{
                width: 694px;
                height: 450px;
                background-image: url(https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/market/market/home/<USER>
                background-size: 100% 100%;
                margin: 0 auto;
                margin-top: 20px;
                overflow: hidden;
                .d_pt_box{
                    margin-top: 90px;
                    margin-left: 20px;
                    display: flex;
                    overflow-x: auto;
                }
            }
        }
    }
</style>
