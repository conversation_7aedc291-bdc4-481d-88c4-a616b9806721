<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2022-06-16 11:34:36
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-06-24 15:09:39
-->
<template>
  <div class="goods-list">
    <div class="goods-container">
      <div class="column column-left">
        <goodsCard
          v-for="(item, index) in itemList"
          v-show="index % 2 == 0"
          :key="index + 'left'"
          :test="0"
          :item="item"
        />
      </div>
      <div class="column column-right">
        <goodsCard
          v-for="(item, index) in itemList"
          v-show="index % 2 != 0"
          :key="index + 'right'"
          :test="0"
          :item="item"
        />
      </div>

    </div>

    <!-- 空状态 -->
    <div v-if="itemList.length == 0" class="empty-state">
      <div class="empty-content">
        <div class="empty-image">
          <img
            src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/empty/empty.png"
            alt="暂无商品"
          >
        </div>
        <div class="empty-text">
          <h3 class="empty-title">暂时没有上架商品哦</h3>
          <p class="empty-subtitle">稍后再来看看吧</p>
        </div>
        <div class="empty-decoration">
          <div class="decoration-circle circle-1" />
          <div class="decoration-circle circle-2" />
          <div class="decoration-circle circle-3" />
        </div>
      </div>
    </div>
    <div style="height:50px;" />
  </div>
</template>

<script>
import { mallGoodsShelvesShowList } from '@/api/market/home'
import goodsCard from './goodsCard.vue'
export default {
  components: {
    goodsCard
  },
  data() {
    return {
      itemList: []
    }
  },
  created() {
    this.mallGoodsShelvesShowList()
  },
  methods: {
    mallGoodsShelvesShowList() {
      let data = {
        pageNum: 1,
        pageSize: 100,
        search: this.$store.getters.getRegionId
      }
      mallGoodsShelvesShowList(data).then((res) => {
        this.itemList = res.data.list
      })
    }
  }
}
</script>

<style scoped lang="scss">
.goods-list {
  width: 100%;
  margin: 0 auto;
  padding: 20px 15px 60px;
  background: linear-gradient(180deg, #f8f9fa 0%, #ffffff 100%);
  min-height: 100vh;
}

.goods-container {
  display: flex;
  gap: 16px;
  justify-content: space-between;
  position: relative;

  // 添加微妙的背景装饰
  &::before {
    content: "";
    position: absolute;
    top: -20px;
    left: -20px;
    right: -20px;
    bottom: -20px;
    background: radial-gradient(
        circle at 20% 20%,
        rgba(74, 144, 226, 0.03) 0%,
        transparent 50%
      ),
      radial-gradient(
        circle at 80% 80%,
        rgba(245, 101, 101, 0.03) 0%,
        transparent 50%
      ),
      radial-gradient(
        circle at 40% 60%,
        rgba(129, 236, 236, 0.03) 0%,
        transparent 50%
      );
    pointer-events: none;
    z-index: -1;
  }

  .column {
    flex: 1;
    display: flex;
    flex-direction: column;

    &.column-left {
      animation: slideInLeft 0.6s ease-out;
    }

    &.column-right {
      animation: slideInRight 0.6s ease-out 0.2s both;
    }
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.empty-state {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;

  .empty-content {
    text-align: center;
    position: relative;
    z-index: 2;
    animation: fadeInUp 0.8s ease-out;
  }

  .empty-image {
    width: 300px;
    height: 220px;
    margin: 0 auto 40px;
    position: relative;

    img {
      width: 100%;
      height: 100%;
      object-fit: contain;
      filter: drop-shadow(0 10px 30px rgba(0, 0, 0, 0.2));
      animation: float 3s ease-in-out infinite;
    }
  }

  .empty-text {
    .empty-title {
      font-size: 32px;
      font-weight: 600;
      color: #ffffff;
      margin: 0 0 16px 0;
      text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
      letter-spacing: 0.5px;
    }

    .empty-subtitle {
      font-size: 24px;
      color: rgba(255, 255, 255, 0.8);
      margin: 0;
      text-shadow: 0 1px 5px rgba(0, 0, 0, 0.2);
    }
  }

  .empty-decoration {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
    z-index: 1;

    .decoration-circle {
      position: absolute;
      border-radius: 50%;
      background: rgba(255, 255, 255, 0.1);
      animation: float 4s ease-in-out infinite;

      &.circle-1 {
        width: 120px;
        height: 120px;
        top: 10%;
        left: 10%;
        animation-delay: 0s;
      }

      &.circle-2 {
        width: 80px;
        height: 80px;
        top: 20%;
        right: 15%;
        animation-delay: 1s;
      }

      &.circle-3 {
        width: 60px;
        height: 60px;
        bottom: 20%;
        left: 20%;
        animation-delay: 2s;
      }
    }
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-15px);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .goods-list {
    padding: 15px 10px 50px;
  }

  .goods-container {
    gap: 12px;
  }

  .empty-state {
    .empty-image {
      width: 250px;
      height: 180px;
      margin-bottom: 30px;
    }

    .empty-text {
      .empty-title {
        font-size: 28px;
      }

      .empty-subtitle {
        font-size: 22px;
      }
    }
  }
}

// 加载动画
.goods-list {
  opacity: 0;
  animation: fadeIn 0.5s ease-out 0.3s forwards;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
</style>
