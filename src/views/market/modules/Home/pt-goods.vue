<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2022-05-31 17:57:58
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-06-24 18:43:01
-->
<template>
  <div class="home">
    <div class="d_img">
      <img :src="item.pictureUrl" alt="" class="d_img_bj">
      <img
        v-if="false"
        class="d_off"
        src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/home/<USER>/activity/off.png"
        alt=""
      >
      <img
        class="ptsptag"
        src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/market/market/home/<USER>"
        alt=""
      >
    </div>
    <div class="d_title">
      {{ item.goodsName }}
    </div>
    <div class="d_guiji">
      <div class="d_guiji_or">￥{{ item.originPrice }}</div>
      <div class="d_guiji_msg">拼团价</div>
      <div class="d_guiji_or">￥{{ item.originPrice }}</div>
    </div>
    <div class="d_btn">
      <!-- <span>最低￥</span> -->
      <span class="d_btn_price">￥{{ item.pintuanPrice }}</span>
      <!-- <span> 起</span> -->
    </div>
  </div>
</template>

<script>
export default {
  props: {
    item: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  data() {
    return {}
  },
  created() {},
  mounted() {},
  methods: {}
}
</script>

<style scoped lang="scss">
.home {
  width: 224px;
  height: 340px;
  background: #ffffff;
  border-radius: 8px;
  border: 1px solid #dcdcdc;
  margin-right: 15px;
  border-radius: 16px;

  // margin-top: 100px;
  .d_img {
    width: 224px;
    height: 194px;
    position: relative;
    text-align: center;
    .ptsptag {
      width: 123px;
      height: 38px;
      position: absolute;
      top: 0;
      left: 0;
    }
    .d_off {
      position: absolute;
      top: 0;
      left: 0;
      z-index: 99;
      width: 222px;
      height: 194px;
      float: left;
      border-top-left-radius: 16px;
      border-top-right-radius: 16px;
    }
    .d_img_bj {
      width: 222px;
      height: 194px;
      float: left;
      border-top-left-radius: 16px;
      border-top-right-radius: 16px;
    }
  }
  .d_title {
    width: 224px;
    height: 50px;
    overflow: hidden;
    font-size: 26px;
    color: #454545;
    font-family: PingFangSC-Medium;
    padding: 7px;
  }
  .d_guiji {
    width: 213px;
    height: 44px;
    background-image: url(https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/market/market/home/<USER>
    background-size: 100% 100%;
    padding: 2px;
    display: flex;
    justify-content: space-between;
    font-size: 19px;
    color: #a6a6a6;
    .d_guiji_or {
      margin-top: 3px;
    }
    .d_guiji_msg {
      margin-top: -6px;
      font-size: 20px;
    }
  }
  .d_btn {
    width: 154px;
    // max-width: 200px;
    height: 34px;
    line-height: 34px;
    text-align: center;
    color: #fff;
    border-radius: 17px;
    font-size: 20px;
    // background-image: url(https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/market/index/btnbj.png);
    // background-size: 100% 100%;
    background: linear-gradient(90deg, #ffc674, #ffa31e, #ff3900);
    margin: 0 auto;
    letter-spacing: 1px;
    overflow: hidden;
    .d_btn_price {
      font-size: 30px;
      position: relative;
      top: 2px;
    }
  }
  .d_off_btn {
    height: 38px;
    border-radius: 19px;
    background: #dddddd;
    margin-top: 5px;
    line-height: 35px;
  }
}
@media screen and (-webkit-min-device-pixel-ratio:2) {
   .border::after{
     content: '';
     position: absolute;
     left: 0;
     top: 0;
     border: 1px solid #000;
     box-sizing: border-box;
     width: 100%;
     height: 100%;
     transform-origin: 0 0;
     transform: scale(0.5);
   }
}
</style>
