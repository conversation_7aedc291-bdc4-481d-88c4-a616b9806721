<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2022-04-29 15:15:34
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-06-23 20:47:33
-->
<template>
  <div class="home">
    <div class="flex-col d_market_order">
      <div class="justify-between d_market_order_status">
        <div class="d_market_order_status_name">包裹1</div>
        <div v-if="detail.orderStatus == 0" class="d_market_order_status_types">
          <van-count-down style="color:red" :time="detail.payTimeOut*1000" />
          <div> 将自动取消订单</div>
        </div>
        <div v-if="detail.orderRefund == null" style="color:red" class="d_market_order_status_types">{{ detail.cancelType }}</div>
        <div v-else @click="repairshow = true">
          <div v-if="detail.orderRefund.refundStatus == 1" style="color:red" class="d_market_order_status_types">退款处理中</div>
          <div v-if="detail.orderRefund.refundStatus == 2" style="color:red" class="d_market_order_status_types">退款中</div>
          <div v-if="detail.orderRefund.refundStatus == 4" style="color:red" class="d_market_order_status_types">退款失败</div>
          <div v-if="detail.orderRefund.refundStatus == 3" class="d_market_order_status_types">
            <van-button plain type="primary" size="mini">退款完成</van-button>
          </div>
        </div>

      </div>
      <div class="flex-row d_market_order_goods">
        <img
          :src="detail.mainPictureUrl"
          class="image"
        >
        <div class="flex-col d_market_order_goods_info">
          <div class="d_market_order_goods_title">{{ detail.orderItems[0].goodsName }}</div>
          <div v-if="detail.deliveryType == 1" class="flex-col text-wrapper">
            <span class="goods_tag">同城配送</span>
          </div>
          <div class="flex-row d_market_order_goods_price">
            <span class="goods_price">￥{{ detail.orderItems[0].sellPrice }}</span>
            <div v-if="detail.campaignType == 2" class="flex-col goods_orprice">
              <span>￥{{ detail.orderItems[0].originPrice }}</span>
              <div class="divider" />
            </div>
          </div>
        </div>
        <div class="d_market_goods_num">
          x{{ detail.orderItems[0].skuQuantity }}
        </div>
      </div>
    </div>

    <van-popup v-model="repairshow" round closeable position="bottom" :style="{ height: '350px',width: '100%' }" @click-close-icon="repairshow = false">
      <div class="title" @click="repairshow = false">退款详情</div>
      <van-steps v-if="repairshow" direction="vertical" :active="detail.orderRefund.orderRefundProgress.length-1" class="vertical">
        <van-step v-for="item in detail.orderRefund.orderRefundProgress" :key="item.id">
          <div>
            <span>{{ item.content }}</span>
          </div>
          <p v-if="item.part === 1" style="color:#999;font-size: 13px;">{{ item.refundReason }}</p>
          <p v-if="item.part === 2" style="color:#999;font-size: 13px;">{{ item.refuseReason }}</p>
          <p v-if="item.process ===200" style="color:#999;font-size: 13px;">24小时内资金原路返回</p>
          <p style="color:#999;font-size: 13px;">{{ item.createTime }}</p>
        </van-step>
      </van-steps>
    </van-popup>
  </div>
</template>

<script>
export default {
  props: {
    detail: {
      type: Object,
      default: function() {
        return {}
      }
    }
  },
  data() {
    return {
      repairshow: false
    }
  },
  created() {},
  mounted() {
    console.log(this.detail)
  },
  methods: {}
}
</script>

<style scoped lang="scss">
.home {
  .d_market_order {
    margin-top: 20px;
    padding: 0 20px;
    background-color: rgb(255, 255, 255);
    .d_market_order_status {
      padding: 20px 12px 19px;
      border-bottom: solid 2px rgb(244, 244, 244);
      .d_market_order_status_name {
        width: 60%;
        color: rgb(51, 51, 51);
        font-size: 30px;
        font-weight: 500;
        line-height: 42px;
        white-space: nowrap;
      }
      .d_market_order_status_types {
        display: flex;
        margin: 3px 0;
        color: rgb(153, 153, 153);
        font-size: 28px;
        font-weight: 500;
        line-height: 37px;
        letter-spacing: -0.2px;
        white-space: nowrap;
      }
    }
    .d_market_order_goods {

      padding: 23px 8px 24px 12px;
      .image {
        border-radius: 8px;
        width: 160px;
        height: 160px;
      }
      .d_market_order_goods_info {
        margin-left: 16px;
        flex: 1 1 auto;
        .d_market_order_goods_title {
          width: 450px;
          min-height: 75px;
          color: rgb(51, 51, 51);
          font-size: 32px;
          font-weight: 500;
          line-height: 45px;
          word-wrap:break-word;
        }
        .text-wrapper {
          align-self: flex-start;
          color: rgb(255, 163, 0);
          font-size: 26px;
          font-weight: 500;
          line-height: 37px;
          white-space: nowrap;
          background-color: rgb(255, 245, 193);
          border-radius: 4px;
          .goods_tag {
            font-family: PingFangSC-Medium;
            margin: 0 8px;
          }
        }
        .d_market_order_goods_price {
          padding-top: 8px;
          .goods_price {
            color: rgb(51, 51, 51);
            font-size: 32px;
            font-weight: 500;
            line-height: 45px;
            white-space: nowrap;
          }
          .goods_orprice {
            margin: 6px 0 6px 10px;
            color: rgb(153, 153, 153);
            font-size: 24px;
            line-height: 33px;
            white-space: nowrap;
            position: relative;
            .divider {
              background-color: rgb(153, 153, 153);
              width: 54px;
              height: 2px;
              position: absolute;
              left: 50%;
              top: 50%;
              transform: translate(-50%, -50%);
            }
          }
        }
      }
      .d_market_goods_num{
        font-size: 28px;
        margin-top: 35px;
        color: #999;
      }
    }
  }
  .title {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 10;
    width: 100%;
    box-sizing: border-box;
    text-align: center;
    height: 124px;
    line-height: 104px;
    font-size: 38px;
    color: #000;
    border-radius: 20px 20px 0 0;
  }
  .vertical{
    height: 580px;
    overflow-y: auto;
    margin-top: 90px;
  }
}
</style>
