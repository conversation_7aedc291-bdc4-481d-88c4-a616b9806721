<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2022-04-29 15:57:17
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-06-24 11:22:02
-->
<template>
  <div class="home">
    <div class="flex-col section_2">
      <div v-if="detail.deliveryType == 2" class="d_zt">
        <div class="d_zt_left">
          <div class="d_zt_title">
            <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/market/order/mdzt2.png" alt="">
            {{ detail.orderSelfTakeMsg.selfTakeName }}
          </div>
          <div class="d_zt_address">地址：{{ detail.orderSelfTakeMsg.selfTakeProvince+detail.orderSelfTakeMsg.selfTakeCity+detail.orderSelfTakeMsg.selfTakeDistrict+detail.orderSelfTakeMsg.selfTakeAddress }}</div>
          <!-- <div class="d_zt_time" :class="detail.orderStatus===80?'isclose':''">自提时间：{{ formatTime(detail.orderSelfTakeMsg.selfTakeTime) }}</div> -->
          <div class="d_zt_qh" :class="detail.orderStatus===80?'isclose':''">取货码
            <span>{{ detail.orderSelfTakeMsg.selfTakeNo==null?'--':detail.orderSelfTakeMsg.selfTakeNo }}</span>
          </div>
        </div>
        <div class="d_zt_right" @click="goMarket(detail.orderSelfTakeMsg)">
          <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/market/order/dh.png" alt="">
        </div>
      </div>
      <div v-if="detail.deliveryType == 1" class="flex-col group_8">
        <div class="flex-row">
          <div class="d_market_item_title">收货信息：</div>
          <span class="d_market_item_cont">{{ detail.orderAddress.receiver }} {{ detail.orderAddress.receiverTelephone }}</span>
        </div>
        <span class="d_market_item_address">{{ detail.orderAddress.receiverAddress }}</span>
      </div>
      <div class="divider_1" />
      <div class="group_10 flex-col">
        <div class="flex-row">
          <div class="d_market_item_title">支付方式：</div>
          <span v-if="detail.paymentType === 0" class="d_market_item_cont">未付款</span>
          <span v-if="detail.paymentType === 1" class="d_market_item_cont">余额</span>
          <span v-if="detail.paymentType === 2" class="d_market_item_cont">支付宝</span>
          <span v-if="detail.paymentType === 3" class="d_market_item_cont">微信</span>
          <span v-if="detail.paymentType === 5" class="d_market_item_cont">外部支付宝</span>
          <span v-if="detail.paymentType === 6" class="d_market_item_cont">外部微信</span>
          <span v-if="detail.paymentType === 7" class="d_market_item_cont">二类户</span>
          <span v-if="detail.paymentType === 9" class="d_market_item_cont">云闪付</span>
        </div>
        <div class="flex-row item">
          <div class="d_market_item_title">商品合计：</div>
          <span class="d_market_item_cont">￥{{ detail.goodsPay }}</span>
        </div>
        <div v-if="detail.deliveryType == 1" class="flex-row item">
          <div class="d_market_item_title">运费：</div>
          <span class="d_market_item_cont">￥{{ detail.postFee }}</span>
        </div>
      </div>
      <div v-if="detail.couponsAmount>0" class="divider_1" />
      <div v-if="detail.couponsAmount>0" class="group_10 flex-row">
        <div class="d_market_item_title">优惠券：</div>
        <span class="d_market_item_cont"> -￥{{ detail.couponsAmount }}</span>
      </div>
      <div class="divider_1" />
      <div class="group_10 flex-row">
        <div class="d_market_item_title">实付：</div>
        <span class="d_market_item_cont">￥{{ detail.actualPay }}</span>
      </div>
      <div class="divider_1" />
      <div class="group_10 flex-col">
        <div class="flex-row">
          <div class="d_market_item_title">下单时间：</div>
          <span class="d_market_item_cont">{{ detail.createTime }}</span>
        </div>
        <div class="flex-row item">
          <div class="d_market_item_title">订单编号：</div>
          <span class="d_market_item_cont">{{ detail.orderNo }}</span>
        </div>
      </div>
    </div>

    <van-popup v-model="showPops" closeable position="bottom" round :style="{ height: '80%' }">
      <div>
        <ShopNavigation v-if="showPops" :item="distribution" />
      </div>
    </van-popup>

    <!-- 导航 -->
    <van-popup v-model="showPop" position="bottom" round>
      <div class="mapList">
        <div @click="openMap(1)">百度导航</div>
        <div @click="openMap(2)">高德导航</div>
        <div @click="showPop=false">取消</div>
      </div>
    </van-popup>
  </div>
</template>

<script>
import ShopNavigation from '@/views/market/components/ShopNavigation.vue'
export default {
  components: {
    ShopNavigation
  },
  props: {
    detail: {
      type: Object,
      default: function() {
        return {}
      }
    }
  },
  data() {
    return {
      showPops: false,
      showPop: false,
      distribution: ''
    }
  },
  created() {},
  mounted() {},
  methods: {
    formatTime(item) {
      var dayjs = require('dayjs')

      var startTime = dayjs().valueOf()// 计算当前时间戳 (毫秒级)
      var endTime = dayjs(item).valueOf()
      var date32 = endTime - startTime
      // 计算出相差天数sss
      var days = date32 / (24 * 3600 * 1000)
      if (days > 0 && days <= 1) {
        return dayjs(item).format('YYYY-MM-DD HH:mm:ss') + '（明天）'
      }

      var weekday = require('dayjs/plugin/weekday')
      dayjs.extend(weekday)

      let weekdats = ''
      if (dayjs(item).weekday() == 1) {
        weekdats = '一'
      } else if (dayjs(item).weekday() == 2) {
        weekdats = '二'
      } else if (dayjs(item).weekday() == 3) {
        weekdats = '三'
      } else if (dayjs(item).weekday() == 4) {
        weekdats = '四'
      } else if (dayjs(item).weekday() == 5) {
        weekdats = '五'
      } else if (dayjs(item).weekday() == 6) {
        weekdats = '六'
      } else if (dayjs(item).weekday() == 0) {
        weekdats = '天'
      }

      return dayjs(item).format('YYYY-MM-DD HH:mm') + '（周' + weekdats + '）'
    },
    // 打开地图
    openMap(val) {
      let self = this
      if (val == 1) {
        var urlBaiduMap =
						`baidumap://map/marker?location=${this.mapData.bd_lat},${this.mapData.bd_lng}&title=${this.mapData.marketName}&content=${this.mapData.marketName}&src=Hello%20uni-app`
        AlipayJSBridge.call(
          'IsAvailable', {
            packageName: 'com.baidu.BaiduMap'
          },
          function(result) {
            if (result.available == true) {
              window.location.href = urlBaiduMap
            } else {
              self.$toast('未安装百度地图')
            }
          }
        )
      } else {
        var urlAmap =
						`androidamap://viewMap?sourceApplication=${this.mapData.marketName}&poiname=${this.mapData.marketName}&lat=${this.mapData.gd_lat}&lon=${this.mapData.gd_lng}&dev=0`

        var iosAmap =
						`iosamap://viewMap?sourceApplication=${this.mapData.marketName}&poiname=${this.mapData.marketName}&lat=${this.mapData.gd_lat}&lon=${this.mapData.gd_lng}&dev=0`
        var u = navigator.userAgent
        var isiOS = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/) // ios终端
        if (!isiOS) {
          AlipayJSBridge.call(
            'IsAvailable', {
              packageName: 'com.autonavi.minimap'
            },
            function(result) {
              if (result.available == true) {
                window.location.href = urlAmap
              } else {
                self.$toast('未安装高德地图')
              }
            }
          )
        } else {
          window.location.href = iosAmap
        }
      }
    },
    // 导航店铺
    goMarket(data) {
      console.log(data)
      let datas = {
        locationName: data.selfTakeName,
        distance: 0,
        province: data.selfTakeProvince,
        city: data.selfTakeCity,
        district: data.selfTakeDistrict,
        selectAddress: data.selfTakeAddress,
        address: '',
        photo: ''
      }

      this.distribution = datas
      this.showPops = true
    }
  }
}
</script>

<style scoped lang="scss">
.home {
  .mapList {
    div {
      height: 150px;
      line-height: 150px;
      text-align: center;
      font-size: 28px;
    }

    div:not(:last-child) {
      border-bottom: 1px solid #EEEEEE;
      font-weight: bold;
    }
  }
  .d_zt{
    display: flex;
    font-size: 28px;
    padding: 30px;
    .d_zt_left{
      width: 90%;
    }
    .d_zt_title{
      height: 43px;
      line-height: 43px;
      color: #333333;
      display: flex;
      font-size: 32px;
      img{
        width: 160px;
        height: 40px;
        margin-right: 13px;
      }
    }
    .d_zt_address{
      width: 540px;
      color: #999999;
      margin-top: 9px;
    }
    .d_zt_time{
      color: #ff301e;
      margin-top: 9px;
    }
    .d_zt_qh{
      color: #333333;
      font-family: PingFangSC-Medium;
      margin-top: 12px;
      font-size: 32px;
      span{
        font-size: 35px;
      }
    }
    .isclose{
      color: #999;
    }
    .d_zt_right{
      margin-top: 20px;
      img{
        width: 39px;
        height: 80px;
      }
    }
  }
  .section_2 {
    margin-top: 16px;
    background-color: rgb(255, 255, 255);
    .group_8 {
      padding: 24px 32px 24px;
    }
    .divider_1 {
      margin: 0 20px;
      background-color: rgb(244, 244, 244);
      height: 1px;
    }
    .group_10 {
      padding: 23px 32px 24px;
      .item {
        margin-top: 24px;
      }

    }
    .d_market_item_title {
        width: 130px;
        color: rgb(144, 145, 147);
        font-size: 28px;
        line-height: 40px;
        white-space: nowrap;
      }
    .d_market_item_cont{
        color: rgb(69, 69, 69);
        font-size: 28px;
        line-height: 40px;
        white-space: nowrap;
        text-align: left;
    }
    .d_market_item_address{
        margin-left: 130px;
        color: rgb(69, 69, 69);
        font-size: 28px;
        line-height: 40px;
        white-space: nowrap;
        text-align: left;
        margin-top: 6px;
    }
  }
}
</style>
