<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2022-04-29 16:08:12
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-06-23 20:49:06
-->
<template>
  <div class="home" :style="styleVar">
    <div v-if="detail.orderRefund==null">
      <div v-if="detail.orderStatus===50||detail.orderStatus===70||detail.orderStatus===80" class="flex-col items-end d_market_bottom1">
        <div class="flex-row section_4" @click="CallPhone">
          <img
            src="https://codefun-proj-user-res-1256085488.cos.ap-guangzhou.myqcloud.com/6114f1316b68fa00119edb1e/626b447c4eb5590011d400af/16512161049711651472.png"
            class="image_1"
          >
          <span class="text_28">联系客服</span>
        </div>
      </div>

      <div v-if="detail.orderStatus===20||detail.orderStatus===40||detail.orderStatus===30||detail.orderStatus===31||detail.orderStatus===10" class="flex-col d_market_bottom2">
        <div class="justify-between equal-division">
          <div class="flex-row equal-division-item" @click="CallPhone">
            <img
              src="https://codefun-proj-user-res-1256085488.cos.ap-guangzhou.myqcloud.com/6114f1316b68fa00119edb1e/626b447c4eb5590011d400af/16512161049711651472.png"
              class="image_1"
            >
            <span class="text_27">联系客服</span>
          </div>
          <div v-show="true" class="flex-col items-center equal-division-item_1" @click="repairshow = true">
            <span>取消订单</span>
          </div>
        </div>
      </div>

      <div v-if="detail.orderStatus===0" class="justify-between d_market_bottom3">
        <div class="flex-row group_16">
          <div class="left-text-wrapper flex-col items-center" @click="CallPhone">
            <span>联系客服</span>
          </div>
          <div v-show="true" class="left-text-wrapper flex-col items-center view_7" @click="cancelOrder">
            <span>取消订单</span>
          </div>
        </div>
        <div class="flex-col items-center text-wrapper_1" @click="payOrder">
          <span>立即付款</span>
        </div>
      </div>
    </div>
    <!-- 存在售后情况 -->
    <div v-else class="flex-col items-end d_market_bottom1">
      <div v-if="detail.refundStatus==0" class="flex-col d_market_bottom2">
        <div class="justify-between equal-division">
          <div class="flex-row equal-division-item" @click="CallPhone">
            <img
              src="https://codefun-proj-user-res-1256085488.cos.ap-guangzhou.myqcloud.com/6114f1316b68fa00119edb1e/626b447c4eb5590011d400af/16512161049711651472.png"
              class="image_1"
            >
            <span class="text_27">联系客服</span>
          </div>
          <div v-show="true" class="flex-col items-center equal-division-item_1" @click="repairshow = true">
            <span>取消订单</span>
          </div>
        </div>
      </div>
      <div v-else class="flex-row section_4" @click="CallPhone">
        <img
          src="https://codefun-proj-user-res-1256085488.cos.ap-guangzhou.myqcloud.com/6114f1316b68fa00119edb1e/626b447c4eb5590011d400af/16512161049711651472.png"
          class="image_1"
        >
        <span class="text_28">联系客服</span>
      </div>
    </div>

    <van-popup v-model="repairshow" round closeable position="bottom">
      <div>
        <div class="title">地址</div>
        <div class="flex-col section_1">
          <span class="text_msg" :class="reason=='不想要了'?'text_2':''" @click="reason = '不想要了'">不想要了</span>
          <div class="divider" />
          <span class="text_msg" :class="reason=='收货地址或手机号错了'?'text_2':''" @click="reason = '收货地址或手机号错了'">收货地址或手机号错了</span>
          <div class="divider" />
          <span class="text_msg" :class="reason=='太久时间还未发货'?'text_2':''" @click="reason = '太久时间还未发货'">太久时间还未发货</span>
          <div class="divider" />
          <span class="text_msg" :class="reason=='和客服协商一直退款'?'text_2':''" @click="reason = '和客服协商一直退款'">和客服协商一直退款</span>
          <div class="divider" />
          <span class="text_msg" :class="reason=='其他'?'text_2':''" @click="reason = '其他'">其他</span>
          <div class="flex-row group">
            <div class="flex-col items-center text-wrapper" @click="repairshow = false">
              <span>暂不退款</span>
            </div>
            <div class="flex-col items-center text-wrapper_1" @click="refundApply">
              <span>确认退款</span>
            </div>
          </div>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script>
import { cancelOrder, refundApply } from '@/api/market'
export default {
  props: {
    detail: {
      type: Object,
      default: function() {
        return {}
      }
    }
  },
  data() {
    return {
      reason: '',
      repairshow: false
    }
  },
  computed: {
    styleVar() {
      return {
        '---nav-height': this.$store.getters.getNavigationBarHeight + 'px'
      }
    }
  },
  created() {},
  methods: {
    // 取消订单
    cancelOrder() {
      let self = this
      this.$dialog
        .confirm({
          title: '确认取消该订单吗？',
          message: '',
          confirmButtonColor: '#6095f0'
        })
        .then(() => {
          cancelOrder({ orderNo: self.$route.query.orderNo }).then(res => {
            if (res.status === 200) {
              self.$toast('提交成功')
              self.$emit('restOrder')
            }
          })
        })
    },
    // 取消订单---退款！
    refundApply() {
      let data = {
        orderNo: this.$route.query.orderNo,
        reason: this.reason
      }
      this.$toast.loading({
        duration: 0,
        forbidClick: true,
        message: ''
      })
      refundApply(data).then(res => {
        if (res.status === 200) {
          this.$toast('提交成功')
          this.repairshow = false
          this.$emit('restOrder')
        }
      })
    },
    // 支付订单
    payOrder() {
      // 判断couponPayStyleList  [7]是否只有7

      if (this.detail.couponPayStyleList.length == 1 && this.detail.couponPayStyleList[0] == 7) {
        this.$router.push({
          name: 'MarketPay',
          query: {
            orderNo: this.$route.query.orderNo,
            actualPay: this.detail.actualPay,
            marketId: this.detail.marketId,
            poolId: this.detail.poolId,
            from: '2',
            isOnlyBalancePay: 1
          }
        })
        return
      }

      this.$router.push({
        name: 'MarketPay',
        query: {
          orderNo: this.$route.query.orderNo,
          actualPay: this.detail.actualPay,
          marketId: this.detail.marketId,
          poolId: this.detail.poolId,
          from: '2'
        }
      })
    },
    // 打电话
    CallPhone() {
      let phone = '18757156043'
      if (this.$store.getters.getRegionId == 1) {
        phone = '15021881176'
      } else if (this.$store.getters.getRegionId == 3) {
        phone = '17605781836'
      } else if (this.$store.getters.getRegionId == 6) {
        phone = '13587187911'
      } else if (this.$store.getters.getRegionId == 7) {
        phone = '13566993383'
      }
      AlipayJSBridge.call('CallPhone', {
        phoneNum: phone
      }, function(result) {})
    }
  }
}
</script>

<style scoped lang="scss">
.home {
  background-color: #fff;
  height: calc(140px + var(---nav-height));
  position: fixed;
  left: 0;
  bottom: 0;
  .d_market_bottom1 {
    width: 750px;
    padding: 16px 0;
    color: rgb(102, 102, 102);
    font-size: 35px;
    line-height: 53px;
    letter-spacing: -0.28px;
    white-space: nowrap;
    background-color: rgb(255, 255, 255);
    box-shadow: -4px -4px 17px 0px rgba(220, 220, 220, 0.5);
    .section_4 {
      margin-right: 32px;
      padding: 19px 33px;
      border-radius: 47px;
      border: solid 2px rgb(102, 102, 102);
      .image_1 {
        width: 48px;
        height: 48px;
      }
      .text_28 {
        margin-left: 16px;
      }
    }
  }
  .d_market_bottom2 {
    width: 750px;
    padding: 16px 0;
    background-color: rgb(255, 255, 255);
    box-shadow: -4px -4px 27px 0px rgba(220, 220, 220, 0.5);
    .equal-division {
      margin: 0 32px;
      .equal-division-item {
        padding: 19px 33px;
        color: rgb(102, 102, 102);
        font-size: 35px;
        line-height: 53px;
        letter-spacing: -0.28px;
        white-space: nowrap;
        border-radius: 47px;
        height: 94px;
        border: solid 2px rgb(102, 102, 102);
        .image_1 {
          width: 48px;
          height: 48px;
        }
        .text_27 {
          margin-left: 16px;
        }
      }
      .equal-division-item_1 {
        padding: 21px 0;
        color: rgb(255, 255, 255);
        font-size: 38px;
        line-height: 53px;
        letter-spacing: -0.28px;
        white-space: nowrap;
        background-color: rgb(0, 0, 0);
        border-radius: 47px;
        width: 286px;
        height: 94px;
      }
    }
  }
  .d_market_bottom3 {
    width: 750px;
    padding: 16px 32px;
    background-color: rgb(255, 255, 255);
    box-shadow: -4px -4px 27px 0px rgba(220, 220, 220, 0.5);
    .group_16 {
      align-self: center;
      color: rgb(102, 102, 102);
      font-size: 28px;
      line-height: 40px;
      letter-spacing: -0.22px;
      white-space: nowrap;
      .left-text-wrapper {
        padding: 7px 0;
        border-radius: 29px;
        width: 146px;
        height: 58px;
        border: solid 2px rgb(154, 154, 154);
      }
      .view_7 {
        margin-left: 52px;
      }
    }
    .text-wrapper_1 {
      padding: 21px 0;
      color: rgb(255, 255, 255);
      font-size: 38px;
      line-height: 53px;
      letter-spacing: -0.28px;
      white-space: nowrap;
      background-image: linear-gradient(
        90deg,
        rgb(255, 30, 41) 0%,
        rgb(255, 30, 41) 0%,
        rgb(255, 90, 37) 100%,
        rgb(255, 90, 37) 100%
      );
      border-radius: 47px;
      width: 286px;
      height: 94px;
    }
  }
  .title {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 10;
    width: 100%;
    box-sizing: border-box;
    text-align: center;
    height: 124px;
    line-height: 104px;
    font-size: 38px;
    color: #000;
    border-radius: 20px 20px 0 0;
  }
  .section_1 {
    padding: 43px 30px 0;
    flex: 1 1 auto;
    background-color: rgb(255, 255, 255);
    border-radius: 0px;
    overflow-y: auto;
    margin-top: 50px;
    .text_msg {
      margin-left: 10px;
      color: rgb(34, 34, 34);
      font-size: 30px;
      line-height: 42px;
      white-space: nowrap;
      margin-top: 31px;
    }
    .divider {
      margin-top: 31px;
      background-color: rgba(0, 0, 0, 0.1);
      height: 2px;
    }
    .text_2 {
      margin-left: 10px;
      margin-top: 31px;
      color: rgb(22, 157, 27);
      font-size: 30px;
      line-height: 42px;
      white-space: nowrap;
    }

    .group {
      margin-top: 29px;
      padding: 31px 30px 30px;
      color: rgb(255, 255, 255);
      font-size: 32px;
      line-height: 45px;
      white-space: nowrap;
      border-top: solid 2px rgba(0, 0, 0, 0.1);
      .text-wrapper {
        padding: 22px 0;
        flex: 1 1 286px;
        background-color: rgb(198, 198, 198);
        border-radius: 8px;
        height: 88px;
      }
      .text-wrapper_1 {
        margin-left: 58px;
        padding: 22px 0;
        flex: 1 1 286px;
        background-image: linear-gradient(
          90deg,
          rgb(64, 210, 67) 0%,
          rgb(64, 210, 67) 0%,
          rgb(31, 196, 50) 100%,
          rgb(31, 196, 50) 100%
        );
        border-radius: 8px;
        height: 88px;
      }
    }
  }
}
</style>
