<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2022-04-29 14:52:45
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-06-15 15:00:41
-->
<template>
  <div class="home">
    <div class="d_market_order">
      <div class="d_market_order_time">
        <span>已拼商品</span>
        <div>
          <span v-if="item.orderStatus===0" class="status_text">待付款</span>
          <span v-if="item.orderStatus===10" class="status_text">已付款</span>
          <span v-if="item.orderStatus===20" class="status_text">待发货</span>
          <span v-if="item.orderStatus===30" class="status_text">配送中</span>
          <span v-if="item.orderStatus===40" class="status_text">待收货</span>
          <span v-if="item.orderStatus===50" class="status_text">订单完成</span>
          <span v-if="item.orderStatus===60" class="status_text">退款中</span>
          <span v-if="item.orderStatus===70" class="status_text">订单退款</span>
          <span v-if="item.orderStatus===80" class="status_text">订单取消</span>
        </div>

      </div>
      <div class="center-group">
        <img
          :src="item.pictureUrl"
          class="image"
        >
        <div class="right-group">
          <div class="title">{{ item.goodsName }}</div>
          <div class="bottom-group">
            <span class="price">￥{{ item.pintuanPrice }}</span>
            <div class="right-group_1">
              <span>拼团价</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    item: {
      type: Object,
      default: function() {
        return {}
      }
    }
  },
  data() {
    return {}
  },
  created() {},
  methods: {
    goPtDetile() {
      this.$router.push({
        name: 'MarketOrderPtDetails',
        query: {
          activityJoinNo: this.item.activityJoinNo,
          groupNo: this.item.groupNo,
          orderNo: this.item.orderNo
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
.home {
  margin-top: 20px;
  .d_market_order {
    width: 93%;
    border-radius: 20px;
    margin: 0 auto;
    display: flex;
    flex-direction: column;
    padding: 20px 20px 0;
    background-color: rgb(255, 255, 255);
    .d_market_order_time {
      margin-left: 12px;
      color: rgb(51, 51, 51);
      font-size: 29px;
      line-height: 40px;
      white-space: nowrap;
      display: flex;
      justify-content: space-between;
      .d_market_order_status_types{
        font-family: PingFangSC;
      }
    }
    .center-group {
      display: flex;
      flex-direction: row;
      margin-top: 17px;
      padding: 25px 12px 21px;
      border-top: solid 2px rgb(244, 244, 244);
      border-bottom: solid 2px rgb(244, 244, 244);
      .image {
        border-radius: 8px;
        width: 160px;
        height: 160px;
      }
      .right-group {
        display: flex;
        flex-direction: column;
        margin-left: 17px;
        .title {
          width: 500px;
          height: 80px;
          color: rgb(51, 51, 51);
          font-size: 32px;
          font-weight: 500;
          line-height: 45px;
          word-wrap: break-word;
          text-align: left;

        }
        .bottom-group {
          display: flex;
          flex-direction: row;
          margin-top: 22px;
          .price {
            color: rgb(51, 51, 51);
            font-size: 32px;
            font-weight: 500;
            line-height: 45px;
            white-space: nowrap;
            font-family: PingFangSC-Medium;
          }
          .right-group_1 {
            display: flex;
            flex-direction: column;
            margin: 6px 0 6px 10px;
            color: #999;
            font-size: 24px;
            line-height: 33px;
            white-space: nowrap;
            position: relative;
            .divider {
              background-color: rgb(153, 153, 153);
              width: 54px;
              height: 2px;
              position: absolute;
              left: 50%;
              top: 50%;
              transform: translate(-50%, -50%);
            }
          }
        }
      }
    }
    .bottom-group_1 {
      display: flex;
      justify-content: space-between;
      padding: 19px 12px 20px;
      .status_text {
        margin: 8px 0;
        color: #999;
        font-size: 30px;
        line-height: 40px;
        white-space: nowrap;
      }
      .right-group_2 {
        display: flex;
        flex-direction: row;
        color: #333333;
        font-size: 28px;
        line-height: 40px;
        white-space: nowrap;
        width: 296px;
        .left-text-wrapper {
          // font-family: PingFangSC-Medium;
          display: flex;
          flex-direction: column;
          padding: 6px 0;
          border-radius: 8px;
          height: 56px;
          border: solid 2px rgb(154, 154, 154);
          color: #333333;
          font-weight: 400;
          span {
            margin: 0 10px;
          }
        }
        .right-text-wrapper {
          // font-family: PingFangSC-Medium;
          color: #333333;
          font-weight: 400;
          display: flex;
          flex-direction: column;
          margin-left: 22px;
          padding: 6px 0;
          border-radius: 8px;
          height: 56px;
          border: solid 2px rgb(154, 154, 154);
          span {
            margin: 0 10px;
          }
        }
      }
    }
  }
}
</style>
