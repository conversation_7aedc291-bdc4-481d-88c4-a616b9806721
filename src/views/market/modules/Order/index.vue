<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2022-04-29 14:52:26
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-06-25 09:20:25
-->
<template>
  <div class="home">
    <NavHeight bgc="#fff" />
    <Top message="商城订单" :is-left="true" />
    <van-list
      v-model="loading"
      :finished="finished"
      :finished-text="list.length === 0?'':'没有更多了'"
      @load="onLoad"
    >
      <div v-for="item in list" :key="item.id" @click="goOrderD(item)">
        <OrderCardVue :item="item" />
      </div>
    </van-list>
    <div v-if="list.length === 0" class="emptyNull">
      <van-empty
        class="custom-image"
        image="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/empty/empty.png"
        description="您没有订单呢~"
        image-size="210"
      >
        <van-button plain round type="primary" class="bottom-button" @click="goMarket">去逛逛</van-button>
      </van-empty>
    </div>

    <!-- 加载状态 -->
    <Loading :show="loadingShow" />
  </div>
</template>

<script>
import Top from '../../components/Tops.vue'
import OrderCardVue from './components/OrderCard.vue'
import { getOrderList } from '@/api/market'
import Loading from '@/components/Loading/index'
import { activityNo } from '@/config/die'
export default {
  components: {
    Top,
    OrderCardVue,
    Loading
  },
  data() {
    return {
      query: {
        'pageNum': 0,
        'pageSize': 10
      },
      list: [],
      loadingShow: true,
      finished: false,
      loading: false
    }
  },
  created() {},
  methods: {
    onLoad() {
      this.query.pageNum++

      this.getOrderList()
    },
    // 获取订单列表
    getOrderList() {
      getOrderList(this.query).then(res => {
        this.loadingShow = false
        this.loading = false
        if (res.status === 200) {
          this.list.push(...res.data.list)
          if (res.data.list.length == 0) {
            this.finished = true
          }
        }
      })
    },
    goMarket() {
      // this.$router.push({
      //   name: 'Market',
      //   query: {}
      // })
      this.$router.push({
        name: 'MarketPt',
        query: {
          activityNo: activityNo
        }
      })
    },
    goOrderD(item) {
      this.$router.push({
        name: 'MarketOrderDetails',
        query: {
          orderNo: item.orderNo,
          orderStatus: item.orderStatus
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
.custom-image{
  width: 450px;
  height: 300px;
  margin: 0 auto;

  ::v-deep .van-empty__description{
    color: #333333;
    font-size: 30px;
    font-family: PingFangSC-Medium;
  }
  ::v-deep .van-empty__image img {
    width: 420px !important;
    height: 300px !important;
  }
  ::v-deep .bottom-button{
    border: 3px solid #169d1b;
    font-size: 32px;
    color: #169D1B;
    font-family: PingFangSC-Medium;
  }
}
.emptyNull{
  width: 100%;
  height: 95vh;
  background-color: #fff;
  padding-top: 250px;
  margin-top: 20px;
}
.bottom-button {
  width: 230px;
  height: 80px;
  border: 2px solid #169d1b;
  border-radius: 12px;
}
</style>
