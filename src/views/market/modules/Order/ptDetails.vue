<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2022-04-29 14:52:26
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-06-27 17:24:52
-->
<template>
  <div class="home">
    <NavHeight bgc="#fff" />
    <Top message="拼团详情" :is-left="true" />
    <div class="d_bg">
      <div v-if="data.joinStatus === 2">
        <img
          v-if="data.failureCode === 1"
          src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/order/status/2-1.png"
          class="d_status_img"
        >
        <img
          v-if="data.failureCode === 2"
          src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/order/status/2-2.png"
          class="d_status_img"
        >
        <img
          v-if="data.failureCode === 3"
          src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/order/status/2-3.png"
          class="d_status_img"
        >
      </div>
      <div v-if="data.joinStatus === 3">
        <img
          src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/order/status/1.png"
          class="d_status_img"
        >
      </div>

      <div v-if="data.joinStatus === 1">
        <div class="flex-col section">
          <div class="flex-col">
            <span class="text_1">仅剩{{ data.needAmount-data.currentAmount }}个优惠名额</span>
            <span class="text_2">快把优惠消息分享同事/朋友吧</span>
            <div class="justify-center group_1">
              <div>剩余</div>
              <van-count-down class="countDownTime" :time="data.countDownTime*1000" />
              <div class="text_4">结束</div>
            </div>
          </div>
          <div class="justify-center group_2" @click="sharea">
            <img v-for="img in data.groupUserList" :key="img.userId" class="image" :src="img.headPictureUrl!==''&&img.headPictureUrl!==null?img.headPictureUrl:'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/my/anio.png'" alt="">
            <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/market/order/sharea.png" alt="" class="image">
          </div>
          <div class="yqhy" @click="sharea">
            <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/market/market/home/<USER>" alt="">
          </div>
          <div class="justify-center group_3">
            <div class="flex-col items-center text-wrapper">
              <span>{{ data.needAmount }}人团</span>
            </div>
            <span class="text_6">已有{{ data.currentAmount }}人参与购买</span>
          </div>
          <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/market/order/ptjxz.png" class="sectionstatus" alt="">
        </div>
      </div>

      <img
        v-if="data.joinStatus === 1||data.joinStatus === 3"
        src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/market/order/ptwfrule2.png"
        class="d_status_img"
        @click="showPop = true"
      >

      <div @click="godetile">
        <OrderCardPtShow :item="data" />
      </div>
    </div>

    <van-popup v-model="showPop" round>
      <img class="rule_img" src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/market/rule/gz2.png" alt="" @click="showPop = false">
    </van-popup>
  </div>
</template>

<script>
import Top from '../../components/Tops.vue'
import OrderCardPtShow from './components/OrderCardPtShow.vue'
import { userJoinedGroupDetail } from '@/api/market'
export default {
  components: {
    Top, OrderCardPtShow
  },
  data() {
    return {
      data: {},
      showPop: false
    }
  },
  created() {
    this.userJoinedGroupDetail()
  },
  methods: {
    userJoinedGroupDetail() {
      let query = {
        activityJoinNo: this.$route.query.activityJoinNo,
        groupNo: this.$route.query.groupNo,
        orderNo: this.$route.query.orderNo
      }
      userJoinedGroupDetail(query).then((res) => {
        if (res.status === 200) {
          console.log(res)
          this.data = res.data
        }
      })
    },
    sharea() {
      let data = {
        title: this.data.goodsName,
        text: this.data.goodsName,
        image: this.data.pictureUrl,
        path: `/pages/pt/details/index?id=${this.data.activityJoinNo}&goodsId=${this.data.goodsId}&activityNo=${this.data.activityNo}&activityJoinNo=${this.data.activityJoinNo}`
      }
      console.log(data)
      AlipayJSBridge.call('WechatappletShare', data, function(result) {})
    },
    godetile() {
      this.$router.push({
        name: 'MarketOrderDetails',
        query: {
          orderNo: this.data.orderNo,
          orderStatus: this.data.orderStatus,
          from: 1
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
.home {
  width: 100%;
  height: 100vh;
  overflow-y: hidden;
  background: linear-gradient(180deg, #ff7200, #ff3e00);
}
.rule_img{
    width: 640px;
    height: 540px;
    border-radius: 24px;
  }
.d_bg {
  text-align: center;
  .d_status_img {
    width: 710px;
    margin-top: 25px;
    border-radius: 24px;
  }
  .section {
    width: 710px;
    margin: 0 auto;
    margin-top: 25px;
    padding: 120px 133px 64px 136px;
    background-image: url("https://codefun-proj-user-res-1256085488.cos.ap-guangzhou.myqcloud.com/6114f1316b68fa00119edb1e/626b447c4eb5590011d400af/16523542194728279033.png");
    background-position: 0px 0px;
    background-size: 100% 100%;
    background-repeat: no-repeat;
    position: relative;
    .sectionstatus{
      width: 140px;
      height: 140px;
      position: absolute;
      top: 10px;
      right: 10px;
    }
    .group_2 {
      margin-top: 33px;
      .image {
        border-radius: 50%;
        width: 72px;
        height: 72px;
        margin-right: 16px;
      }
    }
    .yqhy{
      width: 430px;
      height: 80px;
      margin-top:50px;
      img{
        width: 430px;
      height: 80px;
      }
    }
    .group_3 {
      margin-top: 78px;
      .text-wrapper {
        color: rgb(255, 69, 52);
        font-size: 28px;
        border-radius: 4px;
        width: 92px;
        height: 38px;
        line-height: 36px;
        border: solid 2px rgb(255, 69, 52);
      }
      .text_6 {
        margin-left: 12px;
        color: rgb(102, 102, 102);
        font-size: 28px;
        line-height: 40px;
        white-space: nowrap;
      }
    }
    .text_1 {
      color: rgb(255, 48, 30);
      font-size: 58px;
      font-weight: 500;
      line-height: 81px;
      white-space: nowrap;
    }
    .text_2 {
      margin-left: 3px;
      margin-top: 9px;
      color: rgb(102, 102, 102);
      font-size: 32px;
      line-height: 45px;
      white-space: nowrap;
    }
    .group_1 {
      margin-top: 13px;
      color: rgb(102, 102, 102);
      font-size: 32px;
      line-height: 45px;
      white-space: nowrap;
      display: flex;
      .countDownTime{
        font-size: 32px;
        color: rgb(102, 102, 102);
        margin-left: 15px;
        margin-top: 3px;
      }
      .text_4 {
        margin-left: 21px;
      }
    }
  }
}
</style>
