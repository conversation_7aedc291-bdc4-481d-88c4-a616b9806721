<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2022-04-29 15:14:38
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-06-23 19:37:26
-->
<template>
  <div class="home">
    <NavHeight bgc="#fff" />
    <Top :message="getStatus()" :is-left="true" />
    <Status :detail="detail" />
    <Info :detail="detail" />
    <Bottom :detail="detail" @restOrder="findByOrderNo" />

    <!-- 加载状态 -->
    <Loading :show="loadingShow" />
  </div>
</template>

<script>
import Top from '../../components/Tops.vue'
import { Status, Info, Bottom } from './components'
import { findByOrderNo } from '@/api/market'
import Loading from '@/components/Loading/index'
export default {
  components: {
    Top, Status, Info, Bottom, Loading
  },
  data() {
    return {
      detail: {
        campaignType: 1,
        orderAddress: {
          receiver: ''
        },
        orderItems: [
          { goodsName: '', sellPrice: '', originPrice: '' }
        ]
      },
      loadingShow: true
    }
  },
  created() {
    this.findByOrderNo()
  },
  methods: {
    // 获取详情
    findByOrderNo() {
      findByOrderNo({ orderNo: this.$route.query.orderNo }).then(res => {
        this.loadingShow = false
        if (res.status == 200) {
          this.detail = res.data
        }
      })
    },
    // 匹配状态值
    getStatus() {
      let status = this.$route.query.orderStatus
      if (status === 0) {
        return '待付款'
      } else if (status === 10) {
        return '已付款'
      } else if (status === 20) {
        return '待配送'
      } else if (status === 30) {
        return '待收货'
      } else if (status === 40) {
        return '待收货'
      } else if (status === 50) {
        return '完成'
      } else if (status === 70) {
        return '退款'
      } else if (status === 80) {
        return '取消'
      } else {
        return '商城订单'
      }
    }
  }
}
</script>

<style scoped lang="scss"></style>
