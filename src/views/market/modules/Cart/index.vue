<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2022-04-29 16:29:05
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-06-24 20:41:22
-->
<template>
  <div class="home">
    <NavHeight bgi="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/takeout/statusbg.png" />
    <Top message="确认订单" :is-left="true" :is-right="false" />
    <div class="d_bj">
      <Address />
      <Goods />
      <Info />
    </div>

    <div v-if="$route.query.isPt !=true" class="flex-col items-center button" @click="createNormalOrder">
      <span>提交订单</span>
    </div>
    <div v-if="$route.query.isPt ==true" class="flex-col items-center button" @click="createOrder">
      <span>提交订单</span>
    </div>
    <!-- <div v-else class="flex-col items-center button noStatus">
      <span>提交订单</span>
    </div> -->

  </div>
</template>

<script>
import Top from './components/Tops.vue'
import { Address, Goods, Info } from './components'
import { createOrder, createNormalOrder, getOrderList } from '@/api/market'
export default {
  components: {
    Top,
    Address,
    Goods,
    Info
  },
  data() {
    return {
    }
  },
  created() {

  },
  methods: {
    // 拼团下单
    createOrder() {
      this.$toast.loading({
        duration: 0,
        forbidClick: true,
        message: ''
      })
      // 自提下单
      if (this.$store.state.mall.creatForm.delieveryType == 2) {
        this.$store.state.mall.creatForm.selfTakeAddrId = this.$store.state.mall.ztData.id
        this.$store.state.mall.creatForm.selfTakeTime = '2025-12-31 23:59:59'
      } else {
        this.$store.state.mall.creatForm.selfTakeTime = ''
      }
      //
      if (this.$store.state.mall.coupon) {
        this.$store.state.mall.creatForm.couponsId = this.$store.state.mall.coupon.couponId
      }
      createOrder(this.$store.state.mall.creatForm).then(res => {
        if (res.status === 200) {
          this.$toast('下单成功')
          this.$router.push({
            name: 'MarketPay',
            query: {
              orderNo: res.data.orderNo,
              actualPay: res.data.actualPay,
              marketId: this.$route.query.marketId,
              poolId: this.$route.query.poolId,
              // isOnlyBalancePay: this.$store.state.mall.coupon ? 1 : 0,
              from: 2// 来自确认订单页
            }
          })
        }
      })
    },
    // 货架商品下单
    async createNormalOrder() {
      this.$toast.loading({
        duration: 0,
        forbidClick: true,
        message: ''
      })

      // 检查下单商品是否超过了限购数量
      if (this.$store.state.mall.creatForm.mallGoods[0].skuId == 133) {
        if (await this.isOver(133, 1)) {
          this.$toast('该商品每人限购1份')
          return false
        }
      }
      // 自提下单
      if (this.$store.state.mall.creatForm.delieveryType == 2) {
        this.$store.state.mall.creatForm.selfTakeAddrId = this.$store.state.mall.ztData.id
        this.$store.state.mall.creatForm.selfTakeTime = '2025-12-31 23:59:59'
      } else {
        this.$store.state.mall.creatForm.selfTakeTime = ''
      }

      if (this.$store.state.mall.coupon) {
        this.$store.state.mall.creatForm.couponsId = this.$store.state.mall.coupon.couponId
      }

      createNormalOrder(this.$store.state.mall.creatForm).then(res => {
        if (res.status === 200) {
          this.$toast('下单成功')
          if (this.$store.state.mall.isActive.pt == true) {
            this.$store.state.mall.isActive.orderNo = res.data.orderNo
          }
          this.$router.push({
            name: 'MarketPay',
            query: {
              orderNo: res.data.orderNo,
              actualPay: res.data.actualPay,
              marketId: this.$route.query.marketId,
              poolId: this.$route.query.poolId,
              // isOnlyBalancePay: this.$store.state.mall.coupon ? 1 : 0,
              from: 2// 来自确认订单页
            }
          })
        }
      })
    },
    async isOver(val, num) {
      const res = await getOrderList({
        'pageNum': 1,
        'pageSize': 50
      })
      let list = res.data.list
      let historyNum = 0
      for (let i = 0; i < list.length; i++) {
        if (list[i].orderItems && list[i].orderItems[0].goodsId == val) {
          if (list[i].orderStatus == 10 || list[i].orderStatus == 40 || list[i].orderStatus == 50 || list[i].orderStatus == 31) {
            historyNum += list[i].orderItems[0].skuQuantity
          }
        }
      }

      if (historyNum >= num) {
        return true
      } else {
        return false
      }
    }
  }
}
</script>

<style scoped lang="scss">
.home {
  width: 100%;
  margin: 0 auto;
  .d_bj{
    width: 100%;
    min-height: 320px;
    background-image: url(https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/takeout/addressbg.png);
		background-size: 100% 100%;
    overflow: hidden;
    background-color: #63CF5A;
  }
  .button {
    padding: 15px 0;
    align-self: center;
    color: rgb(255, 255, 255);
    font-size: 36px;
    font-weight: 500;
    line-height: 50px;
    white-space: nowrap;
    background-color: #39CF3F;
    border-radius: 40px;
    width: 502px;
    margin: 0 auto;
    margin-top: 150px;
  }
  .noStatus{
    background-color: #dddddd;
  }
}
</style>
