<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2022-04-29 16:29:23
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-06-27 13:57:14
-->
<template>
  <div>
    <div class="d_tab">
      <div :class="tabIndex==1?'d_tab_in':'d_tab_left'" @click="settabIndex(1)">同城配送</div>
      <div :class="tabIndex==2?'d_tab_in':'d_tab_right'" @click="settabIndex(2)">门店自提</div>
    </div>
    <div v-if="tabIndex==1" class="d_address">

      <div class="flex-col section_1" @click="addressShow = true">
        <div class="flex-row group_2_1">
          <div v-if="$store.state.mall.address.isDefault" class="flex-col items-center text-wrapper">
            默认
          </div>
          <div v-if="$store.state.mall.address.address" class="text_4">{{ $store.state.mall.address.address }}{{ $store.state.mall.address.street }}</div>
          <div v-else class="text_41 text_4">请选择配送地址</div>
        </div>
        <div class="flex-row group_1">
          <span>{{ $store.state.mall.address.username }}</span>
          <span class="text_2">{{ $store.state.mall.address.mobile }}</span>
        </div>

        <div v-if="$store.state.mall.address.isStatus === 1" class="flex-row group_3">
          <img
            src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/other/16512213161217013043.png"
            class="image"
          >
          <span class="text_5">该地址不支持同城配送</span>
        </div>
      </div>
      <div v-if="false" class="flex-col section_1" @click="addressShow = true">
        <div class="address_null">请选择配送地址</div>
      </div>
      <div class="d_right">
        <img
          src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/other/16511984833440166165.png"
          class="right"
        >
      </div>

      <!-- 选择地址弹框 -->
      <van-popup
        v-model="addressShow"
        position="bottom"
        round
        closeable
        :style="{ width: '100%', height: '350px' }"
      >
        <div class="box">
          <div class="title">地址</div>
          <div class="body">
            <van-radio-group v-model="radiovalue" @change="radioChange">
              <div class="address-pop" style="margin-top: 62px">
                <div v-for="(item, index) in temparr" :key="index" class="item">
                  <div class="item-left">
                    <van-radio
                      :name="index"
                      icon-size="19px"
                      checked-color="#5dcb4f"
                    >
                      <div class="item-detail">
                        <!-- 最多可以输入18个字 -->
                        <div class="item-detail-address">
                          <van-tag
                            v-if="item.isDefault == true"
                            plain
                            type="danger"
                          >默认</van-tag>
                          {{ showAddress(item) | ellipsis(18) }}
                        </div>
                        <div class="item-detail-user">
                          <span style="margin-right: 26px">{{
                            tabStatus == 1 ? item.username : item.contacts
                          }}</span>
                          <span>{{
                            tabStatus == 1 ? item.mobile : item.mobilePhone
                          }}</span>
                        </div>
                      </div>
                    </van-radio>
                  </div>
                  <div
                    v-if="tabStatus == 1"
                    class="item-right"
                    @click="editAddress(item)"
                  >
                    <van-icon
                      name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/order/edit.png"
                      size="17px"
                    />
                  </div>
                </div>

              </div>
            </van-radio-group>
            <!-- 超出配送距离 -->
            <div v-if="temparrNo.length > 0" class="disaddress">
              超出配送范围地址
            </div>
            <div class="address-pop">
              <div v-for="(item, index) in temparrNo" :key="index" class="item">
                <div class="item-left" @click="disAddrShow = true">
                  <div class="item-detail opacity6">
                    <div style="color: #222">
                      <van-tag
                        v-if="item.isDefault == true"
                        plain
                        type="danger"
                      >默认</van-tag>
                      {{ showAddress(item) | ellipsis(18) }}
                    </div>
                    <div style="color: #999">
                      <span style="margin-right: 26px">{{ item.username }}</span>
                      <span>{{ item.mobile }}</span>
                    </div>
                  </div>
                </div>
                <div class="item-right" @click="editAddress(item)">
                  <van-icon
                    name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/order/edit.png"
                    size="17px"
                  />
                </div>
              </div>
            </div>

            <van-empty v-if="temparrNo.length === 0&&temparr === 0" description="地址为空" />
            <div style="height: 50px" />

          </div>
        </div>
        <!-- 添加地址按钮 -->
        <div v-if="tabStatus == 1" class="newAddr" @click="newAddr">
          <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/components/addressbtn1.jpg" alt="">
        </div>
      </van-popup>
    </div>
    <!-- 自提 -->
    <div v-if="tabIndex==2" class="d_address">
      <div class="flex-col section_1">
        <div class="flex-row group_2_1">
          <div class="text_4">
            {{ $store.state.mall.ztData.locationName }}
            <span v-if="$store.state.mall.ztData.distance > 0.5" class="distance">{{ $store.state.mall.ztData.distance.toFixed(2) }}km(距您)</span>
            <span v-else class="distance" style="margin-left:10px;">{{ $store.state.mall.ztData.distance.toFixed(2) * 1000 }}m(距您)</span>
          </div>

        </div>
        <div class="flex-row group_1" @click="goMarkets($store.state.mall.ztData)">
          <div class="group_1_ads">
            <span>{{ $store.state.mall.ztData.province+$store.state.mall.ztData.city+$store.state.mall.ztData.district+$store.state.mall.ztData.selectAddress + $store.state.mall.ztData.address }}</span>
            <div class="d_md_right">
              <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/market/order/dh.png" alt="">
            </div>
          </div>

        </div>

        <!-- <div class="flex-row d_zt" @click="showTime = true">
          自提时间：{{ inDay+' '+inTime }} <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/market/goods/redright.png" alt="">
        </div> -->
        <div class="flex-row d_zt">
          自提时间：周一至周五  8:30-12:00，14:00-17:00
        </div>
      </div>

      <div class="d_right">
        <img
          v-if="tabIndex==1"
          src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/other/16511984833440166165.png"
          class="right"
        >
      </div>

    </div>

    <!-- 时间选择 -->
    <van-popup v-model="showTime" round closeable :lock-scroll="false" :safe-area-inset-bottom="true" position="bottom" :style="{ height: '380px' }">
      <div class="timeTitle">选择预计自取时间</div>
      <div class="showTimeBox">
        <div class="showTimeBox_left">
          <div
            v-for="(item,index) in dataBefore"
            :key="index"
            class="left_item"
            :class="inDay==item?'left_item_in':''"
            @click="setInDay(item)"
          >{{ formatTime(item) }}</div>
        </div>
        <div class="showTimeBox_right">
          <div v-for="(item,index) in columns" :key="index" class="timeItemNo" @click="inTime = item">
            <div class="times">
              {{ item }}
              <img v-if="inTime == item" src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/market/goods/intime.png" alt="">
            </div>
            <div class="test" />
          </div>
        </div>
      </div>

    </van-popup>

    <van-popup v-model="showPops" closeable position="bottom" round :style="{ height: '80%' }">
      <div>
        <ShopNavigation v-if="showPops" :item="distribution" />
      </div>
    </van-popup>

  </div>
</template>

<script>
import ShopNavigation from '../../../components/ShopNavigation.vue'
import { listByGoods, selectSelfFechTimeByLocationId } from '@/api/market'
import { formatDateTwo, getBeforeDate } from '@/utils/orderTime'
export default {
  components: {
    ShopNavigation
  },
  data() {
    return {
      addressShow: false,
      radiovalue: '',
      tabStatus: 1,
      temparr: [],
      temparrNo: [],
      address: '配送位置影响是否配送，请正确选择',
      isEditAddress: true,
      showTime: false,
      columns: [],
      tabIndex: 1,
      inDay: '请选择自提时间',
      inTime: '',
      isOrderTime: true,
      selfFetchTime: 0,
      showPops: false,
      distribution: {},
      dataBefore: ''
    }
  },
  watch: {
    inDay(val) {
      this.$store.state.mall.creatForm.selfTakeTime = this.inDay + ' ' + this.inTime + ':00'
    },
    inTime(val) {
      this.$store.state.mall.creatForm.selfTakeTime = this.inDay + ' ' + this.inTime + ':00'
    },
    showTime(val) {
      if (val == true) {
        let data = this.dataBefore
        this.inDay = data[0]
      } else {
        if (this.inTime == '') {
          this.inDay = '请选择自提时间'
        }
      }
    }
  },
  mounted() {
    // this.onShowTime()
    if (this.$store.state.mall.distributionInfo.length == 2) {
      this.selectSelfFechTimeByLocationId()
      this.getAddressList()
      this.$store.state.mall.creatForm.delieveryType = 1
    } else if (this.$store.state.mall.distributionInfo[0].distributionType == 2) {
      this.selectSelfFechTimeByLocationId()
      this.$store.state.mall.creatForm.delieveryType = 2
      this.tabIndex = 2
    } else if (this.$store.state.mall.distributionInfo[0].distributionType == 1) {
      this.getAddressList()
      this.$store.state.mall.creatForm.delieveryType = 1
    }
  },
  methods: {
    // 导航
    goMarkets(item) {
      this.distribution = item
      this.showPops = true
    },
    // 选择地址
    radioChange(e) {
      let data = this.temparr[e]
      this.address = data.address
      this.addressShow = false
      let addressData = {
        addressId: data.id,
        address: data.address,
        street: data.street,
        username: data.username,
        mobile: data.mobile,
        isDefault: data.isDefault,
        isStatus: 0
      }
      this.$store.state.mall.address = addressData
      this.$store.state.mall.creatForm.addressId = data.id
    },
    // 获取地址列表
    getAddressList() {
      listByGoods({ goodsId: this.$store.state.mall.goodsData.goodsId }).then(res => {
        this.$toast.clear()
        if (res.status == 200) {
          this.temparr = res.data.withinDistance
          this.temparrNo = res.data.beyondDistance
          let data = [...res.data.withinDistance, ...res.data.beyondDistance]
          for (let i = 0; i < data.length; i++) {
            if (data[i].isDefault === true) {
              this.address = data[i].address
              let addressData = {
                addressId: data[i].id,
                address: data[i].address,
                street: data[i].street,
                username: data[i].username,
                mobile: data[i].mobile,
                isDefault: data[i].isDefault,
                isStatus: 0
              }
              this.$store.state.mall.address = addressData
              this.$store.state.mall.creatForm.addressId = data[i].id
              break
            }
          }

          // 修改返回
          if (this.$store.state.mall.isEditAddressId != '') {
            for (let i = 0; i < this.temparr.length; i++) {
              if (this.temparr[i].id == this.$store.state.mall.isEditAddressId) {
                this.$store.state.mall.address.address = this.temparr[i].address
                this.$store.state.mall.address.street = this.temparr[i].street
                this.$store.state.mall.address.username = this.temparr[i].username
                this.$store.state.mall.address.mobile = this.temparr[i].mobile
                this.$store.state.mall.address.isDefault = this.temparr[i].isDefault
                this.$store.state.mall.address.isStatus = 0
                break
              }
            }
            for (let i = 0; i < this.temparrNo.length; i++) {
              if (this.temparrNo[i].id == this.$store.state.mall.isEditAddressId) {
                this.$store.state.mall.address.address = this.temparrNo[i].address
                this.$store.state.mall.address.street = this.temparrNo[i].street
                this.$store.state.mall.address.username = this.temparrNo[i].username
                this.$store.state.mall.address.mobile = this.temparrNo[i].mobile
                this.$store.state.mall.address.isDefault = this.temparr[i].isDefault
                this.$store.state.mall.address.isStatus = 1
                break
              }
            }
            this.$store.state.mall.isEditAddressId = ''
          }
        }
      })
    },
    newAddr() { // 新增地址
      this.$router.push({
        name: 'AddressAdd',
        query: {
          from: 'market'
        }
      })
    },
    editAddress(item) { // 修改地址
      // this.$store.state.mall.address = {}
      // this.$store.state.mall.creatForm.addressId = ''
      this.$router.push({
        name: 'AddressEdit',
        query: {
          id: item.id,
          from: 'market'
        }
      })
    },
    // 格式化地址
    showAddress(row) {
      let dz = row.province + row.city + row.district
      let dzLength = dz.length
      let newdz = row.address.slice(0, dzLength)
      if (dz == newdz) {
        return row.address.slice(dzLength)
      } else {
        return row.address + row.street
      }
    },
    // 时间选择
    showTimePop() {
      // let data = this.dataBefore
      // this.inDay = data[0]
      // this.inTime = this.columns[0]

      // this.$store.state.mall.creatForm.selfTakeTime = this.inDay + ' ' + this.inTime + ':00'
    },
    onShowTime(beginTime, endTime, userOrderTimes) {
      var dayjs = require('dayjs')
      let userOrderTime = dayjs().format('YYYY-MM-DD') + ' ' + userOrderTimes
      this.isOrderTime = dayjs(userOrderTime).format('HH') > dayjs().format('HH') ? true : false
      // this.showTime = true

      let times = formatDateTwo(dayjs().format('YYYY-MM-DD') + ' ' + beginTime, dayjs().format('YYYY-MM-DD') + ' ' + endTime, 40)
      if (times[0] + ':00' != beginTime) {
        times.splice(0, 1)
      }
      this.columns = times
      this.getBefore()
      this.showTimePop()
    },
    // 可选日期
    getBefore() {
      let data = getBeforeDate(this.selfFetchTime)
      console.log(data)
      console.log(this.isOrderTime)
      if (!this.isOrderTime) {
        data.splice(0, 1)
      }
      this.dataBefore = data
      // return data
    },
    formatTime(item) {
      var dayjs = require('dayjs')

      var startTime = dayjs().valueOf()// 计算当前时间戳 (毫秒级)
      var endTime = dayjs(item).valueOf()
      var date32 = endTime - startTime
      // 计算出相差天数sss
      var days = date32 / (24 * 3600 * 1000)
      if (days > 0 && days <= 1) {
        return dayjs(item).format('MM-DD') + '（明天）'
      }

      var weekday = require('dayjs/plugin/weekday')
      dayjs.extend(weekday)

      let weekdats = ''
      if (dayjs(item).weekday() == 1) {
        weekdats = '一'
      } else if (dayjs(item).weekday() == 2) {
        weekdats = '二'
      } else if (dayjs(item).weekday() == 3) {
        weekdats = '三'
      } else if (dayjs(item).weekday() == 4) {
        weekdats = '四'
      } else if (dayjs(item).weekday() == 5) {
        weekdats = '五'
      } else if (dayjs(item).weekday() == 6) {
        weekdats = '六'
      } else if (dayjs(item).weekday() == 0) {
        weekdats = '天'
      }

      return dayjs(item).format('MM-DD') + '（周' + weekdats + ')'
    },
    // 选择配送方式
    settabIndex(val) {
      if (this.$store.state.mall.distributionInfo.length == 1 && this.$store.state.mall.distributionInfo[0].distributionType == 2 && val == 1) {
        this.$toast('暂未开放')
        return
      }
      if (this.$store.state.mall.distributionInfo.length == 1 && this.$store.state.mall.distributionInfo[0].distributionType == 1 && val == 2) {
        this.$toast('暂未开放')
        return
      }
      console.log(val)
      this.tabIndex = val
      this.$store.state.mall.creatForm.delieveryType = val
    },
    // 自提时间
    selectSelfFechTimeByLocationId() {
      selectSelfFechTimeByLocationId(this.$store.state.mall.ztData.shippingLocationId).then(res => {
        this.selfFetchTime = res.data.selfFetchTime
        this.onShowTime(res.data.packUpTimeList[0].beginTime, res.data.packUpTimeList[0].endTime, res.data.userOrderTime)
      })
    },
    //
    setInDay(item) {
      this.inDay = item
    }
  }
}
</script>

<style scoped lang="scss">
.d_tab{
    width: 95%;
    text-align: center;
    margin: 0 auto;
    margin-top: 50px;
    display: flex;
    div{
      width: 50%;
      height: 82px;
      line-height: 82px;
      font-size: 28px;
      background: #ebfff1;
    }
    .d_tab_in{
      height: 102px;
      line-height: 112px;
      background-color: #fff;
      margin-top: -15px;
      font-size: 32px;
      font-family: PingFangSC-Medium;
      border-top-left-radius: 20px;
      border-top-right-radius: 20px;
    }
    .d_tab_left{
      margin-top: 5px;
      border-top-left-radius: 20px;
    }
    .d_tab_right{
      margin-top: 5px;
      border-top-right-radius: 20px;
    }
  }
.d_address {
  width: 95%;
  display: flex;
  background-color: rgb(255, 255, 255);
  margin: 0 auto;
  border-bottom-left-radius: 20px;
  border-bottom-right-radius: 20px;
  margin-top: -3px;
  .d_right{
    width: 10%;
    margin-top: 10px;
    .right{
      width: 33px;
      height: 33px;

    }
  }
  .section_1 {
    width: 90%;
    margin-left: 20px;
    margin-top: 20px;
    margin-bottom: 20px;
    .address_null{
      color: rgb(51, 51, 51);
      font-size: 33px;
    }
    .group_1 {
      color: #999999;
      font-size: 30px;
      line-height: 42px;
      margin-top: 6px;
      .text_2 {
        margin-left: 24px;
      }
      .group_1_ads{
        width: 620px;
        display: flex;
        justify-content: space-between;
      }
      .d_md_right{
        img{
          width: 40px;
          height: 80px;
        }
        margin-left: 18px;
      }
    }
    .group_2_1 {
      margin-top: 6px;
      display: flex;
      // word-wrap: break-word;
      .text-wrapper {
        width: 90px;
        height: 38px;
        color: rgb(255, 255, 255);
        font-size: 26px;
        font-weight: 500;
        line-height: 37px;
        background-color: rgb(254, 40, 40);
        border-radius: 19px;
      }
      .text_4 {
        width: 600px;
        min-height: 30px;
        margin-left: 3px;
        color: #333333;
        font-size: 40px;
        line-height: 42px;
        // white-space: nowrap;
        word-wrap: break-word;
        font-family: PingFangSC-Medium;
      }
      .text_41{
        height: 80px;
        margin-top: 20px;
      }
      .distance{
        font-size: 30px;
        color: #ffa300;
        margin-left: 7px;
      }
    }
    .group_3 {
      margin-top: 8px;
      font-size: 24px;
      color: #999;
      line-height: 33px;
      white-space: nowrap;
      .image {
        margin: 4px 0;
        border-radius: 50%;
        width: 24px;
        height: 24px;
      }
      .text_5 {
        margin-left: 7px;
        margin-top: 1px;
      }
    }
    .d_zt{
      color: #ff301e;
      font-size: 29px;
      margin-top: 8px;
      img{
        width: 14px;
        height: 18px;
        margin-left: 13px;
        display: inline;
        margin-top: 12px;
      }
    }
  }
  .box {
    .title {
      position: absolute;
      top: 0;
      left: 0;
      z-index: 10;
      width: 100%;
      box-sizing: border-box;
      text-align: center;
      height: 124px;
      line-height: 104px;
      font-size: 32px;
      color: #333;
      border-radius: 20px 20px 0 0;
      background: #fff;
    }

    .body {
      width: 100%;
      height: 600px;
      overflow-y: auto;
    }

    .close-icon {
      position: fixed;
      bottom: 625px;
      right: 40px;
      z-index: 10;
    }
    .disaddress {
      font-size: 32px;
      color: #ff301e;
      margin-left: 40px;
      margin-bottom: 20px;
      padding: 20px 0;
    }
    .address-pop {
      margin: 4px 42px 20px 18px;
      .item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 30px;
        color: #000010;
        height: 111px;
        border-bottom: 1px solid #f4f4f4;
        margin-bottom: 20px;
        .item-left {
          display: flex;
          align-items: center;
          .item-detail {
            margin-left: 16px;
            .item-detail-address {
              font-size: 32px;
              color: #222;
              margin-bottom: 8px;
            }
            .item-detail-user {
              font-size: 26px;
              color: #999;
            }
          }
          .opacity6 {
            opacity: 0.6;
          }
        }

        .item-right {
          display: flex;
          align-items: center;
        }
      }
    }

    ::v-deep .van-radio__icon .van-icon {
      border: 1px solid #5dcb4f;
    }
  }
  .newAddr {
    position: fixed;
    bottom: 10px;
    left: 0;
    width: 100%;
    // height: 108px;
    // background-color: #fff;
    font-size: 30px;
    text-align: center;
    img{
      width: 690px;
      height: 88px;
    }
  }
}
.timeTitle{
    width: 750px;
    height: 100px;
    line-height: 100px;
    font-size: 42px;
    text-align: center;
    position: absolute;
    top: 0;
    border-bottom: 1px solid #f4f4f5;
}
.showTimeBox{
  height: 630px;

  font-size: 38px;
  margin-top: 100px;
  display: flex;
  .showTimeBox_left{
    width: 280px;
    height: 630px;
    background: #f7f8f9;
  }
  .left_item{
    width: 280px;
    height: 100px;
    line-height: 100px;
    text-align: center;
    color: #333333;
    font-size: 30px;
  }
  .left_item_in{
    background-color: #fff !important;
  }
  .showTimeBox_right{
    height: 630px;
    overflow-y: auto;
    padding-left: 20px;
    .timeItemNo{
      width: 420px;
      height: 100px;
      line-height: 100px;
      font-size: 30px;
      .test{
        width: 420px;
        height: 1px;
        border: 1px solid #f4f4f5;
      }
    }
    .times{
      display: flex;
      justify-content: space-between;
      img{
        width: 32px;
        height: 32px;
        display: inline;
        margin-top: 30px;
        margin-right: 15px;
      }
    }
  }

}
</style>
