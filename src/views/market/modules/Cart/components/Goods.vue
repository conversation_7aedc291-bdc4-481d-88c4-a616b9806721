<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2022-04-29 16:29:43
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-06-22 17:53:08
-->
<template>
  <div class="d_goods">
    <div class="flex-row group_4">
      <img
        :src="$store.state.mall.goodsData.pictureUrl"
        class="image_1"
      >
      <div class="flex-col group_5">
        <div
          class="text_6"
        >{{ $store.state.mall.goodsData.goodsName }}</div>
        <div class="flex-row group_6">
          <span class="text_7">￥{{ $store.state.mall.goodsData.pintuanPrice }}</span>
          <div v-if="$route.query.isPt" class="flex-col group_7">
            <span>￥{{ $store.state.mall.goodsData.originPrice }}</span>
            <div class="divider" />
          </div>
        </div>

      </div>
      <div class="goods_num">
        <div v-if="skuQuantity == $store.state.mall.goodsData.inventory" class="tips">仅剩{{ $store.state.mall.goodsData.inventory }}份库存</div>
        <!-- <div v-if="skuQuantity >=5" class="tips">最多购买5份</div> -->
        <div class="operation">
          <div @click="reduce">
            <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/market/goods/jian.png" alt="">
          </div>
          <div>{{ skuQuantity }}</div>
          <div @click="add">
            <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/market/goods/jia.png" alt="">
          </div>
        </div>
      </div>

    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      skuQuantity: 1
    }
  },
  created() {},
  methods: {
    add() {
      if (this.$route.query.goodsId == 131 && this.skuQuantity >= 2) {
        this.$toast(`最多购买2份`)
        return
      }
      if (this.skuQuantity == this.$store.state.mall.goodsData.inventory) {
        this.$toast(`仅剩${this.$store.state.mall.goodsData.inventory}份`)
        return
      }
      this.skuQuantity = this.skuQuantity + 1
      this.$store.state.mall.creatForm.mallGoods[0].skuQuantity = this.$store.state.mall.creatForm.mallGoods[0].skuQuantity + 1
      this.$store.state.mall.skuQuantity = this.$store.state.mall.skuQuantity + 1
    },
    reduce() {
      if (this.skuQuantity == 1) {
        this.$toast('最少购买一份')
        return
      }
      this.skuQuantity = this.skuQuantity - 1
      this.$store.state.mall.creatForm.mallGoods[0].skuQuantity = this.$store.state.mall.creatForm.mallGoods[0].skuQuantity - 1
      this.$store.state.mall.skuQuantity = this.$store.state.mall.skuQuantity - 1
    }
  }
}
</script>

<style scoped lang="scss">
.d_goods {
  width: 95%;
  margin: 0 auto;
  background-color: #fff;
  margin-top: 20px;
  border-top-left-radius: 20px;
  border-top-right-radius: 20px;
  .group_4 {
    padding: 24px 28px 23px 32px;
    position: relative;
    .image_1 {
      flex-shrink: 0;
      border-radius: 8px;
      width: 160px;
      height: 160px;
    }
    .group_5 {
      margin-left: 16px;
      flex: 1 1 auto;
      .text_6 {
        width: 480px;
        height: 80px;
        color: rgb(51, 51, 51);
        font-size: 32px;
        font-weight: 500;
        line-height: 45px;
        text-align: justify;
        word-wrap:break-word;
        font-family: PingFangSC-Medium;
      }
      .group_6 {
        margin-top: 26px;
        .text_7 {
          color: rgb(51, 51, 51);
          font-size: 32px;
          font-weight: 500;
          line-height: 45px;
          white-space: nowrap;
        }
        .group_7 {
          margin: 8px 0 6px 10px;
          color: rgb(153, 153, 153);
          font-size: 24px;
          line-height: 33px;
          white-space: nowrap;
          position: relative;
          .divider {
            background-color: rgb(153, 153, 153);
            width: 54px;
            height: 2px;
            position: absolute;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
          }
        }
      }
    }
    .goods_num{
      width: 125px;
      position: absolute;
      right: 23px;
      bottom: 40px;
      .tips{
        color: #ff301e;
        font-size: 22px;
      }
      .operation{
        height: 36px;
        line-height: 36px;
        display: flex;
        justify-content: space-between;
        margin-top: 5px;
        img{
          width: 36px;
          height: 36px;
          display: inline;
        }
        font-size: 26px;
      }
    }
  }
}
</style>
