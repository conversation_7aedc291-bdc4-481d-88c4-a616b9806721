<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2022-04-29 16:30:58
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-06-22 16:42:02
-->
<template>
  <div class="d_goods_info">
    <div class="flex-col group_8">
      <div class="justify-between">
        <div class="text_10">配送</div>
        <div v-if="$store.state.mall.creatForm.delieveryType == 1" class="text_11">同城配送</div>
        <div v-if="$store.state.mall.creatForm.delieveryType == 2" class="text_11">门店自提</div>
      </div>
      <div v-if="$store.state.mall.creatForm.delieveryType == 1" class="justify-between group_10">
        <span class="text_12">运费</span>
        <span class="text_13">￥{{ $store.state.mall.goodsData.freightFee }}</span>
      </div>
      <coupon />
      <div v-if="$store.state.mall.creatForm.delieveryType == 1" class="justify-between group_10">
        <span class="text_12">
          退货说明
        </span>
        <span class="text_13">非质量问题不支持退换，敬请谅解</span>
      </div>
    </div>
    <div class="justify-between group_11">
      <span class="text_14">合计</span>
      <span class="text_15">￥{{ this.$store.getters['mallCartSumPrice'] }}</span>
    </div>
  </div>
</template>

<script>
import coupon from './coupon.vue'
export default {
  components: { coupon },
  data() {
    return {
      delieveryType: 1
    }
  },
  computed: {},
  created() {},
  mounted() {
    console.log(this.$store.state.mall.creatForm)
    this.delieveryType = this.$store.state.mall.creatForm.delieveryType
  },
  methods: {

  }
}
</script>

<style scoped lang="scss">
.d_goods_info {
  width: 95%;
  margin: 0 auto;
  background-color: #fff;
  border-bottom-left-radius: 20px;
  border-bottom-right-radius: 20px;
  .group_8 {
    padding: 31px 24px 32px 32px;
    .group_10 {
      margin-top: 32px;
      .text_12 {
        color: rgb(144, 145, 147);
        font-size: 28px;
        line-height: 40px;
        white-space: nowrap;
      }
      .text_13 {
        color: rgb(51, 51, 51);
        font-size: 28px;
        line-height: 40px;
        white-space: nowrap;
      }
    }
    .text_10 {
      color: rgb(144, 145, 147);
      font-size: 28px;
      line-height: 40px;
      white-space: nowrap;
    }
    .text_11 {
      color: rgb(51, 51, 51);
      font-size: 28px;
      line-height: 40px;
      white-space: nowrap;
    }
  }
  .group_11 {
    padding: 29px 32px 30px;
    .text_14 {
      margin-top: 3px;
      color: rgb(144, 145, 147);
      font-size: 28px;
      line-height: 40px;
      white-space: nowrap;
    }
    .text_15 {
      color: rgb(255, 48, 30);
      font-size: 32px;
      line-height: 45px;
      white-space: nowrap;
    }
  }
}
</style>
