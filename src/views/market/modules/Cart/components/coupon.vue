<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2022-08-05 09:47:38
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-08-17 23:50:41
-->
<template>
  <div class="coupon">
    <div class="flex">
      <span>优惠券</span>
      <div v-if="selectStatus === 1" @click="getCoupon">
        <div class="couponNum">  - ¥ </div>
        <div class="couponNum">
          {{ $store.state.mall.coupon.preferentialAmount }}
        </div>
        <div class="couponNumRight" style="color: #333">
          <van-icon name="arrow" />
        </div>
      </div>
      <div v-if="selectStatus === 2" @click="getCoupon">
        <div class="couponNum" style="color: #333">
          请选择({{ usefulCoupon.length }}张可用)
        </div>
        <div class="couponNumRight" style="color: #333">
          <van-icon name="arrow" />
        </div>
      </div>
      <div v-if="selectStatus === 3" @click="getCoupon">
        <div class="coupon_null">
          暂无可用
        </div>
        <div class="couponNumRight" style="color: #333">
          <van-icon name="arrow" />
        </div>
      </div>
      <div v-if="selectStatus === 0" @click="getCoupon">
        <div class="coupon_null">
          暂无可用
        </div>
        <div class="couponNumRight" style="color: #333">
          <van-icon name="arrow" />
        </div>
      </div>
    </div>

    <!-- 优惠券弹出 -->
    <van-popup v-model="couponShow" :close-on-click-overlay="false" round closeable close-icon-position="top-right" position="bottom" :style="{ height: '70%',backgroundColor:'#F5F5F6' }" @click-close-icon="closepop">
      <div class="pop-title">
        <span>选择红包/抵用券</span>
      </div>
      <div class="coupon-pop">
        <ul style="margin-top:70px">
          <van-empty v-if="couponList.length == 0" description="暂无可用优惠券" />
          <van-checkbox-group v-model="couponRadio">
            <li v-for="(item,index) in couponList" :key="index" class="pop_card">
              <div class="left" :class="item.usefulStatus === 2||item.usefulStatus === 3?'pop_card_die':''">
                <div>
                  <span>￥</span>
                  <span>{{ item.preferentialAmount }}</span>
                </div>
                <div>满{{ item.useThreshold }}元可用</div>
              </div>
              <div class="center" :class="item.usefulStatus === 2?'pop_card_die':''">
                <div>{{ item.couponName }}</div>
                <div>{{ item.applyMarket===0?'全部外卖店铺可用':'部分店铺可用' }}</div>
                <div v-if="item.invalidTime">{{ item.invalidTime }}到期</div>
                <div v-else>领取并使用</div>
              </div>
              <div class="right">
                <img v-if="index === 0&&item.usefulStatus === 1" class="coupon_tj" src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/coupon/card/tj.png" alt="">
                <div v-else class="coupon_radio_down" />
                <div class="coupon_radio">
                  <van-checkbox :disabled="item.usefulStatus === 2||item.usefulStatus === 3" :name="index" icon-size="20px" checked-color="#39cf3f" @click="setListCoupon(item,index)" />
                </div>
              </div>
              <img v-if="item.pickStatus === 0&&item.usefulStatus === 1" src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/coupon/card/zdlq.png" class="coupon_zdlq" alt="">
              <img v-if="item.usefulStatus === 2||item.usefulStatus === 3" src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/coupon/card/zbky.png" class="coupon_zbky" alt="">
            </li>
            <div style="height:100px" />
          </van-checkbox-group>
        </ul>
        <div class="coupon_bottom">
          <div class="box">
            <div v-if="$store.state.mall.coupon">
              <span>已选1张，可减 ￥</span>
              <span>{{ $store.state.mall.coupon.preferentialAmount }}</span>
            </div>
            <div v-else-if="notCoupon">
              <span>已选1张，可减 ￥</span>
              <span>{{ notCoupon.preferentialAmount }}</span>
            </div>
            <div v-else>
              <span>请选择优惠券</span>
            </div>
            <div @click="pick">
              确定
            </div>
          </div>

        </div>
      </div>
    </van-popup>
  </div>
</template>

<script>
import { recommendCoupon, selectCoupon, couponPick } from '@/api/coupon'

export default {
  data() {
    return {
      couponShow: false,
      couponList: [],
      couponRadio: [],
      selectStatus: 0,
      notCoupon: '',
      pickCoupon: [],
      usefulCoupon: []
    }
  },
  mounted() {
    // this.recommendCoupon()
    this.selectCoupon()
  },
  methods: {
    // 提交订单前系统建议优惠券信息
    recommendCoupon() {
      let data = {
        marketId: 2600,
        orderMoneyAmount: this.$store.getters['mallCartSumPrice']
      }
      recommendCoupon(data).then(res => {
        if (res.status === 200) {
          this.selectStatus = res.data.selectStatus
          if (res.data.selectStatus === 1) {
            this.couponRadio = [0]
            this.$store.state.mall.coupon = res.data
          }
        }
      })
    },
    // 用户自选优惠券列表
    selectCoupon() {
      let data = {
        marketId: 2600,
        orderMoneyAmount: this.$store.getters['mallCartSumPrice'],
        usedStyle: 1,
        getChannel: 1 // 默认1领券  2抢券
      }
      selectCoupon(data).then(res => {
        if (res.status === 200) {
          this.couponList = res.data
          for (let i = 0; i < this.couponList.length; i++) {
            console.log(this.couponList[i])

            // pickStatus
            if (this.couponList[i].pickStatus === 1) {
              this.pickCoupon.push(this.couponList[i])
            }
            //
            if (this.couponList[i].usefulStatus === 1) {
              this.usefulCoupon.push(this.couponList[i])
            }
          }

          if (this.usefulCoupon.length > 0) {
            this.selectStatus = 2
          }

          if (this.$store.state.mall.coupon) {
            for (let i = 0; i < this.couponList.length; i++) {
              if (this.$store.state.mall.coupon.couponId === this.couponList[i].couponId) {
                this.couponRadio = [i]
                break
              }
            }
          }
        }
      })
    },
    // 打开优惠券弹出框
    getCoupon() {
      this.couponShow = true
    },
    // 关闭 优惠券弹出框
    closepop() {
      // this.recommendCoupon()
      this.couponShow = false
    },
    // 确认选择
    setListCoupon(item, val) {
      if (item.usefulStatus === 2 || item.usefulStatus === 3) {
        this.$toast('该优惠券不可用')
        return
      }
      if (this.couponRadio.length == 0) {
        this.couponRadio = []
        this.$store.state.mall.coupon = ''
        this.notCoupon = ''
        this.selectStatus = 2
        return
      }
      this.couponRadio = [val]

      this.couponList.map((item, index) => {
        if (index === val && item.pickStatus === 1) {
          this.$store.state.mall.coupon = item
          this.notCoupon = ''
          this.selectStatus = 1
        }
        if (index === val && item.pickStatus === 0) {
          this.notCoupon = item
          this.$store.state.mall.coupon = ''
          this.selectStatus = 1
        }
      })
    },
    // 确认领取
    pick() {
      if (this.notCoupon !== '') {
        this.$toast.loading({ message: '', duration: 0, forbidClick: true })
        couponPick(this.notCoupon.couponManagementId).then(res => {
          setTimeout(() => {
            this.$toast.clear()
          }, 1000)

          if (res.status === 200) {
            this.$toast('领取成功')
            this.selectCoupon()
            this.$store.state.mall.coupon = this.notCoupon
            this.$store.state.mall.coupon.couponId = res.data
            this.couponShow = false
            this.selectStatus = 1
            this.notCoupon = ''
          }
        })
      } else {
        this.couponShow = false
      }
    }
  }
}
</script>

<style scoped lang="scss">
.coupon {
  .flex {
    padding: 12px 0;
    display: flex;
    justify-content: space-between;
    font-family: PingFangSC;
    font-size: 28px;
    margin-top: 15px;

    span{
      color: rgb(144, 145, 147);
    }

    > div {
      display: flex;
      justify-content: space-between;
      color: #fb4c58;
      // font-weight: bold;
      font-family:PingFangSC-Medium;
    }
    .coupon_null{
      color: #999999;
      font-weight: 100;
      font-size: 28px;
    }
  }
  .couponNumRight{
    position: relative;
    top: 1px;
    margin-left: 6px;
  }
  .pop-title {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 117px;
    background-color: #f5f5f5;
    font-size: 34px;
    font-family:PingFangSC-Medium;
    font-weight: 500;
    color: #333333;
    line-height: 48px;
    text-align: center;
    padding-top: 40px;
    z-index: 1;
  }
    .coupon-pop {
        background-color: #f5f5f5;
        width: 100%;
        height: 100%;
        overflow: scroll;
        margin: 0 auto;
        .pop-packet {
            font-size: 34px;
            font-family:PingFangSC-Medium;
            font-weight: 500;
            color: #333333;
            margin-left: 28px;
            padding-bottom: 18px;
        }
        .pop_card {
            width: 706px;
            height: 200px;
            background-color: #fff;
            border-radius: 16px;
            display: flex;
            justify-content: space-between;
            position: relative;
            margin: 0 auto;
            margin-bottom: 20px;
            .left{
                width: 180px;
                text-align: center;
                color: #FF301E;
                div:nth-of-type(1){
                    margin-top: 35px;
                    font-family: PingFangSC-Medium;
                    span:nth-of-type(1){
                        font-size: 26px;
                    }
                    span:nth-of-type(2){
                        font-size: 64px;
                    }
                }
                div:nth-of-type(2){
                    font-size: 22px;
                }
            }
            .center{
                width: 400px;
                div:nth-of-type(1){
                  height: 35px;
                  font-size: 35px;
                  font-family: PingFangSC-Medium;
                  color: #222222;
                  margin-top: 40px;
                }
                div:nth-of-type(2){
                  height: 26px;
                  font-size: 26px;
                  color: #666666;
                  margin-top: 13px;
                  margin-bottom: 14px;
                }
                div:nth-of-type(3){
                  height: 23px;
                  font-size: 22px;
                  color: #999999;
                }
            }
            .right{
                width: 100px;
                text-align: center;
            }
            .coupon_tj{
                width: 64px;
                height: 40px;
                margin-top: 30px;
            }
            .coupon_radio{
                width: 40px;
                height: 20px;
                margin: 0 auto;
            }
            .coupon_radio_down{
              margin-top: 80px;
            }
            .coupon_zdlq{
                width: 200px;
                height: 42px;
                position: absolute;
                top: 0;
                left: 0;
            }
            .coupon_zbky{
                width: 130px;
                height: 42px;
                position: absolute;
                top: 0;
                right: 0;
            }

        }
        .pop_card_die{
          opacity: 0.4;
        }
        .coupon_bottom{
            width: 100%;
            position: fixed;
            bottom: 20px;
            .box{
                width: 678px;
                height: 94px;
                line-height: 94px;
                display: flex;
                margin: 0 auto;
               div:nth-of-type(1){
                    width: 486px;
                    background: #0E0E0D;
                    color: #fff;
                    font-size: 26px;
                    border-top-left-radius: 54px;
                    border-bottom-left-radius: 54px;
                    span:nth-of-type(1){
                        margin-left: 50px;
                    }
                    span:nth-of-type(2){
                        font-size: 32px;
                    }
                }
                div:nth-of-type(2){
                    width: 192px;
                    background: linear-gradient(90deg,#ff1e29, #ff5a25);
                    color: #fff;
                    font-size: 32px;
                    font-family: PingFangSC-Medium;
                    border-top-right-radius: 54px;
                    border-bottom-right-radius: 54px;
                    text-align: center;
                }
            }

        }
        .pop_card:last-child {
            margin-bottom: 34px;
        }
    }
    ::v-deep .van-popup__close-icon--top-right {
        top: 42px;
    }
    ::v-deep .van-popup__close-icon {
        color: #333;
    }
}
</style>
