<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2022-04-30 13:03:35
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-06-25 09:19:38
-->
<template>
  <div class="content">
    <div class="statusTop">
      <van-nav-bar title="" :border="false" left-text="">
        <template #title>
          <div>{{ message }}</div>
        </template>
        <template #left>
          <van-icon
            v-if="isLeft"
            name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/common/arrowlwft.png"
            size="19"
            @click="goback"
          />
        </template>
        <template #right>
          <div v-if="isRight" class="right_text" @click="goRules">我的商城订单</div>
        </template>
      </van-nav-bar>
    </div>
    <div style="height: 46px" />
  </div>
</template>

<script>
import { activityNo } from '@/config/die'
export default {
  components: {},
  props: {
    isLeft: {
      type: Boolean,
      default: false
    },
    isRight: {
      type: Boolean,
      default: false
    },
    message: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
    }
  },
  mounted() {},
  methods: {
    goback() {
      if (this.$route.query.from == 1) {
        // this.$router.push({
        //   name: 'Market'
        // })
        this.$router.push({
          name: 'MarketPt',
          query: {
            activityNo: activityNo
          }
        })
      } else {
        this.$router.go(-1)
      }
    },
    goRules() {
      this.$router.push({
        name: 'MarketOrder'
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.content {
  .statusTop {
    width: 100%;
    height: 120px;
    position: fixed;
    left: 0;
    z-index: 1;
    background-image: url(https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/takeout/statusbg.png);
		background-size: 100% 100%;
  }

  ::v-deep .van-nav-bar__title {
    font-size: 39px;
    font-family: PingFangSC-Medium;
    color: #fff;
  }

  ::v-deep .van-nav-bar {
    background: none;
  }

  ::v-deep .van-nav-bar__right {
    font-size: 30px;
  }
  .right_text{
    color: #169D1B;
  }
}
</style>
