<template>
  <!-- 支付方式 -->
  <div class="content">
    <div class="totalMoney">
      <span class="mini">¥</span>
      <span>{{ $route.query.actualPay }}</span>
    </div>
    <!-- 倒计时 -->
    <div class="countdown-container">
      <div class="countdown-inner">
        <van-icon name="clock-o" class="countdown-icon" />
        <span class="countdown-text">支付剩余时间：</span>
        <span class="countdown-timer">{{ formatTime(minutes) }}:{{ formatTime(seconds) }}</span>
      </div>
    </div>
    <div class="payment">
      <van-radio-group v-model="radiovalue">
        <div v-for="(item,index) in paydata" v-show="item.payStyle === 1||item.payStyle === 7" :key="index+'b'" class="payment-line line1">
          <div class="line-left">
            <van-icon v-if="item.payStyle === 1||item.payStyle === 7" name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/pay/balance.png" size="23px" />
            <span>余额</span>
          </div>
          <div class="line-right">
            <span v-if="item.payStyle === 1||item.payStyle === 7">￥ {{ balance }}</span>
            <van-radio :name="item.payStyle" icon-size="19px" checked-color="#5dcb4f" :disabled="!isSupportPayStyle(item.payStyle)" />
          </div>
        </div>
        <div v-for="(item,index) in paydata" v-show="item.payStyle !== 1&&item.payStyle !== 7" :key="index" class="payment-line line22">
          <div class="line-left">
            <van-icon v-if="item.payStyle === 3" name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/pay/weixinpay.png" size="23px" />
            <van-icon v-if="item.payStyle === 2" name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/pay/alipay.png" size="23px" />
            <van-icon v-if="item.payStyle === 4" name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/pay/dragon.png" size="23px" />
            <van-icon v-if="item.payStyle === 9" name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/shop/goods/unipay.png" size="23px" />
            <van-icon v-if="item.payStyle === 11" name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/pay/abc.png" size="23px" />
            <span>{{ item.payStyleName }}</span>
            <span v-if="!isSupportPayStyle(item.payStyle)" class="notSupport">
              *暂不支持该支付方式
            </span>
          </div>
          <div class="line-right">
            <span v-if="item.payStyle === 1||item.payStyle === 7">￥ {{ balance }}</span>
            <van-radio :name="item.payStyle" icon-size="19px" checked-color="#5dcb4f" :disabled="!isSupportPayStyle(item.payStyle)" />
          </div>
        </div>

      </van-radio-group>
    </div>
  </div>
</template>

<script>
import { mallOrderUserAccount } from '@/api/market'
import {
  getPayStyle
} from '@/api/pay'
import {
  checkInitPwd
} from '@/api/scan'
export default {
  components: {
  },
  data() {
    return {
      radiovalue: '',
      balance: '',
      sysPayList: '',
      paydata: '',
      payChannel: null,
      // 倒计时相关数据
      minutes: 30,
      seconds: 0,
      countdownTimer: null
    }
  },
  watch: {
    radiovalue(val) {
      this.$store.state.mall.payradio = val
      if (val == 1 || val == 7) {
        this.checkInitPwd()
      }
    }
  },
  beforeDestroy() {
    // 清除倒计时定时器
    this.stopCountdown()
  },
  mounted() {
    this.getPayStyle()
    this.getUserAccount()
    this.startCountdown()
  },
  methods: {
    // 检查初始支付密码
    checkInitPwd() {
      let self = this
      checkInitPwd(this.$store.getters.getUserId).then((res) => {
        if (res.status == 200) {
          if (res.data == true) {
            self.$dialog.alert({
              message: '当前为初始支付密码，请尽快修改'
            }).then(() => {
              self.$router.push('/editPayPwd')
            })
          }
        }
      })
    },
    // 获取账户余额
    getUserAccount() {
      this.$toast.loading({
        duration: 0, // 持续展示 toast
        forbidClick: true,
        message: ''
      })
      mallOrderUserAccount(this.$route.query.orderNo)
        .then((res) => {
          this.$toast.clear()
          if (res.status == 200) {
            this.balance = res.data.balance
            this.payChannel = res.data.payChannel
          } else {
            this.$toast(res.message)
          }
        })
    },
    // 获取支付方式
    getPayStyle() {
      getPayStyle({
        marketId: this.$route.query.marketId,
        orderType: 1,
        poolId: this.$route.query.poolId
      }).then(res => {
        if (res.status == 200) {
          this.paydata = res.data
          // 默认选中第一个支付方式
          if (this.paydata && this.paydata.length > 0) {
            // 找到第一个可用的支付方式
            const firstAvailablePayment = this.paydata.find(item => this.isSupportPayStyle(item.payStyle))
            if (firstAvailablePayment) {
              this.radiovalue = firstAvailablePayment.payStyle
            }
          }
        }
      })
    },
    // 判断支付方式是否支持
    isSupportPayStyle(payStyle) {
      // isOnlyBalancePay
      if (this.$route.query.isOnlyBalancePay === '1') {
        return payStyle === 1
      }
      // if (this.$store.state.mall.coupon) {
      //   return this.$store.state.mall.coupon.couponPayStyleList.includes(payStyle)
      // } else {
      //   return true
      // }
      return true
    },

    // 开始倒计时
    startCountdown() {
      this.countdownTimer = setInterval(() => {
        if (this.seconds > 0) {
          this.seconds--
        } else {
          if (this.minutes > 0) {
            this.minutes--
            this.seconds = 59
          } else {
            // 倒计时结束
            this.stopCountdown()
            this.$toast('支付时间已到，请重新下单')
            setTimeout(() => {
              this.$router.push({ name: 'MarketCart' })
            }, 1500)
          }
        }
      }, 1000)
    },

    // 停止倒计时
    stopCountdown() {
      clearInterval(this.countdownTimer)
    },

    // 格式化时间，保证两位数显示
    formatTime(time) {
      return time < 10 ? `0${time}` : time
    }
  }
}
</script>

<style lang="scss" scoped>
.content {
  width: 710px;
  box-sizing: border-box;
  margin: 0 auto;
  .totalMoney {
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    font-size: 76px;
    color: #000010;
    margin: 70px 0 30px;
    .mini {
      font-size: 50px;
      color: #4c4c44;
      margin-right: 16px;
    }
  }

  .countdown-container {
    display: flex;
    justify-content: center;
    margin-bottom: 40px;

    .countdown-inner {
      display: flex;
      align-items: center;
      background: linear-gradient(135deg, #fffbf0 0%, #fff8e0 100%);
      padding: 12px 24px;
      border-radius: 30px;
      box-shadow: 0 4px 12px rgba(255, 204, 0, 0.1);
      border: 1px solid rgba(255, 204, 0, 0.3);

      .countdown-icon {
        color: #ffcc00;
        font-size: 22px;
        margin-right: 8px;
      }

      .countdown-text {
        font-size: 28px;
        color: #666;
        margin-right: 8px;
      }

      .countdown-timer {
        font-size: 32px;
        font-weight: bold;
        color: #ffcc00;
        letter-spacing: 1px;
      }
    }
  }
  .payment {
    .payment-line {
      width: 710px;
      display: flex;
      background-color: #fff;
      justify-content: space-between;
      align-items: center;
      font-size: 34px;
      color: #000010;
      padding: 0 20px;
      box-sizing: border-box;
      .line-left {
        display: flex;
        align-items: center;
        > span {
          margin-left: 24px;
        }
        .alipay{
          margin-left: 24px;
          margin-top: 115px;
          .alipay_name{
            width: 200px;
          }
          .alipay_tj{
            width: 70px;
            height: 30px;
            font-size: 18px;
            text-align: center;
            line-height: 30px;
            color: #fff;
            border-radius: 8px;
            background-color: #ff976a;
            margin-top: -30px;
            position: relative;
            top: -30px;
            left: 160px;
          }
        }
        .alipay_tag{
          display: flex;
          margin-top: 10px;
          div{
            min-width: 100px;
            height: 35px;
            line-height: 35px;
            text-align: center;
            padding-left: 5px;
            padding-right: 5px;
            font-size: 22px;
            border: 1px solid #ff976a;
            color: #ff976a;
            border-radius: 3px;
          }
        }
        .alipay_tag2{
          margin-left: 15px;
        }
        .alipay_tips{
          width: 550px;
          border-top: 1px solid #eee;
          font-size: 25px;
          color: #999;
          margin-top: 20px;
          padding-top: 10px;
        }
        .notSupport{
          color: #999;
          font-size: 24px;
        }
      }
      .line-right {
        display: flex;
        align-items: center;
        > span {
          margin-right: 70px;
        }
      }
    }
    .alipaybox{
      height: 210px;
      background-color: #fff;
    }
    .line1 {
      height:130px;
      border-radius: 16px;
    }
    .line2 {
      height:126px;
    }
    .line22 {
      height:126px;
      border-radius: 16px;
    }
    .line3 {
      height:126px;
      border-radius: 0 0 16px 16px;
    }
    .line {
      width: 100%;
      height:16px;
      background-color: #f3f4f9;
    }
  }
  ::v-deep .van-radio__icon .van-icon {
    border: 1px solid #CCCCCC;
  }
}
</style>
