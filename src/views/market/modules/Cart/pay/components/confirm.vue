<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-06-01 18:18:10
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-11-28 09:37:07
-->
<template>
  <div class="content">
    <div class="confirm">
      <van-button round block type="info" native-type="submit" @click="fastSetOrder">
        确认支付
      </van-button>
    </div>

    <!-- 数字键盘 -->
    <van-popup
      v-model="show"
      round
      closeable
      :style="{
        width: '281px',
        height: '210px',
      }"
    >
      <div class="box">
        <div class="title">输入支付密码</div>
        <div
          style="
              text-align: center;
              margin-top: 11px;
              margin-bottom: 8px;
              font-size: 13px;
              color: #333340;
            "
        >
          金额
        </div>
        <div style="text-align: center;font-size: 27px;margin-bottom: 20px;color: #000;">
          ¥{{ $route.query.actualPay }}
        </div>
        <van-password-input
          :value="password"
          info=""
          :length="6"
          :focused="showKeyboard"
          @focus="showKeyboard = true"
        />
      </div>
    </van-popup>

    <van-number-keyboard v-model="password" title="点滴安全键盘" :show="showKeyboard" z-index="9000" @blur="showKeyboard = false" />

    <div v-if="false" id="app" @dblclick="() => {return false;}">
      <div class="cus-keyboard">
        <Keyboard ref="showKeyboardRef" :length="length" :default-val="defaultVal" :text.sync="password" :keyboard-type="1" />
      </div>
    </div>

    <!-- 验证码弹框 -->
    <van-popup v-model="codeLog" round :style="{ height: '130px', }">
      <div class="popBox">
        <van-field v-model="checkUnionPay.smsCode" center clearable :border="false" label="验证码" placeholder="请输入验证码" />
        <div class="popBtn">
          <div class="cancel" @click="codeLog = false">取消</div>
          <div class="confirm" @click="confirmFinishCheckUnionPay">确认</div>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script>
import { payLoading } from '@/api/takeout'
import { payOrder, mallOrderUserAccount } from '@/api/market'
import { addOrderData, logDataUp } from '@/utils/upLog.js'
import { version } from '@/config/settings'
import { finishCheckUnionPay, fundAccountQuery } from '@/api/bank'

import { findUnionRcbAccount } from '@/api/bank/nsh'
import { fundAbcAccount } from '@/api/bank/abc'

import { query } from '@/utils/sign'
import Keyboard from '@/components/Keyboard'
export default {
  components: { Keyboard },
  props: {
    type: {
      type: [Number, String],
      default: ''
    },
    receiveId: {
      type: [Number, String],
      default: ''
    },
    capitalAccountId: {
      type: [Number, String],
      default: ''
    },
    ybalance: {
      type: [Number, String],
      default: ''
    }
  },
  data() {
    return {
      show: false,
      password: '',
      showKeyboard: false,
      orderNo: this.$route.query.orderNo,
      codeLog: false,
      checkUnionPay: {
        smsId: '',
        smsCode: '',
        unionPayData: ''
      },
      defaultVal: '',
      length: 6
    }
  },
  watch: {
    password(val, old) {
      if (val.length >= 6) {
        this.$throttle(() => {
          this.pay()
        }, 1500)
      }
    },
    show(val) {
      if (val === false) {
        // this.$refs.showKeyboardRef.hide()
        AlipayJSBridge.call('OffScreenshot', {}, function(result) {
          console.log(result)
        })
      }
    }
  },
  mounted() {},
  methods: {
    // 下单前检查
    // 先检查是否满足二类户开通
    fastSetOrder() {
      if (this.$store.state.mall.payradio == 7) {
        mallOrderUserAccount(this.$route.query.orderNo).then(res => {
          if (res.status == 200) {
            if (res.data.payChannel == 2) {
              // 检查是否开户成功

              // 检查是否开户成功
              if (this.$store.getters.getRegionId == 3) {
                this.fundAccountLqQuery(res.data.capitalAccountId)
              } else if (this.$store.getters.getRegionId == 1) {
                this.fundAccountQuery()
              } else if (this.$store.getters.getRegionId == 6 || this.$store.getters.getRegionId == 7) {
                this.jnFundAccountQuery(res.data.capitalAccountId)
              }
            } else {
              this.submit()
            }
          }
        })
      } else {
        this.submit()
      }
    },
    // 检查开户
    fundAccountQuery() {
      let queryVO = {
        'bankType': 'UnionPay',
        'userId': this.$store.getters.getUserId
      }
      fundAccountQuery(queryVO).then(res => {
        if (res.data != null) {
          if (res.data.status != 2) {
            this.$dialog.confirm({
              message: '请先完成开户流程！',
              confirmButtonText: '去开通',
              cancelButtonText: '暂不开通'
            }).then(() => {
              this.$router.push('/wallet')
            })
          } else {
            this.submit()
          }
        } else {
          this.$dialog.confirm({
            message: '请先完成开户流程！',
            confirmButtonText: '去开通',
            cancelButtonText: '暂不开通'
          }).then(() => {
            this.$router.push('/wallet')
          })
        }
      })
    },
    // 龙泉检查开户
    fundAccountLqQuery(val) {
      let data = {
        'userId': this.$store.getters.getUserId,
        'capitalAccountId': val
      }
      findUnionRcbAccount(data).then(res => {
        if (res.status == 200) {
          if (res.data != null) {
            if (res.data.status != 2) {
              this.$dialog.confirm({
                message: '请先完成开户流程！',
                confirmButtonText: '去开通',
                cancelButtonText: '暂不开通'
              }).then(() => {
                this.$router.push('/wallet')
              })
            } else {
              this.submit()
            }
          } else {
            this.$dialog.confirm({
              message: '请先完成开户流程！',
              confirmButtonText: '去开通',
              cancelButtonText: '暂不开通'
            }).then(() => {
              this.$router.push('/wallet')
            })
          }
        }
      })
    },
    // 景宁
    jnFundAccountQuery(val) {
      let queryVO = {
        'capitalAccountId': val,
        'userId': this.$store.getters.getUserId
      }
      fundAbcAccount(queryVO).then(res => {
        if (res.data != null) {
          if (res.data.status != 4) {
            this.$dialog.confirm({
              message: '请先完成开户流程！',
              confirmButtonText: '去开通',
              cancelButtonText: '暂不开通'
            }).then(() => {
              this.$router.push('/wallet')
            })
          } else {
            this.submit()
          }
        } else {
          this.$dialog.confirm({
            message: '请先完成开户流程！',
            confirmButtonText: '去开通',
            cancelButtonText: '暂不开通'
          }).then(() => {
            this.$router.push('/wallet')
          })
        }
      })
    },
    submit() {
      let self = this
      if (this.$store.state.mall.payradio == '') {
        this.$toast('选择支付方式')
        return false
      }
      // 如果支付方式是余额支付则
      if (this.$store.state.mall.payradio == 1 || this.$store.state.mall.payradio == 7) {
        this.showKeyboard = true
        this.show = true
        // this.$refs.showKeyboardRef.show()
        AlipayJSBridge.call('OnScreenshot', {}, function(result) {
          console.log(result)
        })
      } else {
        this.$throttle(() => {
          self.pay()
        }, 3000)
      }

      //   否则进行跳转
    },
    pay() {
      let self = this
      let Base64 = require('js-base64').Base64
      var u = navigator.userAgent
      var isiOS = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/) // ios终端
      let paydata = {
        capitalAccountId: 1,
        paymentType: this.$store.state.mall.payradio,
        payPassword: Base64.encode(self.password),
        terminalSysVer: version,
        // eslint-disable-next-line no-undef
        'clientIp': '127.0.0.1',
        orderNo: this.$route.query.orderNo,
        'deviceId': localStorage.getItem('deviceId') ? localStorage.getItem('deviceId') : 'test',
        longitude: this.$store.getters.getLocation.longitude,
        latitude: this.$store.getters.getLocation.latitude
      }
      this.$toast.loading({
        duration: 0,
        forbidClick: true,
        message: '支付中'
      })
      if (paydata.payPassword === '' || paydata.payPassword === null) {
        delete paydata.payPassword
      }
      if (this.$store.state.mall.payradio == 2) {
        paydata.thirdPayType = 'wapPay'
      }

      // 加密
      paydata.sign = query(paydata)

      payOrder(paydata)
        .then(res => {
          this.$toast.clear()
          if (res.status == 200) {
            if (this.$store.state.mall.payradio == 1 || this.$store.state.mall.payradio == 7) {
              this.showKeyboard = false
              this.show = false
              this.password = ''
              // 余额支付
              // 强校验
              if (this.$store.state.mall.payradio == 7 && res.data.thirdPartPayData.resultCode == '00018') {
                this.checkUnionPay.smsCode = ''
                this.$toast('交易存在风险，请输入银联验证码进行确认')
                this.checkUnionPay.smsId = res.data.thirdPartPayData.smsId
                this.checkUnionPay.unionPayData = res.data.thirdPartPayData.unionPayData
                this.codeLog = true

                return
              }
              // 交易中
              if (this.$store.state.mall.payradio == 7 && res.data.thirdPartPayData.resultCode == '00004') {
                self.orderNo = res.data.orderNo
                const timerccb = window.setInterval(() => {
                  self.appPay()
                }, 2000)

                this.$toast.loading({
                  duration: 3000,
                  forbidClick: true,
                  message: '交易处理中...'
                })

                this.$once('hook:beforeDestroy', () => {
                  window.clearInterval(timerccb)
                })
                return
              }
              this.orderDataLog()
              this.$toast('付款成功')
              this.$store.state.orderList = []
              setTimeout(() => {
                if (this.$store.state.mall.isActive.pt == true) {
                  this.$router.push({
                    name: 'MarketOrderPtDetails',
                    query: {
                      from: 1,
                      activityJoinNo: this.$store.state.mall.isActive.activityJoinNo,
                      groupNo: this.$store.state.mall.isActive.groupNo,
                      orderNo: this.$store.state.mall.isActive.orderNo
                    }
                  })
                } else {
                  this.$router.push({
                    name: 'MarketOrder',
                    query: {
                      from: 1
                    }
                  })
                }
              }, 500)
            } else if (this.$store.state.mall.payradio == 2) {
              // 支付宝支付
              if (!isiOS) {
                AlipayJSBridge.call(
                  'IsAvailable',
                  {
                    packageName: 'com.eg.android.AlipayGphone'
                  },
                  function(result) {
                    if (result.available == true) {
                      let url =
                          'alipays://platformapi/startapp?saId=10000007&qrcode=' +
                          `https://dshop.diandiandidi.top/h5/alipay.html?orderId=${res.data.orderNo}`
                      window.location.href = url
                      setTimeout(() => {
                        self.okPayMess()
                      }, 2000)
                    } else {
                      self.$toast('未安装支付宝')
                    }
                  }
                )
              } else {
                AlipayJSBridge.call(
                  'IsAvailable',
                  {
                    packageName: 'alipay://'
                  },
                  function(result) {
                    if (result.available == true) {
                      let url =
                          'alipays://platformapi/startapp?saId=10000007&qrcode=' +
                          `https://dshop.diandiandidi.top/h5/alipay.html?orderId=${res.data.orderNo}`
                      window.location.href = url
                      setTimeout(() => {
                        self.okPayMess()
                      }, 2000)
                    } else {
                      self.$toast('未安装支付宝')
                    }
                  }
                )
              }
            } else if (this.$store.state.mall.payradio == 3) {
              // 微信支付

              AlipayJSBridge.call('HsbFun', { orderNo: res.data.thirdPartPayData.pyTrnNo }, function(result) {
                if (result.payResult === 'success') {
                  self.orderDataLog()
                  // 支付成功,跳转
                  self.$toast('支付成功')
                  self.$store.state.orderList = []

                  if (self.$store.state.mall.isActive.pt == true) {
                    self.$router.push({
                      name: 'MarketOrderPtDetails',
                      query: {
                        from: 1,
                        activityJoinNo: self.$store.state.mall.isActive.activityJoinNo,
                        groupNo: self.$store.state.mall.isActive.groupNo,
                        orderNo: self.$store.state.mall.isActive.orderNo
                      }
                    })
                  } else {
                    self.$router.push({
                      name: 'MarketOrder',
                      query: {
                        from: 1
                      }
                    })
                  }
                } else {
                  self.$toast('支付失败')
                }
              })
              // AlipayJSBridge.call(
              //   'WeChatPay',
              //   {
              //     nonceStr: res.data.thirdPartPayData.nonceStr,
              //     partnerid: res.data.thirdPartPayData.partnerid,
              //     prepayid: res.data.thirdPartPayData.prepayid,
              //     timeStamp: res.data.thirdPartPayData.timeStamp,
              //     packageStr: 'Sign=WXPay', // 固定值，以微信支付文档为主
              //     paySign: res.data.thirdPartPayData.paySign
              //   },
              //   function(result) {
              //     self.$toast('支付中')
              //     if (result.payResult == 'success') {
              //       self.orderDataLog()
              //       // 支付成功,跳转
              //       self.$toast('支付成功')
              //       self.$store.state.orderList = []

              //       if (self.$store.state.mall.isActive.pt == true) {
              //         self.$router.push({
              //           name: 'MarketOrderPtDetails',
              //           query: {
              //             from: 1,
              //             activityJoinNo: self.$store.state.mall.isActive.activityJoinNo,
              //             groupNo: self.$store.state.mall.isActive.groupNo,
              //             orderNo: self.$store.state.mall.isActive.orderNo
              //           }
              //         })
              //       } else {
              //         self.$router.push({
              //           name: 'MarketOrder',
              //           query: {
              //             from: 1
              //           }
              //         })
              //       }
              //     } else if (result.payResult == 'failed') {
              //       // 支付失败跳转
              //       self.$toast('支付失败')
              //     }
              //   }
              // )
            } else if (this.$store.state.mall.payradio == 9) {
              // 云闪付
              let u = navigator.userAgent
              let isiOS = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/) // ios终端
              if (!isiOS) {
                AlipayJSBridge.call(
                  'IsAvailable',
                  {
                    // 判断ios和安卓包名
                    packageName: 'com.unionpay'
                  },
                  function(result) {
                    if (result.available == true) {
                      let url = res.data.thirdPartPayData.qrUrl
                      let newurl = 'upwallet://html/' + url.slice(8)
                      window.location.href = newurl
                      setTimeout(() => {
                        self.okPayMess()
                      }, 2000)
                    } else {
                      self.$toast('未安装云闪付')
                      setTimeout(() => {
                        self.okPayMess()
                      }, 2000)
                      AlipayJSBridge.call('OpenUrlForAPP', { openurl: 'https://wallet.95516.com/s/wl/webV3/activity/yhtzbtoc/html/snsIndex.html?r=6f040ef12df584d1a98ee25aefacf97c&code=' }, function(result) {})
                    }
                  }
                )
              } else {
                let url = res.data.thirdPartPayData.qrUrl
                let newurl = 'upwallet://html/' + url.slice(8)
                window.location.href = newurl
                setTimeout(() => {
                  self.okPayMess()
                }, 2000)
                // AlipayJSBridge.call('IsAvailable',
                //   {
                //     // 判断ios和安卓包名
                //     packageName: 'upwallet://'
                //   },
                //   function(result) {
                //     if (result.available == true) {
                //       let url = res.data.thirdPartPayData.qrUrl
                //       let newurl = 'upwallet://html/' + url.slice(8)
                //       window.location.href = newurl
                //       setTimeout(() => {
                //         self.okPayMess()
                //       }, 2000)
                //     } else {
                //       self.$toast('未安装云闪付')
                //     }
                //   }
                // )
              }
            }
          } else {
            this.password = ''
            this.$toast(res.message)
          }
        })
    },
    // 检查支付提示
    okPayMess() {
      let self = this
      this.$dialog
        .confirm({
          title: '是否支付完成？',
          message: ''
        })
        .then((res) => {
          self.appPay()
        })
        .catch(() => {
          self.appPay()
        })
    },
    // 加查支付
    appPay() {
      payLoading(this.orderNo)
        .then(res => {
          if (this.status == 200) {
            if (res.data == true) {
              this.orderDataLog()
              this.$toast('支付成功')
              this.$store.state.orderList = []
              // this.$router.push({ name: 'Order' })
              if (this.$store.state.mall.isActive.pt == true) {
                this.$router.push({
                  name: 'MarketOrderPtDetails',
                  query: {
                    from: 1,
                    activityJoinNo: this.$store.state.mall.isActive.activityJoinNo,
                    groupNo: this.$store.state.mall.isActive.groupNo,
                    orderNo: this.$store.state.mall.isActive.orderNo
                  }
                })
              } else {
                this.$router.push({
                  name: 'MarketOrder',
                  query: {
                    from: 1
                  }
                })
              }
            } else {
              this.$toast('支付失败')
              this.$store.state.orderList = []
              setTimeout(() => {
                this.$router.push({
                  name: 'MarketOrder',
                  query: {
                    from: 1
                  }
                })
              }, 500)
            }
          } else {
            this.$toast(res.message)
            this.$store.state.orderList = []
            this.$router.push({ name: 'MarketOrder', query: {
              from: 1
            }})
          }
        })
    },
    // 记录完成订单轨迹
    orderDataLog() {
      let data = {
        orderMoney: this.ybalance,
        orderTime: Date.parse(new Date()),
        orderPayType: this.$store.state.mall.payradio,
        orderNo: this.orderNo
      }
      addOrderData(data)
      // 上报
      logDataUp()
    },
    // 强校验
    confirmFinishCheckUnionPay() {
      finishCheckUnionPay(this.checkUnionPay).then(res => {
        if (res.status == 200) {
          if (res.data.resultCode == '00000') {
            this.$toast('支付成功')
            this.orderDataLog()
            this.$toast('支付成功')
            this.$store.state.orderList = []
            if (this.$store.state.mall.isActive.pt == true) {
              this.$router.push({
                name: 'MarketOrderPtDetails',
                query: {
                  from: 1,
                  activityJoinNo: this.$store.state.mall.isActive.activityJoinNo,
                  groupNo: this.$store.state.mall.isActive.groupNo,
                  orderNo: this.$store.state.mall.isActive.orderNo
                }
              })
            } else {
              this.$router.push({
                name: 'MarketOrder',
                query: {
                  from: 1
                }
              })
            }
          } else {
            this.$toast('支付失败')
          }
        }
      })
    }
  }
}
</script>

  <style lang="scss" scoped>
  .content {
    .confirm {
      ::v-deep .van-button--block {
        width: 452px;
        height: 88px;
        font-family: PingFangSC;
        margin: 200px auto 0;
        font-size: 32px;
      }
      ::v-deep .van-button--round {
        border-radius: 12px;
        background: linear-gradient(90deg,#40d243, #1fc432);
        border: 1px solid #5ecc52;
      }
    }
    .box {
      .title {
        width: 100%;
        box-sizing: border-box;
        padding-left: 28px;
        height: 92px;
        line-height: 92px;
        font-size:24px;
        color: #4c4c57;
        border-bottom: 1px solid #d5d5d5;
      }
    }
  }
  ::v-deep .van-popup__close-icon {
    font-size: 30px;
  }
  ::v-deep .van-key {
    color: #000;
  }
  ::v-deep .van-popup__close-icon--top-right {
    top: 30px;
  }
  ::v-deep[class*="van-hairline"]::after {
    border-color: #d5d5d5;
  }
  ::v-deep .van-popup--center.van-popup--round {
    border-radius:16px;
  }
  ::v-deep .van-popup--center {
    top: 35%;
  }
    .popBox {
  position: relative;
  width: 561px;
  height: 32px;
  padding-top:48px;
  box-sizing: border-box;

  .title {
    color: #7f7f87;
    font-size: 30px;
    padding-left: 42px;
  }

  ::v-deep .van-field__label{
    width: 100px;
    margin-right: 0;
  }
}
.popBtn {
  position: fixed;
  left: 0;
  bottom: 0px;
  display: flex;
  width: 561px;
  height: 97px;
  line-height: 97px;
  font-size: 32px;
  justify-content: space-between;
  border-top: 1px solid #cfcece;
  text-align: center;
  .cancel {
    width: 50%;
    border-right: 1px solid #cfcece;
    color: #999999;
  }
  .confirm {
    width: 50%;
    color: #6095f0;
  }
  }
  </style>
