<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-06-01 17:44:29
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-05-18 17:53:33
-->
<template>
  <div class="home">
    <div class="fixed">
      <van-nav-bar title="支付订单" left-text="" left-arrow>
        <template #left>
          <van-icon name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/user/icon/arrow-black.png" size="18" @click="goBack" />
        </template>
      </van-nav-bar>
    </div>
    <div style="height:46px" />
  </div>
</template>

<script>
export default {
  data() {
    return {

    }
  },
  created() {

  },
  mounted() {

  },
  methods: {
    goBack() {
      if (this.$route.query.from == 2) {
        this.$router.push({
          name: 'MarketOrderDetails',
          query: {
            orderNo: this.$route.query.orderNo,
            from: 1
          }
        })
      } else {
        this.$router.go(-1)
      }
    }
  }
}
</script>

<style scoped lang="scss">
    .home {
        .fixed {
            position: fixed;
            left: 0;
            top: 20;
            z-index: 2;
            width: 100%;
        }
        ::v-deep .van-nav-bar__title {
            font-size: 36px;
            color: #222;
        }
    }
</style>
