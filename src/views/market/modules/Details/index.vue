<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2022-04-29 10:27:45
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-06-24 20:39:12
-->
<template>
  <div class="home">
    <!-- <NavHeight bgc="#fff" /> -->
    <Banner :detail="detail" />
    <Marketing :detail="detail" />

    <GoodsInfoHome :detail="detail" />
    <Address :detail="detail" />
    <Details :detail="detail" />

    <BottomGoods :detail="detail" />
    <!-- 加载状态 -->
    <Loading :show="loadingShow" />
  </div>
</template>

<script>
import { goodsShelvesDetails } from '@/api/market/home'
import { Banner, Marketing, Address, GoodsInfoHome, Details, BottomGoods } from './components'
import Loading from '@/components/Loading/index'
export default {
  components: {
    Banner, Marketing, Address, GoodsInfoHome, Details, BottomGoods, Loading
  },
  data() {
    return {
      detail: {},
      loadingShow: true
    }
  },
  created() {
    document.body.scrollTop = 0
    document.documentElement.scrollTop = 0
    this.goodsShelvesDetails()
  },
  mounted() {
    document.body.scrollTop = 0
    document.documentElement.scrollTop = 0
    this.$store.state.mall.isActive.pt = false

    this.$store.state.mall.coupon = ''
  },
  methods: {
    // 获取详情
    goodsShelvesDetails() {
      this.$store.state.mall.distributionInfo = []
      this.$store.state.mall.skuQuantity = 1
      let data = {
        'goodsId': this.$route.query.id,
        'latitude': this.$store.getters.getLocation.latitude,
        'longitude': this.$store.getters.getLocation.longitude
      }
      goodsShelvesDetails(data).then(res => {
        this.loadingShow = false
        if (res.status == 200) {
          this.detail = res.data

          // 记录配送方式
          let distri = res.data.distributionInfo
          for (let i = 0; i < distri.length; i++) {
            this.$store.state.mall.distributionInfo.push(distri[i])
            if (distri[i].distributionType == 2) {
              this.$store.state.mall.ztData = distri[i]
            }
          }
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
</style>
