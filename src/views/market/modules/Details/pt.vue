<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2022-04-29 10:27:45
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-06-24 20:40:10
-->
<template>
  <div class="home">
    <!-- <NavHeight bgc="#fff" /> -->
    <Banner :detail="detail" />
    <Top :detail="detail" />

    <GoodsInfo :detail="detail" />
    <Address :detail="detail" />
    <CollageItem :detail="detail" />
    <Rule :detail="detail" />
    <Details :detail="detail" />

    <Bottom :detail="detail" />
    <!-- 加载状态 -->
    <Loading :show="loadingShow" />
  </div>
</template>

<script>
import { getActivityDetailV2 } from '../../../../api/market'
import { Banner, Top, Address, GoodsInfo, CollageItem, Rule, Details, Bottom } from './components'
import Loading from '@/components/Loading/index'
export default {
  components: {
    Banner, Top, Address, GoodsInfo, CollageItem, Rule, Details, Bottom, Loading
  },
  data() {
    return {
      detail: {},
      loadingShow: true
    }
  },
  created() {
    document.body.scrollTop = 0
    document.documentElement.scrollTop = 0
    this.getActivityDetailV2()
  },
  mounted() {
    document.body.scrollTop = 0
    document.documentElement.scrollTop = 0
  },
  methods: {
    // 获取详情
    getActivityDetailV2() {
      this.$store.state.mall.distributionInfo = []
      this.$store.state.mall.skuQuantity = 1
      let data = {
        activityJoinNo: this.$route.query.id,
        latitude: this.$store.getters.getLocation.latitude,
        longitude: this.$store.getters.getLocation.longitude
      }
      getActivityDetailV2(data).then(res => {
        this.loadingShow = false
        if (res.status == 200) {
          this.detail = res.data
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
</style>
