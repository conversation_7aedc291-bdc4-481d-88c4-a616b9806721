<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2022-04-29 10:42:23
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-08-06 09:45:59
-->
<template>
  <div class="home">
    <div class="d_market_info">
      <div class="d_market_info_title">
        <img v-if="detail.sales>10" src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/market/order/tag.png" alt="" class="hot-tag">
        {{ detail.title }}
      </div>

      <div class="goods-features">
        <div v-if="detail.inventory > 0" class="feature-tag">
          <i class="feature-icon">✓</i>
          <span>现货</span>
        </div>
        <div class="feature-tag">
          <i class="feature-icon">✓</i>
          <span>正品保障</span>
        </div>
        <div class="feature-tag">
          <i class="feature-icon">✓</i>
          <span>售后无忧</span>
        </div>
      </div>

      <div v-if="detail.description" class="goods-description">
        <p>{{ detail.description }}</p>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    detail: {
      type: Object,
      default: function() {
        return {}
      }
    }
  },
  data() {
    return {}
  },
  created() {},
  mounted() {},
  methods: {}
}
</script>

<style scoped lang="scss">
.home {
  .d_market_info {
    display: flex;
    flex-direction: column;
    padding: 25px 20px 30px;
    background-color: #ffffff;
    border-radius: 20px;
    margin: 15px 15px 0;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);

    .d_market_info_title {
      width: 100%;
      color: #222222;
      font-size: 36px;
      font-weight: 600;
      line-height: 1.4;
      font-family: PingFangSC-Medium;
      word-wrap: break-word;
      position: relative;

      .hot-tag {
        max-width: 63px;
        height: 34px;
        position: relative;
        top: 5px;
        margin-right: 5px;
        transition: transform 0.3s ease;

        &:hover {
          transform: scale(1.1);
        }
      }
    }

    .goods-features {
      display: flex;
      flex-wrap: wrap;
      margin-top: 20px;

      .feature-tag {
        display: flex;
        align-items: center;
        background-color: #F8F8F8;
        padding: 6px 12px;
        border-radius: 6px;
        margin-right: 10px;
        margin-bottom: 10px;

        .feature-icon {
          color: #4CAF50;
          font-size: 24px;
          font-style: normal;
          margin-right: 5px;
        }

        span {
          color: #666666;
          font-size: 24px;
        }
      }
    }

    .goods-description {
      margin-top: 20px;
      padding: 15px;
      background-color: #F9F9F9;
      border-radius: 10px;

      p {
        color: #666666;
        font-size: 28px;
        line-height: 1.5;
        margin: 0;
      }
    }

    .d_market_info_ccb {
      width: 100%;
      min-height: 110px;
      background-color: #FFF5F0;
      border-radius: 10px;
      margin-top: 20px;
      padding: 15px;

      div {
        width: 100%;
        font-size: 28px;
        color: #FF7937;
      }

      img {
        width: 30px;
        height: 30px;
        position: relative;
        top: 5px;
        margin-right: 5px;
      }
    }
  }
}
</style>
