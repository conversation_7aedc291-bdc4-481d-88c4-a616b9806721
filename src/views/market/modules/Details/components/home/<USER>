<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2022-06-13 17:49:38
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-12-29 15:19:16
-->
<template>
  <div class="address">
    <div class="d_box">
      <div v-for="item in detail.distributionInfo" v-show="item.distributionType == 1" :key="item.id+'loin'" class="d_item">
        <div class="d_item_title">配送</div>
        <div class="d_item_content">
          <i class="delivery-icon">📦</i>
          <span>{{ item.district }}</span>
          <span class="divider">|</span>
          <span class="freight-fee">
            <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/market/order/tcps.png" alt="" class="freight-icon">
            <span :class="{'free-delivery': item.basicFreight <= 0}">￥{{ item.basicFreight > 0 ? item.basicFreight : '免配送费' }}</span>
          </span>
        </div>
      </div>

      <div v-if="detail.deliveryTime" class="delivery-time">
        <div class="time-icon">⏱️</div>
        <div class="time-info">预计发货时间：{{ detail.deliveryTime || '下单后48小时内' }}</div>
      </div>

      <div v-for="item in detail.distributionInfo" v-show="item.distributionType == 2" :key="item.id" class="store-pickup-container">
        <div class="d_item">
          <div class="d_item_title">自提</div>
          <div class="d_item_content">
            <i class="store-icon">🏪</i>
            <span>支持门店自提</span>
          </div>
        </div>

        <div class="d_md">
          <div class="d_md_box">
            <div class="d_md_left">
              <div class="d_md_title">
                <div class="d_md_title_tag">
                  门店
                </div>
                <div class="shop_name">{{ item.locationName }}</div>
                <div v-if="item.distance > 0.5" class="shop_names distance">{{ item.distance.toFixed(2) }}km</div>
                <div v-else class="shop_names distance">{{ item.distance.toFixed(2) * 1000 }}m</div>
              </div>
              <div class="d_md_msg">
                {{ item.province+item.city+item.district+item.selectAddress+item.address }}
              </div>
            </div>

            <div v-if="false" class="d_md_right" @click="goMarkets(item)">
              <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/market/order/dh.png" alt="">
              <span>导航</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <van-popup v-model="showPops" closeable position="bottom" round :style="{ height: '80%' }">
      <div>
        <ShopNavigation v-if="showPops" :item="distribution" />
      </div>
    </van-popup>

    <!-- 导航 -->
    <van-popup v-model="showPop" position="bottom" round>
      <div class="mapList">
        <div class="map-option" @click="openMap(1)">
          <i class="map-icon">🗺️</i>百度导航
        </div>
        <div class="map-option" @click="openMap(2)">
          <i class="map-icon">🗺️</i>高德导航
        </div>
        <div class="cancel-option" @click="showPop=false">取消</div>
      </div>
    </van-popup>
  </div>
</template>

<script>
import ShopNavigation from '../../../../components/ShopNavigation.vue'
export default {
  components: {
    ShopNavigation
  },
  props: {
    detail: {
      type: Object,
      default: function() {
        return {}
      }
    }
  },
  data() {
    return {
      showPop: false,
      showPops: false,
      distribution: ''
    }
  },
  methods: {
    //
    goMarkets(item) {
      this.distribution = item
      console.log(item)
      this.showPops = true
    },
    // 打开地图
    openMap(val) {
      let self = this
      if (val == 1) {
        var urlBaiduMap =
						`baidumap://map/marker?location=${this.mapData.bd_lat},${this.mapData.bd_lng}&title=${this.mapData.marketName}&content=${this.mapData.marketName}&src=Hello%20uni-app`
        AlipayJSBridge.call(
          'IsAvailable', {
            packageName: 'com.baidu.BaiduMap'
          },
          function(result) {
            if (result.available == true) {
              window.location.href = urlBaiduMap
            } else {
              self.$toast('未安装百度地图')
            }
          }
        )
      } else {
        var urlAmap =
						`androidamap://viewMap?sourceApplication=${this.mapData.marketName}&poiname=${this.mapData.marketName}&lat=${this.mapData.gd_lat}&lon=${this.mapData.gd_lng}&dev=0`

        var iosAmap =
						`iosamap://viewMap?sourceApplication=${this.mapData.marketName}&poiname=${this.mapData.marketName}&lat=${this.mapData.gd_lat}&lon=${this.mapData.gd_lng}&dev=0`
        var u = navigator.userAgent
        var isiOS = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/) // ios终端
        if (!isiOS) {
          AlipayJSBridge.call(
            'IsAvailable', {
              packageName: 'com.autonavi.minimap'
            },
            function(result) {
              if (result.available == true) {
                window.location.href = urlAmap
              } else {
                self.$toast('未安装高德地图')
              }
            }
          )
        } else {
          window.location.href = iosAmap
        }
      }
    },
    // 导航店铺
    goMarket(data) {
      // 高德转百度坐标
      function bd_encrypt(gg_lng, gg_lat) {
        var X_PI = Math.PI * 3000.0 / 180.0
        var x = gg_lng
        var y = gg_lat
        var z = Math.sqrt(x * x + y * y) + 0.00002 * Math.sin(y * X_PI)
        var theta = Math.atan2(y, x) + 0.000003 * Math.cos(x * X_PI)
        var bd_lng = z * Math.cos(theta) + 0.0065
        var bd_lat = z * Math.sin(theta) + 0.006
        return {
          bd_lat: bd_lat,
          bd_lng: bd_lng
        }
      }
      let zuobiao = bd_encrypt(data.longitude, data.latitude)

      this.mapData = {
        bd_lng: zuobiao.bd_lng,
        bd_lat: zuobiao.bd_lat,
        gd_lng: data.longitude,
        gd_lat: data.latitude,
        marketName: data.marketName
      }
      this.showPop = true
    }
  }
}
</script>

<style scoped lang="scss">
    .address {
        width: 96%;
        background-color: #fff;
        padding: 30px;
        margin: 0 auto;
        margin-top: 15px;
        // margin: 15px 15px 0;
        border-radius: 20px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);

        .d_box {
            .d_item {
                min-height: 85px;
                padding: 15px 0;
                font-size: 30px;
                color: #333333;
                display: flex;
                border-bottom: 1px solid #f4f4f4;
                align-items: center;

                .d_item_title {
                    color: #999999;
                    margin-right: 40px;
                    font-weight: 500;
                    min-width: 60px;
                }

                .d_item_content {
                    display: flex;
                    align-items: center;

                    .delivery-icon, .store-icon {
                        font-style: normal;
                        margin-right: 10px;
                        font-size: 28px;
                    }

                    .divider {
                        margin: 0 10px;
                        color: #ddd;
                    }

                    .freight-fee {
                        display: flex;
                        align-items: center;

                        .freight-icon {
                            width: 120px;
                            height: 38px;
                            margin-right: 5px;
                        }

                        .free-delivery {
                            color: #FF6B6B;
                            font-weight: bold;
                        }
                    }
                }
            }

            .delivery-time {
                display: flex;
                align-items: center;
                padding: 15px 0;
                border-bottom: 1px solid #f4f4f4;

                .time-icon {
                    margin-right: 10px;
                    font-size: 28px;
                }

                .time-info {
                    font-size: 28px;
                    color: #666;
                }
            }

            .store-pickup-container {
                margin-top: 10px;
            }

            .d_md_box {
                min-height: 132px;
                background: #f8f8f8;
                border-radius: 16px;
                padding: 20px;
                font-size: 30px;
                display: flex;
                margin-top: 15px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);
                transition: all 0.3s ease;

                &:hover {
                    transform: translateY(-2px);
                    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
                }

                .d_md_left {
                    width: 95%;

                    .d_md_title {
                        height: 50px;
                        line-height: 50px;
                        display: flex;
                        align-items: center;
                        flex-wrap: wrap;

                        .d_md_title_tag {
                            min-width: 78px;
                            height: 40px;
                            background: #4CAF50;
                            border-radius: 20px;
                            line-height: 40px;
                            height: 37px;
                            color: #ffffff;
                            text-align: center;
                            margin-right: 15px;
                            font-size: 24px;
                            font-weight: 500;
                        }

                        .shop_name {
                            color: #333333;
                            font-size: 32px;
                            font-weight: 500;
                        }

                        .shop_names {
                            color: #666666;
                            font-size: 26px;
                        }

                        .distance {
                            margin-left: 10px;
                            background-color: #f0f0f0;
                            padding: 2px 10px;
                            border-radius: 12px;
                            line-height: 1.2;
                            height: auto;
                        }
                    }

                    .d_md_msg {
                        width: 100%;
                        min-height: 35px;
                        color: #979797;
                        margin-top: 10px;
                        line-height: 1.4;
                        font-size: 28px;
                    }
                }

                .d_md_right {
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: center;

                    img {
                        width: 40px;
                        height: 40px;
                        margin-bottom: 5px;
                    }

                    span {
                        font-size: 24px;
                        color: #4CAF50;
                    }
                }
            }
        }

        .mapList {
            padding: 20px 0;

            .map-option, .cancel-option {
                height: 120px;
                line-height: 120px;
                text-align: center;
                font-size: 32px;
                display: flex;
                align-items: center;
                justify-content: center;

                .map-icon {
                    font-style: normal;
                    margin-right: 10px;
                }
            }

            .map-option {
                color: #333;
                font-weight: 500;
                border-bottom: 1px solid #EEEEEE;

                &:active {
                    background-color: #f9f9f9;
                }
            }

            .cancel-option {
                color: #999;
                font-weight: normal;

                &:active {
                    background-color: #f9f9f9;
                }
            }
        }
    }
</style>
