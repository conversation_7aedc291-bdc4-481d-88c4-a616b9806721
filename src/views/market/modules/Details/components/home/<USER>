<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2022-06-13 17:16:27
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-12-29 15:57:23
-->
<template>
  <div class="home">
    <div class="d_price">
      <div class="price-container">
        <span class="d_showprice_icon">￥</span>
        <span class="d_showprice">{{ detail.price }}</span>
        <div v-if="detail.originalPrice && detail.originalPrice > detail.price" class="d_orprice">￥{{ detail.originalPrice }}</div>
      </div>
      <div v-if="detail.originalPrice && detail.originalPrice > detail.price" class="discount-tag">
        {{ Math.floor((detail.price / detail.originalPrice) * 10) }}折
      </div>
      <div v-else class="inventory-tag">
        库存: {{ detail.inventory }}
      </div>
    </div>

    <div class="marketing-tags">
      <div v-if="detail.sales > 0" class="tag sales-tag">
        <i class="tag-icon">🔥</i>{{ detail.sales }}人已付款
      </div>
      <div class="tag hot-tag">
        <i class="tag-icon">⏱️</i>限时热卖
      </div>
      <div v-if="showCountdown" class="countdown">
        <span class="countdown-label">距结束</span>
        <span class="countdown-time">{{ countdownTime }}</span>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    detail: {
      type: Object,
      default: function() {
        return {}
      }
    }
  },
  data() {
    return {
      showCountdown: true,
      countdownTime: '23:59:59',
      timer: null,
      endTime: null
    }
  },
  created() {
    // 设置一个24小时后的结束时间
    this.setCountdown()
  },
  mounted() {
    this.startCountdown()
  },
  beforeDestroy() {
    if (this.timer) {
      clearInterval(this.timer)
    }
  },
  methods: {
    setCountdown() {
      // 设置24小时后的结束时间
      const now = new Date()
      this.endTime = new Date(now.getTime() + 24 * 60 * 60 * 1000)
      this.updateCountdown()
    },
    startCountdown() {
      this.timer = setInterval(() => {
        this.updateCountdown()
      }, 1000)
    },
    updateCountdown() {
      const now = new Date()
      const diff = this.endTime - now

      if (diff <= 0) {
        this.countdownTime = '00:00:00'
        clearInterval(this.timer)
        return
      }

      const hours = Math.floor(diff / (1000 * 60 * 60))
      const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60))
      const seconds = Math.floor((diff % (1000 * 60)) / 1000)

      this.countdownTime = `${this.padZero(hours)}:${this.padZero(minutes)}:${this.padZero(seconds)}`
    },
    padZero(num) {
      return num < 10 ? `0${num}` : num
    }
  }
}
</script>

<style scoped lang="scss">
    .home {
        width: 100%;
        height: 210px;
        background: linear-gradient(
        90deg,
        rgb(255, 50, 40) 0%,
        rgb(255, 50, 41) 0%,
        rgb(255, 90, 37) 100%,
        rgb(255, 90, 37) 100%
      );
        border-radius: 0 0 20px 20px;
        margin-top: -90px;
        position: relative;
        z-index: 2;
        padding: 20px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);

        .d_price {
            width: 100%;
            display: flex;
            justify-content: space-between;
            align-items: center;
            color: #ffffff;

            .inventory-tag {
                font-size: 26px;
                margin-left: 10px;
                opacity: 0.8;
            }

            .price-container {
                display: flex;
                align-items: baseline;

                .d_showprice_icon {
                    font-size: 32px;
                    font-weight: bold;
                    margin-right: 2px;
                }

                .d_showprice {
                    font-size: 69px;
                    font-family: PingFangSC-Medium;
                    font-weight: bold;
                    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
                }

                .d_orprice {
                    font-size: 28px;
                    margin-left: 10px;
                    text-decoration: line-through;
                    opacity: 0.8;
                }
            }

            .discount-tag {
                background-color: #FFD700;
                color: #333;
                font-size: 26px;
                font-weight: bold;
                padding: 5px 12px;
                border-radius: 15px;
                margin-left: 10px;
                box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            }
        }

        .marketing-tags {
            display: flex;
            flex-wrap: wrap;
            margin-top: 15px;

            .tag {
                display: flex;
                align-items: center;
                background-color: rgba(255, 255, 255, 0.2);
                color: #ffffff;
                font-size: 26px;
                padding: 5px 12px;
                border-radius: 15px;
                margin-right: 10px;
                margin-bottom: 10px;

                .tag-icon {
                    margin-right: 5px;
                    font-style: normal;
                }
            }

            .sales-tag {
                background-color: rgba(255, 255, 255, 0.25);
            }

            .hot-tag {
                background-color: rgba(255, 255, 255, 0.25);
            }

            .countdown {
                display: flex;
                align-items: center;
                margin-left: auto;

                .countdown-label {
                    font-size: 26px;
                    color: #ffffff;
                    margin-right: 8px;
                }

                .countdown-time {
                    background-color: rgba(0, 0, 0, 0.2);
                    color: #ffffff;
                    font-size: 26px;
                    font-weight: bold;
                    padding: 5px 10px;
                    border-radius: 8px;
                    letter-spacing: 1px;
                }
            }
        }
    }
</style>
