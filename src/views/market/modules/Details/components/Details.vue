<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2022-04-29 10:55:08
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-06-16 18:38:43
-->
<template>
  <div class="home">
    <div class="d_market_details">
      <div class="d_market_details_header">
        <span class="d_market_details_title">商品详情</span>
        <div class="details-divider" />
      </div>

      <div class="d_market_details_box">
        <img
          v-for="(item, index) in detail.detailsPicturesList"
          :key="index"
          :src="item"
          alt=""
          class="detail-image"
          loading="lazy"
          @click="previewImage(item, index)"
        >
      </div>
    </div>
    <div style="height:120px" />
  </div>
</template>

<script>
export default {
  props: {
    detail: {
      type: Object,
      default: function() {
        return {}
      }
    }
  },
  data() {
    return {}
  },
  created() {},
  mounted() {},
  methods: {
    previewImage(_, index) {
      // 图片预览功能
      const images = this.detail.detailsPicturesList || []
      if (images.length > 0) {
        AlipayJSBridge.call('previewImage', {
          current: index,
          urls: images
        }, function() {})
      }
    }
  }
}
</script>

<style scoped lang="scss">
.home {
  .d_market_details {
    display: flex;
    flex-direction: column;
    margin: 15px 15px 0;
    padding: 30px 20px;
    background-color: #ffffff;
    border-radius: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);

    .d_market_details_header {
      display: flex;
      align-items: center;
      margin-bottom: 25px;

      .d_market_details_title {
        color: #333333;
        font-size: 36px;
        font-weight: 600;
        line-height: 50px;
        white-space: nowrap;
        font-family: PingFangSC-Medium;
        position: relative;
        padding-left: 15px;

        &::before {
          content: '';
          position: absolute;
          left: 0;
          top: 10px;
          height: 30px;
          width: 6px;
          background-color: #FF6B6B;
          border-radius: 3px;
        }
      }

      .details-divider {
        flex: 1;
        height: 1px;
        background: linear-gradient(to right, #f0f0f0, transparent);
        margin-left: 15px;
      }
    }

    .d_market_details_box {
      width: 100%;
      height: auto;

      .detail-image {
        width: 100%;
        height: auto;
        display: block;
        margin-bottom: 15px;
        border-radius: 10px;
        transition: all 0.3s ease;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);

        &:hover {
          transform: translateY(-3px);
          box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }
      }
    }
  }
}
</style>
