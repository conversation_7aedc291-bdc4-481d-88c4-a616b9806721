<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2022-04-29 11:48:29
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-12-27 18:10:29
-->
<template>
  <div class="home" :style="styleVar">
    <NavHeight v-if="opacity > 0" bgc="#fff" />
    <div
      ref="element"
      class="opacity_back"
      :class="{ fixedBack: opacity >= 1 }"
      :style="styleVar"
    >
      <div class="opacity_top">
        <van-icon
          style="margin-left: 19px"
          class="icons"
          name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/review/back.png"
          size="19px"
          @click="goBack()"
        />
        <div class="marketName">{{ detail.title }}</div>
        <div />
      </div>
    </div>
    <div class="back_img">
      <img
        v-if="opacity == 0"
        src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/market/goback.png"
        alt=""
        @click="goBack()"
      >
      <img
        v-show="detail.activityJoinNo"
        v-if="opacity == 0"
        src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/market/market/home/<USER>"
        alt=""
        @click="getSharea"
      >
    </div>

    <van-swipe class="my-swipe" :autoplay="3000" indicator-color="#FFD700" :show-indicators="true">
      <van-swipe-item v-for="(item,index) in detail.coverPicturesList" :key="index" @click="previewImage(item, index)">
        <img
          :src="item"
          style="object-fit: cover;"
          class="swipe-image"
        >
      </van-swipe-item>
    </van-swipe>

  </div>
</template>

<script>
export default {
  props: {
    detail: {
      type: Object,
      default: function() {
        return {}
      }
    }
  },
  data() {
    return {
      opacity: 0
    }
  },
  computed: {
    styleVar() {
      return {
        '---nav-height': this.$store.getters.getStatusHeight + 'px'
      }
    }
  },
  watch: {
    itemindex(n) {
      let lis = this.$refs.lItem
      if (lis && lis[n]) {
        if (this.isElementNotInViewport(lis[n])) {
          document.getElementById('lefts').scrollTop = 200
        }
        if (this.isElementNotInViewportbottom(lis[n])) {
          document.getElementById('lefts').scrollTop = -100
        }
      }
    }
  },
  created() {},
  mounted() {
    // js动态获取高度
    window.addEventListener('scroll', this.handleScrollx, true)
  },
  destroyed() {
    window.removeEventListener('scroll', this.handleScrollx, true)
  },
  methods: {
    previewImage(_, index) {
      // 图片预览功能
      const images = this.detail.coverPicturesList || []
      if (images.length > 0) {
        AlipayJSBridge.call('previewImage', {
          current: index,
          urls: images
        }, function() {})
      }
    },
    getSharea() {
      let data = {
        title: this.detail.title,
        text: this.detail.title,
        image: this.detail.cover + '?x-oss-process=image/resize,w_440/format,jpg/quality,q_35',
        path: `/pages/pt/details/index?id=${this.detail.activityJoinNo}&goodsId=${this.detail.skuId}&activityNo=${this.detail.activityNo}&activityJoinNo=${this.detail.activityJoinNo}`
      }
      console.log(data)
      AlipayJSBridge.call('WechatappletShare', data, function() {})
    },
    goBack() {
      this.$router.go(-1)
    },
    // 判断顶部区域是否在可视区域
    isElementNotInViewport(el) {
      let rect = el.getBoundingClientRect()
      return (
        rect.top >=
        (window.innerHeight - 200 ||
          document.documentElement.clientHeight - 200)
      )
    },
    // 判断顶部区域是否在可视区域
    isElementNotInViewportbottom(el) {
      let rect = el.getBoundingClientRect()
      return rect.bottom <= 300
    },
    // 监听滚动
    handleScrollx() {
      // 获取页面滚动距离
      let top =
        document.documentElement.scrollTop ||
        document.body.scrollTop ||
        window.pageYOffset
      const opacityBack = document.querySelector('.opacity_back')
      if (top <= 120 && top >= 0) {
        opacityBack.style.setProperty('opacity', 0)
        this.opacity = 0
      } else {
        opacityBack.style.setProperty('opacity', (top - 80) / 100)
        this.opacity = (top - 50) / 100
      }
    }
  }
}
</script>

<style scoped lang="scss">
.home {
  width: 100%;
  // height: calc(634px + var(---nav-height));
  .my-swipe {
    width: 100%;
    height: 750px;
    border-radius: 0 0 20px 20px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);

    .swipe-image {
      width: 100%;
      height: 750px;
      transition: transform 0.3s ease;

      &:hover {
        transform: scale(1.02);
      }
    }

    :deep(.van-swipe__indicators) {
      bottom: 20px;
    }

    :deep(.van-swipe__indicator) {
      width: 10px;
      height: 10px;
      background-color: rgba(255, 255, 255, 0.6);
      opacity: 0.8;
      border-radius: 50%;
      margin: 0 6px;
    }

    :deep(.van-swipe__indicator--active) {
      width: 24px;
      border-radius: 5px;
      background-color: #FFD700;
    }
  }

  .back_img {
    width: 90%;
    height: 100px;
    display: flex;
    justify-content: space-between;
    position: fixed;
    top: 90px;
    left: 30px;
    z-index: 2;

    img {
      width: 56px;
      height: 56px;
      float: left;
      background-color: rgba(0, 0, 0, 0.3);
      border-radius: 50%;
      padding: 10px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
      transition: all 0.3s ease;

      &:active {
        transform: scale(0.95);
        background-color: rgba(0, 0, 0, 0.4);
      }
    }
  }
  .opacity_back {
    width: 100%;
    position: fixed;
    background-color: #fff;
    // top: 0;
    top: var(--nav-height);
    left: 0;
    opacity: 0;
    z-index: 100;
  }
  .opacity_top {
    width: 100%;
    height: 80px;
    line-height: 80px;
    background-color: #fff;
    display: flex;
    justify-content: space-between;
    .marketName {
      font-size: 36px;
      margin-right: 60px;
    }
    .icons {
      position: relative;
      top: 20px;
    }
    .iconsShare {
      margin-right: 30px;
    }
  }
  .fixedBack {
    z-index: 20;
  }
}
</style>
