<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2022-04-29 14:00:56
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-05-17 21:08:51
-->
<template>
  <div class="home">
    <div class="d_market_address">
      <div class="d_market_address_left">
        <span class="text_msg">送至</span>
        <div class="d_market_address_tag">
          <span class="d_market_address_mag">同城配送</span>
        </div>
      </div>
      <div class="d_market_address_right" @click="$store.state.mall.addressShow = true">
        <span
          class="d_market_address_name"
        >{{ address }}</span>
        <img
          src="https://codefun-proj-user-res-1256085488.cos.ap-guangzhou.myqcloud.com/6114f1316b68fa00119edb1e/626b447c4eb5590011d400af/16511984833440166165.png"
          class="right"
        >
      </div>
    </div>

    <!-- 选择地址弹框 -->
    <van-popup
      v-model="$store.state.mall.addressShow"
      position="bottom"
      round
      closeable
      :style="{ width: '100%', height: '350px' }"
    >
      <div class="box">
        <div class="title">地址</div>
        <div class="body">
          <van-radio-group v-model="radiovalue" @change="radioChange">
            <div class="address-pop" style="margin-top: 62px">
              <div v-for="(item, index) in temparr" :key="index" class="item">
                <div class="item-left">
                  <van-radio
                    :name="index"
                    icon-size="19px"
                    checked-color="#5dcb4f"
                  >
                    <div class="item-detail">
                      <!-- 最多可以输入18个字 -->
                      <div class="item-detail-address">
                        <van-tag
                          v-if="item.isDefault == true"
                          plain
                          type="danger"
                        >默认</van-tag>
                        {{ showAddress(item) | ellipsis(18) }}
                      </div>
                      <div class="item-detail-user">
                        <span style="margin-right: 26px">{{
                          tabStatus == 1 ? item.username : item.contacts
                        }}</span>
                        <span>{{
                          tabStatus == 1 ? item.mobile : item.mobilePhone
                        }}</span>
                      </div>
                    </div>
                  </van-radio>
                </div>
                <div
                  v-if="tabStatus == 1"
                  class="item-right"
                  @click="editAddress(item)"
                >
                  <van-icon
                    name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/order/edit.png"
                    size="17px"
                  />
                </div>
              </div>
            </div>
          </van-radio-group>
          <!-- 超出配送距离 -->
          <div
            v-if="
              temparrNo.length > 0
            "
            class="disaddress"
          >
            超出配送范围地址
          </div>
          <div
            v-if="temparrNo.length>0"
            class="address-pop"
          >
            <div v-for="(item, index) in temparrNo" :key="index" class="item">
              <div class="item-left" @click="disAddrShow = true">
                <!-- <van-radio :name="index" icon-size="19px" checked-color="#5dcb4f" /> -->
                <div class="item-detail opacity6">
                  <div style="color: #222">
                    <van-tag
                      v-if="item.isDefault == true"
                      plain
                      type="danger"
                    >默认</van-tag>
                    {{ showAddress(item) | ellipsis(18) }}
                  </div>
                  <div style="color: #999">
                    <span style="margin-right: 26px">{{ item.username }}</span>
                    <span>{{ item.mobile }}</span>
                  </div>
                </div>
              </div>
              <div class="item-right" @click="editAddress(item)">
                <van-icon
                  name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/order/edit.png"
                  size="17px"
                />
              </div>
            </div>
          </div>
          <div style="height: 50px" />
          <!-- 添加地址按钮 -->
          <div v-if="tabStatus == 1" class="newAddr" @click="newAddr">
            <div />
          </div>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script>
import { listByGoods } from '../../../../../api/market'
export default {
  data() {
    return {
      addressShow: false,
      radiovalue: '',
      tabStatus: 1,
      temparr: [],
      temparrNo: [],
      address: '配送位置影响是否配送，请正确选择'
    }
  },
  created() {
    this.getAddressList()
  },
  methods: {
    // 选择地址
    radioChange(e) {
      let data = this.temparr[e]
      this.address = data.address + data.street
      this.$store.state.mall.addressShow = false
      let addressData = {
        addressId: data.id,
        address: data.address,
        street: data.street,
        username: data.username,
        mobile: data.mobile,
        isDefault: data.isDefault,
        isStatus: 0
      }
      this.$store.state.mall.address = addressData
      this.$store.state.mall.isBtn = 1
    },
    // 获取地址列表
    getAddressList() {
      listByGoods({ goodsId: this.$route.query.goodsId }).then(res => {
        this.$toast.clear()
        if (res.status == 200) {
          this.temparr = res.data.withinDistance
          this.temparrNo = res.data.beyondDistance

          let data = [...res.data.withinDistance, ...res.data.beyondDistance]
          for (let i = 0; i < data.length; i++) {
            if (data[i].isDefault === true) {
              this.address = data[i].address
              let addressData = {
                addressId: data[i].id,
                address: data[i].address,
                street: data[i].street,
                username: data[i].username,
                mobile: data[i].mobile,
                isDefault: data[i].isDefault,
                isStatus: 0
              }
              this.$store.state.mall.address = addressData
              break
            }
          }

          // 底部按钮展示--1：参与拼团，2：位置不支持配送，3：无地址
          if (this.temparr.length > 0) {
            for (let i = 0; i < this.temparr.length; i++) {
              if (this.temparr[i].isDefault === true) {
                this.$store.state.mall.isBtn = 1
                return
              }
            }
          }
          if (this.temparrNo.length > 0) {
            for (let i = 0; i < this.temparrNo.length; i++) {
              if (this.temparrNo[i].isDefault === true) {
                this.$store.state.mall.isBtn = 2
                this.$store.state.mall.address.isStatus = 1
                return
              }
            }
          }
          this.$store.state.mall.isBtn = 3
        }
      })
    },
    // 格式化地址
    showAddress(row) {
      let dz = row.province + row.city + row.district
      let dzLength = dz.length
      let newdz = row.address.slice(0, dzLength)
      if (dz == newdz) {
        return row.address.slice(dzLength)
      } else {
        return row.address + row.street
      }
    },
    newAddr() { // 新增地址
      this.$router.push({
        name: 'AddressAdd'
      })
    },
    editAddress(item) { // 修改地址
      this.$router.push({
        name: 'AddressEdit',
        query: {
          id: item.id
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
.home {
  .d_market_address {
    display: flex;
    justify-content: space-between;
    margin-top: 20px;
    padding: 24px 24px;
    background-color: rgb(255, 255, 255);
    .d_market_address_left,
    .d_market_address_right {
      display: flex;
      flex-direction: row;
    }
    .text_msg {
      font-family: PingFangSC-Medium;
      color: rgb(51, 51, 51);
      font-size: 29px;
      font-weight: 500;
      line-height: 40px;
      white-space: nowrap;
    }
    .d_market_address_tag {
      display: flex;
      flex-direction: column;
      margin-left: 8px;
      color: rgb(255, 163, 0);
      font-size: 28px;
      font-weight: 500;
      line-height: 37px;
      white-space: nowrap;
      background-color: rgb(255, 245, 193);
      border-radius: 4px;
      height: 38px;
      .d_market_address_mag {
        margin: 1px 9px;
        font-family: PingFangSC-Medium;
      }
    }
    .d_market_address_name {
      margin-left: 24px;
      margin-right: 10px;
      color: rgb(51, 51, 51);
      font-size: 29px;
      line-height: 38px;
      white-space: nowrap;
    }
    .right {
      margin: 6px 0;
      width: 24px;
      height: 24px;
    }
  }
  .box {
    .title {
      position: absolute;
      top: 0;
      left: 0;
      z-index: 10;
      width: 100%;
      box-sizing: border-box;
      text-align: center;
      height: 124px;
      line-height: 104px;
      font-size: 32px;
      color: #333;
      border-radius: 20px 20px 0 0;
      background: #fff;
    }

    .body {
      width: 100%;
      height: 600px;
      overflow-y: auto;
    }

    .close-icon {
      position: fixed;
      bottom: 625px;
      right: 40px;
      z-index: 10;
    }
    .disaddress {
      font-size: 32px;
      color: #ff301e;
      margin-left: 40px;
      margin-bottom: 20px;
      padding: 20px 0;
    }
    .address-pop {
      margin: 4px 42px 20px 18px;
      .item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 30px;
        color: #000010;
        height: 111px;
        border-bottom: 1px solid #f4f4f4;
        margin-bottom: 20px;
        .item-left {
          display: flex;
          align-items: center;
          .item-detail {
            margin-left: 16px;
            .item-detail-address {
              font-size: 32px;
              color: #222;
              margin-bottom: 8px;
            }
            .item-detail-user {
              font-size: 26px;
              color: #999;
            }
          }
          .opacity6 {
            opacity: 0.6;
          }
        }

        .item-right {
          display: flex;
          align-items: center;
        }
      }
    }

    .newAddr {
      position: fixed;
      bottom: 0;
      left: 0;
      width: 100%;
      background-color: #fff;
      font-size: 30px;
      padding: 10px 0 9px;

      > div {
        width: 690px;
        height: 88px;
        margin: 0 auto;
        background-image: url("https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/user/address/address-btn.png?x-oss-process=image/resize,w_700/format,jpg/quality,q_80");
        background-size: 100%;
      }
    }

    ::v-deep .van-radio__icon .van-icon {
      border: 1px solid #5dcb4f;
    }
  }
}
</style>
