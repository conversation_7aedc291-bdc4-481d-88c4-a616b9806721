<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2022-04-29 10:42:23
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-08-06 09:45:51
-->
<template>
  <div class="home">
    <div class="d_market_info">
      <div class="d_market_info_left">
        <div class="d_market_info_sum">
          <div class="d_market_info_tag">
            {{ detail.pintuanUnitAmount }}人团
          </div>
          <span class="d_market_info_sum_num">{{ detail.pintuanUserAmount }}人已拼团</span>
        </div>
        <div class="d_market_info_msg">
          <!-- <span>同城配送</span> -->
          <!-- <span v-for="item in detail.distributionInfo" v-show="item.distributionType == 1" :key="item.id+'loin'">
            <span v-if="item.basicFreight>0">运费￥{{ item.basicFreight }}</span>
            <span v-else>免运费</span>

          </span> -->
        </div>
      </div>
      <div class="d_market_info_title">{{ detail.title }}</div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    detail: {
      type: Object,
      default: function() {
        return {}
      }
    }
  },
  data() {
    return {}
  },
  created() {},
  mounted() {},
  methods: {}
}
</script>

<style scoped lang="scss">
.home {
  .d_market_info {
    display: flex;
    flex-direction: column;
    padding: 0 20px 24px;
    background-color: rgb(255, 255, 255);
    .d_market_info_left {
      display: flex;
      justify-content: space-between;
      padding: 23px 4px 22px;
      border-bottom: solid 2px rgb(244, 244, 244);
      .d_market_info_sum{
        display: flex;
        flex-direction: row;

        .d_market_info_sum_num {
          margin-left: 12px;
          color: rgb(102, 102, 102);
          font-size: 29px;
          line-height: 40px;
          white-space: nowrap;
        }
        .d_market_info_tag{
          display: flex;
          flex-direction: column;
          align-items: center;
          color: rgb(255, 69, 52);
          font-size: 28px;
          white-space: nowrap;
          border-radius: 4px;
          width: 92px;
          height: 38px;
          line-height: 36px;
          border: solid 2px rgb(255, 69, 52);
        }
      }
      .d_market_info_msg{
        display: flex;
        flex-direction: row;
        // span:nth-child(1) {
        //   color: rgb(51, 51, 51);
        //   font-size: 29px;
        //   font-weight: 500;
        //   line-height: 40px;
        //   white-space: nowrap;
        //   font-family: PingFangSC-Medium;
        // }
        span:nth-child(1) {
          margin-left: 24px;
          color: #999;
          font-size: 29px;
          line-height: 40px;
          white-space: nowrap;
        }
      }

    }
    .d_market_info_title {
      width: 650px;
      margin-left: 4px;
      margin-top: 23px;
      color: rgb(34, 34, 34);
      font-size: 33px;
      font-weight: 500;
      line-height: 45px;
      font-family: PingFangSC-Medium;
      word-wrap: break-word;
    }
    .d_market_info_ccb{
      width: 100%;
      height: 110px;
      background-color: #FFF5F0;
      // background-image: url(https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/home/<USER>/activity/jhbj.png);
      // background-size: 100% 100%;
      margin-top: 18px;
      div{
        width: 92%;
        margin: 0 auto;
        font-size: 29px;
        color: #ff7d3e;
        margin-top: 16px;
      }
      img{
        width: 30px;
        height: 30px;
        position: relative;
        top: 5px;
      }
    }
  }
}
</style>
