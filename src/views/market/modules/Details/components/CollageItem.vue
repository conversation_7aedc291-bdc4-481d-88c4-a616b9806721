<template>
  <div class="home">
    <div class="d_market_collage">
      <div class="d_market_collage_top">
        <div>这些人刚刚完成拼单，和他们一起拼单</div>
        <div class="d_market_collage_top_right" @click="$store.state.mall.showPop = true">
          查看全部
          <img
            src="https://codefun-proj-user-res-1256085488.cos.ap-guangzhou.myqcloud.com/6114f1316b68fa00119edb1e/626b447c4eb5590011d400af/16511984833440166165.png"
            class="right"
          >
        </div>

      </div>
      <div v-for="item in listTwo" :key="item.id" class="d_market_collage_list">
        <div class="d_market_collage_list_portrait">
          <img v-if="item.groupUserList.length === 0" src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/my/anio.png" class="portrait">
          <img v-for="(img,index) in item.groupUserList" :key="index" class="portrait" :style="index>0?'left:-'+index*7+'px;':''" :src="img.headPictureUrl!==''&&img.headPictureUrl!==null?img.headPictureUrl:'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/my/anio.png'" alt="">
        </div>
        <div class="d_market_collage_people">
          <span class="d_market_collage_people_num">还差{{ item.needAmount-item.currentAmount }}人拼成</span>
          <div class="flex-col d_market_collage_people_btn">
            <span v-if="item.isJoined == true" @click="goPtDetil(item)">拼团详情</span>
            <span v-else @click="createOrder(item)">一起拼团</span>
          </div>
        </div>
      </div>

      <!-- 弹出 -->
      <van-popup v-model="$store.state.mall.showPop" round :lock-scroll="false" closeable>
        <div class="pop_list">
          <div class="pop_top">
            可参与的拼团
          </div>
          <div class="pop_box">
            <div v-for="item in listAll" :key="item.id" class="d_market_collage_list">
              <div class="d_market_collage_list_portrait">
                <img v-if="item.groupUserList.length === 0" src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/my/anio.png" class="portrait">
                <img v-for="(img,index) in item.groupUserList" :key="img.userId" class="portrait" :style="index>0?'left:-'+index*7+'px;':''" :src="img.headPictureUrl!==''&&img.headPictureUrl!==null?img.headPictureUrl:'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/my/anio.png'" alt="">
              </div>
              <div class="d_market_collage_people">
                <div class="flex-col d_market_collage_people_btn">
                  <span v-if="item.isJoined == true" @click="goPtDetil(item)">拼团详情</span>
                  <span v-else @click="createOrder(item)">一起拼团</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </van-popup>
    </div>

    <!-- 待支付弹出 -->
    <van-popup v-model="showPayPop" round>
      <div class="d_payWite">
        <div class="d_payWite_tips">请先完成待付款订单</div>
        <div class="d_payWite_btn">
          <div class="d_payWite_btn_yes" @click="goOrderPay">
            去支付
          </div>
          <div class="d_payWite_btn_no" @click="showPayPop = false">暂不支付</div>
        </div>
      </div>
    </van-popup>

  </div>
</template>

<script>
import { getTopTwoGroupWithActivityJoinStatus, moreGroup, findUnPayByActiveNo, judgeGroupIsFull } from '@/api/market'
export default {
  props: {
    detail: {
      type: Object,
      default: function() {
        return {}
      }
    }
  },
  data() {
    return {
      showPop: false,
      showPayPop: false,
      listTwo: [],
      listAll: [],
      form: {
        activityJoinNo: this.$route.query.id,
        addressId: '',
        delieveryType: 1,
        groupNo: '',
        mallGoods: [{
          skuId: '',
          skuQuantity: 1
        }],
        marketId: ''
      },
      orderNo: '',
      actualPay: ''
    }
  },
  created() {
    this.getTopTwoGroupWithActivityJoinStatus()

    // 3s刷新
    const timerccb = window.setInterval(() => {
      this.getTopTwoGroupWithActivityJoinStatus()
    }, 3000)

    this.$once('hook:beforeDestroy', () => {
      window.clearInterval(timerccb)
    })

    this.moreGroup()
  },
  methods: {
    // 参加拼团
    createOrder(item) {
      // 商品信息
      let goodsData = {
        goodsId: this.detail.id,
        goodsName: this.detail.title,
        pictureUrl: this.detail.cover,
        pintuanPrice: this.detail.pintuanPrice,
        originPrice: this.detail.price
      }
      this.$store.state.mall.goodsData = goodsData
      // 计算运费
      let distribution = this.detail.distributionInfo
      for (let i = 0; i < distribution.length; i++) {
        if (distribution[i].distributionType == 1) {
          this.$store.state.mall.goodsData.freightFee = distribution[i].basicFreight
        }
        // if (distribution[i].distributionType == 2) {
        //   this.$store.state.mall.goodsData.freightFee = distribution[i].basicFreight
        // }
      }

      // 记录配送方式
      let distri = this.detail.distributionInfo
      for (let i = 0; i < distri.length; i++) {
        this.$store.state.mall.distributionInfo.push(distri[i])
        if (distri[i].distributionType == 2) {
          this.$store.state.mall.ztData = distri[i]
        }
      }

      // 下单信息
      this.form.addressId = this.$store.state.mall.address.addressId
      this.form.groupNo = item.groupNo
      this.form.mallGoods[0].skuId = this.detail.skuId
      this.form.marketId = this.detail.mallMarketId
      this.$store.state.mall.creatForm = this.form

      this.$store.state.mall.isActive.pt = true
      this.$store.state.mall.isActive.activityJoinNo = this.$route.query.id
      this.$store.state.mall.isActive.groupNo = item.groupNo

      this.findUnPayByActiveNo()
    },
    // 拼团校验是否有已支付
    findUnPayByActiveNo() {
      findUnPayByActiveNo({ activeNo: this.$route.query.activityJoinNo }).then(res => {
        if (res.status === 200) {
          if (res.data === null) {
            // 校验该团是否已满
            this.judgeGroupIsFull()
          } else {
            this.orderNo = res.data.orderNo
            this.actualPay = res.data.actualPay
            this.showPayPop = true
          }
        }
      })
    },
    // 判断该团是否已满
    judgeGroupIsFull() {
      judgeGroupIsFull({ groupNo: this.form.groupNo }).then(res => {
        if (res.status === 200) {
          if (res.data === false) {
            // 跳转下单
            this.$router.push({
              name: 'MarketCart',
              query: {
                marketId: this.detail.mallMarketId,
                inventory: this.detail.inventory,
                isPt: true,
                poolId: this.detail.poolId
              }
            })
            this.$store.state.mall.showPop = false
          } else {
            this.$toast('该团已满，请参加其他团！')
            // 更新数据
            this.getTopTwoGroupWithActivityJoinStatus()
            this.moreGroup()
          }
        }
      })
    },
    // 跳转支付
    goOrderPay() {
      this.$router.push({
        name: 'MarketOrderDetails',
        query: {
          orderNo: this.orderNo
        }
      })
      this.$store.state.mall.showPop = false
    },
    // 跳转拼团详情
    goPtDetil(item) {
      this.$router.push({
        name: 'MarketOrderPtDetails',
        query: {
          activityJoinNo: item.activityJoinNo,
          groupNo: item.groupNo
        }
      })
      this.$store.state.mall.showPop = false
    },
    // 获取前两个
    getTopTwoGroupWithActivityJoinStatus() {
      getTopTwoGroupWithActivityJoinStatus({ activityJoinNo: this.$route.query.id }).then(res => {
        if (res.status === 200) {
          this.listTwo = res.data.joinedGroupList
        }
      })
    },
    // 获取全部
    moreGroup() {
      moreGroup({ activityJoinNo: this.$route.query.id }).then(res => {
        if (res.status === 200) {
          this.listAll = res.data
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
.home {
  .pop_list{
    width: 602px;
    max-height: 983px;
    position: relative;
    overflow: hidden;
    .pop_top{
      width: 100%;
      height: 50px;
      line-height: 50px;
      font-size: 35px;
      text-align: center;
      margin-top: 30px;
      position:absolute;
      top: 0;
      z-index: 1;
      background-color: #fff;
    }
    .pop_box{
      padding: 25px;
      margin-top: 75px;
      max-height: 983px;
      overflow-y: auto;
    }
  }
  .d_market_collage {
    display: flex;
    flex-direction: column;
    margin-top: 20px;
    padding: 0 20px 29px;
    background-color: rgb(255, 255, 255);
    .d_market_collage_top {
      display: flex;
      justify-content: space-between;
      flex-direction: row;
      padding: 22px 4px 21px;
      border-bottom: solid 2px rgb(244, 244, 244);
      color: #333333;
      font-size: 29px;
      line-height: 40px;
      white-space: nowrap;
      .d_market_collage_top_right {
        color: rgb(153, 153, 153);
      }
      .right {
        width: 24px;
        height: 24px;
      }
    }
    .d_market_collage_list {
      display: flex;
      justify-content: space-between;
      padding: 23px 2px;
      border-bottom: solid 2px rgb(244, 244, 244);
      .d_market_collage_list_portrait {
        width: 130px;
        position: relative;
        display: flex;
        .portrait{
          width: 48px;
          height: 48px;
          border-radius: 50%;
          position: relative;
        }
      }

      .d_market_collage_people{
        display: flex;
        flex-direction: row;
        .d_market_collage_people_num {
          margin: 7px 0;
          color: rgb(51, 51, 51);
          font-size: 28px;
          line-height: 40px;
          white-space: nowrap;
        }
        .d_market_collage_people_btn{
          display: flex;
          flex-direction: column;
          margin-left: 22px;
          padding: 7px 0;
          color: rgb(255, 255, 255);
          font-size: 28px;
          font-weight: 500;
          line-height: 40px;
          letter-spacing: -0.22px;
          white-space: nowrap;
          background-image: linear-gradient(
            90deg,
            rgb(64, 210, 67) 0%,
            rgb(64, 210, 67) 0%,
            rgb(31, 196, 50) 100%,
            rgb(31, 196, 50) 100%
          );
          border-radius: 10px;
          height: 54px;
          span {
            margin: 0 14px;
          }
        }
      }
    }
  }
  .d_payWite {
    width: 640px;
    height: 370px;
    background-color: rgb(255, 255, 255);
    border-radius: 24px;
    text-align: center;
    overflow: hidden;
    .d_payWite_tips{
      font-size: 40px;
      color: #222222;
      font-family: PingFangSC-Medium;
      margin-top: 56px;
    }
    .d_payWite_btn{
      margin-top: 64px;
      .d_payWite_btn_yes{
        width: 312px;
        height: 80px;
        line-height: 80px;
        text-align: center;
        background: #39cf3f;
        border-radius: 40px;
        font-size: 36px;
        color: #fff;
        font-family: PingFangSC-Medium;
        margin: 0 auto;
      }
      .d_payWite_btn_no{
        margin-top: 24px;
        font-size: 30px;
        color: #999999;
      }
    }
  }
}
</style>
