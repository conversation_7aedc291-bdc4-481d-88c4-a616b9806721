<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2022-04-29 10:56:48
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-06-15 11:20:34
-->
<template>
  <div class="home">
    <div class="justify-between d_market_bottom" :style="styleVar">
      <img
        src="https://codefun-proj-user-res-1256085488.cos.ap-guangzhou.myqcloud.com/6114f1316b68fa00119edb1e/626b447c4eb5590011d400af/16511984831754708843.png"
        class="d_service_icon"
        @click="CallPhone"
      >
      <div v-if="false" class="d_markrt_btn">
        <span>您的位置不支持配送</span>
      </div>
      <div class="flex-col items-center d_markrt_btn1" @click="$store.state.mall.showPop = true">
        <span class="text_30">￥{{ detail.pintuanPrice }}</span>
        <span class="text_31">参与拼团</span>
      </div>

      <div v-if="false" class="flex-row d_markrt_btn2" @click="newAddr">
        <span class="text_27">未找到您的收货地址</span>
        <div class="flex-col items-center text-wrapper_4">
          <span>添加地址</span>
        </div>
      </div>

    </div>
  </div>
</template>

<script>
export default {
  props: {
    detail: {
      type: Object,
      default: function() {
        return {}
      }
    }
  },
  data() {
    return {}
  },
  computed: {
    styleVar() {
      return {
        '---nav-height': this.$store.getters.getNavigationBarHeight + 'px'
      }
    }
  },
  created() {},
  methods: {
    // 打电话
    CallPhone(data) {
      let phone = '18757156043'
      if (this.$store.getters.getRegionId == 1) {
        phone = '15021881176'
      } else if (this.$store.getters.getRegionId == 3) {
        phone = '17605781836'
      } else if (this.$store.getters.getRegionId == 6) {
        phone = '13587187911'
      } else if (this.$store.getters.getRegionId == 7) {
        phone = '13566993383'
      }
      AlipayJSBridge.call('CallPhone', {
        phoneNum: phone
      }, function(result) {})
    },
    goCart() {
      this.$router.push({
        name: 'MarketCart',
        query: {}
      })
    },
    newAddr() { // 新增地址
      this.$store.state.mall.addressShow = true
    }
  }
}
</script>

<style scoped lang="scss">
.home {
  .d_market_bottom {
    width: 100%;
    height: calc(140px + var(---nav-height));
    padding: 23px 32px;
    color: rgb(102, 102, 102);
    font-size: 28px;
    line-height: 40px;
    letter-spacing: -0.22px;
    white-space: nowrap;
    background-color: rgb(255, 255, 255);
    box-shadow: -4px -4px 27px 0px rgba(220, 220, 220, 0.5);
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    .d_service_icon {
      width: 48px;
      height: 48px;
      margin-top: 23px;
    }
    .d_markrt_btn {
      display: flex;
      flex-direction: column;
      padding: 27px 0;
      background-color: rgb(221, 221, 221);
      border-radius: 47px;
      width: 522px;
      height: 94px;
      text-align: center;
    }
    .d_markrt_btn1 {
      padding: 6px 0 7px;
      overflow: hidden;
      border-radius: 47px;
      background-image: linear-gradient(
        90deg,
        rgb(255, 30, 41) 0%,
        rgb(255, 30, 41) 0%,
        rgb(255, 90, 37) 100%,
        rgb(255, 90, 37) 100%
      );
      width: 286px;
      height: 94px;
      .text_30 {
        color: rgb(255, 255, 255);
        font-size: 35px;
        line-height: 53px;
        letter-spacing: -0.28px;
        white-space: nowrap;
      }
      .text_31 {
        color: rgb(255, 255, 255);
        font-size: 23px;
        line-height: 30px;
        letter-spacing: -0.2px;
        white-space: nowrap;
      }
    }

    .d_markrt_btn2 {
      padding-left: 33px;
      background-color: rgb(221, 221, 221);
      border-radius: 47px;
      width: 522px;
      height: 94px;
      justify-content: space-between;
      .text_27 {
        align-self: center;
        color: rgb(102, 102, 102);
        font-size: 29px;
        line-height: 40px;
        letter-spacing: -0.22px;
        white-space: nowrap;
      }
      .text-wrapper_4 {
        margin-left: 32px;
        padding: 25px 0;
        color: rgb(255, 255, 255);
        font-size: 32px;
        font-weight: 500;
        line-height: 45px;
        letter-spacing: -0.26px;
        white-space: nowrap;
        background-color: rgb(14, 14, 13);
        border-radius: 0px 47px 47px 0px;
        width: 206px;
        height: 94px;
        font-family: PingFangSC-Medium;
      }
    }
  }
}
</style>
