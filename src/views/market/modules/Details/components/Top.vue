<template>
  <div class="home">
    <div class="d_market_discount">
      <div class="flex-row">
        <img
          src="https://codefun-proj-user-res-1256085488.cos.ap-guangzhou.myqcloud.com/6114f1316b68fa00119edb1e/626b447c4eb5590011d400af/16511984832736346948.png"
          class="image_2"
        >
        <img
          src="https://codefun-proj-user-res-1256085488.cos.ap-guangzhou.myqcloud.com/6114f1316b68fa00119edb1e/626b447c4eb5590011d400af/16511984832827193125.png"
          class="image_2"
        >
        <img
          src="https://codefun-proj-user-res-1256085488.cos.ap-guangzhou.myqcloud.com/6114f1316b68fa00119edb1e/626b447c4eb5590011d400af/16511984832905163592.png"
          class="image_5"
        >
      </div>
      <div class="flex-row d_market_discount_price">
        <span class="d_price_icon">￥</span>
        <span class="d_now_price">{{ detail.pintuanPrice }}</span>
        <div class="d_market_discount_orprice">
          ￥{{ detail.price }}
        </div>
      </div>
      <div class="d_market_discount_n">
        <span>{{ getDiscount(detail.pintuanPrice,detail.price) + '折' }}</span>
      </div>
    </div>
    <div class="justify-end d_market_discount_time">
      <img
        src="https://codefun-proj-user-res-1256085488.cos.ap-guangzhou.myqcloud.com/6114f1316b68fa00119edb1e/626b447c4eb5590011d400af/16511984832200868597.png"
        class="d_market_discount_time_bg"
      >
      <span>距结束 </span>
      <van-count-down :time="detail.countDownTime*1000" format="DD 天 HH:mm:ss" />
    </div>
  </div>
</template>

<script>
export default {
  props: {
    detail: {
      type: Object,
      default: function() {
        return {}
      }
    }
  },
  data() {
    return {
      time: 30 * 60 * 60 * 1000
    }
  },
  created() {},
  methods: {
    // 计算折扣
    getDiscount(val1, val2) {
      let num = val1 / val2 * 10
      var f_x = parseFloat(num)
      if (isNaN(f_x)) {
        // alert('function:changeTwoDecimal->parameter error')
        return false
      }
      f_x = Math.round(num * 100) / 100
      var s_x = f_x.toString()
      var pos_decimal = s_x.indexOf('.')
      if (pos_decimal < 0) {
        pos_decimal = s_x.length
        s_x += '.'
      }
      while (s_x.length == pos_decimal + 1) {
        s_x = s_x.slice(0, s_x.length - 1)
      }
      if (s_x > 0) {
        return s_x
      }
    }
  }
}
</script>

<style scoped lang="scss">
.home {
  position: relative;
  .d_market_discount {
    display: flex;
    flex-direction: column;
    padding: 22px 24px 14px 24px;
    border-radius: 0 61px 0 0;
    background-image: url("https://codefun-proj-user-res-1256085488.cos.ap-guangzhou.myqcloud.com/6114f1316b68fa00119edb1e/626b447c4eb5590011d400af/16511984832560544992.png");
    background-size: 100% 100%;
    background-repeat: no-repeat;
    width: 353px;
    position: absolute;
    left: 0;
    bottom: 0;
    z-index: 1;
    .flex-row {
      display: flex;
      flex-direction: row;
    }
    .d_market_discount_price {
      margin-top: 2px;
      .d_price_icon {
        margin-top: 32px;
        color: rgb(255, 255, 255);
        font-size: 25px;
        font-weight: 500;
        line-height: 36px;
        white-space: nowrap;
      }
      .d_now_price {
        color: rgb(255, 255, 255);
        font-size: 56px;
        font-weight: 500;
        line-height: 78px;
        letter-spacing: -0.88px;
        white-space: nowrap;
      }
      .d_market_discount_orprice {
        color: rgb(255, 255, 255);
        font-size: 26px;
        margin-top: 32px;
        margin-left: 5px;
        text-decoration: line-through;
      }
    }
    .d_market_discount_n{
      width: 84px;
      display: flex;
      flex-direction: column;
      align-items: center;
      color: rgb(254, 35, 9);
      font-size: 22px;
      font-weight: 500;
      line-height: 30px;
      white-space: nowrap;
      background-color: rgb(255, 255, 255);
      border-radius: 16px;
      font-family: PingFangSC-Medium;
    }
    .image_2 {
      width: 49px;
      height: 32px;
    }
    .image_5 {
      width: 48px;
      height: 32px;
    }
  }
  .d_market_discount_time {
    display: flex;
    justify-content: flex-end;
    padding: 0 24px 3px;
    color: rgb(255, 255, 255);
    font-family: PingFangSC;
    font-weight: 400;
    font-size: 30px;
    line-height: 42px;
    white-space: nowrap;
    overflow: hidden;

    background-image: linear-gradient(
      155.5deg,
      rgb(255, 72, 44) 0%,
      rgb(255, 72, 44) -5.55%,
      rgb(241, 45, 72) 177.48%,
      rgb(241, 45, 72) 100%
    );
    height: 120px;
    .d_market_discount_time_bg {
      margin-right: 68px;
      filter: blur(30px);
      width: 184px;
      height: 117px;
    }
    span {
      align-self: center;
      margin-right: 20px;
    }
    .van-count-down{
      line-height: 117px;
      color: rgb(255, 255, 255);
      font-size: 30px;
    }
  }
}
</style>
