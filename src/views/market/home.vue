<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2022-06-16 10:16:19
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-12-27 18:10:07
-->
<template>
  <div class="home">
    <NavHeight bgc="#fff" />
    <Top message="点滴商城" :is-right="true" :is-left="false" />
    <Pt v-if="true" />

    <GoodsList />
  </div>
</template>

<script>
import Top from './components/Tops.vue'
import Pt from './modules/Home/pt.vue'
import GoodsList from './modules/Home/GoodsList.vue'
export default {
  components: {
    Top, Pt, GoodsList
  },
  data() {
    return {

    }
  },
  created() {

  },
  mounted() {
    this.$store.state.mall.creatForm = { delieveryType: 1 }
  },
  methods: {

  }
}
</script>

<style scoped lang="scss">
.padingbottom{
  width: 100%;
  height: 250px;
}
</style>
