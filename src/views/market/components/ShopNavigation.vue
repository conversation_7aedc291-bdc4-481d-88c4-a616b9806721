<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2022-06-22 10:08:52
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-06-24 11:25:29
-->
<template>
  <div class="navigs">
    <div class="mendianlog">
      <img
        src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/market/market/home/<USER>"
        alt=""
      >
    </div>
    <div class="d_md">
      <div class="d_md_left">
        <div v-if="item.distance>0" class="title">
          {{ item.locationName }}
          <span
            v-if="item.distance > 0.5"
            class="shop_name"
            style="margin-left: 10px"
          >{{ item.distance.toFixed(2) }}km（距您）</span>
          <span
            v-else
            class="shop_name"
            style="margin-left: 10px"
          >{{ item.distance.toFixed(2) * 1000 }}m（距您）</span>
        </div>
        <div v-else class="title">
          {{ item.locationName }}
        </div>
        <div class="address">{{ item.province+item.city+item.district+item.selectAddress + item.address }}</div>
      </div>
      <div v-if="!isAmap" class="d_md_right" @click="goMarket(item)">
        <img
          src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/market/order/dh.png"
          alt=""
        >
      </div>
      <div
        v-else
        v-clipboard:copy="item.address"
        v-clipboard:success="onCopy"
        class="d_md_right"
      >
        <img
          src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/market/order/dh.png"
          alt=""
        >
      </div>
    </div>

    <div v-if="item.photo!=''" class="shopPhoto">
      <img :src="item.photo" alt="">
    </div>

    <!-- 导航 -->
    <van-popup v-model="showPop" position="bottom" round>
      <div class="mapList">
        <div @click="openMap(1)">百度导航</div>
        <div @click="openMap(2)">高德导航</div>
        <div @click="showPop = false">取消</div>
      </div>
    </van-popup>
  </div>
</template>

<script>
export default {
  props: {
    item: {
      type: Object,
      default: function() {
        return {}
      }
    }
  },
  data() {
    return {
      showPop: false,
      mapData: {},
      isAmap: false
    }
  },
  mounted() {
    // 判断是系统环境
    var u = navigator.userAgent
    var isiOS = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/) // ios终端
    if (isiOS) {
      this.isAmap = true
    }
  },
  methods: {
    onCopy(e) {
      this.$toast({
        duration: 5000, // 持续展示 toast
        forbidClick: false,
        message: '复制成功,请打开地图应用,粘贴店铺地址进行导航'
      })
    },
    // 打开地图
    openMap(val) {
      let self = this
      if (val == 1) {
        var urlBaiduMap = `baidumap://map/marker?location=${this.mapData.bd_lat},${this.mapData.bd_lng}&title=${this.mapData.marketName}&content=${this.mapData.marketName}&src=Hello%20uni-app`
        AlipayJSBridge.call(
          'IsAvailable',
          {
            packageName: 'com.baidu.BaiduMap'
          },
          function(result) {
            if (result.available == true) {
              window.location.href = urlBaiduMap
            } else {
              self.$toast('未安装百度地图')
            }
          }
        )
      } else {
        var urlAmap = `androidamap://viewMap?sourceApplication=${this.mapData.marketName}&poiname=${this.mapData.marketName}&lat=${this.mapData.gd_lat}&lon=${this.mapData.gd_lng}&dev=0`

        var iosAmap = `iosamap://viewMap?sourceApplication=${this.mapData.marketName}&poiname=${this.mapData.marketName}&lat=${this.mapData.gd_lat}&lon=${this.mapData.gd_lng}&dev=0`
        var u = navigator.userAgent
        var isiOS = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/) // ios终端
        if (!isiOS) {
          AlipayJSBridge.call(
            'IsAvailable',
            {
              packageName: 'com.autonavi.minimap'
            },
            function(result) {
              if (result.available == true) {
                window.location.href = urlAmap
              } else {
                self.$toast('未安装高德地图')
              }
            }
          )
        } else {
          window.location.href = iosAmap
        }
      }
    },
    // 导航店铺
    goMarket(data) {
      // 高德转百度坐标
      function bd_encrypt(gg_lng, gg_lat) {
        var X_PI = (Math.PI * 3000.0) / 180.0
        var x = gg_lng
        var y = gg_lat
        var z = Math.sqrt(x * x + y * y) + 0.00002 * Math.sin(y * X_PI)
        var theta = Math.atan2(y, x) + 0.000003 * Math.cos(x * X_PI)
        var bd_lng = z * Math.cos(theta) + 0.0065
        var bd_lat = z * Math.sin(theta) + 0.006
        return {
          bd_lat: bd_lat,
          bd_lng: bd_lng
        }
      }
      let zuobiao = bd_encrypt(data.longitude, data.latitude)

      this.mapData = {
        bd_lng: zuobiao.bd_lng,
        bd_lat: zuobiao.bd_lat,
        gd_lng: data.longitude,
        gd_lat: data.latitude,
        marketName: data.locationName
      }
      this.showPop = true
    }
  }
}
</script>

<style scoped lang="scss">
.navigs {
  .mendianlog {
    text-align: center;
    margin-top: 110px;
    img {
      width: 135px;
      height: 135px;
    }
  }
  .d_md {
    width: 642px;
    height: 138px;
    display: flex;
    justify-content: space-between;
    line-height: 50px;
    text-align: left;

    margin: 0 auto;
    margin-top: 20px;
    margin-bottom: 30px;
    .d_md_left {
      .title {
        color: #333333;
        font-size: 35px;
        font-family: PingFangSC-Medium;
      }
      .shop_name {
        font-size: 30px;
        font-family: PingFangSC;
        color: #ffa300;
      }
      .address {
        color: #999999;
        font-size: 30px;
      }
    }
    .d_md_right {
      img {
        width: 40px;
        height: 80px;
      }
    }
  }
  .shopPhoto {
    text-align: center;
    img {
      width: 642px;
      height: 642px;
      border-radius: 24px;
    }
  }

  .mapList {
    div {
      height: 150px;
      line-height: 150px;
      text-align: center;
      font-size: 28px;
    }

    div:not(:last-child) {
      border-bottom: 1px solid #eeeeee;
      font-weight: bold;
    }
  }
}
</style>
