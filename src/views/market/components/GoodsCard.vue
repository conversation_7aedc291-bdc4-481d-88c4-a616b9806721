<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2022-04-28 11:27:07
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-06-22 09:27:44
-->
<template>
  <div class="cards">
    <div class="card_box">
      <div class="card_top">
        <div class="card_img">
          <img :src="item.pictureUrl" alt="">
          <div class="t_peo">{{ item.pintuanUnitAmount }}人团</div>
        </div>
        <div class="card_info">
          <div v-if="item.tagPictureUrlList!=null&&item.tagPictureUrlList.length>0" class="card_title_tag">
            <img v-for="(tagig,index) in item.tagPictureUrlList" :key="index+'tag'" :src="tagig" alt="">
          </div>
          <div class="card_title">{{ item.goodsName }}</div>
          <div class="card_tag">
            <div v-if="isdistribution(item) == 1||isdistribution(item) == 3" class="card_tag_bg">同城配送</div>
            <div v-if="isdistribution(item) == 2||isdistribution(item) == 3" class="card_tag_bg3">
              <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/market/market/home/<USER>" alt="">
            </div>
            <!-- <div v-if="item.freightFee === 0">免运费</div> -->
            <div class="zgzj">
              <div>
                {{ sumPrice(item.originPrice,item.pintuanPrice) }}
              </div>
            </div>
          </div>
          <div class="card_price">
            <div class="card_price_msg">拼团价</div>
            <div class="card_price_icon">￥</div>
            <div class="card_price_num">{{ item.pintuanPrice }}</div>
            <div class="card_orprice">￥{{ item.originPrice }}</div>
          </div>

        </div>
      </div>
      <div v-if="item.groupUserHeadPictureUrlList.length>0" class="card_bottom">
        <div class="card_bottom_left">
          <div class="card_people_list">
            <img v-for="(list,index) in item.groupUserHeadPictureUrlList" :key="index" class="portrait" :style="index>0?'margin-left:-'+7+'px;':''" :src="list!==''&&list!==null?list:'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/my/anio.png'" alt="">
          </div>
          <div class="card_people_num">
            {{ item.pintuanUserAmount }}人已拼
          </div>
        </div>
        <div class="card_bottom_right">
          去拼单
          <van-icon class="righticon" name="arrow" />
        </div>

      </div>
      <div v-else class="card_bottom">
        <div class="card_bottom_left">
          <div class="card_people_list">
            <img class="portrait" :src="'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/my/anio.png'" alt="">
          </div>
          <div class="card_people_num">
            0人已拼
          </div>
        </div>

        <div class="card_bottom_right">
          去拼单
          <van-icon class="righticon" name="arrow" />
        </div>
      </div>
    </div>

    <div v-if="item.activityStatus === 3" :class="item.goodsName.length>16?'cards_end1':'cards_end'">
      <div v-if="item.endReasonCode === 3">已售罄</div>
      <div v-else>拼团结束</div>
    </div>
  </div>
</template>

<script>
import Maths from '@/utils/math.js'
export default {
  props: {
    item: {
      type: Object,
      default: function() {
        return {}
      }
    }
  },
  data() {
    return {

    }
  },
  created() {

  },
  methods: {
    sumPrice(num1, num2) {
      let num = new Maths(num1, num2).minus()
      num = new Maths(num, 8).sum()
      return num
    },
    isdistribution(item) {
      if (item.distributionInfoList.length == 1) {
        return item.distributionInfoList[0].distributionType
      }
      if (item.distributionInfoList.length == 2) {
        return 3
      }
    }
  }
}
</script>

<style scoped lang="scss">
    .cards {
        width: 730px;
        min-height: 340px;
        background: #ffffff;
        border-radius: 15px;
        margin:  0 auto;
        margin-top: 20px;
        overflow: hidden;
        .card_box{
            padding: 32px;
        }
        .card_top{
            min-height: 220px;
            display: flex;
            .card_img{
                width: 220px;
                height: 220px;
                background-color: #eee;
                border-radius: 10px;
                position: relative;
                img{
                    width: 100%;
                    height: 100%;
                    border-radius: 10px;
                }
                .t_peo{
                  width: 100px;
                  height: 35px;
                  text-align: center;
                  line-height: 35px;
                  color: #fff;
                  position: absolute;
                  top: 0;
                  left: 0;
                  font-size: 25px;
                  background-color: #D09D87;
                  border-radius: 3px;
                }
            }
            .card_info{
                width: 420px;
                min-height: 220px;
                margin-left: 20px;
                .card_title_tag{
                  height: 40px;
                  display: flex;
                  img{
                    max-width: 92px;
                    height: 30px;
                    float: left;
                    margin-right: 13px;
                  }
                }
                .card_title{
                    // width: 420px;
                    min-height: 42px;
                    font-size: 32px;
                    font-family: PingFangSC-Medium;
                    font-weight: 500;
                    color: #222222;
                    // margin-top: 5px;
                    word-wrap: break-word;
                }
                .card_tag{
                    height: 38px;
                    line-height: 38px;
                    margin-top: 11px;
                    font-size: 26px;
                    color: #666666;
                    display: flex;
                    position: relative;
                    .zgzj{
                      width: 300px;
                      height: 106px;
                      position: absolute;
                      top: 0px;
                      right: -50px;
                      background-image: url(https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/home/<USER>/activity/zgzj.png);
                      background-size: 100% 100%;
                      img{
                        width: 300px;
                        height: 106px;
                      }
                      div{
                        text-align: center;
                        margin-top: 36px;
                        font-size: 26px;
                        color: #fff;
                      }
                    }

                    .card_tag_bg{
                      font-family: PingFangSC-Medium;
                      width: 120px;
                      height: 38px;
                      background: #fff5c1;
                      border-radius: 4px;
                      text-align: center;
                      line-height: 38px;
                      color: #FFA300;
                      margin-right: 10px;
                    }
                    .card_tag_bg3{
                      img{
                        width: 108px;
                        height: 38px;
                      }
                    }
                    .card_tag_bg2{
                      font-family: PingFangSC-Medium;
                      width: 120px;
                      height: 38px;
                      background: #fff5c1;
                      border-radius: 4px;
                      text-align: center;
                      line-height: 38px;
                      color: #FFA300;
                      margin-right: 10px;
                    }
                }
                .card_people{
                    margin-top: 10px;
                    font-size: 29px;
                    color: #666666;
                }
                .card_price{
                    margin-top: 56px;
                    height: 50px;
                    line-height: 50px;
                    font-size: 28px;
                    display: flex;
                    .card_price_msg{
                        color: #333;
                    }
                    .card_price_icon{
                        font-size: 23px;
                        color: #ff301e;
                        margin-top: 3px;
                        margin-left: 7px;
                        font-family: PingFangSC-Medium;
                    }
                    .card_price_num{
                        font-size: 38px;
                        font-family: PingFangSC-Medium;
                        font-weight: 500;
                        color: #ff301e;
                        margin-top: -1px;
                    }
                    // .card_orprice{
                    //   font-size: 22px;
                    //   margin-left: 10px;
                    //   margin-top: -3px;
                    // }
                    .card_orprice{
                        color: #999999;
                        font-size: 22px;
                        margin-top: 3px;
                        text-decoration: line-through;
                        margin-left: 5px;
                    }
                }
            }
        }
        .card_bottom{
            margin-top: 26px;
            margin-left: 5px;
            display: flex;
            justify-content: space-between;
            .card_bottom_left{
              display: flex;
            }
            .card_bottom_right{
              width: 210px;
              height: 74px;
              line-height: 74px;
              font-size: 38px;
              color: #fff;
              text-align: center;
              background-color: #e02e24;
              border-radius: 7px;
              .righticon{
                position: relative;
                top: 5px;
              }
            }
            .card_people_list{
              display: flex;
              margin-top: 19px;
              .portrait{
                width: 40px;
                height: 40px;
                display: block;
                border-radius: 50%;
                position: relative;
              }
            }
            .card_people_num{
              margin-top: 20px;
              height: 38px;
              line-height: 42px;
              font-size: 31px;
              color: #333;
              margin-left: 16px;
            }
        }
        .cards_end{
          width: 100%;
          min-height: 410px;
          background-color: rgba(0,0,0,.5);
          border-radius: 15px;
          position: relative;
          margin-top: -410px;
          overflow: hidden;
          font-family: PingFangSC-Medium;
          div{
              width: 220px;
              min-height: 220px;
              text-align: center;
              line-height: 220px;
              background-color: #EEEEEE;
              margin: 0 auto;
              border-radius: 50%;
              font-size: 35px;
              margin-top: 13%;
          }
        }
        .cards_end1{
          width: 100%;
          min-height: 420px;
          background-color: rgba(0,0,0,.5);
          border-radius: 15px;
          position: relative;
          margin-top: -460px;
          overflow: hidden;
          font-family: PingFangSC-Medium;
          div{
              width: 220px;
              min-height: 220px;
              text-align: center;
              line-height: 220px;
              background-color: #EEEEEE;
              margin: 0 auto;
              border-radius: 50%;
              font-size: 35px;
              margin-top: 17%;
          }
        }
    }
</style>
