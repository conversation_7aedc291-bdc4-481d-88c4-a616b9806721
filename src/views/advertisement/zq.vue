<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2021-06-17 10:28:17
 * @LastEditors: 孙立政
 * @LastEditTime: 2021-09-18 13:56:04
-->
<template>
  <div class="home">
    <div class="back">
      <van-icon
        name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/common/arrowlbft.png"
        size="19"
        @click="goBack"
      />
    </div>
    <div class="top" />

    <div class="body">
      <div class="center">
        <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/index/actieve/zq/center.png" alt="">
      </div>

      <div v-for="(item,index) in list" :key="index" class="goodsCard">
        <div class="left" @click="goCard(item)">
          <img :src="item.cover + '?x-oss-process=image/resize,w_700/format,jpg/quality,q_100'" alt="">
        </div>
        <div class="right" @click="goCard(item)">
          <div class="title">{{ item.goodsName }}</div>
          <div class="brief">{{ item.description }}</div>
          <div class="price">
            <span class="price_1">￥</span>
            <span class="price_2">{{ item.price }}</span>
            <span v-if="item.oriPrice!=0" class="price_3">原价：￥{{ item.oriPrice }}</span>
          </div>
          <div>
            <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/shoppingMall/actieve/btn.png" alt="">
          </div>
        </div>
      </div>
    </div>
    <div style="height:30px" />
    <Loading :show="loadingShow" />
  </div>
</template>

<script>
import {
  getZq
} from '@/api/takeout'
import Loading from '@/components/Loading/index'
import { addData } from '@/utils/upLog.js'
export default {
  components: {
    Loading
  },
  data() {
    return {
      list: [],
      loadingShow: true
    }
  },
  created() {
    this.getList()
  },
  mounted() {
    this.$store.state.cart.cartData[0].goodsList = []
    this.$store.state.market.marketData.remark = ''
    addData(63)
  },
  methods: {
    // 获取商品
    getList() {
      getZq().then((res) => {
        this.loadingShow = false
        this.list = res.data
      })
    },
    goBack() {
      this.$router.push('/index')
    },
    goCard(data) {
      this.$store.state.cart.cartData[0].goodsList = []
      this.$store.state.market.marketData.remark = ''
      let cart = this.$store.state.cart.cartData[0].goodsList
      let cartList = {}
      cartList.skuId = data.skuList[0].skuId
      cartList.goodsId = data.id
      cartList.attrs = data.attrs
      cartList.price = data.price
      cartList.agentId = data.agentId
      cartList.cover = data.skuList[0].pic
      cartList.difference = data.skuList[0].skuName
      cartList.stock = data.skuList[0].actualStocks
      cartList.goodsName = data.goodsName
      cartList.oriPrice = data.oriPrice
      cartList.packPrice = data.skuList[0].packPrice
      cartList.marketId = data.marketId
      cart.push(cartList)
      this.$router.push(`/shop?id=${data.marketId}&goodsId=` + data.id)
    }
  }
}
</script>

<style scoped lang="scss">
    .home {
        background-image: url(https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/index/actieve/zq/zqactivitybj.png);
        background-size: 100%;
        background-repeat: no-repeat;
        background-color: #FBDCAD;
        .back{
          position: fixed;
          top: 50px;
          left: 25px;
        }
        .top{
            width: 100%;
            height: 854px;
            img{
                width: 100%;
                height: 854px;
            }
        }
        .body {
            width: 100%;
            overflow: hidden;
            margin-top: -300px;
            .center{
                width: 540px;
                height: 88px;
                text-align: center;
                margin: 0 auto;
                img{
                    width: 100%;
                    height: 100%;
                }
                margin-bottom: 25px;
                margin-top: 32px;
            }
            .goodsCard{
                width: 710px;
                min-height: 332px;
                background-image: url(https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/index/actieve/zq/goodsbj.png);
                background-size: 100% 100%;
                margin: 0 auto;
                display: flex;
                justify-content: space-between;
                margin-bottom: 10px;
                .left{
                    width: 50%;
                    width: 346px;
                    // height: 308px;
                    img{
                        width: 346px;
                        height: 308px;
                        border-radius: 21px;
                        margin-top: 38px;
                        margin-left: 18px;
                    }
                }
                .right{
                    width: 50%;
                    font-family: PingFangSC;
                    div{
                        margin-left: 24px;
                    }
                    img{
                        width: 198px;
                        height: 52px;
                        margin-top: 15px;
                    }
                    .title{
                      height: 100px;
                      font-size: 34px;
                      font-weight: 600;
                      color: #222222;
                      margin-top: 42px;
                    }
                    .brief{
                        height: 80px;
                        font-size: 26px;
                        font-weight: 400;
                        margin-top: 5px;
                        color: #333333;
                    }
                    .price{
                        margin-top: 13px;
                        .price_1{
                            font-size: 24px;
                            color: #ff0000;
                            font-weight: 400;
                        }
                        .price_2{
                            font-size: 56px;
                            color: #ff0000;
                            font-weight: 700;
                        }
                        .price_3{
                            font-size: 24px;
                            color: #333333;
                            font-weight: 400;
                            text-decoration: line-through;
                            margin-left: 30px;
                        }
                    }
                }
            }
        }
    }
</style>
