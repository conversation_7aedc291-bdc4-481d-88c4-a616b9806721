<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2021-07-07 11:29:51
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-06-10 17:50:42
-->
<template>
  <div class="home">
    <div class="back">
      <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/advertisement/back.png" alt="" @click="goBack">
    </div>

    <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/advertisement/20240520/1.jpg" alt="">

  </div>
</template>

<script>
export default {
  components: {},
  data() {
    return {}
  },
  created() {

  },
  mounted() {

  },
  methods: {
    goBack() {
      this.$router.go(-1)
    }
  }
}
</script>

<style scoped lang="scss">
    .home {
      width: 100%;
        // .top {
        //     background: linear-gradient(45deg, #fff 0%, #fff 100%);
        //     ::v-deep .van-nav-bar {
        //         background: none;
        //     }
        // }

        img {
            width: 100%;
            float: left;
        }
        .back{
          width: 100%;
          position: fixed;
          top: 65px;
          left: 38px;
          img{
            width: 66px;
            height: 66px;
          }
        }
    }
</style>
