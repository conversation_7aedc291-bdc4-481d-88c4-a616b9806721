<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2021-07-07 11:29:51
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-12-30 10:04:25
-->
<template>
  <div class="home">
    <div class="back">
      <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/advertisement/back.png" alt="" @click="goBack">
    </div>
    <div class="top">
      <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/active/nhj/1.jpg" alt="">
      <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/active/nhj/4.jpg" alt="" @click="goTo(101)">
      <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/active/nhj/3.jpg" alt="">
    </div>

    <div class="goods">
      <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/active/nhj/g1.jpg" alt="" @click="goTo(102)">
      <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/active/nhj/g2.jpg" alt="" @click="goTo(103)">
    </div>
    <div class="goods">
      <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/active/nhj/g3.jpg" alt="" @click="goTo(105)">
      <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/active/nhj/g4.jpg" alt="" @click="goTo(91)">
    </div>
    <div class="goods">
      <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/active/nhj/g5.jpg" alt="" @click="goTo(92)">
      <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/active/nhj/g6.jpg" alt="" @click="goTo(93)">
    </div>
    <div class="goods">
      <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/active/nhj/g7.jpg" alt="" @click="goTo(94)">
      <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/active/nhj/g8.jpg" alt="" @click="goTo(95)">
    </div>
    <div class="goods">
      <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/active/nhj/g9.jpg" alt="" @click="goTo(96)">
      <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/active/nhj/g10.jpg" alt="" @click="goTo(97)">
    </div>
    <div class="goods">
      <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/active/nhj/g11.jpg" alt="" @click="goTo(98)">
      <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/active/nhj/g12.jpg" alt="" @click="goTo(99)">
    </div>
    <div class="goods">
      <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/active/nhj/g13.jpg" alt="" @click="goTo(100)">
      <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/active/nhj/g19.jpg" alt="" @click="goTo(104)">
    </div>
    <div class="goods">
      <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/active/nhj/g15.jpg" alt="" @click="goTo(106)">
      <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/active/nhj/g16.jpg" alt="" @click="goTo(107)">
    </div>
    <div class="goods">
      <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/active/nhj/g17.jpg" alt="" @click="goTo(108)">
      <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/active/nhj/g18.jpg" alt="">
    </div>

    <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/active/nhj/11.jpg" alt="">

  </div>
</template>

<script>
export default {
  components: {},
  data() {
    return {}
  },
  created() {

  },
  mounted() {

  },
  methods: {
    goTo(val) {
      this.$router.push({
        name: 'MarketDetails',
        query: {
          id: val
        }
      })
    },
    goBack() {
      this.$router.go(-1)
    }
  }
}
</script>

<style scoped lang="scss">
    .home {
      width: 100%;

        img {
            width: 100%;
            display: block;
        }
        .back{
          width: 100%;
          position: fixed;
          top: 65px;
          left: 38px;
          img{
            width: 66px;
            height: 66px;
          }
        }
        .goods{
          display: flex;
          img {
              width: 50%;
          }
        }
    }
</style>
