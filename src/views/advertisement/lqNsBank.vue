<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2021-07-07 11:29:51
 * @LastEditors: 孙立政
 * @LastEditTime: 2021-07-08 10:45:51
-->
<template>
  <div class="home">
    <NavHeight bgc="#fff" />
    <Top />

    <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/index/lq/nsh.png" alt="">

  </div>
</template>

<script>
import Top from './components/top.vue'
export default {
  components: {
    Top
  },
  data() {
    return {}
  },
  created() {

  },
  mounted() {

  },
  methods: {
    goback() {
      this.$router.go(-1)
    }
  }
}
</script>

<style scoped lang="scss">
    .home {
        // .top {
        //     background: linear-gradient(45deg, #fff 0%, #fff 100%);
        //     ::v-deep .van-nav-bar {
        //         background: none;
        //     }
        // }
        img {
            width: 100%;
        }
    }
</style>
