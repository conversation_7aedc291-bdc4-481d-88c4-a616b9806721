<template>
  <div class="content">
    <NavHeight bgc="#fff" />
    <div class="statusTop">
      <van-nav-bar title="" left-text="" left-arrow>
        <template #title>
          <div v-if="id == 1">食堂</div>
          <div v-if="id == 2">菜市场</div>
          <div v-if="id == 3">美食</div>
          <div v-if="id == 'hotel'">酒店</div>
          <div v-if="id == 6">美宿</div>
          <div v-if="id == 7">美景</div>
          <div v-if="id == 8">乡村</div>
          <div v-if="id == 9">乡食</div>
          <div v-if="id == 66">搜索</div>
        </template>
        <template #left>
          <van-icon
            name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/common/arrowlwft.png"
            size="18"
            @click="goback"
          />
        </template>
      </van-nav-bar>
    </div>
    <div style="height: 46px" />

    <div v-if="id == 2" class="banner">
      <div class="image">
        <img :src="src">
      </div>
    </div>

    <van-list
      v-model="loading"
      :finished="finished"
      finished-text="没有更多了"
      :immediate-check="false"
      @load="onLoad"
    >
      <div class="tag-content">
        <!-- 判断是否为空 -->
        <div>
          <div
            v-for="item in itemData"
            :key="item.id"
            class="list"
            @click="goShop(item)"
          >
            <div class="img">
              <img :src="item.cover" border-radius="7" class="image">
              <div
                v-if="
                  item.discount != null &&
                    item.discount != 1 &&
                    item.discount != 0
                "
                class="huodong"
              >
                <span class="hdspan">
                  {{ (item.discount * 10).toFixed(2) }}折
                </span>
              </div>
            </div>
            <div class="data">
              <!-- 注意判断是否为酒店 -->
              <div v-if="id != 'hotel'" class="title">
                {{ item.marketName }}
              </div>
              <div v-else class="title">
                <span>
                  {{ item.marketName | ellipsis(8) }}
                </span>
                <span
                  style="
                    float: right;
                    color: #ff6634;
                    font-size: 20px;
                  "
                >
                  营业时间：{{ item.openingHours }}~{{ item.closingHours }}
                </span>
              </div>

              <div class="date">
                <span v-if="id != 'hotel'" style="color: #ff6634">
                  营业时间：{{ item.openingHours }}~{{ item.closingHours }}
                </span>
              </div>

              <div class="address">
                <span v-if="id != 'hotel'" style="float: left">
                  {{ item.address | ellipsis(15) }}
                </span>
                <span v-else style="float: left">
                  月销 {{ item.evaluation }}
                </span>
                <span style="float: right">
                  {{ item.distance.toFixed(2) }}km
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </van-list>
  </div>
</template>

<script>
import { List } from '@/api/rest'
export default {
  data() {
    return {
      id: '',
      pages: 1,
      regionid: '',
      src:
        'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/user/classify/banner1.png',
      loading: false,
      finished: false,
      itemData: [],
      loadingImg:
        'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/user/index/lazy.png',
      ifnull: true,
      elsenull: false
    }
  },
  created() {
    this.id = this.$route.query.id
    this.regionid = this.$route.query.regionid
  },
  mounted() {
    this.getItem()
  },
  methods: {
    onLoad() {
      this.getItem()
    },
    goback() {
      this.$router.go(-1)
    },
    getItem() {
      // 获取店铺列表
      let self = this
      let data = {
        pages: self.pages++,
        marketName: this.$route.query.keyword,
        lat1: self.$store.state.location.latitude,
        lng1: self.$store.state.location.longitude,
        regionId: self.regionid == 2 ? 2 : self.$store.getters.getRegionId,
        type: self.id != 66 ? self.id : ''
      }
      List(data)
        .then(function(res) {
          if (res.status == 200) {
            self.loading = false
            self.itemData.push(...res.data.list)
            self.total = res.data.total
            if (res.data.list.length == 0) {
              self.finished = true
            }
          }
        })
    },
    goShop(item) {
      // 跳转店铺
      this.$router.push({
        name: 'Shop',
        query: {
          id: item.id
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.content::before {
  // 利用伪元素设置整个页面的背景色
  content: " ";
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: -100;
  min-height: 100%;
  background-color: #fff;
}

.content {
  background-color: #fff;
  .statusTop {
    width: 100%;
    position: fixed;
    // top: 0;
    z-index: 1;
    background-color: #fff;
  }

  ::v-deep .van-nav-bar__title {
    font-size:34px;
    color: #000010;
  }

  ::v-deep .van-nav-bar {
    background: none;
  }

  ::v-deep .van-nav-bar__right {
    font-size: 30px;
  }

  .banner {
    width: 93%;
    margin: 0 auto;
    margin-top: 18px;

    .image {
      width: 100%;
      height: 280px;
    }
  }
}

.tag-content {
  width: 93%;
  margin: 0 auto;
  margin-top: 48px;

  .list {
    height: 150px;
    margin-top: 45px;
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;

    .img {
      width: 20%;
      height: 106px;
      .image {
        width: 114px;
        height: 106px;
        border-radius:10px;
      }

      .huodong {
        width: 75px;
        height: 36px;
        background-image: url(https://shenghuofw.oss-cn-beijing.aliyuncs.com/3607f4b05c1e49b098b088e14c953ebe.png);
        background-size: 100% 100%;
        position: relative;
        top: -106px;
        right:30px;
        text-align: center;
        font-size: 20px;
        line-height: 36px;
        color: #ffffff;
        float: right;
      }
    }

    .data {
      width: 80%;
      height: 150px;
      border-bottom: 2px solid #eeeeee;

      .title {
        font-size: 32px;
        font-weight: bold;
      }

      .date {
        font-size: 20px;
        margin-top:10px;
        margin-bottom: 12px;
      }

      .address {
        font-size: 20px;
        color: rgba(0, 0, 0, 0.5);
      }
    }
  }
  .item {
    padding: 24px 0;
    font-size: 28px;
  }
}
</style>
