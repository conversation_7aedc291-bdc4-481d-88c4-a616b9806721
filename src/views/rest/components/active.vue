<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-06-05 14:08:30
 * @LastEditors: zhaoyuxin
 * @LastEditTime: 2021-06-05 14:28:31
-->
<template>
  <div class="content">
    <div class="nav">
      <span>商户活动</span>
      <span>本土特色 甄选商家</span>
    </div>
    <!-- 图文列表 -->
    <div class="img" @click="goActive"><img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/user/rest/dahushan.jpg" mode=""></div>
  </div>
</template>

<script>
export default {
  data() {
    return {
    }
  },
  mounted() {
  },
  methods: {
    goActive() {
      this.$router.push({
        path: '/rest/travelList',
        query: {
          id: 2
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
img {
	width: 100%;
	height: 100%;
}
.content {
	width: 95%;
	margin: 0 auto;

	.nav {
		height: 60px;
		line-height: 60px;
		border-left: 4px solid #2ac845;
		margin-top: 40px;
		margin-bottom: 20px;

		span:nth-child(1) {
			display: inline-block;
			margin-left: 10px;
			margin-right: 10px;
			font-size: 40px;
			font-weight: bold;
		}

		span:nth-child(2) {
			font-size: 32px;
			color: #929292;
		}
	}
	.img {
		width: 710px;
		height:165px;
	}
}
</style>
