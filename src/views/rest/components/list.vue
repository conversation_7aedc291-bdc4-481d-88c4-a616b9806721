<template>
  <div class="list">
    <div v-if="wdata != ''" class="nav">
      <!-- <div class="nav"> -->
      <span>资讯</span>
      <span>疗休养政策</span>
    </div>
    <van-skeleton title avatar :row="3" :loading="loading">
      <div
        v-for="(item, index) in wdata"
        :key="index"
        class="l_nr"
        @click="godescription(item)"
      >
        <div class="img"><img :src="item.cover" mode=""></div>
        <div>
          <div class="articleName">{{ item.articleName }}</div>
          <div
            class="description"
            style="word-wrap: break-word; word-break: normal;font-size:16px"
            v-html="item.description"
          />
          <div class="time" v-text="item.updateTime" />
        </div>
      </div>
    </van-skeleton>
    <div
      style="width: 100%;text-align: center;color: #999;height: 50px;line-height: 50px;font-size:16px"
    >
      -到底啦-
    </div>
  </div>
</template>

<script>
import { findClassDetial } from '@/api/rest'
export default {
  components: {},
  data() {
    return {
      wdata: '',
      loading: true
    }
  },
  created() {
    this.reloadData()
  },
  mounted() {
    let self = this
    self.loading = true
    findClassDetial(1)
      .then(function(res) {
        if (res.status == 200) {
          self.wdata = res.data
          self.loading = false
        } else {
          self.loading = false
        }
      })
  },
  methods: {
    reloadData() {
      setTimeout(() => {
        this.loading = false
      }, 3000)
    },
    godescription(item) {
      this.$router.push({
        path: '/rest/travelDetails',
        query: {
          id: item.id
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped="scoped">
img {
  width: 100%;
  height: 100%;
}
.list {
  width: 95%;
  margin: 0 auto;

  .nav {
    height:60px;
    line-height:60px;
    border-left:.4px solid #2ac845;
    margin-top:40px;
    margin-bottom: 20px;

    span:nth-child(1) {
      font-size:40px;
      margin-left: 10px;
      font-weight: bold;
    }

    span:nth-child(2) {
      color: #929292;
      margin-left: 10px;
      font-size: 32px;
    }
  }

  .l_nr {
    width: 100%;
    overflow-x: hidden;
    display: flex;
    height:200px;
    margin-bottom:20px;
    .img {
      width: 300px;
      height: 200px;
    }
    .descmsg {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    div:nth-child(2) {
      position: relative;
      padding: 10px;

      .articleName {
        overflow: hidden;
        font-size: 40px;
        text-overflow: ellipsis;
        white-space: pre-wrap;
      }
      .description {
        color: #929292;
      }
      .time {
        position: absolute;
        font-size: 28px;
        left: 10px;
        bottom: 0;
      }
    }
  }
}
</style>
