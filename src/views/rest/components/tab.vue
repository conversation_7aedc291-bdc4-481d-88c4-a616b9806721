<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: zhaoyu<PERSON>
 * @Date: 2021-06-05 14:08:30
 * @LastEditors: zhaoyuxin
 * @LastEditTime: 2021-06-05 14:26:16
-->
<template>
  <div class="content">
    <div class="tab" @click="gomarket(6)">
      <div class="img">
        <img
          src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/user/rest/tab1.png"
          mode=""
        >
      </div>
      <span>美宿</span>
    </div>
    <div class="tab" @click="gomarket(7)">
      <div class="img">
        <img
          src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/user/rest/tab2.png"
          mode=""
        >
      </div>
      <span>美景</span>
    </div>
    <div class="tab" @click="gomarket(8)">
      <div class="img">
        <img
          src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/user/rest/tab3.png"
          mode=""
        >
      </div>
      <span>乡村</span>
    </div>
    <div class="tab" @click="gomarket(9)">
      <div class="img">
        <img
          src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/user/rest/tab4.png"
          mode=""
        >
      </div>
      <span>乡食</span>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {}
  },
  methods: {
    gomarket(type) {
      this.$router.push({
        path: '/rest/tagly',
        query: {
          id: type,
          regionid: 2
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
img {
  width: 100%;
  height: 100%;
}
.content::before {
  // 利用伪元素设置整个页面的背景色
  content: " ";
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: -100;
  min-height: 100%;
  background-color: #fff;
}
.content {
  margin: 30px auto 0;
  display: flex;
  justify-content: space-around;
  flex-wrap: wrap;
  margin-top: 30rpx;
  .tab {
    width: 140px;
    height: 220px;
    text-align: center;
    float: left;
    font-size: 36px;
    .img {
      width: 140px;
      height: 120px;
    }
  }
}
</style>
