<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <PERSON>haoyu<PERSON>
 * @Date: 2021-06-05 14:08:30
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-03-30 15:13:04
-->
<template>
  <div class="content">
    <div class="statusTop">
      <van-nav-bar title="" left-text="" left-arrow>
        <template #title>
          <div>{{ message }}</div>
        </template>
        <template #left>
          <van-icon name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/common/arrowlbft.png" size="18" @click="goback" />
        </template>
      </van-nav-bar>
    </div>
    <div style="height: 46px;" />
    <!-- 搜索框 -->
    <Search />
  </div>
</template>

<script>
import Search from './search'
export default {
  components: { Search },
  props: {
    message: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
    }
  },
  mounted() {
  },
  methods: {
    goback() {
      this.$router.go(-1)
    }
  }
}
</script>

<style lang="scss" scoped>
	.content {
    background-color: #638014;
    .search{
      margin-top: -3px;
    }
		.statusTop{
			width: 100%;
			position: fixed;
			// top: 0;
			z-index: 1;
		}

		::v-deep .van-nav-bar__title {
			font-size: 34px;
			color: #fff;
		}

		::v-deep .van-nav-bar {
			background: none;
		}

		::v-deep .van-nav-bar__right {
			font-size: 30px;
		}

	}
</style>
