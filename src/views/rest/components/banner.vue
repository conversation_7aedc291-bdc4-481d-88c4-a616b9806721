<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-06-05 14:08:30
 * @LastEditors: zhaoyuxin
 * @LastEditTime: 2021-06-05 14:25:15
-->
<template>
  <div class="content">
    <van-swipe class="my-swipe" :autoplay="3000" indicator-color="#fff" touchable>
      <van-swipe-item v-for="(image, index) in list" :key="index">
        <img :src="image.image+'?x-oss-process=image/resize,w_700/format,jpg/quality,q_75'">
      </van-swipe-item>
    </van-swipe>
  </div>
</template>

<script>
export default {
  data() {
    return {
      list: [
        {
          image: 'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/user/rest/banner.png',
          title: '蒹葭苍苍'
        }
      ]
    }
  }
}
</script>

<style lang="scss" scoped>
	.content {
		img {
			width: 100%;
		}
	}
</style>
