<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-07-09 10:41:18
 * @LastEditors: 孙立政
 * @LastEditTime: 2021-07-13 16:45:35
-->
<template>
  <div class="home">
    <Top />
    <Classify v-if="false" />
    <Advertise v-if="false" />
    <Tab />
    <Featured v-if="false" />
  </div>
</template>

<script>
import Top from './components/top.vue'
import Classify from './components/classify.vue'
import Advertise from './components/advertise.vue'
import Tab from './components/tab.vue'
import Featured from './components/featured.vue'
export default {
  components: {
    Top,
    Classify,
    Advertise,
    Tab,
    Featured
  },
  data() {
    return {

    }
  },
  created() {
  },
  mounted() {
    // 回到顶部
    document.body.scrollTop = 0
    document.documentElement.scrollTop = 0
  },
  beforeRouteLeave(to, from, next) {
    if (to.path == '/rest/longquan/agency' || to.path == '/rest/longquan/list' || to.path == '/rest/longquan/agencyDetail') {
      from.meta.keepAlive = true
    } else {
      this.removeKeepAliveCache()
    }
    next()
  },
  methods: {
    removeKeepAliveCache() {
      if (this.$vnode && this.$vnode.data.keepAlive && this.$vnode.parent) {
        const tag = this.$vnode.tag
        let caches = this.$vnode.parent.componentInstance.cache
        let keys = this.$vnode.parent.componentInstance.keys
        for (let [key, cache] of Object.entries(caches)) {
          if (cache.tag === tag) {
            if (keys.length > 0 && keys.includes(key)) {
              keys.splice(keys.indexOf(key), 1)
            }
            delete caches[key]
          }
        }
      }
      this.$destroy()
    }
  }
}
</script>

<style scoped lang="scss">
</style>
