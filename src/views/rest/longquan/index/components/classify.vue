<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-07-09 10:50:24
 * @LastEditors: 孙立政
 * @LastEditTime: 2021-07-13 19:17:56
-->
<template>
  <div class="home">
    <div class="classify">
      <div v-for="(item,index) in temp" :key="index" class="calssify-item" @click="goList(item)">
        <img :src="item.img" alt="" class="classify-item-img">
        <div class="classify-item-name">{{ item.name }}</div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      temp: [
        {
          name: '亲子游',
          img: 'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/rest/lq/icon1.png'
        },
        {
          name: '红色教育',
          img: 'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/rest/lq/icon4.png'
        },
        {
          name: '民族风情',
          img: 'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/rest/lq/icon5.png'
        },
        {
          name: '大自然',
          img: 'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/rest/lq/icon2.png'
        },
        {
          name: '网红景点',
          img: 'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/rest/lq/icon3.png'
        }

      ]
    }
  },
  methods: {
    goList(item) {
      this.$router.push({
        name: 'AgencyList',
        query: {
          attrTitles: item.name
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
    .home {
        .classify {
            display: flex;
            justify-content: space-between;
            flex-wrap: wrap;
            width: 710px;
            background: #ffffff;
            border-radius: 24px;
            box-shadow: 0px 6px 26px 0px rgba(0,0,0,0.04);
            margin:-145px auto 24px;
            padding: 28px 24px;
            .calssify-item {
                flex-shrink: 0;
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                width: 20%;
                height: 132px;
                .classify-item-img {
                    width: 62px;
                    height: 62px;
                    margin-bottom: 10px;
                }
                .classify-item-name {
                    font-size: 24px;
                    text-align: center;
                    font-family: PingFangSC
                }
            }
        }
    }
</style>
