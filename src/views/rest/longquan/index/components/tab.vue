<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-07-09 10:28:46
 * @LastEditors: 孙立政
 * @LastEditTime: 2021-07-19 16:40:13
-->
<template>
  <div class="home">
    <div class="tab-content">
      <div v-for="(item, index) in list" :key="index" class="tab-item" @click="onTab(item)">
        <div class="tab-item-imgList">
          <img
            v-if="active != 0"
            :src="item.pictures.split(',')[0] + '?x-oss-process=image/resize,w_700/format,jpg/quality,q_85'"
            alt=""
            class="tab-item-img"
          >
          <img
            v-else
            :src="item.cover + '?x-oss-process=image/resize,w_700/format,jpg/quality,q_85'"
            alt=""
            class="tab-item-img1"
          >
        </div>
        <div class="tab-item-label">{{ active != 0 ? item.goodsName : item.marketName | ellipsis(8) }}</div>
        <div v-if="active != 0" class="tab-item-price">
          <div class="tab-item-price-icon">￥</div>
          <div class="tab-item-price-num">{{ item.showPrice }}</div>
          <div class="tab-item-price-pop">起</div>
        </div>
      </div>
    </div>

    <Loading :show="loadingShow" />
  </div>
</template>

<script>
import { getRandomMarket, findFirstGoodsInMarketId } from '@/api/rest/lxy'
import Loading from '@/components/Loading/index'
export default {
  components: {
    Loading
  },
  data() {
    return {
      active: 0,
      list: [],
      loadingShow: true,
      marketIds: []
    }
  },
  watch: {
    'active': function(val) {
      if (val == 0) {
        this.getMarketData()
      } else {
        this.getGoodsData()
      }
    }
  },
  created() {
    this.getMarketData()
  },
  methods: {
    // 获取店铺
    getMarketData() {
      let data = {
        regionId: 3,
        size: 30
      }
      getRandomMarket(data).then((res) => {
        this.loadingShow = false
        this.list = res.data
        res.data.map(item => {
          this.marketIds.push(item.id)
        })
      })
    },
    // 获取商品
    getGoodsData() {
      let data = {
        marketIds: this.marketIds
      }
      findFirstGoodsInMarketId(data).then((res) => {
        this.list = res.data
      })
    },
    onTab(item) {
      if (this.active == 0) {
        this.$router.push({
          name: 'Agency',
          query: {
            marketId: item.id,
            from: this.$router.history.current.name
          }
        })
      } else {
        this.$router.push({
          name: 'AgencyDetail',
          query: {
            id: item.id,
            marketId: item.marketId,
            from: this.$router.history.current.name
          }
        })
      }
    }
  }
}
</script>

<style scoped lang="scss">
.home {
  padding: 20px;
  margin-top: -150px;
  border-radius: 10px;

  .tab {
    height: 98px;
    background-color: #fff;

    ::v-deep .van-tab {
      font-size: 32px;
      font-family: PingFangSC-Medium;
    }

    ::v-deep .van-tabs__line {
      bottom: 45px;
      background-color: #169D1B;
    }
  }

  .tab-content {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    gap: 10px;
    width: 100%;
    padding: 20px;
    background-color: #fff;
    border-radius: 10px;

    .tab-item {
      flex-shrink: 0;
      width: 310px;
      height: 228px;
      margin-bottom: 31px;
      margin-right: 20px;

      .tab-item-imgList {
        width: 100%;
        height: 182px;
      }

      .tab-item-img {
        width: 100%;
        height: 192px;
        border-radius: 15px;
        object-fit: cover;
      }

      .tab-item-img1 {
        width: 100%;
        height: 192px;
        border-radius: 15px;
      }

      .tab-item-label {
        font-size: 28px;
        font-family: PingFangSC-Medium;
        font-weight: 500;
        color: #333333;
        line-height: 29px;
        padding-left: 5px;
        margin-top: 20px;
      }

      .tab-item-price {
        font-family: PingFangSC;
        color: #ff301e;
        display: flex;
        margin-top: 7px;

        .tab-item-price-icon {
          font-size: 24px;
          margin-top: 8px;
        }

        .tab-item-price-num {
          font-size: 34px;
          font-family: PingFangSC-Medium;
        }

        .tab-item-price-pop {
          font-size: 24px;
          color: #999999;
          margin-top: 8px;
          margin-left: 3px;
        }
      }
    }
  }
}
</style>
