<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-07-09 14:16:03
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-09-03 15:56:20
-->
<template>
  <div class="home">
    <div class="header">精选</div>
    <div class="featured">
      <div v-for="(item,index) in list" :key="index" class="featured-item" @click="goGoods(item)">
        <van-image
          v-if="item.pictures"
          fit="cover"
          :src="item.pictures.split(',')[2] + '?x-oss-process=image/resize,w_700/format,jpg/quality,q_85'"
        />
        <van-image
          v-else
          fit="cover"
          :src="item.cover + '?x-oss-process=image/resize,w_700/format,jpg/quality,q_85'"
        />
        <div class="featured-item-name">{{ item.goodsName }}</div>
      </div>
    </div>
    <div class="bottom" />
  </div>
</template>

<script>
import { getRandomGoods } from '@/api/rest/lxy'
export default {
  data() {
    return {
      list: []
    }
  },
  created() {
    this.getGoodsData()
  },
  methods: {
    // 获取商品
    getGoodsData() {
      let data = {
        regionId: 3,
        size: 10
      }
      getRandomGoods(data).then((res) => {
        this.list = res.data
      })
    },
    goGoods(item) {
      this.$router.push({
        name: 'AgencyDetail',
        query: {
          id: item.id,
          marketId: item.marketId,
          from: this.$router.history.current.name
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
    .home {
        background-color: #fff;
        .header {
            font-size: 36px;
            font-family:PingFangSC-Medium;
            font-weight: 500;
            color: #333333;
            line-height: 50px;
            margin-left: 20px;
            margin-bottom: 24px;
        }
        .featured {
            width: 95%;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            flex-wrap: wrap;
            .featured-item {
                flex-shrink: 0;
                width: 348px;
                height: 422px;
                margin-bottom: 15px;

                ::v-deep .van-image__img{
                  border-radius: 13px;
                  width: 100%;
                  height: 346px;
                }
                .featured-item-img {
                    width: 348px;
                    height: 346px;
                    vertical-align:middle;
                    margin-bottom: 10px;
                    border-radius: 15px;
                }
                .featured-item-name {
                    font-size: 30px;
                    font-family:PingFangSC-Medium;
                    font-weight: 500;
                    text-align: left;
                    color: #333333;
                    line-height: 42px;
                    margin-top: 5px;
                    overflow: hidden;
                    text-overflow:ellipsis;
                    white-space: nowrap;
                }
            }
        }
        .bottom{
          width: 100%;
          height: 100px;
        }
    }
</style>
