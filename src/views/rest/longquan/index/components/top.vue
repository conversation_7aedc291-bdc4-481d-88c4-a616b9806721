<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-07-07 16:28:35
 * @LastEditors: 孙立政
 * @LastEditTime: 2023-04-20 14:17:51
-->
<template>
  <div class="home" :style="styleVar">
    <div class="warp">
      <div class="top">
        <div class="left" @click="goBack()">
          <van-icon
            name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/components/back.png"
            size="20"
          />
        </div>
        <div v-if="false" class="center">
          <van-search
            v-model="keyword"
            label="龙泉站"
            :autofocus="false"
            placeholder="去哪儿"
            @search="goSearch"
            @clear="goClear"
          />
        </div>
        <div class="subscribe" @click="goToSub">
          <van-icon
            name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/tabBar/order.png"
            size="20"
            class="subscribeIconfont"
          />
          记录
        </div>
      </div>
      <div v-if="height>60" class="top fixed">
        <div class="left" @click="goBack()">
          <van-icon
            name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/rest/arrow_black.png"
            size="20"
          />
        </div>
        <div class="center">
          <van-search
            v-model="keyword"
            label="龙泉站"
            left-icon=""
            placeholder="去哪儿"
            :autofocus="false"
            @search="goSearch"
            @clear="goClear"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      keyword: '',
      height: 0
    }
  },
  computed: {
    styleVar() {
      return {
        '---nav-height': this.$store.getters.getStatusHeight + 'px'
      }
    }
  },
  created() {

  },
  mounted() {
    window.addEventListener('scroll', this.handleScrollx, true)
  },
  destroyed() {
    window.removeEventListener('scroll', this.handleScrollx, true)
  },
  methods: {
    goBack() {
      this.$router.push('/index')
    },
    handleScrollx() {
      let top = document.documentElement.scrollTop || document.body.scrollTop || window.pageYOffset
      this.height = top
    },
    goSearch() {
      this.$router.push({
        name: 'AgencyList',
        query: {
          attrTitles: '全部',
          keyword: this.keyword
        }
      })
    },
    goClear() {},
    remove() {
      window.removeEventListener('scroll', this.handleScrollx, true)
    },
    goToSub() {
      this.$router.push({
        name: 'SubscribeIndex'
      })
    }
  }
}
</script>

  <style scoped lang="scss">
      .home {
          .warp {
            //   position: relative;
              width:100%;
              height: 600px;
              background-image: url(https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/rest/rest-index-bg.png);
              background-size: 100%;
            //   z-index: -1;
              .top {
                  position: fixed;
                  left: 0;
                  right: 0;
                  z-index: 2;
                  display: flex;
                  align-items: center;
                  justify-content: space-between;
                  width: 100%;
                  // height: calc(84px + var(---nav-height));
                  padding-top:calc(20px + var(---nav-height));
                  padding-bottom: 20px;
                  .left {
                      margin-left: 34px;
                      margin-right: 18px;
                      height: 40px;
                      line-height: 33px;
                  }

                  .center {
                      ::v-deep .van-search {
                          padding: 0;
                          width: 560px;
                          border-radius: 34px;
                          overflow: hidden;
                      }

                      ::v-deep .van-field__left-icon {
                          color: #a8a8a8;
                          img{
                            width: 24px;
                            height: 24px;
                            position: relative;
                            top: -4px;
                          }
                      }

                      ::v-deep .van-cell__value--alone {
                          font-size: 26px;
                          color: #A8A8A8;
                          font-family: PingFangSC;
                      }
                      ::v-deep .van-search__label {
                          display: flex;
                          align-items: center;
                          color: #169D1B;
                          font-size: 26px;
                          font-family: PingFangSC;
                          margin-right: 12px;
                          padding-right: 16px;
                          border-right: 1px solid #e8e8e8;
                      }
                      ::v-deep .van-search__label::before {
                          content: '';
                          display: inline-block;
                          width: 26px;
                          height: 26px;
                          background-image: url(https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/rest/rest_location.png);
                          background-size: 100% 100%;
                          margin-right: 6px;
                      }
                      input {
                          border: none;
                      }
                  }

                  .subscribe{
                    display: flex;
                    align-items: center;
                    width: 150px;
                    font-size: 35px;
                    margin-left: 20px;
                    margin-right: 20px;
                    color: #4a4848;
                    font-weight: bold;
                    .subscribeIconfont{
                      margin-right: 10px;
                    }
                  }
              }
              .fixed {
                background-color: #fff;
              }
          }
      }
  </style>
