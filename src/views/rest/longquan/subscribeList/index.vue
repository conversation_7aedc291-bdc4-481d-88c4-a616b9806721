<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2021-07-08 14:08:22
 * @LastEditors: 孙立政
 * @LastEditTime: 2021-07-19 14:13:08
-->
<template>
  <div class="list">
    <div ref="element" class="fixeds" :style="styleVar">
      <div class="top">
        <div class="left" @click="goBack()">
          <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/common/leftblack.png" alt="">
        </div>
        <div class="center" />
      </div>
    </div>
    <div :style="'height:'+height+'px'" />

    <!-- 卡片 -->
    <div class="cards">
      <van-cell v-for="item in list" :key="item.orderNo" :title="item.marketName" :value="'￥'+item.actualPay" :label="item.orderTime" />
    </div>
    <van-empty v-show="list.length === 0" description="暂无数据" />

    <div style="height:50px;background-color: #fff;" />

    <Loading :show="loadingShow" />
  </div>
</template>

<script>
import { bookOrderList } from '@/api/rest/lxy'
import Loading from '@/components/Loading/index'
export default {
  components: {
    Loading
  },
  data() {
    return {
      loadingShow: true,
      list: [],
      height: 0
    }
  },
  computed: {
    styleVar() {
      return {
        '---nav-height': this.$store.getters.getStatusHeight + 'px'
      }
    }
  },
  mounted() {
    this.height = this.$refs.element.offsetHeight
    this.getList()
  },
  methods: {
    getList() {
      bookOrderList().then(res => {
        this.loadingShow = false
        this.list = res.data
      })
    },
    goBack() {
      this.$router.push('/rest/longquan/index')
    }
  }
}
</script>

<style scoped lang="scss">
    .list {
      width: 100%;
      height: 100%;
      background-color: #fff;
      ::v-deep .van-list__finished-text{
        background-color: #fff;
      }
      .fixeds{
        width: 100%;
        padding-top:calc(20px + var(---nav-height));
        position: fixed;
        top: 0;
        z-index: 2;
        background-color: #fff;
      }
      .top {
          display: flex;
          width: 100%;
          height: 80px;
          background-color: #fff;

          .left {
            width: 40px;
            height: 100px;
            margin-left: 34px;
            margin-right: 18px;
            img{
              width: 40px;
              height: 40px;
              float: left;
              margin-top: 11px;
            }
          }

          .center {
            ::v-deep .van-search {
              padding: 0;
              width: 538px;
              height: 64px;
              line-height: 64px;
              border-radius: 34px;
              overflow: hidden;
            }
            ::v-deep .van-field__left-icon {
              color: #8e8e93;
            }
            ::v-deep .van-cell__value--alone {
              color: #c4c4c7;
            }
            input {
              border: none;
            }
          }
      }
      .cards{
        padding: 5px;
      }
    }
</style>
