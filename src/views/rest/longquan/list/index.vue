<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2021-07-08 14:08:22
 * @LastEditors: 孙立政
 * @LastEditTime: 2021-07-19 14:13:08
-->
<template>
  <div class="list">
    <div ref="element" class="fixeds" :style="styleVar">
      <div class="top">
        <div class="left" @click="goBack()">
          <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/common/leftblack.png" alt="">
        </div>
        <div class="center">
          <van-search
            v-model="query.search.goodsName"
            placeholder=""
            :autofocus="false"
            @search="goSearch"
          />
        </div>
      </div>
      <!-- 类目tab -->
      <van-tabs v-model="active" animated color="#169D1B" @click="onTab">
        <van-tab v-for="(item,index) in classList" :key="index" :title="item" :name="item" />
      </van-tabs>
      <!-- 类目tab -->
      <van-tabs v-model="attrIndex" swipeable color="rgba(0,0,0,0)" title-active-color="#169D1B" title-inactive-color="#454545">
        <van-tab title="综合排序" />
        <van-tab title="价格降序" />
        <van-tab title="价格升序" />
      </van-tabs>
    </div>
    <div :style="'height:'+height+'px'" />

    <!-- 卡片 -->
    <div class="cards">
      <van-list v-model="loading" :finished="finished" finished-text="没有更多啦~" @load="onLoad">
        <Card v-for="(item,index) in goodsList" :key="index" :data="item" />
      </van-list>
    </div>

    <div style="height:50px;background-color: #fff;" />

  </div>
</template>

<script>
import Card from '../components/card.vue'
import { goodsPage } from '@/api/rest/lxy'
export default {
  components: {
    Card
  },
  data() {
    return {
      classList: ['全部', '亲子游', '红色教育', '民族风情', '大自然', '网红景点'],
      list: [],
      finished: false,
      loading: false,
      active: '',
      attrIndex: 0,
      query: {
        pageNum: 0,
        pageSize: 100,
        search: {
          goodsName: '',
          attrTitles: [],
          orderBy: 0,
          regionId: 3
        }
      },
      goodsList: [],
      height: 0
    }
  },
  computed: {
    styleVar() {
      return {
        '---nav-height': this.$store.getters.getStatusHeight + 'px'
      }
    }
  },
  watch: {
    'attrIndex': function(val) {
      this.query.search.orderBy = val
      this.query.pageNum = 1
      this.goodsList = []
      this.goodsPage()
    }
  },
  created() {
  },
  beforeRouteLeave(to, from, next) {
    if (to.path == '/rest/longquan/agencyDetail') {
      from.meta.keepAlive = true
    } else {
      this.removeKeepAliveCache()
    }
    next()
  },
  mounted() {
    this.height = this.$refs.element.offsetHeight
    this.active = this.$route.query.attrTitles
    this.query.search.attrTitles = [this.$route.query.attrTitles]
    if (this.$route.query.keyword) {
      this.query.search.goodsName = this.$route.query.keyword
      if (this.$route.query.attrTitles == '全部') {
        this.query.search.attrTitles = this.classList
      }
    }
  },
  methods: {
    // 获取店铺
    goodsPage() {
      this.$toast.loading({
        duration: 0,
        forbidClick: true,
        message: ''
      })
      goodsPage(this.query).then((res) => {
        this.finished = true
        this.$toast.clear()
        if (res.status == 200) {
          this.goodsList.push(...res.data.list)
          this.loading = false
        }
      })
    },
    // 切换tab
    onTab(name, titl) {
      this.query.pageNum = 1
      if (name == '全部') {
        this.query.search.attrTitles = this.classList
      } else {
        this.query.search.attrTitles = [name]
      }
      this.query.search.goodsName = ''
      this.goodsList = []
      this.goodsPage()
    },
    onLoad() {
      this.query.pageNum = this.query.pageNum + 1
      this.goodsPage()
    },
    // 搜索
    goSearch() {
      this.query.pageNum = 1
      this.query.search.attrTitles = this.classList
      this.active = '全部'
      this.goodsList = []
      this.goodsPage()
    },
    goBack() {
      this.$router.push('/rest/longquan/index')
    },
    removeKeepAliveCache() {
      if (this.$vnode && this.$vnode.data.keepAlive && this.$vnode.parent) {
        const tag = this.$vnode.tag
        let caches = this.$vnode.parent.componentInstance.cache
        let keys = this.$vnode.parent.componentInstance.keys
        for (let [key, cache] of Object.entries(caches)) {
          if (cache.tag === tag) {
            if (keys.length > 0 && keys.includes(key)) {
              keys.splice(keys.indexOf(key), 1)
            }
            delete caches[key]
          }
        }
      }
      this.$destroy()
    }
  }
}
</script>

<style scoped lang="scss">
    .list {
      width: 100%;
      height: 100%;
      background-color: #fff;
      ::v-deep .van-list__finished-text{
        background-color: #fff;
      }
      .fixeds{
        width: 100%;
        padding-top:calc(20px + var(---nav-height));
        position: fixed;
        top: 0;
        z-index: 2;
        background-color: #fff;
      }
      .top {
          display: flex;
          width: 100%;
          height: 80px;
          background-color: #fff;

          .left {
            width: 40px;
            height: 100px;
            margin-left: 34px;
            margin-right: 18px;
            img{
              width: 40px;
              height: 40px;
              float: left;
              margin-top: 11px;
            }
          }

          .center {
            ::v-deep .van-search {
              padding: 0;
              width: 538px;
              height: 64px;
              line-height: 64px;
              border-radius: 34px;
              overflow: hidden;
            }
            ::v-deep .van-field__left-icon {
              color: #8e8e93;
            }
            ::v-deep .van-cell__value--alone {
              color: #c4c4c7;
            }
            input {
              border: none;
            }
          }
      }
    }
</style>
