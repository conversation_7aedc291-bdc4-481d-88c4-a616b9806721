<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-07-07 17:49:38
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-09-03 17:13:58
-->
<template>
  <div class="home" :style="styleVar">
    <div class="detail">
      <img :src="row.cover+'?x-oss-process=image/resize,w_700/format,jpg/quality,q_100'" alt="" class="detail-bg">
      <div class="detail-nav">
        <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/rest/rest-arrow.png" alt="" @click="goBack">
        <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/rest/rest-share.png" alt="" @click="getShare">
      </div>
      <div class="detail-card">
        <div class="detail-card-price">
          <span class="small">¥</span>
          <span class="small fs50">{{ row.showPrice }}</span> 起
        </div>

        <div class="detail-card-name">{{ row.goodsName }}</div>
        <div class="detail-card-box">
          <div class="detail-card-feature">
            <div class="detail-card-label">特色</div>
            <div class="detail-card-tag">
              <div v-for="(item,index) in row.attrs" :key="index" class="tag">{{ item.title }}</div>
            </div>
          </div>
          <div class="detail-card-schedule">
            <div class="detail-card-label">预订</div>
            <div class="icon">
              <van-icon name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/rest/rest-mark.png" size="11" style="margin-right:2px" />有条件退
            </div>
            <div class="icon" @click="callPhone">
              <van-icon name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/rest/rest-phone.png" size="11" style="margin-right:2px" />预定电话
            </div>
          </div>
          <div class="detail-card-ensure">
            <div class="detail-card-label">保障</div>
            点滴7*24售后客服·行程透明·更改则赔付
          </div>
        </div>
        <div style="height:20px" />
      </div>
    </div>
    <div class="card-shop" @click="goShop">
      <div>
        <img class="card-shop-img" :src="shopData.pic + '?x-oss-process=image/resize,w_700/format,jpg/quality,q_85'">
        <div class="card-shop-center">
          <div class="card-shop-name">{{ shopData.marketName | ellipsis(13) }}</div>
          <div class="card-shop-desc">{{ shopData.announcement | ellipsis(20) }}</div>
        </div>
      </div>
      <div class="card-shop-enter">
        进店
        <van-icon name="arrow" color="#999" size="8" />
      </div>
    </div>

    <!-- 详情富文本 -->
    <div v-if="row.goodsDetail!=null" class="html" v-html="row.goodsDetail.goodsDetail" />
    <div class="tort">*图片来源网络，如有侵权请联系删除</div>

    <!-- 分享弹出 -->
    <van-popup v-model="onShare" position="bottom" :style="{ height: '30%' }">
      <div class="shareList">
        <div class="shareListIcon">
          <div class="shareListIconLetf" @click="getShare('wechat')">
            <div>
              <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/share/weixin.png" alt="">
            </div>
            <div>微信</div>
          </div>
          <div class="shareListIconRight" @click="getShare('wechattimeline')">
            <div>
              <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/share/pyq.png" alt="">
            </div>
            <div>朋友圈</div>
          </div>
        </div>
        <div class="close" @click="onShare = false">取消</div>
      </div>
    </van-popup>

    <Loading :show="loadingShow" />
  </div>
</template>

<script>
import { goodsInfo } from '@/api/rest/lxy'
import { getShopDataV2 } from '@/api/takeout'
import Loading from '@/components/Loading/index'
export default {
  components: {
    Loading
  },
  data() {
    return {
      onShare: false,
      form: {
        sharePlatform: '',
        title: '点滴',
        content: '点滴畅享生活',
        redirectUrl: '',
        contentType: 'url',
        url: '',
        imgUrl: ''
      },
      row: {
        goodsDetail: {
          goodsDetail: '店铺详情'
        }
      },
      shopData: {},
      goodsList: {},
      loadingShow: true
    }
  },
  computed: {
    styleVar() {
      return {
        '---nav-height': this.$store.getters.getStatusHeight + 'px'
      }
    }
  },
  created() {
    this.getData()
    this.getShopData()
  },
  mounted() {

  },
  methods: {
    // 获取商品详情
    getData() {
      goodsInfo(this.$route.query.id).then(res => {
        this.loadingShow = false
        if (res.status == 200) {
          this.row = res.data
        }
      })
    },
    // 店铺信息
    getShopData() {
      let data = {
        marketId: this.$route.query.marketId,
        longitude: this.$store.getters.getLocation.longitude,
        latitude: this.$store.getters.getLocation.latitude
      }
      getShopDataV2(data).then((res) => {
        this.shopData = res.data
      })
    },
    // 分享
    getShare(val) {
      this.form.sharePlatform = val
      // let url = `https://share.zjntwl.com/#/rest/longquan/agencyDetail?id=${this.$route.query.id}&marketId=${this.$route.query.marketId}&from=share`
      // this.form.url = url
      // this.form.title = this.row.goodsName
      // this.form.imgUrl = this.row.cover

      // AlipayJSBridge.call('Share', this.form, function(result) {
      //   console.log(result)
      // })

      let data = {
        text: this.row.goodsName,
        title: this.row.goodsName,
        url: `https://share.zjntwl.com/#/rest/longquan/agencyDetail?id=${this.$route.query.id}&marketId=${this.$route.query.marketId}&from=share`,
        image: this.row.cover
      }
      AlipayJSBridge.call('WatchShare', data, function(result) {
        console.log(result)
      })
    },
    // 跳转店铺
    goShop() {
      this.$router.push({
        name: 'Agency',
        query: {
          marketId: this.$route.query.marketId
        }
      })
    },
    goBack() {
      if (this.$route.query.from == 'AgencyList') {
        this.$router.go(-1)
      } else if (this.$route.query.from == 'AgencyIndex') {
        this.$router.go(-1)
      } else if (this.$route.query.from == 'trans') {
        this.$router.push({ name: 'AgencyIndex' })
      } else {
        this.$router.go(-1)
      }
    },
    callPhone() {
      AlipayJSBridge.call('CallPhone', {
        phoneNum: this.shopData.mobilePhone
      }, function(result) {})
    }
  }
}
</script>

<style scoped lang="scss">
    .home {
      margin-bottom: 16px;
      .html{
        margin-top: 16px;
        padding: 30px;
        background-color: #fff;
        font-family: PingFangSC;
        font-size: 26px;
        line-height: 39px;
        color: #454545;
        ::v-deep img{
          width: 100%;
          border-radius: 15px;
          margin-top: 15px;
          margin-bottom: 15px;
        }
      }
      .tort{
        padding: 30px;
        background-color: #fff;
        font-family: PingFangSC;
        font-size: 26px;
        margin-top: -30px;
      }
        .detail {
          margin-bottom: 16px;
            .detail-bg {
              position: relative;
              z-index: -1;
              width: 100%;
              height: 342px;
              vertical-align: middle;
              object-fit: cover;
            }
            .detail-nav {
                position: fixed;
                left: 28px;
                top: calc(20px + var(---nav-height));
                z-index: 1;
                padding-top:0;
                width: 694px;
                display: flex;
                justify-content: space-between;
                img{
                  width: 52px;
                  height: 52px;
                }
            }
            .detail-card {
              width: 750px;
              // height: 426px;
              background: #ffffff;
              border-radius: 24px 24px 0px 0px;
              margin-top: -24px;
              padding: 24px 20px 0;
              .detail-card-price {
                height: 70px;
                line-height: 70px;
                font-size: 22px;
                font-family: PingFangSC;
                color: #454545;
                margin-bottom: 10px;
              }
              .detail-card-box{
                width: 710px;
                height: 194px;
                background: #F9FAFA;
                border-radius: 16px;
              }
              .detail-card-name {
                font-size: 37px;
                font-family:PingFangSC-Medium;
                font-weight: 500;
                color: #333333;
                line-height: 41px;
                margin-bottom: 24px;
              }
              .detail-card-label {
                width: 84px;
                height: 33px;
                font-size: 24px;
                font-family: PingFangSC;
                color: #999999;
              }
              .detail-card-feature {
                display: flex;
                padding: 24px 0 0 18px;
                .detail-card-tag {
                  display: flex;
                  .tag {
                    display: flex;
                    align-items: center;
                    // width: 92px;
                    min-height: 34px;
                    padding:0 11px;
                    background: #ffe9d6;
                    border-radius: 5px;
                    font-size: 24px;
                    font-family: PingFangSC;
                    color: #ff7807;
                    line-height: 33px;
                    text-align: center;
                    margin-right: 16px;
                  }
                }
              }
              .detail-card-schedule {
                padding: 24px 0 0 18px;
                display: flex;
                align-items: center;
                font-size: 24px;
                font-family: PingFangSC;
                color: #333333;
                line-height: 33px;
                .icon {
                  display: flex;
                  align-items: center;
                  margin-right: 24px;
                }
              }
              .detail-card-ensure {
                display: flex;
                padding: 24px 0 0 18px;
                font-size: 24px;
                font-family:PingFangSC;
                color: #333333;
                line-height: 33px;
              }
            }
                .small {
                font-size: 22px;
                font-family: PingFangSC;
                color: #ff301e;
              }
              .fs50 {
                  font-size: 50px;
                  font-family: PingFangSC-Medium;
                  font-weight: 500;
              }
        }
        .card-shop {
          display: flex;
          align-items: center;
          justify-content: space-between;
          width: 100%;
          height: 140px;
          background: #ffffff;
          padding:30px 20px;
          >div {
            display: flex;
          }
          .card-shop-img {
            margin-right: 20px;
            width: 80px;
            height: 80px;
            border-radius: 13px;
          }
          .card-shop-center {
            width: 500px;
            .card-shop-name {
              height: 42px;
              font-size: 30px;
              font-family:PingFangSC-Medium;
              font-weight: 500;
              color: #333333;
              line-height: 42px;
              margin-bottom: 6px;
            }
            .card-shop-desc {
              font-size: 24px;
              font-family: PingFangSC;
              color: #999999;
              line-height: 33px;
            }
          }
          .card-shop-enter {
            display: flex;
            align-items: center;
            font-size: 22px;
            font-family: PingFangSC;
            color: #999999;
            line-height: 30px;
          }
        }
        .van-popup{
          background-color: rgba(0,0,0,0);
        }
        .shareList{
          width: 90%;
          height: 92%;
          background-color: #fff;
          margin: 0 auto;
          border-radius: 15px;
          position: relative;
          overflow: hidden;
          .shareListIcon{
            display: flex;
            font-size: 28px;
            text-align: center;
            width: 90%;
            margin: 0 auto;
            margin-top: 10%;
            img{
              width: 100px;
              height: 100px;
              margin-bottom: 10px;
            }
            .shareListIconLetf{
              width: 50%;
            }
            .shareListIconRight{
              width: 50%;
            }
          }
          .close{
            width: 100%;
            height: 150px;
            text-align: center;
            line-height: 150px;
            font-size: 35px;
            font-weight: PingFangSC-Medium;
            position: absolute;
            bottom: 0;
          }
        }
    }
</style>
