<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-07-07 15:06:14
 * @LastEditors: 孙立政
 * @LastEditTime: 2023-05-04 18:12:53
-->
<template>
  <div class="agency">
    <Top @getSearch="getSearch" />
    <div v-if="false" class="tab">
      <van-tabs v-model="active" swipeable title-active-color="#169D1B" title-inactive-color="#454545">
        <van-tab title="综合排序" />
        <van-tab title="价格降序" />
        <van-tab title="价格升序" />
      </van-tabs>
    </div>
    <div>
      <van-radio-group v-model="restRadio">
        <van-cell-group>
          <van-cell v-for="(item, index) in goodsList" :key="index" style="height: 80px;align-items: center;" class="" :title="item.goodsName+' ¥'+item.price" clickable @click="restRadio = String(item.id)">
            <template #right-icon>
              <van-radio :name="String(item.id)" />
            </template>
          </van-cell>
        </van-cell-group>
      </van-radio-group>
    </div>
    <!-- <Card v-for="(item, index) in goodsList" :key="index" :data="item" /> -->

    <div
      class="reservation"
      @click="onReservationShow"
    >
      确认下单
    </div>

    <van-popup v-model="reservationShow" round position="bottom" :style="{ height: '30%' }">
      <van-picker show-toolbar title="年份" :columns="columns" @confirm="onReservationConfirm" @cancel="reservationShow = false" />
    </van-popup>

    <van-dialog v-model="confirmReservationShow" title="确认" :show-confirm-button="false">
      <div class="eeservationTips">
        <div>金额：<span style="color: red;">￥{{ bookOrderIndex.amount }}</span></div>
        <div>单位：{{ bookOrderIndex.companyName }}</div>
      </div>
      <div class="yy_btn">
        <van-button class="btn" type="default" @click="confirmReservationShow = false">取 消</van-button>
        <van-button class="btn" type="primary" @click="confirmOrderPay">确 认</van-button>
      </div>
    </van-dialog>

    <!-- 数字键盘 -->
    <van-popup
      v-model="keyShow"
      round
      closeable
      :style="{
        width: '281px',
        height: '210px',
      }"
    >
      <div class="box">
        <div class="title">输入支付密码</div>
        <div style="text-align: center;margin-top: 11px;margin-bottom: 8px;font-size: 13px;color: #333340;">
          金额
        </div>
        <div style="text-align: center;font-size: 27px;margin-bottom: 20px;color: #000;">
          ¥{{ bookOrderIndex.amount }}
        </div>
        <van-password-input :value="password" info="" :length="6" :focused="showKeyboard" @focus="showKeyboard = true" />
      </div>
    </van-popup>

    <van-number-keyboard
      v-model="password"
      title="点滴安全键盘"
      :show="showKeyboard"
      z-index="9000"
      @blur="showKeyboard = false"
    />

  </div>
</template>

<script>
import Top from './components/top.vue'
// import Card from '../components/card.vue'
import { marketGoods, moneyList, bookOrderPay } from '@/api/rest/lxy'
import { version } from '@/config/settings'
import { query } from '@/utils/sign'
export default {
  components: {
    Top
    // Card
  },
  data() {
    return {
      query: {
        pageNum: 1,
        pageSize: 1000,
        search: {
          goodsName: '',
          orderBy: 0,
          marketId: this.$route.query.marketId
        }
      },
      active: 0,
      goodsList: [
        {
          id: '1',
          goodsName: '套餐A',
          price: 3000
        },
        {
          id: '2',
          goodsName: '套餐B',
          price: 1000
        },
        {
          id: '3',
          goodsName: '套餐C',
          price: 1500
        },
        {
          id: '4',
          goodsName: '套餐D',
          price: 1800
        },
        {
          id: '5',
          goodsName: '套餐E',
          price: 2000
        },
        {
          id: '6',
          goodsName: '套餐F',
          price: 2250
        }
      ],
      reservationShow: false,
      confirmReservationShow: false,
      columns: [],
      bookOrderIndex: {},
      keyShow: false,
      password: '',
      showKeyboard: false,
      restRadio: ''
    }
  },
  watch: {
    'active': function(val) {
      this.query.search.orderBy = val
      this.marketGoods()
    },
    password(val, old) {
      if (val.length >= 6) {
        this.$throttle(() => {
          this.bookOrderPay()
        }, 1500)
      }
    }
  },
  created() {
    // this.marketGoods()
    this.moneyList()
  },
  methods: {
    onReservationShow() {
      if (this.restRadio == '') {
        this.$toast('请选择商品')
        return
      }
      if (this.columns.length == 0) {
        this.$toast('疗休养账户余额不足，请向本单位核实疗养费是否已经发放')
        return
      }
      this.reservationShow = true
    },
    // 获取商品
    marketGoods() {
      marketGoods(this.query).then((res) => {
        this.goodsList = res.data.list
      })
    },
    getSearch(val) {
      this.query.search.goodsName = val
      this.marketGoods()
    },
    // 确认预约
    onReservationConfirm(val) {
      for (let i = 0; i < this.goodsList.length; i++) {
        if (this.goodsList[i].id == this.restRadio) {
          if (val.amount != this.goodsList[i].price) {
            this.$toast('请选择与年度金额一致的套餐价格')
            return
          }
        }
      }

      console.log(val.amount)

      this.confirmReservationShow = true
      this.reservationShow = false
      this.bookOrderIndex = val
    },
    // 预约订单查询用户年款记录列表
    moneyList() {
      moneyList({
        poolId: 10
      }).then((res) => {
        if (res.status == 200) {
          for (let i = 0; i < res.data.length; i++) {
            res.data[i].text = res.data[i].years + `年度(${res.data[i].amount}元)`
          }
          this.columns = res.data
        }
      })
    },
    confirmOrderPay() {
      this.showKeyboard = true
      this.keyShow = true
    },
    bookOrderPay() {
      console.log()
      let Base64 = require('js-base64').Base64
      let postQuery = {
        'marketId': this.$route.query.marketId,
        'amount': this.bookOrderIndex.amount,
        'moneyRecordId': this.bookOrderIndex.id,
        'paymentType': 1,
        'payPassword': Base64.encode(this.password),
        'terminalSysVer': version,
        'deviceId': localStorage.getItem('deviceId') ? localStorage.getItem('deviceId') : 'test',
        'longitude': this.$store.getters.getLocation.longitude,
        'latitude': this.$store.getters.getLocation.latitude
      }
      // 加签
      postQuery.sign = query(postQuery)

      bookOrderPay(postQuery).then((res) => {
        if (res.status == 200) {
          this.$toast.success('操作成功')
          this.confirmReservationShow = false
          this.showKeyboard = false
          this.keyShow = false
          this.moneyList()
        } else {
          this.password = ''
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
.agency {
  width: 100%;
  height: 100%;
  background-color: #fff;

  .tab {
    ::v-deep .van-tab {
      font-family: PingFangSC-Medium;
    }

    ::v-deep .van-tabs__line {
      background-color: #169D1B;
    }
  }

  .reservation {
    width: 500px;
    height: 100px;
    line-height: 100px;
    font-size: 40px;
    text-align: center;
    margin: 0 auto;
    margin-top: 80px;
    background-color: #2fba33;
    border-radius: 10px;
    color: #fff;
  }

  .eeservationTips {
    width: 90%;
    margin: 0 auto;
    margin-top: 50px;
    font-size: 35px;

    div {
      margin-bottom: 20px;
    }
  }

  .yy_btn {
    width: 93%;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    margin-top: 50px;
    margin-bottom: 25px;

    .btn {
      width: 280px;
      border-radius: 10px;
      font-size: 30px;
    }
  }

  .confirm {
    ::v-deep .van-button--block {
      width: 452px;
      height: 88px;
      font-family: PingFangSC;
      margin: 200px auto 0;
      font-size: 32px;
    }

    ::v-deep .van-button--round {
      border-radius: 12px;
      background: linear-gradient(90deg, #40d243, #1fc432);
      border: 1px solid #5ecc52;
    }
  }

  .box {
    .title {
      width: 100%;
      box-sizing: border-box;
      padding-left: 28px;
      height: 92px;
      line-height: 92px;
      font-size: 24px;
      color: #4c4c57;
      border-bottom: 1px solid #d5d5d5;
    }
  }
}

::v-deep .van-popup__close-icon {
  font-size: 30px;
}

::v-deep .van-key {
  color: #000;
}

::v-deep .van-popup__close-icon--top-right {
  top: 30px;
}

::v-deep[class*="van-hairline"]::after {
  border-color: #d5d5d5;
}

::v-deep .van-popup--center.van-popup--round {
  border-radius: 16px;
}

::v-deep .van-popup--center {
  top: 35%;
}

.popBox {
  position: relative;
  width: 561px;
  height: 32px;
  padding-top: 48px;
  box-sizing: border-box;

  .title {
    color: #7f7f87;
    font-size: 30px;
    padding-left: 42px;
  }

  ::v-deep .van-field__label {
    width: 100px;
    margin-right: 0;
  }
}

.popBtn {
  position: fixed;
  left: 0;
  bottom: 0px;
  display: flex;
  width: 561px;
  height: 97px;
  line-height: 97px;
  font-size: 32px;
  justify-content: space-between;
  border-top: 1px solid #cfcece;
  text-align: center;

  .cancel {
    width: 50%;
    border-right: 1px solid #cfcece;
    color: #999999;
  }

  .confirm {
    width: 50%;
    color: #6095f0;
  }
}
</style>
