<template>
  <div class="list">
    <NavHeight bgc="#fff" />
    <div class="statusTop">
      <van-nav-bar title="" left-text="" left-arrow>
        <template #title>
          <div v-if="id == 1">资讯</div>
          <div v-if="id == 2">商户活动</div>
        </template>
        <template #left>
          <van-icon
            name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/common/arrowlwft.png"
            size="18"
            @click="goback"
          />
        </template>
      </van-nav-bar>
    </div>
    <div style="height: 46px;" />
    <van-skeleton title avatar :row="3" :loading="loading">
      <div
        v-for="(item, index) in wdata"
        :key="index"
        class="l_msg"
        @click="godescription(item)"
      >
        <div class="img"><img :src="item.cover"></div>
        <div>
          <div class="articleName">{{ item.articleName }}</div>
          <div
            class="description"
            style="word-wrap: break-word; word-break: normal;"
            v-html="item.description"
          />
        </div>
      </div>
    </van-skeleton>
  </div>
</template>

<script>
import { findClassDetial } from '@/api/rest'
export default {
  filters: {
    ellipsis(value) {
      if (!value) return ''
      if (value.length > 53) {
        return value.slice(0, 40) + '....'
      }
      return value
    }
  },
  data() {
    return {
      loading: false,
      wdata: '',
      id: ''
    }
  },
  created() {
    this.id = this.$route.query.id
  },
  mounted() {
    let self = this
    findClassDetial(this.id).then(function(res) {
      self.wdata = res.data
    })
  },
  methods: {
    goback() {
      this.$router.go(-1)
    },
    godescription(item) {
      this.$router.push({
        path: '/rest/travelDetails',
        query: {
          id: item.id
        }
      })
    }
  }
}
</script>

<style scoped="scoped" lang="scss">
.list::before {
  // 利用伪元素设置整个页面的背景色
  content: " ";
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: -100;
  min-height: 100%;
  background-color: #fff;
}
.list {
  .l_msg {
    width: 710px;
    height:480px;
    box-shadow: 0 4px 24px 0 rgba(0, 0, 0, 0.2);
    margin: 0 20px 0;
    img {
      width: 710px;
      height: 300px;
    }
    div:nth-child(2) {
      .articleName {
        padding: 20px;
        font-weight: bold;
        font-size: 31px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      .description {
        width: 95%;
        font-size: 24px;
        color: #999999;
        margin-top: 10px;
        padding-left: 20px;
      }
    }
  }
}
.statusTop {
  width: 100%;
  position: fixed;
  // top: 0;
  z-index: 1;
}

::v-deep .van-nav-bar__title {
  font-size: 34px;
  color: #000010;
}

::v-deep .van-nav-bar {
  background: none;
}

::v-deep .van-nav-bar__right {
  font-size: 30px;
}
</style>
