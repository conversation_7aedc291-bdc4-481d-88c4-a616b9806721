<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <PERSON>haoyu<PERSON>
 * @Date: 2021-07-07 11:39:08
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-09-03 16:08:11
-->
<template>
  <div class="cardC">
    <div class="card">
      <div class="card-cell" @click="goDetial">
        <img v-if="data.pic" class="card-img" :src="data.pic.split(',')[1] + '?x-oss-process=image/resize,w_700/format,jpg/quality,q_85'">
        <img v-else class="card-img" :src="data.cover + '?x-oss-process=image/resize,w_700/format,jpg/quality,q_85'">
        <div class="card-content">
          <div class="card-content-name">{{ data.goodsName | ellipsis(26) }}</div>
          <div class="card-content-tag">
            <div v-for="(item,index) in tagList" :key="index" :class="'tag'+index">{{ item.title | ellipsis(4) }}</div>
          </div>
          <div class="card-content-price">
            <span class="small">¥</span>
            <span class="small fs42">{{ data.showPrice }}</span>起
          </div>
        </div>
      </div>
    </div>
    <div class="bottom" />
  </div>
</template>

<script>
export default {
  props: {
    data: {
      type: [Object, Array],
      default: function() {
        return {}
      }
    }
  },
  data() {
    return {
      tagList: []
    }
  },
  mounted() {
    this.restTag()
  },
  methods: {
    restTag() {
      if (this.data.attrs != null) {
        this.data.attrs.map(item => {
          if (item.type == 1) {
            this.tagList.push(item)
          }
        })
      }
    },
    goDetial() {
      if (this.$router.history.current.name == 'AgencyDetail') {
        this.$router.push({
          name: 'AgencyTransfer',
          query: {
            id: this.data.id,
            marketId: this.data.marketId
          }
        })
      } else {
        this.$router.push({
          name: 'AgencyDetail',
          query: {
            id: this.data.id,
            marketId: this.data.marketId,
            from: this.$router.history.current.name
          }
        })
      }
    }
  }
}
</script>

  <style scoped lang="scss">
      .cardC {
        .bottom{
              width: 93%;
              height: 2px;
              margin: 0 auto;
              border-bottom: 2px solid #F4F4F4;
            }
          .card {
            padding-top: 29px;
            background-color: #fff;

              .card-cell {
                position: relative;
                display: flex;
                height: 197px;
                margin: auto;
                background-color: #fff;
                padding: 0 20px;
                .card-img {
                  width: 208px;
                  height: 164px;
                  margin-right: 16px;
                  border-radius: 15px;
                  object-fit: cover;
                }
                .card-content {
                  width: 450px;
                    .card-content-name {
                        font-size: 30px;
                        font-family:PingFangSC-Medium;
                        font-weight: 500;
                        color: #333333;
                        margin-bottom: 15px;
                    }
                    .card-content-tag {
                        display: flex;
                        font-size: 22px;
                        .tag0 {
                            color: #fe5475;
                            border: 1px solid #fe5475;
                        }
                        .tag1 {
                            border: 1px solid #ff7807;
                            color: #ff7807;
                        }
                        .tag2 {
                            border: 1px solid #3488ff;
                            color: #3488ff;
                        }
                        .tag3 {
                            border: 1px solid #3488ff;
                            color: #3488ff;
                        }
                        .tag0,.tag1,.tag2,.tag3 {
                            font-size: 22px;
                            font-family: PingFangSC;
                            border-radius: 4px;
                            margin-right: 8px;
                            padding: 0 4px;
                        }
                    }
                    .card-content-price {
                        right: 20px;
                        text-align: right;
                        font-size: 22px;
                        font-family: PingFangSC;
                        color: #454545;
                    }
                }
              }
          }
          .small {
            font-size: 22px;
            font-family: PingFangSC;
            color: #ff301e;
          }
          .fs42 {
              font-size: 42px;
              font-family: PingFangSC-Medium;
              font-weight: 500;
          }
      }
  </style>
