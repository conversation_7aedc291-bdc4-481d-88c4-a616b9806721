<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-07-09 10:28:46
 * @LastEditors: 孙立政
 * @LastEditTime: 2021-07-19 16:40:13
-->
<template>
  <div class="home">
    <div class="tab">
      <van-tabs v-model="active" swipeable title-active-color="#169D1B" title-inactive-color="#454545" line-width="77px" line-height="2px">
        <van-tab title="推荐旅行社" />
        <van-tab title="打卡热门景点" />
      </van-tabs>
    </div>
    <div class="tab-content">
      <div v-for="(item,index) in list" :key="index" class="tab-item" @click="onTab(item)">
        <div class="tab-item-imgList">
          <img v-if="active!=0" :src="item.pictures.split(',')[0] + '?x-oss-process=image/resize,w_700/format,jpg/quality,q_85'" alt="" class="tab-item-img">
          <img v-else :src="item.cover + '?x-oss-process=image/resize,w_700/format,jpg/quality,q_85'" alt="" class="tab-item-img1">
        </div>
        <div class="tab-item-label">{{ active!=0?item.goodsName:item.marketName | ellipsis(8) }}</div>
        <div v-if="active!=0" class="tab-item-price">
          <div class="tab-item-price-icon">￥</div>
          <div class="tab-item-price-num">{{ item.showPrice }}</div>
          <div class="tab-item-price-pop">起</div>
        </div>
      </div>
    </div>

    <Loading :show="loadingShow" />
  </div>
</template>

<script>
import { getRandomMarket, findFirstGoodsInMarketId } from '@/api/rest/lxy'
import Loading from '@/components/Loading/index'
export default {
  components: {
    Loading
  },
  data() {
    return {
      active: 0,
      list: [],
      loadingShow: true,
      marketIds: []
    }
  },
  watch: {
    'active': function(val) {
      if (val == 0) {
        this.getMarketData()
      } else {
        this.getGoodsData()
      }
    }
  },
  created() {
    this.getMarketData()
  },
  methods: {
    // 获取店铺
    getMarketData() {
      let data = {
        regionId: 7,
        size: 6
      }
      getRandomMarket(data).then((res) => {
        this.loadingShow = false
        this.list = res.data
        res.data.map(item => {
          this.marketIds.push(item.id)
        })
      })
    },
    // 获取商品
    getGoodsData() {
      let data = {
        marketIds: this.marketIds
      }
      findFirstGoodsInMarketId(data).then((res) => {
        this.list = res.data
      })
    },
    onTab(item) {
      if (this.active == 0) {
        this.$router.push({
          name: 'AgencyQY',
          query: {
            marketId: item.id,
            from: this.$router.history.current.name
          }
        })
      } else {
        this.$router.push({
          name: 'AgencyDetailQY',
          query: {
            id: item.id,
            marketId: item.marketId,
            from: this.$router.history.current.name
          }
        })
      }
    }
  }
}
</script>

  <style scoped lang="scss">
      .home {
          .tab {
              height: 98px;
              background-color: #fff;
              ::v-deep .van-tab {
                  font-size: 32px;
                  font-family:PingFangSC-Medium;
              }
              ::v-deep .van-tabs__line {
                  bottom: 45px;
                  background-color: #169D1B;
              }
          }
          .tab-content {
              display: flex;
              flex-wrap: wrap;
              padding: 0 20px;
              background-color: #fff;
              padding-bottom: 22px;
              .tab-item {
                flex-shrink: 0;
                width: 230px;
                // height: 218px;
                margin-bottom: 21px;
                .tab-item-imgList{
                  width: 230px;
                  height: 162px;
                }
                .tab-item-img {
                    width: 230px;
                    height: 162px;
                    border-radius: 15px;
                    object-fit: cover;
                }
                .tab-item-img1 {
                    width: 230px;
                    height: 162px;
                    border-radius: 15px;
                }
                .tab-item-label {
                    font-size: 25px;
                    font-family:PingFangSC-Medium;
                    font-weight: 500;
                    color: #333333;
                    line-height: 29px;
                    padding-left: 5px;
                    margin-top: 16px;
                }
                .tab-item-price{
                  font-family: PingFangSC;
                  color: #ff301e;
                  display: flex;
                  margin-top: 7px;
                  .tab-item-price-icon{
                    font-size: 24px;
                    margin-top: 8px;
                  }
                  .tab-item-price-num{
                    font-size: 34px;
                    font-family: PingFangSC-Medium;
                  }
                  .tab-item-price-pop{
                    font-size: 24px;
                    color: #999999;
                    margin-top: 8px;
                    margin-left: 3px;
                  }
                }
              }
              .tab-item:not(:nth-of-type(3n+3)){
                  margin-right: 10px;
              }
          }
      }
  </style>
