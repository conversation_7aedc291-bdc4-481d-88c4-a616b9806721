<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-07-09 14:16:03
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-09-03 15:56:20
-->
<template>
  <div class="home">
    <div class="header">精选</div>
    <div class="featured">
      <div v-for="(item,index) in list" :key="index" class="featured-item" @click="goGoods(item)">
        <van-image
          fit="cover"
          :src="item.cover + '?x-oss-process=image/resize,w_700/format,jpg/quality,q_85'"
        />
        <div class="featured-item-name">{{ item.marketName }}</div>
      </div>
    </div>
    <div class="bottom" />
  </div>
</template>

<script>
import { getRandomGoods, getRandomMarket } from '@/api/rest/lxy'
export default {
  data() {
    return {
      list: [],
      marketIds: []
    }
  },
  created() {
    // this.getGoodsData()
    this.getMarketData()
  },
  methods: {
    // 获取店铺
    getMarketData() {
      let data = {
        regionId: 7,
        size: 100
      }
      getRandomMarket(data).then((res) => {
        this.loadingShow = false
        this.list = res.data
        res.data.map(item => {
          this.marketIds.push(item.id)
        })
      })
    },
    // 获取商品
    getGoodsData() {
      let data = {
        regionId: 3,
        size: 10
      }
      getRandomGoods(data).then((res) => {
        this.list = res.data
      })
    },
    goGoods(item) {
      this.$router.push({
        name: 'AgencyQY',
        query: {
          id: item.id,
          marketId: item.id,
          from: this.$router.history.current.name
        }
      })
      // this.$router.push({
      //   name: 'AgencyDetail',
      //   query: {
      //     id: item.id,
      //     marketId: item.marketId,
      //     from: this.$router.history.current.name
      //   }
      // })
    }
  }
}
</script>

<style scoped lang="scss">
    .home {
        padding: 10px;
        background-color: #fff;
        width: 710px;
        margin: 0 auto;
        border-radius: 16px;
        .header {
            font-size: 36px;
            font-family:PingFangSC-Medium;
            font-weight: 500;
            color: #333333;
            line-height: 50px;
            margin-left: 20px;
            margin-bottom: 24px;
            margin-top: 15px;
        }
        .featured {
            width: 95%;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            flex-wrap: wrap;
            .featured-item {
                flex-shrink: 0;
                width: 308px;
                height: 400px;
                margin-bottom: 15px;

                ::v-deep .van-image__img{
                  border-radius: 13px;
                  width: 100%;
                  height: 346px;
                }
                .featured-item-img {
                    width: 346px;
                    height: 346px;
                    vertical-align:middle;
                    margin-bottom: 10px;
                    border-radius: 15px;
                }
                .featured-item-name {
                    font-size: 30px;
                    font-family:PingFangSC-Medium;
                    font-weight: 500;
                    text-align: center;
                    color: #333333;
                    line-height: 42px;
                    margin-top: 5px;
                    overflow: hidden;
                    text-overflow:ellipsis;
                    white-space: nowrap;
                }
            }
        }
        .bottom{
          width: 100%;
          height: 100px;
        }
    }
</style>
