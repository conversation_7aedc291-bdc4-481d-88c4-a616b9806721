<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-07-09 10:55:47
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-09-03 15:01:50
-->
<template>
  <div class="home">
    <div class="advertise" @click="getActieve">
      <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/rest/lqrest.png" alt="">
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {

    }
  },
  created() {

  },
  mounted() {

  },
  methods: {
    getActieve() {
      this.$toast('敬请期待')
    }
  }
}
</script>

<style scoped lang="scss">
    .home {
        margin-bottom: 16px;
        .advertise {
            width: 710px;
            height: 201px;
            margin: auto;
            img {
                width: 100%;
                height: auto;
            }
        }
    }
</style>
