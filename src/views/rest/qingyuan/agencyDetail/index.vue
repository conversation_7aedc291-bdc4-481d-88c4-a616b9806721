<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-07-08 14:14:23
 * @LastEditors: 孙立政
 * @LastEditTime: 2021-07-12 16:14:00
-->
<template>
  <div class="home">
    <DetailCard />
    <div class="header">更多好玩</div>
    <Card v-for="(item,index) in goodsList" :key="index" :data="item" />
    <!-- <div class="esc" @click="goLink" /> -->
  </div>
</template>

<script>
import DetailCard from '../agencyDetail/components/detailCard.vue'
import Card from '../components/card.vue'
import { getRandomGoods } from '@/api/rest/lxy'
export default {
  components: {
    DetailCard,
    Card
  },
  data() {
    return {
      goodsList: []
    }
  },
  created() {
    this.getRandomGoods()
  },
  mounted() {

  },
  methods: {
    // 获取推荐店铺
    getRandomGoods() {
      let data = {
        regionId: 3,
        size: 3
      }
      getRandomGoods(data).then((res) => {
        this.goodsList = res.data
      })
    },
    goLink() {
      window.location.href = 'http://www.diandiandidi.top/app/index.html'
    }
  }
}
</script>

  <style scoped lang="scss">
      .home {
        .header {
          font-size: 34px;
          font-family:PingFangSC-Medium;
          font-weight: 500;
          color: #333333;
          padding: 39px 0 0 20px;
          background-color: #fff;
        }
        .esc{
          width: 100%;
          height: 100vh;
          position: fixed;
          top: 0;
          background-color: rgba(0,0,0,0);
          z-index: 100;
        }
      }
  </style>
