<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-07-07 15:06:14
 * @LastEditors: 孙立政
 * @LastEditTime: 2023-05-04 18:12:53
-->
<template>
  <div class="agency">
    <Top :shop-data="shopData" />
    <div class="payment">
      <div>
        <van-field v-model="priceVal" placeholder="请输入金额" input-align="right" style="width:330px;">
          <template #label>
            <div class="input_title">
              消费金额
            </div>
          </template>
        </van-field>
      </div>
    </div>

    <div class="people">
      <!-- <van-field
        v-for="(item, index) in people"
        :key="index"
        v-model="item.name"
        right-icon="cross"
        placeholder="请输入姓名"
        input-align="right"
        style="width: 350px;"
        @click-right-icon="delPeople(index)"
      >
        <template #label>
          <div class="input_title">
            消费人员{{ index + 1 }}
          </div>
        </template>
      </van-field> -->
      <van-field
        v-model="people.num"
        rows="2"
        autosize
        label="人员数量"
        type="number"
        placeholder="请输入消费人员数量"
      />
      <van-field
        v-model="people.names"
        rows="2"
        autosize
        label="消费人员"
        type="textarea"
        placeholder="请输入或粘贴消费人员信息"
        show-word-limit
        maxlength="999999"
      />
    </div>
    <div class="add_people" @click="addPeople">
      <van-icon name="add-o" />
      快捷粘贴
    </div>

    <div class="tips">
      <div class="tips_title">温馨提示</div>
      <div v-if="isShow38()" class="tips_content">
        <div>1. 输入"消费人员"姓名与姓名之间用逗号隔开。</div>
        <div>2. "消费人员"必须为本单位女性职工。</div>
        <div>3. "消费人员"名单必须与结算确认单名单保持一致。</div>
        <div>4. 确认消费金额、人员信息、人员数量无误后再进行支付。</div>
      </div>
      <div v-else class="tips_content">
        <div>1. 输入"消费人员"姓名与姓名之间用逗号隔开。</div>
        <div>2. "消费人员"必须为本单位且经县总工会审批人员。</div>
        <div>3. "消费人员"名单必须与结算确认单名单保持一致。</div>
        <div>4. 确认消费金额、人员信息、人员数量无误后再进行支付。</div>
      </div>
    </div>

    <div class="btn" :class="priceVal === '' ? 'null_price' : ''" @click="submit">
      确认买单
      <text v-if="priceVal !== ''">￥{{ priceVal }}</text>
    </div>
    <div class="qt" @click="getPhone">问题反馈</div>

    <!-- 数字键盘 -->
    <van-popup
      v-model="popup_show"
      round
      closeable
      :style="{
        width: '300px',
        height: '306px',
        marginTop: '-100px',
      }"
    >
      <div class="box">
        <div class="title">输入支付密码</div>
        <div style="" class="money">
          <span class="small">¥</span>{{ priceVal }}
        </div>
        <div class="balance">
          <div class="left">
            <van-icon name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/pay/balance.png" size="23" />
            <span>账户余额</span>
          </div>
          <div class="right">{{ balance }}元</div>
        </div>
        <van-password-input
          v-if="popup_show"
          :value="password"
          info=""
          :length="6"
          :focused="showKeyboard"
          @focus="showKeyboard = true"
        />
      </div>
    </van-popup>
    <van-number-keyboard
      v-model="password"
      :show="showKeyboard"
      title="点滴安全键盘"
      z-index="9000"
      @blur="showKeyboard = false"
    />

  </div>
</template>

<script>
import Top from './components/top.vue'
import { getShopDataV2 } from '@/api/takeout'
import { version } from '@/config/settings'
import {
  Pay,
  Balance,
  saveQrcodeOrder
} from '@/api/scan'
import { query } from '@/utils/sign'
export default {
  components: {
    Top
  },
  data() {
    return {
      priceVal: '',
      shopData: {
        mobilePhone: ''
      },
      peoples: '',
      popup_show: false,
      password: '',
      balance: 0,
      capitalAccountId: '',
      showKeyboard: false,
      orderNo: '',
      peoplesNum: '',
      people: {
        names: '',
        num: ''
      }
    }
  },
  watch: {
    password(val, old) {
      console.log(val)
      let self = this
      if (val.length >= 6) {
        this.$throttle(() => {
          self.saveQrcodeOrder()
        }, 1500)
      }
    }
  },

  created() {
    this.getShopData()
    this.getBalance()
  },
  methods: {
    isShow38() {
      return this.$route.query.cateId == '154' ? true : false
    },
    addPeople() {
      // this.people.push({
      //   name: ''
      // })
      // 粘贴剪贴板内容 到 peoples
      navigator.clipboard.readText().then(text => {
        this.peoples = text
      })
    },
    delPeople(index) {
      if (this.people.length === 1) {
        this.$toast('至少保留一个消费人员')
        return
      }
      this.people.splice(index, 1)
    },
    // 店铺信息
    getShopData() {
      let data = {
        marketId: this.$route.query.marketId,
        longitude: this.$store.getters.getLocation.longitude,
        latitude: this.$store.getters.getLocation.latitude
      }
      getShopDataV2(data).then((res) => {
        this.shopData = res.data
      })
    },
    // 拨打电话
    getPhone() {
      AlipayJSBridge.call(
        'CallPhone',
        {
          phoneNum: this.shopData.mobilePhone
        },
        function(result) { }
      )
    },
    // 获取余额
    getBalance() {
      let data = {
        'userId': this.$store.getters.getUserId,
        'receiveId': this.$route.query.marketId
      }
      Balance(data)
        .then((res) => {
          if (res.status == 200) {
            this.balance = res.data.balance == null ? 0 : res.data.balance
            this.capitalAccountId = res.data.capitalAccountId
          }
        })
    },
    submit() {
      let self = this
      self.password = ''
      if (this.priceVal > 9999999) {
        this.$toast('超过最大支付金额9999999，请修改')
        return false
      }
      if (this.priceVal < 0.01) {
        this.$toast('支付金额必须大于0.01元')
        return false
      }
      if (this.people.num === '') {
        this.$toast('请输入消费人员数量')
        return false
      }
      // 验证人员名单
      if (!this.people.names || this.people.names.trim() === '') {
        this.$toast('请输入消费人员')
        return false
      }

      // 验证人数是否匹配
      const namesStr = this.people.names.trim().replace(/，/g, ',')
      const nameList = namesStr.split(',').filter(name => name.trim() !== '')
      if (nameList.length !== parseInt(this.people.num)) {
        this.$toast(`消费人员数量与实际输入人数不符，应输入${this.people.num}人`)
        return false
      }
      // 如果支付方式是余额支付则
      // if (this.balance <= 0) {
      //   this.$toast('余额不足')
      // } else {
      //   this.popup_show = true
      // }
      this.popup_show = true
      this.showKeyboard = true
    },
    // 下单
    saveQrcodeOrder() {
      // 数量和人员信息
      let peoples = this.people.num + '&' + this.people.names
      let data = {
        type: 1,
        payPassword: this.password,
        amount: this.priceVal,
        receiveId: this.$route.query.marketId,
        capitalAccountId: this.capitalAccountId,
        paymentType: 1,
        // note: JSON.stringify(this.people)
        note: peoples
      }
      this.$toast.loading({
        duration: 0,
        forbidClick: true,
        message: '下单中'
      })
      saveQrcodeOrder(data).then(res => {
        if (res.status == 200) {
          this.orderNo = res.data.orderNo
          this.pay()
        } else {
          this.password = ''
        }
      })
    },
    pay() {
      let self = this
      // 数量和人员信息
      let peoples = this.people.num + '&' + this.people.names
      let Base64 = require('js-base64').Base64
      let data = {
        type: 1,
        amount: this.priceVal,
        noDiscountAmount: null,
        receiveId: this.$route.query.marketId,
        capitalAccountId: this.capitalAccountId,
        paymentType: 1,
        payPassword: Base64.encode(self.password),
        terminalSysVer: version, // 当前版本号,
        // eslint-disable-next-line no-undef
        clientIp: '127.0.0.1',
        'deviceId': localStorage.getItem('deviceId') ? localStorage.getItem('deviceId') : 'test',
        longitude: this.$store.getters.getLocation.longitude,
        latitude: this.$store.getters.getLocation.latitude,
        orderNo: this.orderNo,
        note: peoples
      }
      self.$toast.loading({
        duration: 0,
        forbidClick: true,
        message: '支付中'
      })

      // 加签
      if (data.noDiscountAmount === null) {
        delete data.noDiscountAmount
      }
      if (data.payPassword === null || data.payPassword === '') {
        delete data.payPassword
      }
      data.sign = query(data)

      Pay(data)
        .then((res) => {
          self.$toast.clear()
          if (res.status == 200) {
            // 余额支付
            this.showKeyboard = false
            this.popup_show = false
            this.priceVal = ''
            this.peoples = ''
            this.password = ''
            self.$toast('支付成功')
          } else {
            self.$toast(res.message)
            self.password = ''
          }
        })
    }
  }
}
</script>

<style scoped lang="scss">
.agency {
  width: 100%;
  height: 100%;

  .payment {
    width: 710px;
    margin: 0 auto;
    background-color: #fff;
    border-radius: 16px;
    margin-top: 50px;
    padding: 20px;
    display: flex;
    justify-content: space-between;
    border-bottom: 1px solid #f5f5f5;

    .input_title {
      font-size: 33px;
      color: #000;
      font-weight: bold;
    }
  }

  .people {
    width: 700px;
    margin: 0 auto;
    background-color: #fff;
    border-radius: 16px;
    margin-top: 20px;
    padding: 20px;

    .input_title {
      font-size: 30px;
      color: #000;
    }
  }

  .add_people {
    width: 700px;
    margin: 0 auto;
    background-color: #fff;
    border-radius: 16px;
    margin-top: 20px;
    padding: 20px;
    font-size: 28px;
    color: #000;
    text-align: center;
  }

  .btn {
    width: 680px;
    height: 80px;
    border-radius: 40px;
    text-align: center;
    line-height: 80px;
    margin: 0 auto;
    color: #fff;
    background: linear-gradient(45deg, #71d774 0%, #5ecc52 100%);
    margin-top: 60px;
    font-weight: bold;
    font-size: 28px;
  }

  .null_price {
    opacity: 0.6;
  }

  .qt {
    text-align: center;
    font-size: 26px;
    color: #666;
    margin-top: 30px;
  }

  .box {
    .title {
      text-align: center;
      width: 100%;
      box-sizing: border-box;
      font-size: 34px;
      font-family: PingFangSC;
      color: #222222;
      padding: 56px 0 48px 0;
    }

    .money {
      text-align: center;
      font-size: 60px;
      margin-bottom: 79px;
      font-family: PingFangSC;
      color: #222222;
    }

    .small {
      font-size: 36px;
      font-family: PingFangSC-Medium;
      font-weight: 500;
      color: #222222;
    }

    .balance {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin: 0 50px 48px;
      font-size: 34px;
      font-family: PingFangSC;

      .left {
        display: flex;
        justify-content: space-between;
        align-items: center;

        >span {
          margin-left: 10px;
        }
      }

      .right {
        font-size: 32px;
        color: #999999;
      }
    }
  }

  .tips {
    width: 700px;
    margin: 0 auto;
    border-radius: 16px;
    margin-top: 30px;
    padding: 20px;

    .tips_title {
      font-size: 26px;
      color: #000;
    }

    .tips_content {
      font-size: 23px;
      color: red;
      margin-top: 20px;
      div{
        margin-bottom: 10px;
      }
    }
  }

}
</style>
