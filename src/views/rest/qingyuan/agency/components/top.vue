<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-07-07 16:28:35
 * @LastEditors: 孙立政
 * @LastEditTime: 2021-07-13 11:39:39
-->
<template>
  <div class="home" :style="styleVar">
    <div class="warp">
      <div class="top">
        <div class="left" @click="goBack()">
          <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/common/arrowlbft.png" alt="">
        </div>
        <div class="center">
          <span>返回</span>
        </div>
      </div>
      <div class="top fixed">
        <div class="left" @click="goBack()">
          <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/common/arrowlwft.png" alt="">
        </div>
        <div class="center">
          <span>返回</span>
        </div>
      </div>
      <div class="cover">
        <div class="shop_name">
          <img :src="shopData.pic + '?x-oss-process=image/resize,w_700/format,jpg/quality,q_85'" alt="" class="">
          <span>{{ shopData.marketName | ellipsis(13) }}</span>
        </div>
        <div class="shop_address">
          {{ shopData.address }}
        </div>

      </div>
    </div>
  </div>
</template>

<script>
import { getShopDataV2 } from '@/api/takeout'
export default {
  props: {
    shopData: {
      type: Object,
      default: () => { }
    }
  },
  data() {
    return {
      keyword: ''
    }
  },
  computed: {
    styleVar() {
      return {
        '---nav-height': this.$store.getters.getStatusHeight + 'px'
      }
    }
  },
  created() {
    // this.getShopData()
  },
  mounted() {
    // 获取nav
    const nav = document.querySelector('.fixed')
    addEventListener('scroll', () => {
      // 获取偏移值
      const top = document.documentElement.scrollTop
      // 设置一个合适的范围
      if (top <= 45 + this.$store.getters.getStatusHeight && top >= 0) {
        // 令header的渐变色位置变成计算后的渐变位置
        nav.style.setProperty('opacity', top / 100)
      } else {
        // 在移动一定范围后令其完全不透明
        nav.style.setProperty('opacity', 1)
      }
    })
  },
  methods: {
    // 店铺信息
    getShopData() {
      let data = {
        marketId: this.$route.query.marketId,
        longitude: this.$store.getters.getLocation.longitude,
        latitude: this.$store.getters.getLocation.latitude
      }
      getShopDataV2(data).then((res) => {
        this.shopData = res.data
      })
    },
    goSearch() {
      this.$emit('getSearch', this.keyword)
    },
    goClear() { },
    goBack() {
      this.$router.go(-1)
    }
  }
}
</script>

<style scoped lang="scss">
.home {
  .warp {
    position: relative;
    width: 100%;
    height: 342px;
    background-image: url(https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/rest/rest-bg.png);
    background-size: 100%;
    border-bottom-left-radius: 6px;
    border-bottom-right-radius: 6px;

    .top {
      position: fixed;
      left: 0;
      right: 0;
      z-index: 2;
      display: flex;
      align-items: center;
      width: 100%;
      padding-top: calc(20px + var(---nav-height));
      padding-bottom: 20px;

      .left {
        margin-left: 45px;
        margin-right: 28px;

        img {
          width: 25px;
          height: 40px;
          float: left;
        }
      }

      .center {
        font-size: 32px;
        color: #fff;
      }
    }

    .fixed {
      opacity: 0;
      background-color: #fff;
    }

    .cover {
      position: absolute;
      left: 35px;
      bottom: 86px;
      height: 90px;
      font-size: 34px;
      font-family: PingFangSC-Medium;
      font-weight: 500;
      color: #ffffff;

      .shop_name {
        display: flex;
        align-items: center;

        img {
          width: 100px;
          height: 100px;
          margin-right: 25px;
          border-radius: 12px;
        }
      }
      .shop_address{
        font-size: 24px;
        color: #fff;
        margin-top: 20px;
        margin-left: 5px;
      }
    }
  }
}
</style>
