<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-06-05 14:08:30
 * @LastEditors: 孙立政
 * @LastEditTime: 2021-06-25 10:03:08
-->
<template>
  <div class="content">
    <!-- 疗休养展示页 -->
    <NavHeight bgc="#638014" />
    <Top :message="message" class="top" />
    <Banner />
    <Tap />
    <Advertise />
    <Active />
    <List />
  </div>
</template>

<script>
import Top from './components/top'
import Banner from './components/banner.vue'
import Tap from './components/tab.vue'
import Advertise from './components/advertise.vue'
import Active from './components/active.vue'
import List from './components/list.vue'
export default {
  components: {
    Top,
    Banner,
    Tap,
    Advertise,
    Active,
    List
  },
  data() {
    return {
      message: '疗休养'
    }
  }
}
</script>

<style lang="scss" scoped="scoped">
	.content {
		background-color: #fff;
		.top {
			::v-deep .van-nav-bar {
			background: #638014;
		}
		}
	}
</style>
