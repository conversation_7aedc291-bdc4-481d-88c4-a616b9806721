<template>
  <!-- 疗休养详情 -->
  <div class="content">
    <NavHeight bgc="#fff" />
    <div class="topImg" :style="'background-image: url(' + bcimg + ')'">
      <van-sticky bg-color="rgba(0,0,0,.0);">
        <div>
          <van-icon
            name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/common/arrowlbft.png"
            color="#E7E7E6"
            size="22"
            style="float: left;margin-left: 30rpx;"
            @click="goback()"
          />
        </div>
      </van-sticky>
    </div>
    <!-- 图文 -->
    <div class="body" v-html="content" />
  </div>
</template>

<script>
import { tbTripArticle } from '@/api/rest'
export default {
  components: {},
  data() {
    return {
      bcimg: '',
      detailid: '',
      content: ''
    }
  },
  created() {
    this.detailid = this.$route.query.id
  },
  mounted() {
    this.getdetil()
  },
  methods: {
    getdetil() {
      let self = this
      tbTripArticle(self.detailid)
        .then(function(res) {
          self.bcimg = res.data.cover
          self.content = res.data.content
        })
    },
    goback() {
      this.$router.go(-1)
    }
  }
}
</script>

<style lang="scss" scoped>
.content {
	.top {
		width: 100%;
		color: #e7e7e6;
		text-align: center;
		font-size:36px;
	}
	.topImg {
		width: 100%;
		height: 550px;
		background-size: 100% 100%;
		overflow: hidden;
	}

	.body {
		width: 93%;
		margin: 0 auto;
		.title {
			margin-top:460px;
			text:nth-child(1) {
				font-weight: bold;
				font-size: 48px;
			}
			text:nth-child(2) {
				font-size: 28px;
				color: rgba(0, 0, 16, 0.6);
				margin-left: 44px;
			}
		}
		.desc {
			margin-top:22px;
			font-size: 32px;
			color: rgba(0, 0, 16, 1);
		}
		.title2 {
			margin-top:50px;
			font-size: 34px;
			color: rgba(0, 0, 16, 0.8);
		}
		.table {
			margin-top: 44px;
		}
		.step {
			div:nth-child(1) {
				margin-top:20px;
			}
			image {
				margin-top:20px;
				width: 100%;
				height: 660px;
				border-radius:10px;
			}
			div:nth-child(2) {
				margin-top: 30px;
			}
		}
	}
}
</style>
