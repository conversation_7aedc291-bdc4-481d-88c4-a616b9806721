<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2022-09-20 15:37:32
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-09-27 16:36:56
-->
<template>
  <div class="apply">
    <NavHeight bgc="#fff" />
    <Nav message="申请退款" />

    <div class="apply_card">
      <div class="title">退款内容：</div>
      <div class="cell">
        现金：
        <span class="price"> {{ this.$route.query.actualPay }}元（以实际退款金额为准）</span>
      </div>
    </div>

    <div class="apply_card">
      <div class="title">现金退还方式：</div>
      <div class="cell">
        原路退回
        <span class="date"> （1-3个工作日内退款到原支付方）</span>
      </div>
    </div>

    <div class="apply_card">
      <div class="title">退款原因(至少选1项)</div>
      <div class="list">
        <van-checkbox-group v-model="result">
          <van-cell-group>
            <van-cell
              v-for="(item, index) in list"
              :key="item"
              clickable
              :title="`${item}`"
              @click="toggle(index)"
            >
              <template #right-icon>
                <van-checkbox ref="checkboxes" checked-color="#169D1B" shape="square" :name="item" />
              </template>
            </van-cell>
          </van-cell-group>
        </van-checkbox-group>
      </div>
    </div>

    <div style="height: 120px;" />

    <div class="bottom">
      <div class="btn" @click="refundApply">
        申请退款
      </div>

    </div>
  </div>
</template>

<script>
import { refundApply } from '@/api/order/serMeal'
import { Nav } from './components'
export default {
  components: {
    Nav
  },
  data() {
    return {
      info: {},
      list: ['计划有变，没时间消费', '误认为是外卖',
        '没看清使用规则，要用时才发现限制多',
        '用现金/刷卡/微信/支付宝支付了团购价',
        '店里活动更优惠',
        '预约不上',
        '商家营业但不接待',
        '商家停业/装修/转让',
        '去过了，不太满意',
        '朋友/网上评价不好',
        '买多了/买错了',
        '商家说可以直接以团购价到店消费',
        '后悔了，不想要了',
        '联系不上商家'
      ],
      result: []
    }
  },
  created() {},
  methods: {
    refundApply() {
      let self = this
      if (this.result.length === 0) {
        this.$toast('请选择退款原因')
        return
      }
      this.$toast.loading({
        duration: 0,
        forbidClick: true
      })
      refundApply({
        orderNo: this.$route.query.orderNo,
        reason: this.result.join(',')
      }).then(res => {
        if (res.status === 200) {
          this.$toast('申请成功')
          this.$router.push({
            name: 'SetMealorderRefund',
            query: {
              orderNo: this.$route.query.orderNo,
              from: 1
            }
          })

          setTimeout(() => {
            self.$toast.clear()
          }, 1500)
        }
      })
    },
    toggle(index) {
      this.$refs.checkboxes[index].toggle()
    }
  }
}
</script>

  <style scoped lang="scss">
  .apply {
    .apply_card{
        margin-top: 35px;
        .title{
            color: #333333;
            font-size: 35px;
            margin-left: 32px;
        }
        .cell{
            width: 750px;
            height: 80px;
            background: #ffffff;
            line-height: 80px;
            padding-left: 32px;
            font-size: 30px;
            color: #666666;
            margin-top: 24px;
            .price{
                color: #ff7807;
            }
            .date{
                color: #169d1b;
            }
        }
        .list{
            margin-top: 24px;
        }
        ::v-deep .van-cell__title{
            font-size: 30px;
            color: #666666;
        }
    }
    .bottom{
        width: 100%;
        height: 177px;
        position: fixed;
        bottom: 0;
        background-color: #f2f2f2;
        .btn{
            width: 634px;
            height: 88px;
            background: linear-gradient(90deg,#40d243, #1fc432);
            border-radius: 8px;
            line-height: 88px;
            margin: 0 auto;
            text-align: center;
            color: #ffffff;
            font-size: 35px;
            font-weight: 500;
            margin-top: 30px;
        }
    }
  }
  </style>
