<template>
  <div class="home">
    <NavHeight bgc="#fff" />
    <Nav message="退款详情" />
    <div class="flex-col page">
      <div class="flex-col group">
        <img
          v-if="info.refundStatus === 1||info.refundStatus === 2"
          class="image"
          src="https://codefun-proj-user-res-1256085488.cos.ap-guangzhou.myqcloud.com/6114f1316b68fa00119edb1e/62a6f2acddda960011b34640/16636675565128979645.png"
        >
        <img
          v-if="info.refundStatus === 3"
          class="image"
          src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/shop/fineFood/refundok.png"
        >
        <span v-if="info.refundStatus === 1||info.refundStatus === 2" class="text_2">退款中{{ info.refundAmount }}元</span>
        <span v-if="info.refundStatus === 3" class="text_2">退款成功{{ info.refundAmount }}元</span>
        <div class="flex-col space-y-24 group_2">
          <div class="flex-row justify-between section" @click="goBack">
            <span class="font_1 text_3">订单信息</span>
            <div class="flex-row group_3">
              <span class="font_2">{{ this.$route.query.name }}</span>
              <!-- <img
                class="image_2"
                src="https://codefun-proj-user-res-1256085488.cos.ap-guangzhou.myqcloud.com/6114f1316b68fa00119edb1e/62a6f2acddda960011b34640/16636675565104986188.png"
              > -->
            </div>
          </div>
          <div class="flex-col space-y-24 section_2">
            <div class="flex-row justify-between">
              <span class="font_3">实际退款金额</span>
              <span class="font_2">{{ info.refundAmount }}元</span>
            </div>
            <div class="flex-row justify-between">
              <span class="font_3">申请退款金额</span>
              <span class="font_2">{{ info.refundAmount }}元</span>
            </div>
          </div>
          <div v-if="info.refundStatus === 1||info.refundStatus === 2" class="flex-col section_3">
            <span class="font_1 text_4">退款进度</span>
            <div class="divider" />
            <div class="flex-row space-x-8 group_4">
              <img
                class="image_3"
                src="https://codefun-proj-user-res-1256085488.cos.ap-guangzhou.myqcloud.com/6114f1316b68fa00119edb1e/62a6f2acddda960011b34640/d52d564511e765f1eb56bb404a62f3ff.png"
              >
              <span class="font_4 text_5">原支付方处理中</span>
            </div>
            <div class="flex-row group_5">
              <div class="section_4" />
              <div class="flex-col items-start space-y-4 group_6">
                <span class="font_5">预计原支付方会在1个工作日内处理完成</span>
                <span class="font_5">{{ showTime(1) }}</span>
              </div>
            </div>
            <div class="flex-col space-y-4">
              <div class="flex-row space-x-8">
                <div class="section_5" />
                <span class="font_4 text_6">审核通过</span>
              </div>
              <span class="font_5 text_7">已受理您的退款申请</span>
              <span class="font_5 text_8">{{ showTime(0) }}</span>
            </div>
          </div>

          <div v-if="info.refundStatus === 3" class="flex-col section_3">
            <span class="font_1 text_4">退款进度</span>
            <div class="divider" />
            <div class="flex-row space-x-8 group_4">
              <img
                class="image_3"
                src="https://codefun-proj-user-res-1256085488.cos.ap-guangzhou.myqcloud.com/6114f1316b68fa00119edb1e/62a6f2acddda960011b34640/d52d564511e765f1eb56bb404a62f3ff.png"
              >
              <span class="font_4 text_5">原支付方退款到账中</span>
            </div>
            <div class="flex-row group_5">
              <div class="section_4" />
              <div class="flex-col items-start space-y-4 group_6">
                <span class="font_5">原支付方退款处理完成后会在3个工作内将入账值您的原支付账户</span>
                <span class="font_5">{{ showTime(1) }}</span>
              </div>
            </div>
            <div class="flex-row space-x-8 group_4" style="margin-top: -1px;">
              <div class="section_5" />
              <span class="font_4 text_6">原支付方已受理退款</span>
            </div>
            <div class="flex-row group_5">
              <div class="section_4" />
              <div class="flex-col items-start space-y-4 group_6">
                <span class="font_5">预计原支付方会在1个工作日内处理完成</span>
                <span class="font_5">{{ showTime(1) }}</span>
              </div>
            </div>

            <div class="flex-col space-y-4">
              <div class="flex-row space-x-8">
                <div class="section_5" />
                <span class="font_4 text_6">审核通过</span>
              </div>
              <span class="font_5 text_7">已受理您的退款申请</span>
              <span class="font_5 text_8">{{ showTime(0) }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { Nav } from './components'
import { getRefundByOrderNo } from '@/api/order/serMeal'
export default {
  components: {
    Nav
  },
  data() {
    return {
      info: {}
    }
  },
  created() {
    this.getRefundByOrderNo()
  },
  methods: {
    getRefundByOrderNo() {
      getRefundByOrderNo(this.$route.query.orderNo).then(res => {
        if (res.status === 200) {
          this.info = res.data
        }
      })
    },
    showTime(type) {
      let data = this.info.orderRefundProgress
      for (let i = 0; i < data.length; i++) {
        if (type === 0 && data[i].orderStatus === 70) {
          return data[i].createTime
        }
        if (type === 1 && data[i].orderStatus === 60) {
          return data[i].createTime
        }
      }
    },
    goBack() {
      this.$router.go(-1)
    }
  }
}
</script>

<style scoped lang="scss">
.home {
  .page {
    background-color: #f5f5f5ff;
    width: 100%;
    overflow-y: auto;
    overflow-x: hidden;
    height: 100%;
    .group {
      padding: 64px 22px 167px;
      flex: 1 1 auto;
      overflow-y: auto;
      .image {
        align-self: center;
        width: 138px;
        height: 138px;
      }
      .text_2 {
        margin-top: 36px;
        align-self: center;
        color: #333333;
        font-size: 42px;
        font-family: "PingFang SC";
        font-weight: 500;
        line-height: 59px;
      }
      .space-y-24 {
        & > *:not(:first-child) {
          margin-top: 24px;
        }
        .section {
          padding: 22px 24px 21px;
          background-color: #ffffff;
          border-radius: 20px;
          .text_3 {
            font-weight: 500;
          }
          .group_3 {
            margin-bottom: 3px;
            .image_2 {
              margin: 8px 0;
              flex-shrink: 0;
              width: 24px;
              height: 24px;
            }
          }
        }
        .section_2 {
          padding: 24px 24px 32px;
          background-color: #ffffff;
          border-radius: 20px;
        }
        .font_3 {
          font-size: 28px;
          font-family: "PingFang SC";
          line-height: 40px;
          font-weight: 500;
          color: #333333;
        }
        .font_2 {
          font-size: 28px;
          font-family: "PingFang SC";
          line-height: 40px;
          color: #333333;
        }
        .section_3 {
          padding: 32px 0 180px 24px;
          background-color: #ffffff;
          border-radius: 24px;
          .text_4 {
            align-self: flex-start;
          }
          .divider {
            margin-top: 23px;
            background-color: #e2e2e2;
            height: 1px;
          }
          .space-x-8 {
            & > *:not(:first-child) {
              margin-left: 8px;
            }
            .image_3 {
              margin: 6px 0;
              border-radius: 50%;
              width: 30px;
              height: 30px;
            }
            .font_4 {
              font-size: 30px;
              font-family: "PingFang SC";
              line-height: 42px;
            }
            .text_5 {
              color: #169d1b;
            }
            .section_5 {
              margin: 6px 0;
              border-radius: 50%;
              width: 30px;
              height: 30px;
              border: solid 2px #cccccc;
            }
            .text_6 {
              color: #999999;
            }
          }
          .group_4 {
            margin-top: 20px;
          }
          .group_5 {
            padding: 0 14px;
            .section_4 {
              background-color: #cccccc;
              width: 1px;
              height: 142px;
            }
            .group_6 {
              margin-top: 6px;
              padding: 0 23px;
              flex: 1 1 auto;
            }
          }
          .space-y-4 {
            & > *:not(:first-child) {
              margin-top: 4px;
            }
            .font_5 {
              font-size: 28px;
              font-family: "PingFang SC";
              line-height: 40px;
              color: #999999;
            }
            .text_7 {
              margin-left: 38px;
              align-self: flex-start;
            }
            .text_8 {
              margin-left: 38px;
              align-self: flex-start;
            }
          }
        }
        .font_1 {
          font-size: 32px;
          font-family: "PingFang SC";
          line-height: 45px;
          color: #333333;
        }
      }
      .group_2 {
        margin-top: 84px;
      }
    }
  }
}
</style>
