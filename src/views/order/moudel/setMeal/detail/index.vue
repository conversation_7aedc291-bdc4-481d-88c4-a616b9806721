<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2022-09-20 15:37:32
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-09-22 17:05:25
-->
<template>
  <div v-if="loading" class="home">
    <NavHeight bgc="#fff" />
    <Nav message="套餐详情" />
    <Goods :info="info" />
    <Code :info="info" />
    <Shop :info="info" />
    <Group :info="info" />
    <Tips :info="info" />
    <Info :info="info" />
    <Bottom :info="info" />

    <div style="height: 100px;" />
  </div>
</template>

<script>
import { findByOrderNo } from '@/api/order/serMeal'
import { Nav, Goods, Code, Shop, Group, Tips, Info, Bottom } from './components'
export default {
  components: {
    Nav, Goods, Code, Shop, Group, Tips, Info, Bottom
  },
  data() {
    return {
      loading: false,
      info: {}
    }
  },
  created() {
    this.findByOrderNo()
  },
  methods: {
    findByOrderNo() {
      findByOrderNo(this.$route.query.orderNo).then(res => {
        if (res.status === 200) {
          this.info = res.data
          this.loading = true
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
.home {
}
</style>
