<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2022-09-22 18:40:10
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-09-27 12:13:59
-->
<template>
  <div class="home">
    <NavHeight bgc="#fff" />
    <Nav message="查看券码" />

    <div class="box">
      <div class="shop_img">
        <img class="shop_img" :src="info.marketCover" alt="">
      </div>
      <div class="shop_name"><van-icon name="shop-o" /> {{ info.marketName }}</div>
      <div class="setMale_name">{{ info.setMealName }}</div>
      <div class="code">
        <span>券码 </span>
        <span>{{ info.verificationCode }}</span>
      </div>
      <div class="divider" />
      <div class="qrcode">
        <qrcode :value="info.verificationCode" :options="qroptions" />
      </div>
      <div class="time">
        有效期至：{{ info.endTime }}
      </div>
    </div>
  </div>
</template>

<script>
import { Nav } from './components'
import { findVerificationCodeByOrderNo } from '@/api/order/serMeal'
export default {
  components: {
    Nav
  },
  data() {
    return {
      info: {},
      qroptions: {
        width: 200
      }
    }
  },
  created() {
    this.findVerificationCodeByOrderNo()
  },
  methods: {
    findVerificationCodeByOrderNo() {
      findVerificationCodeByOrderNo(this.$route.query.orderNo).then(res => {
        if (res.status === 200) {
          this.info = res.data[0]
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
    .home {
      .box{
        width: 690px;
        height: 922px;
        margin: 0 auto;
        background-color: #fff;
        border-radius: 12px;
        margin-top: 180px;
        .shop_img{
          width: 154px;
          height: 154px;
          border-radius: 50%;
          margin: 0 auto;
          margin-top: -50px;

        }
        .shop_name{
          color: #222222;
          font-weight: 500;
          font-size: 38px;
          text-align: center;
          margin-top: 16px;
        }
        .setMale_name{
          color: #222222;
          font-size: 30px;
          text-align: center;
          margin-top: 13px;
        }
        .code{
          height: 50px;
          line-height: 50px;
          text-align: center;
          margin-top: 30px;
          span:nth-child(1){
            font-size: 34px;
            color: #666666;
          }
          span:nth-child(2){
            font-size: 42px;
            color: #333333;
            position: relative;
            top: 5px;
          }
        }
        .dashed{
          width: 640px;
          height: 1px;
          border: 1px dashed #979797;
          opacity: .5;
          margin: 0 auto;
          margin-top: 30px;
        }
        .divider {
          width: 640px;
          height: 1px;
          background-image: url(https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/order/divider.png);
          background-size: 100% 100%;
          margin: 0 auto;
          margin-top: 30px;
        }
        .qrcode{
          width: 360px;
          margin: 0 auto;
        }
        .time{
          text-align: center;
          color: #333333;
          font-size: 28px;
          margin-top: -16px;
        }
      }
    }
</style>
