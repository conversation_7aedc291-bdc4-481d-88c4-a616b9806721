<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2022-09-19 15:28:46
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-09-27 18:39:00
-->
<template>
  <div class="agein">
    <div class="box">
      <div class="conents">
        <div class="card">
          <div class="card-content">
            <div class="card-content-name">{{ info.marketName }}</div>
            <div class="card-shop-desc">
              <span>营业时间：</span>
              <span style="font-size: 12px;">{{ market.openingHours }}-{{ market.closingHours }}</span>
              <!-- <span v-for="(item,index) in info.orderItemFsm.fsmAgentInfo.marketBusTimes" :key="index">{{ item.startTime }}-{{ item.endTime }}</span> -->
            </div>
            <div class="card-content-desc">
              <span v-if="market.distance > 0.5" style="font-size: 12px;" class="goodsSalesDistance"><van-icon name="location-o" />{{ market.distance.toFixed(2) }}km</span>
              <span v-else style="font-size: 12px;" class="goodsSalesDistance"><van-icon name="location-o" />{{ market.distance.toFixed(2) * 1000 }}m</span>
              {{ market.address }}
            </div>
          </div>
          <div class="card-right" @click="CallPhone(market.mobilePhone)">
            <div>
              <img
                src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/shop/fineFood/phone.png"
                alt=""
              >
            </div>
            <div>联系商家</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { findMarketDistance } from '@/api/order/serMeal'
export default {
  props: {
    info: {
      type: Object,
      default: function() {
        return {}
      }
    }
  },
  data() {
    return {
      evaScore: 5,
      market: {
        distance: 0
      }
    }
  },
  mounted() {
    this.findMarketDistance()
  },
  methods: {
    findMarketDistance() {
      findMarketDistance({
        marketId: this.info.marketId,
        latitude: this.$store.getters.getLocation.latitude,
        longitude: this.$store.getters.getLocation.longitude
      }).then(res => {
        if (res.status === 200) {
          this.market = res.data
        }
      })
    },
    // 拨打电话
    CallPhone(data) {
      AlipayJSBridge.call('CallPhone', {
        phoneNum: data
      }, function(result) {})
    }
  }
}
</script>

  <style scoped lang="scss">
  .agein {
    width: 706px;
    background: #ffffff;
    border-radius: 24px;
    margin: 0 auto;
    display: flex;
    align-items: center;
    margin-top: 20px;
    .box {
      width: 93%;
      height: 93%;
      margin: 0 auto;
    }
    .conents {
        margin-top: 20px;
      .card {
        display: flex;
        justify-content: space-between;
        min-height: 170px;
        .card-content {
          position: relative;
          min-height: 170px;
          .card-content-name {
            font-size: 36px;
            font-family: PingFangSC-Medium;
            font-weight: 500;
            color: #222222;
            margin-bottom: 8px;
          }
          .card-shop-desc {
            font-size: 25px;
            font-family: PingFangSC;
            color: #333;
          }
          .card-content-desc {
            font-size: 26px;
            font-family: PingFangSC;
            color: #787878;
            margin-top: 10px;
          }
        }
        .card-right {
          text-align: center;
          img {
            width: 33px;
            height: 33px;
            margin-top: 38px;

          }
          font-size: 23px;
          color: #333;
        }
      }
    }
  }
  </style>
