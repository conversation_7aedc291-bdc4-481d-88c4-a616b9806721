<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2022-09-20 16:33:31
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-09-23 10:55:21
-->
<template>
  <div v-if="info.status === 2||info.status === 4" class="code">
    <div v-if="info.status === 2" class="code_img">
      <!-- <img
        src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/shop/fineFood/bj.png"
        alt=""
      > -->
      <qrcode class="qrcode" :value="info.verificationCode" :options="qroptions" />
    </div>
    <div v-if="info.status === 2" class="code_info">
      <div class="left">
        <div class="code_name">券码信息</div>
        <div class="code_time">{{ info.endTime }} 到期</div>
        <div class="code_num">· {{ info.verificationCode }}</div>
      </div>
      <div class="right">
        <div class="code_btn" @click="applyRefund(info)">申请退款</div>
        <div class="code_status">待使用</div>
      </div>
    </div>
    <div v-if="info.orderStatus === 60" class="code_info">
      <div class="left">
        <div class="code_name">券码信息</div>
        <div class="code_num die">·{{ info.verificationCode?info.verificationCode:'无' }}</div>
      </div>
      <div class="right" @click="goRefundStatus(info)">
        <div class="code_btn_mag">
          <span>退款中</span>
          <van-icon class="icon" name="arrow" size="13" color="#CCCCCC" />
        </div>
      </div>
    </div>
    <div v-if="info.orderStatus === 70" class="code_info">
      <div class="left">
        <div class="code_name">券码信息</div>
        <div class="code_num die">·{{ info.verificationCode?info.verificationCode:'无' }}</div>
      </div>
      <div class="right" @click="goRefundStatus(info)">
        <div class="code_btn_mag">
          <span class="die">退款完成</span>
          <van-icon class="icon" name="arrow" size="13" color="#CCCCCC" />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    info: {
      type: Object,
      default: function() {
        return {}
      }
    }
  },
  data() {
    return {
      encodeStr: 'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/scan/delcode.png',
      qroptions: {
        width: 200
      }
    }
  },
  created() {},
  mounted() {},
  methods: {
    applyRefund(val) {
      this.$router.push({
        name: 'ApplyRefund',
        query: {
          orderNo: this.$route.query.orderNo,
          name: val.setMealName,
          actualPay: val.actualPay
        }
      })
    },
    // 退款进度
    goRefundStatus(val) {
      this.$router.push({
        name: 'SetMealorderRefund',
        query: {
          orderNo: this.$route.query.orderNo,
          name: val.setMealName,
          actualPay: val.actualPay
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
.code {
  width: 710px;
  background: #ffffff;
  border-radius: 16px;
  margin: 0 auto;
  margin-top: 20px;
  overflow: hidden;
  .code_img {
    width: 380px;
    height: 380px;
    margin: 0 auto;
    // margin-top: 52px;
  }
  .code_info {
    width: 92%;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    margin-top: 39px;
    margin-bottom: 10px;
    .left {
      .code_name {
        font-family: PingFangSC-Medium;
        color: #333333;
        font-size: 35px;
        font-weight: 500;
      }

      .code_time {
        color: #999999;
        font-size: 26px;
      }
      .code_num {
        font-family: PingFangSC-Medium;
        font-weight: 500;
        color: #333333;
        font-size: 35px;
      }
      .die{
        color: #CCCCCC;
        text-decoration:line-through;
      }
    }
    .right {
        text-align: center;
        padding-top: 20px;
      .code_btn {
        width: 144px;
        height: 54px;
        background: #ff4a26;
        border-radius: 27px;
        text-align: center;
        line-height: 54px;
        color: #fff;
        font-size: 30px;
        font-family: PingFangSC-Medium;
        font-weight: 500;
      }
      .code_btn_mag{
        span{
          font-size: 30px;
          font-weight: 400;
          color: #169d1b;
        }
        .icon{
          position: relative;
          top: -2px;
        }

      }
      .code_status {
        color: #333333;
        font-size: 26px;
        margin-top: 8px;
      }
    }
  }
}
</style>
