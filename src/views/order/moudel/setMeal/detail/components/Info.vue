<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2022-09-19 15:20:59
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-09-27 18:40:20
-->
<template>
  <div class="tips">
    <div class="box">
      <div class="title">订单信息</div>
      <div v-clipboard:copy="info.orderNo" v-clipboard:success="onCopy" class="conents">
        <div class="lately">订单号</div>
        <div class="msg" style="font-size: 15px;">
          {{ info.orderNo }}
        </div>
        <div class="copy">复制</div>
      </div>
      <div class="conents">
        <div class="lately">手机号</div>
        <div class="msg" style="font-size: 15px;">
          {{ info.telephone }}
        </div>
      </div>
      <div class="conents">
        <div class="lately">下单时间</div>
        <div class="msg" style="font-size: 14px;">
          {{ info.createTime }}
        </div>
      </div>
      <div class="conents">
        <div class="lately">数量</div>
        <div class="msg" style="font-size: 15px;">
          {{ info.quantity }}
        </div>
      </div>
      <div class="conents">
        <div v-if="info.status === 0" class="lately">待付款</div>
        <div v-else class="lately">实付</div>
        <div class="msg" style="font-size: 15px;">
          ￥{{ info.actualPay }}
        </div>
      </div>
      <div style="height: 10px;" />
    </div>
  </div>
</template>

<script>
export default {
  props: {
    info: {
      type: Object,
      default: function() {
        return {}
      }
    }
  },
  data() {
    return {}
  },
  created() {},
  mounted() {},
  methods: {
    onCopy(e) {
      this.$toast({
        duration: 1500, // 持续展示 toast
        forbidClick: false,
        message: '复制成功'
      })
    }
  }
}
</script>

  <style scoped lang="scss">
  .tips {
    width: 706px;
    background: #ffffff;
    border-radius: 24px;
    margin: 0 auto;
    display: flex;
    align-items: center;
    margin-top: 20px;
    .box {
      width: 93%;
      height: 93%;
      margin: 0 auto;
    }
    .title {
      height: 80px;
      line-height: 80px;
      font-size: 40px;
      font-family: PingFangSC-Medium;
    }
    .conents{
      color: #333333;
      font-size: 30px;
      display: flex;
      margin-bottom: 20px;
      .lately{
        width: 120px;
        text-align-last: justify;
        color: #999999;
        margin-right: 15px;
      }
      .msg{
        width: 480px;
      }
      .copy{
        width: 56px;
        height: 28px;
        border: 1px solid #bbbbbb;
        border-radius: 8px;
        color: #666666;
        text-align: center;
        line-height: 28px;
        font-size: 20px;
        margin-top: 6px;
      }
    }
  }
  </style>
