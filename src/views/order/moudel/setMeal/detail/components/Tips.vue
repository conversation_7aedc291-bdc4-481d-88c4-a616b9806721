<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2022-09-19 15:20:59
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-09-27 23:04:28
-->
<template>
  <div class="tips">
    <div class="box">
      <div class="title">温馨提示</div>
      <div class="conents">
        <div class="lately">有效期</div>
        <div class="msg">
          <span style="font-size: 13px;">{{ info.createTime?info.createTime:'/' }} 至 {{ info.endTime }} </span>
          <span v-if="info.orderItemFsm.fsmSetMealConfig.notAvailableWeekly!==''">（{{ formWeekly(info.orderItemFsm.fsmSetMealConfig.notAvailableWeekly) }}可用）</span>
          <span v-else>（周一至周天</span>
          <span v-if="info.orderItemFsm.fsmSetMealConfig.isHolidays === 1">、法定节假日通用</span>
          <span>）</span>
        </div>
      </div>
      <div v-if="info.orderItemFsm.fsmSetMealConfig.dissipateTimes.length!=0" class="conents">
        <div class="lately">使用时间</div>
        <div class="msg">
          <span v-for="(item,index) in info.orderItemFsm.fsmSetMealConfig.dissipateTimes" :key="index" style="margin-right: 6px;">{{ item.startTime.substring(0, item.startTime.length - 3) }}-{{ item.endTime.substring(0, item.endTime.length - 3) }} ;</span>
        </div>
      </div>
      <div class="conents">
        <div class="lately">使用规则</div>
        <div class="msg">
          <!-- <div v-if="info.orderItemFsm.fsmSetMealConfig.supportWifi === 0">商家提供免费WiFi</div>
          <div v-if="info.orderItemFsm.fsmSetMealConfig.supportProvideInvoice === 0">本单发票由商家提供，请在消费时向商家索取</div>
          <div v-if="info.orderItemFsm.fsmSetMealConfig.supportTakeOut === 0">堂食外卖均可，可打包</div>
          <div v-if="info.orderItemFsm.fsmSetMealConfig.supportUseBox === 0">可使用包厢</div>
          <div v-if="info.orderItemFsm.fsmSetMealConfig.supportFreeParking === 0">免费提供车位</div>
          <div v-if="info.orderItemFsm.fsmSetMealConfig.meanwhileDiscount === 1">团购用户不可同时享受商家其他优惠</div>
          <div>酒水饮料等问题，请致电商家咨询，以商家反馈为准如 部分菜品因时令或其他不可抗因素导致无法提供，商家 会用等价菜品替换，具体事宜请与商家协商</div>
          <div v-if="info.orderItemFsm.fsmSetMealConfig.makeAnAppointment === 0">无需预约，消费高峰期可能需要等位</div> -->

          <div>
            <span v-if="info.orderItemFsm.fsmSetMealConfig.supportWifi === 0">商家提供免费WiFi</span>
            <span v-else>商家不提供WiFi</span>
          </div>
          <div>
            <span v-if="info.orderItemFsm.fsmSetMealConfig.supportProvideInvoice === 0">本单发票由商家提供，请在消费时向商家索取</span>
            <span v-else>发票问题，请咨询商家</span>

          </div>
          <div>
            <span v-if="info.orderItemFsm.fsmSetMealConfig.supportTakeOut === 0">堂食外卖均可打包</span>
            <!-- <span v-else>仅限堂食，不提供餐前外带，餐毕未吃完可打包</span> -->
            <span v-else>仅限堂食</span>
          </div>
          <div>
            <span v-if="info.orderItemFsm.fsmSetMealConfig.supportUseBox === 0">可使用包厢</span>
            <span v-else>不可使用包厢，包厢问题咨询商家</span>
          </div>
          <div>
            <span v-if="info.orderItemFsm.fsmSetMealConfig.supportFreeParking === 0">免费提供车位</span>
            <span v-else>不提供免费车位</span>
          </div>
          <div>
            <span v-if="info.orderItemFsm.fsmSetMealConfig.meanwhileDiscount === 1">团购用户不可同时享受商家其他优惠</span>
            <span v-else>同享优惠咨询商家</span>
          </div>
          <div>酒水饮料等问题，请致电商家咨询，以商家反馈为准如 部分菜品因时令或其他不可抗因素导致无法提供，商家 会用等价菜品替换，具体事宜请与商家协商</div>
          <div>
            <span v-if="info.orderItemFsm.fsmSetMealConfig.makeAnAppointment === 0">无需预约，消费高峰期可能需要等位</span>
            <span v-else>预约: {{ info.orderItemFsm.fsmSetMealConfig.appointmentOther }}</span>
          </div>
          <div v-if="info.orderItemFsm.fsmSetMealConfig.restrictedUsePerson > 0">每人最多使用{{ info.orderItemFsm.fsmSetMealConfig.restrictedUsePerson }}张套餐团购券</div>
          <div v-if="info.orderItemFsm.fsmSetMealConfig.restrictedUseTable > 0">每桌最多使用{{ info.orderItemFsm.fsmSetMealConfig.restrictedUseTable }}张套餐团购券</div>
        </div>

      </div>
      <div class="conents">
        <div class="lately">备注</div>
        <div class="msg">
          {{ info.orderItemFsm.fsmSetMealConfig.supplementaryNote?info.orderItemFsm.fsmSetMealConfig.supplementaryNote:'无' }}
        </div>

      </div>
      <div style="height: 10px;" />
    </div>
  </div>
</template>

<script>
export default {
  props: {
    info: {
      type: Object,
      default: function() {
        return {}
      }
    }
  },
  data() {
    return {}
  },
  created() {},
  mounted() {},
  methods: {
    // 格式化周几
    formWeekly(val) {
      let week = ['周一', '周二', '周三', '周四', '周五', '周六', '周天']
      let arr = val.split('、')
      let newarr = []
      for (let i = 0; i < week.length; i++) {
        for (let j = 0; j < arr.length; j++) {
          if (week[i] == arr[j]) {
            newarr.push(week[i])
          }
        }
      }
      let result = []
      for (let i = 0; i < week.length; i++) {
        let obj = week[i]
        let isExist = false
        for (let j = 0; j < newarr.length; j++) {
          let aj = newarr[j]
          if (aj == obj) {
            isExist = true
            break
          }
        }
        if (!isExist) {
          result.push(obj)
        }
      }
      const format1 = ['周一', '周二', '周三', '周四', '周五']
      const format2 = ['周一', '周二', '周三', '周四', '周五', '周六']
      if (result == format1) {
        return '周一至周五'
      } else if (result == format2) {
        return '周一至周六'
      } else {
        return result.join('、')
      }
    }
  }
}
</script>

<style scoped lang="scss">
.tips {
  width: 706px;
  background: #ffffff;
  border-radius: 24px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  margin-top: 20px;
  .box {
    width: 93%;
    height: 93%;
    margin: 0 auto;
  }
  .title {
    height: 80px;
    line-height: 80px;
    font-size: 40px;
    font-family: PingFangSC-Medium;
  }
  .conents{
    color: #333333;
    font-size: 28px;
    display: flex;
    margin-bottom: 20px;
    .lately{
      width: 120px;
      text-align-last: justify;
      color: #999999;
      margin-right: 15px;
    }
    .msg{
      width: 500px;
    }
  }
}
</style>
