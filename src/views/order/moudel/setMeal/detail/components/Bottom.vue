<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2022-09-20 16:27:10
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-09-27 11:54:18
-->
<template>
  <div class="bottom">
    <div class="left">
      <div v-if="info.status === 1">距离支付结束</div>
      <div v-if="info.status === 1">
        <van-count-down class="countDownTime" :time="info.payTimeOut" />
      </div>
    </div>
    <div v-if="info.status === 1" class="right" @click="payOrder(info)">
      继续支付￥{{ info.actualPay }}
    </div>
    <div v-else class="right" @click="anotherOrder(info)">
      再来一单
    </div>
  </div>
</template>

<script>
export default {
  props: {
    info: {
      type: Object,
      default: function() {
        return {}
      }
    }
  },
  data() {
    return {}
  },
  created() {},
  mounted() {},
  methods: {
    // 去支付
    payOrder(val) {
      this.$store.state.market.marketData.payradio = ''
      this.$router.push({
        name: 'SetMealOrderPayment',
        query: {
          orderNo: val.orderNo,
          actualPay: val.actualPay,
          marketSn: val.marketSn,
          payAsync: val.payAsync
        }
      })
    },
    anotherOrder(val) {
      this.$router.push({
        name: 'SetMeal',
        query: {
          id: val.marketId,
          type: 2
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
.bottom {
  width: 750px;
  height: 126px;
  background: #ffffff;
  box-shadow: -4px -4px 27px 0px rgba(220, 220, 220, 0.5);
  position: fixed;
  bottom: 0;
  display: flex;
  justify-content: space-between;
  .left{
    margin-left: 46px;
    margin-top: 20px;
    div:nth-child(1){
        font-size: 26px;
        font-weight: 400;
        color: #333333;
    }
    div:nth-child(2){
        font-size: 32px;
        font-weight: 400;
        color: #FF301E;
        font-family: PingFangSC-Medium;
    }
    .countDownTime{
      font-size: 32px !important;
        font-weight: 400 !important;
        color: #FF301E !important;
        font-family: PingFangSC-Medium !important;
    }
  }
  .right{
    width: 328px;
    height: 94px;
    background: linear-gradient(90deg,#ff1e29, #ff5a25);
    border-radius: 47px;
    text-align: center;
    line-height: 94px;
    color: #fff;
    font-size: 40px;
    font-family: PingFangSC-Medium;
    margin-right: 32px;
    margin-top: 16px;
  }
}
</style>
