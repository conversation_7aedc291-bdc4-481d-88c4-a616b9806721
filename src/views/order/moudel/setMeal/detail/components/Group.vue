<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2022-09-19 14:55:46
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-09-26 16:07:50
-->
<template>
  <div class="packageContent">
    <div class="box">
      <div class="title">套餐内容</div>
      <div class="conents">
        <ul v-for="(item,index) in info.orderItemFsm.fsmSetMealDishInfos" :key="index">
          <div class="conents_title">
            <span>{{ item.dishGroupingName }}</span>
            <span v-if="item.dishTotalQuantity!=item.dishChoiceQuantity">·{{ item.dishTotalQuantity }}选{{ item.dishChoiceQuantity }}</span>
          </div>
          <li v-for="(list,index1) in item.setMealDishList" :key="index1">
            <div class="conents_name">·{{ list.dishName }}（{{ list.dishQuantity+list.dishSpecs }}）</div>
            <div class="conents_price">
              <span>￥</span>
              {{ list.dishPrice }}
            </div>
          </li>
        </ul>
      </div>
      <div style="height: 10px;" />

      <!-- <div class="divider" />
      <div class="sum_price">
        <span>总价值</span>
        <span>￥666</span>
      </div> -->
    </div>
  </div>
</template>

<script>
export default {
  props: {
    info: {
      type: Object,
      default: function() {
        return {}
      }
    }
  },
  data() {
    return {}
  },
  created() {},
  mounted() {},
  methods: {}
}
</script>

  <style scoped lang="scss">
  .packageContent {
    width: 706px;
  //   height: 632px;
    background: #ffffff;
    border-radius: 24px;
    margin: 0 auto;
    display: flex;
    align-items: center;
    margin-top: 20px;
    .box {
      width: 93%;
      height: 93%;
      margin: 0 auto;
    }
    .title {
      height: 80px;
      line-height: 80px;
      font-size: 40px;
      font-family: PingFangSC-Medium;
    }
    .conents{
      .conents_title{
          font-size: 30px;
          color: #333333;
          font-family: PingFangSC-Medium;

      }
      li{
          display: flex;
          justify-content: space-between;
          list-style: disc !important;
          margin-top: 10px;
          .conents_name{
              font-size: 28px;
              color: #333333;
          }
          .conents_price{
              font-size: 33px;
              color: #333333;
              font-weight: 500;
              font-family: PingFangSC-Medium;
              span{
                  font-size: 26px;
              }
          }
      }
    }
    .divider {
      width: 100%;
      margin-top: 8px;
      background-image: repeating-linear-gradient(
        90deg,
        #e2e2e2,
        #e2e2e2 0.9174311926605505%,
        transparent 0.9174311926605505%,
        transparent 1.834862385321101%
      );
      height: 3px;
    }
    .sum_price {
      text-align: right;
      height: 70px;
      line-height: 70px;
      font-size: 28px;
      color: #333333;
      font-family: PingFangSC-Medium;
      margin-top: 15px;
      margin-bottom: 10px;
      font-weight: 500;
      span:nth-child(1) {
        margin-right: 20px;
      }
      span:nth-child(2) {
          font-size: 35px;
      }
    }
  }
  </style>
