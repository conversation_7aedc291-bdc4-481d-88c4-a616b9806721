<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2022-09-20 14:52:29
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-09-22 15:43:12
-->
<template>
  <div class="goods">
    <div class="box">
      <div class="center">
        <div class="goods">
          <div class="goods_img">
            <img :src="info.headPicture" alt="">
          </div>
          <div class="goods_info">
            <div class="goods_name">{{ info.setMealName }}</div>
            <div class="goods_time">
              <span>{{ info.orderItemFsm.fsmSetMealConfig.notAvailableWeekly===''?'周一至周天':formWeekly(info.orderItemFsm.fsmSetMealConfig.notAvailableWeekly) }}</span>
              <span v-if="info.orderItemFsm.fsmSetMealConfig.makeAnAppointment === 0">·免预约</span>
              <span v-else>·需预约</span>
            </div>
            <div class="goods_time">随时退 过期自动退</div>
          </div>
        </div>
      </div>
      <div class="bottom">
        <span>小计</span>
        <span>￥</span>
        <span>{{ info.actualPay }}</span>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    info: {
      type: Object,
      default: function() {
        return {}
      }
    }
  },
  data() {
    return {}
  },
  created() {},
  mounted() {},
  methods: {
    // 格式化周几
    formWeekly(val) {
      let week = ['周一', '周二', '周三', '周四', '周五', '周六', '周天']
      let arr = val.split('、')
      let newarr = []
      for (let i = 0; i < week.length; i++) {
        for (let j = 0; j < arr.length; j++) {
          if (week[i] == arr[j]) {
            newarr.push(week[i])
          }
        }
      }
      let result = []
      for (let i = 0; i < week.length; i++) {
        let obj = week[i]
        let isExist = false
        for (let j = 0; j < newarr.length; j++) {
          let aj = newarr[j]
          if (aj == obj) {
            isExist = true
            break
          }
        }
        if (!isExist) {
          result.push(obj)
        }
      }
      const format1 = ['周一', '周二', '周三', '周四', '周五']
      const format2 = ['周一', '周二', '周三', '周四', '周五', '周六']
      if (result == format1) {
        return '周一至周五'
      } else if (result == format2) {
        return '周一至周六'
      } else {
        return result.join('、')
      }
    }
  }
}
</script>

  <style scoped lang="scss">
  .goods {
    width: 710px;
    background: #ffffff;
    border-radius: 16px;
    margin: 0 auto;
    margin-top: 20px;
    .box{
      width: 93%;
      margin: 0 auto;
      overflow: hidden;
      .center{
          .goods{
              height: 160px;
              display: flex;
              margin-top: 21px;
              .goods_img{
                  img{
                      width: 160px;
                      height: 160px;
                      float: left;
                      border-radius: 6px;
                      margin-right: 16px;
                  }
              }
              .goods_info{
                  width: 435px;
                  .goods_name{
                      font-size: 35px;
                      font-family: PingFangSC-Medium;
                      font-weight: 500;
                      color: #333333;
                  }
                  .goods_time{
                      font-size: 26px;
                      color: #999999;
                      margin-top: 15px;
                  }
              }
          }
      }
      .bottom{
          text-align: right;
          span:nth-child(1){
            color: #999999;
            font-size: 26px;
          }
          span:nth-child(2){
            color: #ff301e;
            font-size: 26px;
            font-family: PingFangSC-Medium;
            font-weight: 500;
          }
          span:nth-child(3){
            color: #ff301e;
            font-size: 41px;
            font-family: PingFangSC-Medium;
            font-weight: 500;
          }
      }
    }
  }
  </style>
