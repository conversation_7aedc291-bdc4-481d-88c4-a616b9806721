<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2022-09-20 14:52:29
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-09-27 14:25:33
-->
<template>
  <div class="card">
    <div class="box">
      <div class="top">
        <div class="shop_name">{{ item.marketName }}</div>
        <div class="order_status">
          <div v-if="item.orderStatus === 0" class="order_status_time">
            <span>待付款，剩余</span>
            <van-count-down class="countDownTime" :time="item.payTimeOut" />
          </div>

          <span v-if="item.orderStatus === 10">待使用</span>
          <span v-if="item.orderStatus === 50" style="color: #FF7807;">已完成</span>
          <span v-if="item.orderStatus === 60" style="color: #169D1B;">退款中</span>
          <span v-if="item.orderStatus === 70" style="color: #169D1B;">已退款</span>
          <span v-if="item.orderStatus === 80">已关闭</span>
        </div>
      </div>
      <div class="center" @click="godetail(item)">
        <div class="goods">
          <div class="goods_img">
            <img :src="item.headPicture" alt="">
          </div>
          <div class="goods_info">
            <div class="goods_name">{{ item.setMealName }}</div>
            <div class="goods_time">下单时间：<span style="font-size: 12.5px;">{{ item.createTime }}</span> </div>
            <div class="goods_time">有效期至：<span style="font-size: 12.5px;">{{ item.endTime }}</span></div>
            <div class="goods_time">数量：{{ item.quantity }}</div>
          </div>
          <div class="goods_price">
            <div>￥{{ item.actualPay }}</div>
            <div>
              <span v-if="item.orderStatus === 0" class="no_price">需付款</span>
              <span v-else>实付</span>
            </div>
          </div>
        </div>
      </div>
      <div class="bottom">
        <div v-if="item.orderStatus === 0" class="btn_pay" @click="payOrder(item)">付款</div>
        <div v-if="item.orderStatus === 10||item.orderStatus === 40||item.orderStatus === 80" class="btn_other" @click="anotherOrder(item)">再来一单</div>
        <div v-if="item.orderStatus === 10||item.orderStatus === 20||item.orderStatus === 30||item.orderStatus === 40" class="btn_other" @click="lookCode(item)">查看券码</div>
        <div v-if="item.orderStatus === 70" class="btn_other" @click="goRefund(item)">退款进度</div>
        <div v-if="item.orderStatus === 60" class="btn_other" @click="goRefund(item)">退款进度</div>
        <div v-if="item.orderStatus === 50" class="btn_other" @click="evalute(item)">已完成</div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    item: {
      type: Object,
      default: function() {
        return {}
      }
    }
  },
  data() {
    return {}
  },
  created() {},
  mounted() {},
  methods: {
    payOrder(val) {
      this.$store.state.market.marketData.payradio = ''
      this.$router.push({
        name: 'SetMealOrderPayment',
        query: {
          orderNo: val.orderNo,
          actualPay: val.actualPay,
          marketSn: val.marketSn,
          payAsync: val.payAsync
        }
      })
    },
    // 查看券码
    lookCode(val) {
      this.$router.push({
        name: 'LookCode',
        query: {
          orderNo: val.orderNo
        }
      })
    },
    // 去退款
    goRefund(val) {
      this.$router.push({
        name: 'SetMealorderRefund',
        query: {
          orderNo: val.orderNo,
          name: val.setMealName
        }
      })
    },
    // 详情
    godetail(val) {
      this.$router.push({
        name: 'SetMealorderDetail',
        query: {
          orderNo: val.orderNo
        }
      })
    },
    // 再来一单
    anotherOrder(val) {
      this.$router.push({
        name: 'SetMeal',
        query: {
          id: val.marketId,
          type: 2
        }
      })
    },
    // 评价
    evalute(val) {
      this.$router.push({
        name: 'PublishEvalute',
        query: {
          marketId: val.marketId,
          orderNo: val.orderNo,
          deliverPart: val.orderDeliver.deliverPart,
          deliveryType: val.deliveryType
        }
      })
    }

  }
}
</script>

<style scoped lang="scss">
.card {
  width: 710px;
  min-height: 344px;
  background: #ffffff;
  border-radius: 16px;
  margin: 0 auto;
  margin-top: 20px;
  display: flex;
  align-items: center;
  .box{
    width: 93%;
    margin: 0 auto;
    .top{
        display: flex;
        justify-content: space-between;
        height: 86px;
        line-height: 86px;
        border-bottom: 2px solid #F4F4F4;
        .shop_name{
            font-size: 33px;
            font-family: PingFangSC-Medium;
            font-weight: 500;
            color: #333333;
        }
        .order_status{
            font-size: 28px;
            color: #999999;
        }
        .order_status_time{
          display: flex;
          .countDownTime{
            font-size: 28px;
            color: #FF301E;
            margin-left: 15px;
            margin-top: 25px;
          }
        }
    }
    .center{
        .goods{
            height: 125px;
            display: flex;
            margin-top: 21px;
            .goods_img{
                img{
                    width: 125px;
                    height: 125px;
                    float: left;
                    border-radius: 6px;
                    margin-right: 16px;
                }
            }
            .goods_info{
                width: 435px;
                .goods_name{
                    font-size: 35px;
                    font-family: PingFangSC-Medium;
                    font-weight: 500;
                    color: #333333;
                }
                .goods_time{
                    font-size: 26px;
                    color: #999999;
                }
            }
            .goods_price{
                text-align: right;
                font-size: 28px;
                margin-top: 25px;
                div:nth-child(1){
                    color: #333333;
                    font-size: 30px;
                    font-family: PingFangSC-Medium;
                    font-weight: 500;
                }
                div:nth-child(2){
                    color: #999;
                }
                .no_price{
                    color: #FF301E;
                }
            }
        }
    }
    .bottom{
        height: 102px;
        display: flex;
        align-items: center;
        float: right;
        margin-top: 20px;
        .btn_pay{
            width: 136px;
            height: 56px;
            background: linear-gradient(90deg,#ff1e29, #ff5a25);
            border-radius: 8px;
            text-align: center;
            line-height: 56px;
            font-size: 28px;
            font-family: PingFangSC-Medium;
            font-weight: 500;
            color: #ffffff;
        }
        .btn_other{
            width: 136px;
            height: 56px;
            border: 1px solid #9a9a9a;
            border-radius: 8px;
            text-align: center;
            line-height: 56px;
            font-size: 28px;
            color: #333;
            margin-left: 24px;
        }
    }
  }
}
</style>
