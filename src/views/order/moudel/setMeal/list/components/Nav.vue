<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2022-04-30 13:03:35
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-09-27 11:48:33
-->
<template>
  <div class="content">
    <div class="statusTop">
      <van-nav-bar title="" left-text="">
        <template #title>
          <div>我的套餐团购订单</div>
        </template>
        <template #left>
          <van-icon
            name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/loginV2/left.png"
            size="19"
            @click="goback"
          />
        </template>
        <template #right>
          <div v-if="isRight" class="right_text" @click="goRules">我的商城订单</div>
        </template>
      </van-nav-bar>
    </div>
    <div style="height: 46px" />
  </div>
</template>

<script>
export default {
  components: {},
  props: {
    isLeft: {
      type: <PERSON><PERSON><PERSON>,
      default: false
    },
    isRight: {
      type: Boolean,
      default: false
    },
    message: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
    }
  },
  mounted() {},
  methods: {
    goback() {
      if (this.$route.query.from == 3) {
        this.$router.push('/index')
      } else {
        this.$router.go(-1)
      }
    },
    goRules() {
      this.$router.push({
        name: 'MarketOrder'
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.content {
  .statusTop {
    width: 100%;
    position: fixed;
    left: 0;
    z-index: 3;
    background-color: #fff;
  }

  ::v-deep .van-nav-bar__title {
    font-size: 39px;
    // font-family: PingFangSC-Medium;
    color: #000010;
  }

  ::v-deep .van-nav-bar {
    background: none;
  }

  ::v-deep .van-nav-bar__right {
    font-size: 30px;
  }
  .right_text{
    color: #169D1B;
  }
}
</style>
