<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2022-09-20 14:50:51
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-09-23 10:07:41
-->
<template>
  <div class="home">
    <NavHeight bgc="#fff" />
    <Nav />
    <van-tabs v-model="query.search.status" title-active-color="#169d1b" color="#169d1b" @change="orderList">
      <van-tab title="全部" :name="null" />
      <van-tab title="待付款" :name="1" />
      <van-tab title="待使用" :name="2" />
      <van-tab title="已完成" :name="3" />
      <van-tab title="退款/售后" :name="4" />
    </van-tabs>

    <Card v-for="item in list" :key="item.id" :item="item" />
    <div style="height: 60px;" />

    <Empty v-if="list.length === 0" msg="您还没有相关订单" msg1="去下一单试试吧" type="去逛逛" />

  </div>
</template>

<script>
import { orderList } from '@/api/order/serMeal'
import { Nav, Card } from './components'
import Empty from './components/empty.vue'
import { Toast } from 'vant'
export default {
  components: {
    Nav, Card, Empty
  },
  data() {
    return {
      query: {
        'pageNum': 1,
        'pageSize': 1000,
        'search': {
          'status': null
        }
      },
      list: []
    }
  },
  created() {
    this.orderList()
  },
  methods: {
    orderList() {
      Toast.loading({
        duration: 10000,
        forbidClick: true
      })
      orderList(this.query).then(res => {
        if (res.status === 200) {
          this.list = res.data.list
        }
        setTimeout(() => {
          Toast.clear()
        }, 800)
      })
    }
  }
}
</script>

<style scoped lang="scss">
    .home {}
</style>
