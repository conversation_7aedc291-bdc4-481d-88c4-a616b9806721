<template>
  <!-- 支付方式 -->
  <div class="content">
    <div class="totalMoney">
      <span class="mini">¥</span>
      <span>{{ $route.query.actualPay }}</span>
    </div>
    <div class="payment">
      <van-radio-group v-model="radiovalue">
        <div v-if="payChannel == 1">
          <div v-if="sysPayList[0] == 1 || sysPayList[1] == 1 || sysPayList[2] == 1 || sysPayList[3] == 1">
            <div v-if="paydata[0] == 1 || paydata[1] == 1 || paydata[2] == 1 || paydata[3] == 1" class="payment-line line1">
              <div class="line-left">
                <van-icon name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/pay/balance.png" size="25" />
                <span>账户余额</span>
              </div>
              <div class="line-right">
                <span>￥{{ balance }}</span>
                <van-radio name="1" icon-size="24" checked-color="#5dcb4f" />
              </div>
            </div>
          </div>
        </div>

        <!-- 改造后余额支付 -->
        <div v-if="payChannel == 2">
          <div class="payment-line line3">
            <div class="line-left">
              <van-icon
                name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/pay/balance.png"
                size="25"
              />
              <span>账户余额</span>
            </div>
            <div class="line-right">
              <span>￥ {{ balance }}</span>
              <van-radio
                name="7"
                icon-size="24"
                checked-color="#5dcb4f"
              />
            </div>
          </div>
        </div>
        <div class="line" />
        <div v-if="$route.query.payAsync!=1">
          <div v-if=" sysPayList[0] == 3 || sysPayList[1] == 3 || sysPayList[2] == 3 || sysPayList[3] == 3">
            <div v-if="paydata[0] == 3 || paydata[1] == 3 || paydata[2] == 3 || paydata[3] == 3" class="payment-line line22">
              <div class="line-left">
                <van-icon
                  name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/pay/weixinpay.png"
                  size="25"
                />
                <span>微信支付</span>
              </div>
              <div class="line-right">
                <van-radio
                  name="3"
                  icon-size="24"
                  checked-color="#5dcb4f"
                />
              </div>
            </div>
          </div>
          <div v-if="sysPayList[0] == 2 || sysPayList[1] == 2 || sysPayList[2] == 2 || sysPayList[3] == 2">
            <div v-if="paydata[0] == 2 ||paydata[1] == 2 ||paydata[2] == 2 ||paydata[3] == 2" class="payment-line line2">
              <div class="line-left">
                <van-icon name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/pay/alipay.png" size="25" />
                <span>支付宝支付</span>
              </div>
              <div class="line-right">
                <van-radio name="2" icon-size="24" checked-color="#5dcb4f" />
              </div>
            </div>
          </div>

          <div v-if="sysPayList[0] == 9||sysPayList[1] == 9 || sysPayList[2] == 9 || sysPayList[3] == 9 || sysPayList[4] == 9 || sysPayList[5] == 9">
            <div v-if="paydata[0] == 9 || paydata[1] == 9 || paydata[2] == 9 || paydata[3] == 9|| paydata[4] == 9|| paydata[5] == 9" class="payment-line line3">
              <div class="line-left">
                <van-icon
                  name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/shop/goods/unipay.png"
                  size="29"
                />
                <span>云闪付</span>
              </div>
              <div class="line-right">
                <span />
                <van-radio
                  name="9"
                  icon-size="24"
                  checked-color="#5dcb4f"
                />
              </div>
            </div>
          </div>

        </div>

      </van-radio-group>
    </div>
  </div>
</template>

<script>
import { systemPayList, checkpaylist } from '@/api/takeout'
import { fsmOrderCapital } from '@/api/order/serMeal'
import {
  checkInitPwd
} from '@/api/scan'
export default {
  components: {
  },
  data() {
    return {
      radiovalue: '',
      balance: '',
      sysPayList: '',
      paydata: '',
      payChannel: null
    }
  },
  watch: {
    radiovalue(val) {
      this.$store.state.market.marketData.payradio = val
      if (val == 1 || val == 7) {
        this.checkInitPwd()
      }
    }
  },
  mounted() {
    this.systemPayList()
    this.checkpaylist()
    this.getUserAccount()
  },
  methods: {
    // 检查初始支付密码
    checkInitPwd() {
      let self = this
      checkInitPwd(this.$store.getters.getUserId).then((res) => {
        if (res.status == 200) {
          if (res.data == true) {
            self.$dialog.alert({
              message: '当前为初始支付密码，请尽快修改'
            }).then(() => {
              self.$router.push('/editPayPwd')
            })
          }
        }
      })
    },
    // 获取账户余额
    getUserAccount() {
      this.$toast.loading({
        duration: 0, // 持续展示 toast
        forbidClick: true,
        message: ''
      })
      fsmOrderCapital(this.$route.query.orderNo)
        .then((res) => {
          this.$toast.clear()
          if (res.status == 200) {
            this.balance = res.data.balance
            this.payChannel = res.data.payChannel
          } else {
            this.$toast(res.message)
          }
        })
    },
    // 获取系统支付方式
    systemPayList() {
      systemPayList()
        .then(res => {
          if (res.status == 200) {
            this.sysPayList = res.data
          } else {
            this.$toast(res.message)
          }
        })
    },
    // 获取店铺支付方式
    checkpaylist() {
      checkpaylist(this.$route.query.marketSn)
        .then(res => {
          if (res.status == 200) {
            this.paydata = res.data
          } else {
            this.$toast(res.message)
          }
        })
    }
  }
}
</script>

<style lang="scss" scoped>
.content {
  width: 710px;
  box-sizing: border-box;
  margin: 0 auto;
  .totalMoney {
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    font-size: 76px;
    color: #000010;
    margin: 70px 0;
    .mini {
      font-size: 50px;
      color: #4c4c44;
      margin-right: 16px;
    }
  }
  .payment {
    .payment-line {
      width: 710px;
      display: flex;
      background-color: #fff;
      justify-content: space-between;
      align-items: center;
      font-size: 34px;
      color: #000010;
      padding: 0 20px;
      box-sizing: border-box;
      .line-left {
        display: flex;
        align-items: center;
        > span {
          margin-left: 24px;
        }
      }
      .line-right {
        display: flex;
        align-items: center;
        > span {
          margin-right: 70px;
        }
      }
    }
    .line1 {
      height:130px;
      border-radius: 16px;
    }
    .line2 {
      height:126px;
    }
    .line22 {
      height:126px;
      border-radius: 16px;
    }
    .line3 {
      height:126px;
      border-radius: 0 0 16px 16px;
    }
    .line {
      width: 100%;
      height:16px;
      background-color: #f3f4f9;
    }
  }
  ::v-deep .van-radio__icon .van-icon {
    border: 1px solid #CCCCCC;
  }
}
</style>
