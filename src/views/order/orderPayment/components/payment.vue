<template>
  <!-- 支付方式 -->
  <div class="content">
    <div class="totalMoney">
      <span class="mini">¥</span>
      <span>{{ $route.query.actualPay }}</span>
    </div>
    <div class="payment">
      <van-radio-group v-model="radiovalue">
        <div v-for="(item,index) in paydata" :key="index" class="payment-line line2">
          <div class="line-left">
            <van-icon v-if="item.payStyle === 1||item.payStyle === 7" name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/pay/balance.png" size="23px" />
            <van-icon v-if="item.payStyle === 3" name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/pay/weixinpay.png" size="23px" />
            <van-icon v-if="item.payStyle === 2" name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/pay/alipay.png" size="23px" />
            <van-icon v-if="item.payStyle === 4" name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/pay/dragon.png" size="23px" />
            <van-icon v-if="item.payStyle === 9" name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/shop/goods/unipay.png" size="23px" />
            <span>{{ item.payStyleName==='二类户余额'?'余额':item.payStyleName }}</span>
          </div>
          <div class="line-right">
            <span v-if="item.payStyle === 1||item.payStyle === 7">￥ {{ balance }}</span>
            <van-radio :name="item.payStyle" icon-size="19px" checked-color="#5dcb4f" />
          </div>
        </div>

      </van-radio-group>
    </div>
  </div>
</template>

<script>
import { orderUserAccount } from '@/api/takeout'
import {
  getPayStyle
} from '@/api/pay'
import {
  checkInitPwd
} from '@/api/scan'
export default {
  components: {
  },
  data() {
    return {
      radiovalue: '',
      balance: '',
      sysPayList: '',
      paydata: [],
      payChannel: null
    }
  },
  watch: {
    radiovalue(val) {
      this.$store.state.market.marketData.payradio = val
      if (val == 1 || val == 7) {
        this.checkInitPwd()
      }
    }
  },
  mounted() {
    this.getUserAccount()
    this.getPayStyle()
  },
  methods: {
    // 检查初始支付密码
    checkInitPwd() {
      let self = this
      checkInitPwd(this.$store.getters.getUserId).then((res) => {
        if (res.status == 200) {
          if (res.data == true) {
            self.$dialog.alert({
              message: '当前为初始支付密码，请尽快修改'
            }).then(() => {
              self.$router.push('/editPayPwd')
            })
          }
        }
      })
    },
    // 获取账户余额
    getUserAccount() {
      this.$toast.loading({
        duration: 0, // 持续展示 toast
        forbidClick: true,
        message: ''
      })
      orderUserAccount(this.$route.query.orderNo)
        .then((res) => {
          this.$toast.clear()
          if (res.status == 200) {
            this.balance = res.data.balance
            this.payChannel = res.data.payChannel
          } else {
            this.$toast(res.message)
          }
        })
    },
    // 获取支付方式
    getPayStyle() {
      getPayStyle({
        marketId: this.$route.query.marketId,
        orderType: this.$route.query.orderType,
        poolId: this.$store.getters.getRegionId
      }).then(res => {
        if (res.status == 200) {
          this.paydata = res.data
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.content {
  width: 710px;
  box-sizing: border-box;
  margin: 0 auto;
  .totalMoney {
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    font-size: 76px;
    color: #000010;
    margin: 70px 0;
    .mini {
      font-size: 50px;
      color: #4c4c44;
      margin-right: 16px;
    }
  }
  .payment {
    .payment-line {
      width: 710px;
      display: flex;
      background-color: #fff;
      justify-content: space-between;
      align-items: center;
      font-size: 34px;
      color: #000010;
      padding: 0 20px;
      box-sizing: border-box;
      .line-left {
        display: flex;
        align-items: center;
        > span {
          margin-left: 24px;
        }
      }
      .line-right {
        display: flex;
        align-items: center;
        > span {
          margin-right: 70px;
        }
      }
    }
    .line1 {
      height:130px;
      border-radius: 16px;
    }
    .line2 {
      height:126px;
    }
    .line22 {
      height:126px;
      border-radius: 16px;
    }
    .line3 {
      height:126px;
      border-radius: 0 0 16px 16px;
    }
    .line {
      width: 100%;
      height:16px;
      background-color: #f3f4f9;
    }
  }
  ::v-deep .van-radio__icon .van-icon {
    border: 1px solid #CCCCCC;
  }
}
</style>
