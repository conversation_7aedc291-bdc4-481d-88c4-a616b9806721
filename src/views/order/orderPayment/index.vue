<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-05-31 19:46:16
 * @LastEditors: 孙立政
 * @LastEditTime: 2021-06-25 10:02:36
-->
<template>
  <div class="home">
    <!-- 头部 -->
    <NavHeight bgc="#fff" />
    <Top />
    <!-- 支付方式 -->
    <Payment />
    <!-- 确认支付 -->
    <Confirm />
  </div>
</template>

<script>
import Top from './components/top'
import Payment from './components/payment'
import Confirm from './components/confirm'
export default {
  components: {
    Top,
    Payment,
    Confirm
  },
  data() {
    return {

    }
  },
  created() {

  },
  mounted() {

  },
  methods: {

  }
}
</script>

<style scoped lang="scss">
</style>
