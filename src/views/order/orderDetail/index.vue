<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-06-01 14:58:21
 * @LastEditors: 孙立政
 * @LastEditTime: 2021-12-06 16:14:01
-->
<template>
  <div class="home">
    <NavHeight bgc="#f9f9f9" />
    <!-- 返回按钮 -->
    <div class="arrow-back" @click="goBack">
      <van-icon name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/user/icon/arrow-black.png" size="21px" style="margin-left:17px" />
    </div>
    <div class="occupyHeight" />
    <div v-if="$route.query.type != 12&&$route.query.type != 50">
      <!-- 地图 -->
      <Amap v-if="orderData.status == 3&&orderData.orderDeliver.deliverPart==2" :mapdata="orderData" />
      <!-- 等待支付 -->
      <WaitPay v-if="orderData.status == 0" :order-data="orderData" @reloadMethod="reloadMethod" />
      <!-- 订单状态 -->
      <Status :order-data="orderData" />
      <!-- 自取订单 -->
      <TakeItFrom v-if="orderData.deliveryType==2" :order-data="orderData" />
      <!-- 下单地址信息 -->
      <InfoAddr v-else :order-data="orderData" @reloadMethod="reloadMethod" />
      <!-- 退款信息 -->
      <Refund :order-data="orderData" />
      <!-- 商品 -->
      <Goods :order-data="orderData" />
      <!-- 配送信息,订单信息 -->
      <InfoOrder :order-data="orderData" />
    </div>

    <div v-if="$route.query.type == 12">
      <div style="height:15px" />
      <!-- <PlatformStatus :order-data="orderData" /> -->
      <!-- 下单地址信息 -->
      <PlatformInfoAddr :order-data="orderData" />
      <!-- 平台商品 -->
      <PlatformGoods :order-data="orderData" />
    </div>

    <QyLxy v-if="$route.query.type == 50&&!loadingShow" :order-data="orderData" />

    <Loading :show="loadingShow" />
  </div>
</template>

<script>
import { orderDetail, selfEmployed } from '@/api/order'
import Status from './components/status'
import TakeItFrom from './components/takeItFrom'
import Refund from './components/refund'
import Goods from './components/goods'
import InfoOrder from './components/infoOrder'
import InfoAddr from './components/infoAddr'
import WaitPay from './components/waitPay'
import Loading from '@/components/Loading/index'
import Amap from './components/amap.vue'

import PlatformGoods from './components/platformGoods'
import PlatformInfoAddr from './components/platformInfoAddr'
import QyLxy from './components/qy-lxy'
// import PlatformStatus from './components/platformStatus'

export default {
  components: {
    Amap,
    Status,
    Refund,
    Goods,
    InfoOrder,
    InfoAddr,
    WaitPay,
    Loading,
    PlatformGoods,
    PlatformInfoAddr,
    TakeItFrom,
    QyLxy
    // PlatformStatus
  },
  data() {
    return {
      orderData: {
        orderAddress: {
          userAddress: ''
        },
        orderDeliver: {}
      },
      loadingShow: true
    }
  },
  created() {
    this.getDetail()
  },
  mounted() {
    document.body.scrollTop = 0
    document.documentElement.scrollTop = 0
  },
  methods: {
    goBack() {
      // 判断是否外卖进入
      if (this.$route.query.from == 1) {
        this.$router.push({
          name: 'Order'
        })
      } else {
        this.$router.go(-1)
      }
    },
    // 获取详情
    getDetail() {
      if (this.$route.query.type != 12) {
        orderDetail(this.$route.query.orderNo).then((res) => {
          this.loadingShow = false
          if (res.status == 200) {
            this.orderData = res.data
          }
        })
      } else {
        selfEmployed(this.$route.query.orderNo).then((res) => {
          this.loadingShow = false
          if (res.status == 200) {
            this.orderData = res.data
          }
        })
      }
    },
    // 子组件刷新详情
    reloadMethod() {
      this.getDetail()
    }
  }
}
</script>

<style scoped lang="scss">
    .home {
        .arrow-back {
            width: 100%;
            height: 92px;
            line-height: 92px;
            position: fixed;
            left: 0;
            // top: 0;
            z-index: 999;
            // background-color: #f5f5f5;
        }
        .occupyHeight {
            height: 92px;
        }
    }
</style>
