<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: zhaoyu<PERSON>
 * @Date: 2021-06-01 15:13:57
 * @LastEditors: zhaoyuxin
 * @LastEditTime: 2021-06-01 16:29:52
-->
<template>
  <div class="home">
    <!-- 配送信息 -->
    <div v-if="orderData.orderDeliver.deliverPart>0" class="content">
      <div class="info-cell">
        <div class="cell-name">
          配送信息
        </div>
        <div class="cell-line">
          <span>期望时间</span>
          <div style="color:#000">
            <span>立即配送</span>
          </div>
        </div>
        <div class="cell-line">
          <span>配送地址</span>
          <div>
            <span>{{ orderData.orderAddress.userAddress.split(' ')[0] }}/{{ orderData.orderAddress.userName }}/{{ orderData.orderAddress.userMobile }}</span>
          </div>
        </div>
        <div class="cell-line">
          <span>配送方式</span>
          <div>
            <span v-if="orderData.orderDeliver.deliverPart == 2||orderData.orderDeliver.deliverPart == 3">配送骑手</span>
            <span v-if="orderData.orderDeliver.deliverPart == 1">商家配送</span>
          </div>
        </div>
        <div v-if="orderData.orderDeliver.deliverPart == 2||orderData.orderDeliver.deliverPart == 3" class="cell-line">
          <span>配送骑手</span>
          <div>
            <span>{{ orderData.orderDeliver.courierName }}</span>
          </div>
        </div>
        <div v-if="orderData.orderDeliver.deliverPart == 2||orderData.orderDeliver.deliverPart == 3" class="cell-line">
          <span>骑手电话</span>
          <div>
            <span>{{ orderData.orderDeliver.courierMobile }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 订单信息 -->
    <div class="content">
      <div class="info-cell">
        <div class="cell-name">
          订单信息
        </div>
        <div class="cell-line">
          <span>订单号</span>
          <div>
            <span>{{ orderData.orderNo }}</span>
            <!-- <div class="copy"></div> -->
          </div>
        </div>
        <div class="cell-line">
          <span>下单时间</span>
          <div>
            <span>{{ orderData.createTime }}</span>
          </div>
        </div>
        <div v-if="orderData.payTime!=null" class="cell-line">
          <span>支付时间</span>
          <div>
            <span>{{ orderData.payTime }}</span>
          </div>
        </div>
        <!-- <div class="cell-line">
				<span>取消时间</span>
				<div>
					<span>{{orderData.cancelTime}}</span>
				</div>
			</div> -->
        <div v-if="orderData.note!=''" class="cell-line">
          <span>备注</span>
          <div class="beizhu">
            <span>{{ orderData.note }}</span>
            <!-- <van-icon name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/order/right.png" size=".22rem" /> -->
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    orderData: {
      type: Object,
      default: function() {
        return {}
      }
    }
  },
  data() {
    return {}
  },
  mounted() {},
  methods: {}
}
</script>

<style lang="scss" scoped>
.home {
  padding-bottom: 30px;
    .content {
        width: 710px;
        margin: 0 auto 12px;
        background-color: #fff;
        border-radius: 20px;
        .info-cell {
            margin: 0 24px;
            .cell-name {
                height: 79px;
                line-height: 79px;
                border-bottom: 1px solid #f4f4f4;
                color: #333;
                font-size: 32px;
                font-family: PingFangSC;
                margin-bottom:30px;
            }
            .cell-line {
                display: flex;
                justify-content: space-between;
                padding: 12px 0;
                font-size: 26px;
                >span {
                    color: #999;
                }
                >div {
                    width: 60%;
                    text-align: right;
                    color: #333;
                }
            }
        }
    }
}
</style>
