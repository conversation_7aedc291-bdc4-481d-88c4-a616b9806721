<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: zhaoyu<PERSON>
 * @Date: 2021-06-01 15:06:16
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-08-17 16:00:16
-->
<template>
  <!-- 商品信息 -->
  <div class="home">
    <div class="info-cell">
      <div class="cell-name" @click="goShop">
        <span>{{ orderData.marketName | ellipsis(10) }}</span>
        <van-icon name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/order/right.png" size="10px" style="margin-top:20px" />
      </div>
      <div v-for="(item, index) in orderData.orderGoodsList" :key="index" class="cell-line">
        <div class="cell-line-left">
          <div class="cell-line-img">
            <img :src="item.skuImage + '?x-oss-process=image/resize,w_700/format,jpg/quality,q_80'" style="width: 100%;height: 100%;">
          </div>
          <div class="cell-line-goods">
            <div class="line-name">
              {{ item.goodsName | ellipsis(10) }}
            </div>
            <div class="line-tag">
              {{ item.skuName| ellipsis(15) }}
            </div>
            <div class="line-count">×<span>&nbsp;{{ item.skuQuantity }}</span></div>
          </div>
        </div>
        <div class="cell-line-right">
          <div class="price">
            <div class="small">¥</div>
            {{ item.sellPrice }}
          </div>
          <div v-if="item.sellPrice!=item.originPrice" class="oriprice">
            <div class="small">¥</div>
            {{ item.originPrice }}
          </div>
        </div>
      </div>
      <!-- <div class="readmore" v-if="temparr.length >= 3">
				<span>展开更多</span>
				<van-icon name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/order/bottom.png" size=".2rem" />
			</div> -->
      <div class="orderfree">
        <div class="flex">
          <span>配送费</span>
          <div>
            <div class="small">¥</div>
            {{ orderData.postFee }}
          </div>
        </div>
        <div class="flex">
          <span>包装费</span>
          <div>
            <div class="small">¥</div>
            {{ orderData.packageTotalPrice }}
          </div>
        </div>
        <div v-if="orderData.goodsDiscountAmount>0" class="flex">
          <span>店铺商品优惠</span>
          <div>
            <div class="small">- ¥</div>
            {{ orderData.goodsDiscountAmount }}
          </div>
        </div>
        <div v-if="orderData.couponsAmount>0" class="flex">
          <span>优惠券</span>
          <div>
            <div class="small">- ¥</div>
            {{ orderData.couponsAmount }}
          </div>
        </div>
      </div>
      <div class="all">
        <div v-if="discounts>0" class="cutprice">
          <span>总优惠</span>
          <div class="small red">¥</div>
          <span class="red">{{ orderData.totalDiscountAmount }}</span>
        </div>
        <div class="allprice">
          <span>合计</span>
          <div class="small">¥</div>
          <span>{{ orderData.actualPay }}</span>
        </div>
      </div>

    </div>
    <div class="concat" @click="CallPhone(orderData.orderAddress.marketMobile)">
      <van-icon name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/order/smalltel.png" size="13px" />
      <span>联系商家</span>
    </div>

    <van-popup v-model="postShow" position="bottom" :style="{ height: '50%' }" closeable>
      <div class="post">
        <van-steps direction="vertical" :active="0">
          <van-step v-for="(item,index) in postData" :key="index">
            <h3>{{ item.status }}</h3>
            <p>{{ item.time }}</p>
          </van-step>
        </van-steps>
      </div>
    </van-popup>
  </div>
</template>

<script>
import NP from 'number-precision'
export default {
  props: {
    orderData: {
      type: Object,
      default: function() {
        return {}
      }
    }
  },
  data() {
    return {
      postShow: false,
      postData: []
    }
  },
  computed: {
    discounts() {
      return NP.minus(Number(this.orderData.totalPay), Number(this.orderData.actualPay))
    }
  },
  mounted() {
  },
  methods: {
    // 拨打电话
    CallPhone(data) {
      AlipayJSBridge.call('CallPhone', {
        phoneNum: data
      }, function(result) {})
    },
    goShop() {
      this.$store.state.cart.cartData[0].goodsList = []
      this.$router.push({
        name: 'Shop',
        query: {
          id: this.orderData.marketId
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.home {
    width: 710px;
    margin: 0 auto 12px;
    background-color: #fff;
    border-radius: 20px;
    .info-cell {
        margin: 0 24px;
        .cell-name {
            height: 93px;
            line-height: 93px;
            border-bottom: 1px solid #f4f4f4;
            color: #2c2c2c;
            font-size: 32px;
            font-family: PingFangSC;
            margin-bottom: 30px;
            >span {
                margin-right: 20px;
            }
        }
        .cell-line {
            display: flex;
            justify-content: space-between;
            height: 150px;
            font-size: 30px;
            color: #000010;
            .cell-line-left {
                display: flex;
                justify-content: space-between;
                padding-top: 20px;
                .cell-line-img {
                    width: 100px;
                    height: 104px;
                    border-radius:8px;
                    overflow: hidden;
                    margin-right:16px;
                }
                .cell-line-goods {
                    color: #000010;
                    .line-name {
                        font-size: 30px;
                    }
                    .line-tag {
                        font-size: 22px;
                        opacity: 0.47;
                    }
                    .line-count {
                        font-size:24px;
                        color: #999999;
                    }
                }
            }
        }
        .cell-line-right {
            text-align: right;
            padding-top: 20px;
            .price {
                display: flex;
                justify-content: flex-end;
                color: #000000;
                font-size: 32px;
                font-family: PingFangSC;
            }
            .oriprice {
                display: flex;
                justify-content: flex-end;
                font-size: 24px;
                font-family: PingFang SC;
                color: #b6b4b4;
                text-decoration: line-through;
            }
        }
        // .readmore {
        // 	text-align: center;
        // 	font-size: 26px;
        // 	font-family: PingFang SC;
        // 	color: #000010;
        // 	opacity: 0.8;
        // }
        .orderfree {
            font-size: 26px;
            color: #000010;
            border-bottom: 1px solid #f4f4f4;
            padding-top: 28px;
            >.flex {
                padding: 12px 0;
                display: flex;
                justify-content: space-between;
                >div {
                    display: flex;
                    justify-content: space-between;
                    font-weight: bold;
                }
            }
        }
        .all {
            display: flex;
            justify-content: flex-end;
            align-items: center;
            height: 106px;
            font-size: 26px;
            color: #000000;
            .allprice {
                display: flex;
                align-items: center;
                margin-left: 32px;
                color: #42424d;
                >span:nth-of-type(1) {
                    margin-right: 8px;
                }
                >span:nth-of-type(2) {
                    font-size: 42px;
                    margin-left: 8px;
                    color: #010101;
                    font-weight: bold;
                }
            }
            .cutprice {
                display: flex;
                align-items: center;
                >span:nth-of-type(1) {
                    margin-right: 8px;
                }
            }
        }
    }
    .concat {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 82px;
        line-height: 82px;
        background: #F5F5F5;
        font-size: 28px;
        color: #333;
        >span {
            margin-left:14px;
        }
    }
    .post{
      width: 90%;
      margin: 0 auto;
      margin-top: 70px;
    }
}
.small {
    transform: scale(0.78);
    font-weight: normal;
}
.red {
    color: #fb4c58;
    font-size: 30px;
}
</style>
