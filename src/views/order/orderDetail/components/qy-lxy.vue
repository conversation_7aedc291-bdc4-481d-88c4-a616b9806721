<template>
  <!-- 庆元疗休养信息 -->
  <div class="home">
    <div class="info-cell">
      <div class="cell-name">
        <span>消费信息</span>
      </div>
      <div class="cell-line">
        <span>订单号</span>
        <div>
          <span>{{ orderData.orderNo }}</span>
        </div>
      </div>
      <div class="cell-line">
        <span>下单时间</span>
        <div>
          <span>{{ orderData.payTime }}</span>
        </div>
      </div>
      <div class="cell-line">
        <span>消费金额</span>
        <div>
          <span style="font-weight: bold;">￥{{ orderData.totalPay }}</span>
        </div>
      </div>
    </div>
    <div class="info-cell">
      <div class="cell-name">
        <span>消费人员信息({{ orderData.note.split('&')[0] }}人)</span>
      </div>
      <div class="info-body">
        {{ orderData.note.split('&')[1] }}
      </div>
    </div>
  </div>
</template>

<script>

export default {
  props: {
    orderData: {
      type: Object,
      default: function() {
        return {}
      }
    }
  },
  data() {
    return {
      notes: []
    }
  },
  mounted() {

  },
  methods: {}
}
</script>

<style lang="scss" scoped>
.home {
    margin-top: 30px;
    .info-cell {
        margin: 0 24px;
        background-color: #fff;
        border-radius: 20px;
        padding: 20px;
        margin-bottom: 20px;
        .cell-name {
            height: 89px;
            line-height: 89px;
            color: #2c2c2c;
            font-size: 32px;
            font-family: PingFangSC;
            display: flex;
            justify-content: space-between;
            font-weight: bold;
        }
        .info-body{
            padding: 23px 0;
            font-size: 28px;
            color: #333;
        }

        .cell-line {
                display: flex;
                justify-content: space-between;
                padding: 12px 0;
                font-size: 26px;
                >span {
                    color: #999;
                }
                >div {
                    width: 60%;
                    text-align: right;
                    color: #333;
                }
            }

    }

}
</style>
