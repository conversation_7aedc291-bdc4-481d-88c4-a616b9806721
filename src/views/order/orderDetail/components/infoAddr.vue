<template>
  <!-- 订单基本信息 -->
  <div class="home">
    <div class="info-cell">
      <div class="cell-name">
        <span>订单基本信息</span>
        <span v-if="orderData.status==0||orderData.status==1||orderData.status==2||orderData.status==3" @click="addressShowTrue">修改地址</span>
      </div>
      <div v-if="orderData.status==2||orderData.status==3" class="cell-line">
        <span>预计送达时间</span>
        <div>
          <span v-if="orderData.orderDeliver.expectFinishTime!=null">{{ orderData.orderDeliver.expectFinishTime }}</span>
          <span v-else>/</span>
        </div>
      </div>
      <div class="cell-line">
        <span>地址</span>
        <div>
          <span>{{ orderData.orderAddress.userAddress.split(' ')[0] }}</span>
        </div>
      </div>
      <div class="cell-line">
        <span>电话</span>
        <div>
          <span>{{ orderData.orderAddress.userMobile }}</span>
        </div>
      </div>
      <div class="cell-line">
        <span>姓名</span>
        <div>
          <span>{{ orderData.orderAddress.userName }}</span>
        </div>
      </div>
    </div>
    <!-- 选择地址弹框 -->
    <van-popup v-model="addressShow" position="bottom" round closeable :style="{ width: '100%', height: '350px', }">
      <div class="box">
        <div class="title">选择收货地址</div>
        <div class="close-icon" @click="addressShow=false">
          <van-icon name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/order/closeicon.png" size="15" />
        </div>
        <div class="body">
          <van-radio-group v-model="radiovalue" @change="radioChange">
            <div class="address-pop" style="margin-top: 50px;">
              <div v-for="(item,index) in temparr" :key="index" class="item">
                <div class="item-left">
                  <van-radio :name="index" icon-size="19" checked-color="#5dcb4f" />
                  <div class="item-detail">
                    <div class="">{{ item.address | ellipsis(20) }}</div>
                    <div class="">
                      <span style="margin-right:26px">{{ item.username }}</span>
                      <span>{{ item.mobile }}</span>
                    </div>
                  </div>
                </div>
                <div class="item-right" @click="editAddress(item)">
                  <van-icon name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/order/edit.png" size="17" />
                </div>
              </div>
            </div>
            <div style="height: 100px;" />
          </van-radio-group>
        </div>

        <div class="newAddr" @click="newAddr">
          <div>
            <van-icon name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/order/plus.png" size="20" />
            <span style="margin-left:24px">新增收货地址</span>
          </div>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script>
import {
  updateAddressList,
  updateAddress
} from '@/api/order'
export default {
  props: {
    orderData: {
      type: Object,
      default: function() {
        return {}
      }
    }
  },
  data() {
    return {
      addressShow: false,
      temparr: [],
      radiovalue: '',
      temparrNo: ''
    }
  },
  mounted() {},
  methods: {
    addressShowTrue() {
      this.getList()
    },
    // 获取地址列表
    getList() {
      this.$toast.loading({
        duration: 0, // 持续展示 toast
        forbidClick: true
      })
      let orderNo = this.orderData.orderNo
      updateAddressList(orderNo).then(res => {
        if (res.status == 200) {
          this.$toast.clear()
          this.addressShow = true
          this.temparr = res.data.withinDistance
          this.temparrNo = res.data.beyondDistance
        } else {
          this.$toast(res.message)
          setTimeout(() => {
            this.$toast.clear()
          }, 1500)
        }
      })
    },
    radioChange(e) {
      let self = this
      this.$dialog.confirm({
        title: '确认',
        message: '是否修改地址？'
      }).then(() => {
        self.okEdit(self.temparr[e].id)
      }).catch(() => {})
    },
    okEdit(data) {
      let newdata = {
        'addressId': data,
        'orderNo': this.orderData.orderNo
      }
      updateAddress(newdata).then(res => {
        if (res.status == 200) {
          this.addressShow = false
          this.$toast('修改成功')
          this.$emit('reloadMethod')
        } else {
          this.$toast(res.message)
        }
      })
    },
    // 新增地址
    newAddr() {
      this.$router.push({ name: 'AddressAdd' })
    },
    editAddress(item) { // 修改地址
      this.$router.push(
        { name: 'AddressEdit',
          query: {
            id: item.id
          }
        })
    }
  }
}
</script>

<style lang="scss" scoped>
.home {
    width: 710px;
    margin: 0 auto 12px;
    background-color: #fff;
    border-radius:20px;
    .info-cell {
        margin: 0 24px;
        .cell-name {
            height: 89px;
            line-height: 89px;
            color: #2c2c2c;
            font-size: 32px;
            font-family: PingFangSC;
            display: flex;
            justify-content: space-between;
            span:nth-child(2){
                font-size: 28px;
                text-decoration: underline;
                color: #7C7C7C;
            }
        }
        .cell-line {
            display: flex;
            justify-content: space-between;
            padding: 20px 0;
            font-size: 28px;
            .van-icon {
                top: 4px;
                margin-left: 14px;
            }
            >span {
                color: #7F7F87;
            }
            >div {
                width: 74%;
                text-align: right;
                color: #000010;
            }
        }
    }
    .box {
        .title {
            position: absolute;
            top: 0;
            z-index: 10;
            width: 100%;
            box-sizing: border-box;
            text-align: center;
            height: 104px;
            line-height: 104px;
            font-size: 32px;
            color: #000010;
            border-radius: 20px 20px 0 0;
            background: #fff
        }
        .body{
            width: 100%;
            height: 600px;
            overflow-y: auto;
        }
        .close-icon {
            position: fixed;
            bottom: 625px;
            right: 40px;
            z-index: 10;
        }
        .address-pop {
            margin: 4px 42px 20px 18px;
            .item {
                display: flex;
                justify-content: space-between;
                align-items: center;
                font-size: 30px;
                color: #000010;
                height: 130px;
                .item-left {
                    display: flex;
                    align-items: center;
                    .item-detail {
                        margin-left: 22px;
                        >div:nth-of-type(1) {
                            font-family: PingFangSC;
                            color: #33333f;
                            margin-bottom: 4px;
                        }
                        >div:nth-of-type(2) {
                            font-family: PingFang SC;
                            font-size: 22px;
                            color: #696969;
                        }
                    }
                }
                .item-right {
                    display: flex;
                    align-items: center;
                }
            }
        }
        .newAddr {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            background-color: #fff;
            color: #000010;
            font-size: 30px;
            padding: 0 0 22px;
            >div {
                display: flex;
                align-items: center;
                justify-content: center;
                width: 684px;
                height: 76px;
                line-height: 76px;
                background-color: #F5F5F5;
                border-radius: 10px;
                margin: 0 auto;
            }
        }
        ::v-deep .van-radio__icon .van-icon {
            border: 1px solid #5dcb4f;
        }
    }
}
</style>
