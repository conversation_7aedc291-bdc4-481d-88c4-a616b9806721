<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-06-01 15:13:57
 * @LastEditors: 孙立政
 * @LastEditTime: 2021-08-26 18:21:03
-->
<template>
  <div class="home">
    <!-- 自取信息 -->
    <div class="content">
      <div class="info-cell">
        <div class="cell-name">
          自取信息
        </div>
        <div v-if="orderData.marketCount!=null" class="cell-line onNum">
          <span>取餐码</span>
          <div>
            <span>{{ orderData.marketCount }}号</span>
          </div>
        </div>
        <div class="cell-line">
          <span>自取时间</span>
          <div>
            <span>{{ orderData.orderDeliver.expectFinishTime }}</span>
          </div>
        </div>
        <div class="cell-line">
          <span>商家地址</span>
          <div v-if="!isAmap" @click="goMarket(orderData)">
            <span>{{ orderData.orderAddress.marketAddress }}</span>
            <img class="mapIcon" src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/order/gomap.png" alt="">
          </div>
          <div v-else v-clipboard:copy="orderData.orderAddress.marketAddress" v-clipboard:success="onCopy">
            <span>{{ orderData.orderAddress.marketAddress }}</span>
            <img class="mapIcon" src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/order/gomap.png" alt="">
          </div>
        </div>
        <div class="cell-line">
          <span>预留电话</span>
          <div @click="CallPhone(orderData.orderAddress.userMobile)">
            <span>{{ orderData.orderAddress.userMobile }}</span>
          </div>
        </div>
        <div style="height:15px" />
      </div>
    </div>
    <!-- 导航 -->
    <van-popup v-model="showPop" position="bottom" round>
      <div class="mapList">
        <div @click="openMap(1)">百度导航</div>
        <div @click="openMap(2)">高德导航</div>
        <div @click="showPop=false">取消</div>
      </div>
    </van-popup>
  </div>
</template>

<script>
export default {
  props: {
    orderData: {
      type: Object,
      default: function() {
        return {}
      }
    }
  },
  data() {
    return {
      isAmap: false,
      mapData: '',
      showPop: false
    }
  },
  mounted() {
    // 判断是系统环境
    var u = navigator.userAgent
    var isiOS = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/) // ios终端
    if (isiOS) {
      this.isAmap = true
    }
  },
  methods: {
    // 拨打电话
    CallPhone(data) {
      AlipayJSBridge.call('CallPhone', {
        phoneNum: data
      }, function(result) {})
    },
    // 打开地图
    openMap(val) {
      let self = this
      if (val == 1) {
        var urlBaiduMap =
						`baidumap://map/marker?location=${this.mapData.bd_lat},${this.mapData.bd_lng}&title=${this.mapData.marketName}&content=${this.mapData.marketName}&src=Hello%20uni-app`
        AlipayJSBridge.call(
          'IsAvailable', {
            packageName: 'com.baidu.BaiduMap'
          },
          function(result) {
            if (result.available == true) {
              window.location.href = urlBaiduMap
            } else {
              self.$toast('未安装百度地图')
            }
          }
        )
      } else {
        var urlAmap =
						`androidamap://viewMap?sourceApplication=${this.mapData.marketName}&poiname=${this.mapData.marketName}&lat=${this.mapData.gd_lat}&lon=${this.mapData.gd_lng}&dev=0`

        var iosAmap =
						`iosamap://viewMap?sourceApplication=${this.mapData.marketName}&poiname=${this.mapData.marketName}&lat=${this.mapData.gd_lat}&lon=${this.mapData.gd_lng}&dev=0`
        var u = navigator.userAgent
        var isiOS = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/) // ios终端
        if (!isiOS) {
          AlipayJSBridge.call(
            'IsAvailable', {
              packageName: 'com.autonavi.minimap'
            },
            function(result) {
              if (result.available == true) {
                window.location.href = urlAmap
              } else {
                self.$toast('未安装高德地图')
              }
            }
          )
        } else {
          window.location.href = iosAmap
        }
      }
    },
    onCopy(e) {
      this.$toast({
        duration: 5000,
        forbidClick: false,
        message: '复制成功,请打开地图应用,粘贴店铺地址进行导航'
      })
    },
    // 导航店铺
    goMarket(data) {
      // 高德转百度坐标
      function bd_encrypt(gg_lng, gg_lat) {
        var X_PI = Math.PI * 3000.0 / 180.0
        var x = gg_lng
        var y = gg_lat
        var z = Math.sqrt(x * x + y * y) + 0.00002 * Math.sin(y * X_PI)
        var theta = Math.atan2(y, x) + 0.000003 * Math.cos(x * X_PI)
        var bd_lng = z * Math.cos(theta) + 0.0065
        var bd_lat = z * Math.sin(theta) + 0.006
        return {
          bd_lat: bd_lat,
          bd_lng: bd_lng
        }
      }
      let zuobiao = bd_encrypt(data.longitude, data.latitude)

      this.mapData = {
        bd_lng: zuobiao.bd_lng,
        bd_lat: zuobiao.bd_lat,
        gd_lng: data.orderAddress.marketLongitude,
        gd_lat: data.orderAddress.marketLatitude,
        marketName: data.orderAddress.marketAddress
      }
      this.showPop = true
    }
  }
}
</script>

<style lang="scss" scoped>
.home {
  padding-bottom: 30px;
    .content {
        width: 710px;
        margin: 0 auto 12px;
        background-color: #fff;
        border-radius: 20px;
        .info-cell {
            margin: 0 24px;
            .cell-name {
                height: 79px;
                line-height: 79px;
                border-bottom: 1px solid #f4f4f4;
                color: #333;
                font-size: 32px;
                font-family: PingFangSC-Medium;
                margin-bottom:10px;
            }
            .cell-line {
                display: flex;
                justify-content: space-between;
                padding: 12px 0;
                font-size: 26px;
                margin-bottom: 10px;
                .mapIcon{
                  width: 30px;
                  height: 30px;
                  margin-left: 3px;
                  position: relative;
                  top: 6px;
                }
                >span {
                    color: #999;
                }
                >div {
                    width: 60%;
                    text-align: right;
                    color: #333;
                }
            }
            .onNum{
              span {
                color: #FE9C16;
                font-family: PingFangSC-Medium;
                font-weight: 500;
                font-size: 36px;
              }
            }
        }
    }
}
</style>
