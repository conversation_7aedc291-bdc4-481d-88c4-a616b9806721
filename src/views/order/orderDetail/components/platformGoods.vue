<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-06-01 15:06:16
 * @LastEditors: 孙立政
 * @LastEditTime: 2021-12-30 16:12:51
-->
<template>
  <!-- 商品信息 -->
  <div class="home">
    <div class="info-cell">
      <div class="cell-name" @click="goShop">
        <span>{{ orderData.marketName | ellipsis(10) }}</span>
        <van-icon name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/order/right.png" size="10" style="margin-top:20px" />
      </div>
      <div v-for="(item, index) in orderData.orderChilds" :key="index" class="cell-line">
        <div v-for="list in item.orderGoodses" :key="list.goodsId" class="cell-line-box">
          <div class="cell-line-left">
            <div class="cell-line-img">
              <img :src="list.skuImage" style="width: 100%;height: 100%;">
            </div>
            <div class="cell-line-goods">
              <div class="line-name">
                {{ list.goodsName | ellipsis(10) }}
              </div>
              <div class="line-tag">
                {{ list.skuName| ellipsis(15) }}
              </div>
              <div class="line-count">×<span>&nbsp;{{ list.skuQuantity }}</span></div>
            </div>
          </div>
          <div class="cell-line-right">
            <div class="price">
              <div class="small">¥</div>
              {{ list.sellPrice }}
            </div>
            <div v-if="item.sellPrice!=item.originPrice" class="oriprice">
              <div class="small">¥</div>
              {{ list.originPrice }}
            </div>
          </div>
        </div>
        <div class="orderfreeFix">
          <div class="flex">
            <span>状态</span>
            <div>
              {{ item.status | statusName }}
            </div>
          </div>
        </div>
        <div class="cell-line-post">
          <van-button v-if="item.status<=50&&item.status>=22&&item.agentLogisticsNo!=null&&item.agentLogisticsNo!=''" plain type="default" size="mini" @click="getLogistics(item)">查看物流</van-button>
        </div>
      </div>
      <div class="orderfree">
        <div class="flex">
          <span>配送费</span>
          <div>
            <div class="small">¥</div>
            {{ orderData.postFee }}
          </div>
        </div>
        <div class="flex">
          <span>包装费</span>
          <div>
            <div class="small">¥</div>
            {{ orderData.packageTotalPrice }}
          </div>
        </div>
      </div>
      <div class="all">
        <div v-if="discounts>0" class="cutprice">
          <span>已优惠</span>
          <div class="small red">¥</div>
          <span class="red">{{ discounts }}</span>
        </div>
        <div class="allprice">
          <span>合计</span>
          <div class="small">¥</div>
          <span>{{ orderData.actualPay }}</span>
        </div>
      </div>
      <div class="concat" @click="CallPhone">
        <van-icon name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/order/smalltel.png" size="13px" />
        <span>售后</span>
      </div>
    </div>
    <van-popup v-model="postShow" position="bottom" :style="{ height: '50%' }" closeable>
      <div class="post">
        <van-steps direction="vertical" :active="0">
          <van-step v-for="(item,index) in postData" :key="index">
            <h3>{{ item.status }}</h3>
            <p>{{ item.time }}</p>
          </van-step>
        </van-steps>
      </div>
    </van-popup>
  </div>
</template>

<script>
import { getLogistics } from '@/api/order'
import NP from 'number-precision'
export default {
  filters: {
    statusName(val) {
      if (val == 0) { return '待付款' }
      if (val == 20) { return '待接单' }
      if (val == 21) { return '待发货' }
      if (val == 22) { return '已发货' }
      if (val == 23) { return '待配送' }
      if (val == 24) { return '待自提' }
      if (val == 30) { return '配送中' }
      if (val == 50) { return '完成' }
      if (val == 70) { return '取消' }
      if (val == 80) { return '已退款' }
      if (val == 90) { return '支付失败' }
    }
  },
  props: {
    orderData: {
      type: Object,
      default: function() {
        return {}
      }
    }
  },
  data() {
    return {
      postShow: false,
      postData: []
    }
  },
  computed: {
    discounts() {
      return NP.minus(Number(this.orderData.totalPay), Number(this.orderData.actualPay))
    }
  },
  mounted() {},
  methods: {
    // 获取物流信息
    getLogistics(row) {
      let data = {
        agentLogisticsNo: row.agentLogisticsNo,
        phone: this.orderData.orderAddress.userMobile
      }
      getLogistics(data).then(res => {
        if (res.data != null) {
          this.postData = res.data.list
          this.postShow = true
        } else {
          this.$toast('未查询到物流信息')
        }
      })
    },
    // 拨打电话
    CallPhone() {
      let phone = '18757156043'
      if (this.$store.getters.getRegionId == 1) {
        phone = '15021881176'
      } else if (this.$store.getters.getRegionId == 3) {
        phone = '17605781836'
      } else if (this.$store.getters.getRegionId == 6) {
        phone = '13587187911'
      } else if (this.$store.getters.getRegionId == 7) {
        phone = '13566993383'
      }
      AlipayJSBridge.call('CallPhone', {
        phoneNum: phone
      }, function(result) {})
    },
    goShop() {
      this.$store.state.cart.cartData[0].goodsList = []
      this.$router.push({
        name: 'ShoppingMallIndex',
        query: {
          id: this.orderData.marketId
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.home {
padding-bottom: 40px;
    .info-cell {
      background-color: #fff;
      width: 710px;
      margin: 0 auto;
      border-radius: 20px;
      padding: 0 24px;
        .cell-name {
            height: 93px;
            line-height: 93px;
            border-bottom: 1px solid #f4f4f4;
            color: #2c2c2c;
            font-size: 32px;
            font-family: PingFangSC;
            margin-bottom: 30px;
            >span {
                margin-right: 20px;
            }
        }
        .cell-line {
            font-size: 30px;
            color: #000010;
            border-bottom: 2px solid #eee;
            .cell-line-box{
                width: 100%;
                display: flex;
                justify-content: space-between;
            }
            .cell-line-post{
                text-align: right;
                font-size: 28px;
                margin-top: 5px;
            }
            .cell-line-left {
              width: 75%;
                display: flex;
                padding-top: 20px;
                .cell-line-img {
                    width: 100px;
                    height: 104px;
                    border-radius:8px;
                    overflow: hidden;
                    margin-right:16px;
                }
                .cell-line-goods {
                  width: calc(100% - 100px);
                  overflow: hidden;
                    color: #000010;
                    .line-name {
                        font-size: 30px;
                    }
                    .line-tag {
                        font-size: 22px;
                        opacity: 0.47;
                    }
                    .line-count {
                        font-size:24px;
                        color: #999999;
                    }
                }
            }
        }
        .cell-line-right {
          width: 25%;
            text-align: right;
            padding-top: 20px;
            .price {
                display: flex;
                justify-content: flex-end;
                color: #000000;
                font-size: 32px;
                font-family: PingFangSC;
            }
            .oriprice {
                display: flex;
                justify-content: flex-end;
                font-size: 24px;
                font-family: PingFang SC;
                color: #b6b4b4;
                text-decoration: line-through;
            }
        }
        .orderfree {
            font-size: 26px;
            color: #000010;
            border-bottom: 1px solid #f4f4f4;
            padding-top: 28px;
            >.flex {
                padding: 12px 0;
                display: flex;
                justify-content: space-between;
                >div {
                    display: flex;
                    justify-content: space-between;
                    font-weight: bold;
                }
            }
        }
        .orderfreeFix{
          font-size: 26px;
            color: #000010;
            padding-top: 20px;
            >.flex {
                padding: 12px 0;
                display: flex;
                justify-content: space-between;
                >div {
                    display: flex;
                    justify-content: space-between;
                    font-weight: bold;
                }
            }
        }
        .all {
            display: flex;
            justify-content: flex-end;
            align-items: center;
            height: 106px;
            font-size: 26px;
            color: #000000;
            .allprice {
                display: flex;
                align-items: center;
                margin-left: 32px;
                color: #42424d;
                >span:nth-of-type(1) {
                    margin-right: 8px;
                }
                >span:nth-of-type(2) {
                    font-size: 42px;
                    margin-left: 8px;
                    color: #010101;
                    font-weight: bold;
                }
            }
            .cutprice {
                display: flex;
                align-items: center;
                >span:nth-of-type(1) {
                    margin-right: 8px;
                }
            }
        }
    }
    .concat {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 82px;
        line-height: 82px;
        background: #fff;
        border-bottom-left-radius: 12px;
        border-bottom-right-radius: 12px;
        font-size: 28px;
        color: #333;
        >span {
            margin-left:14px;
        }
    }
    .post{
      width: 90%;
      margin: 0 auto;
      margin-top: 70px;
    }
}
.small {
    transform: scale(0.78);
    font-weight: normal;
}
.red {
    color: #fb4c58;
    font-size: 30px;
}
</style>
