<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-06-01 15:23:53
 * @LastEditors: 孙立政
 * @LastEditTime: 2021-07-02 15:11:36
-->
<template>
  <!-- 订单已取消 -->
  <div class="home">
    <div v-if="orderData.status == 5">
      <div v-if="orderData.status == 5" class="status">
        <span>订单已取消</span>
        <!-- <van-icon name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/order/right.png" size=".22rem" /> -->
      </div>
      <div v-if="orderData.status == 5" class="cancel-cell">
        <div class="cancel-status">
          <span v-if="orderData.status == 5" style="float: left;">您的订单已取消</span>
          <span
            v-if="orderData.cancelType != null"
            style="float: right;margin-right: 20px;color: #666666;font-size: 13px;"
          >{{ orderData.cancelType|cancelType }}</span>
        </div>
        <div class="cancel-bottom">
          <div class="bottom-left" @click="goIndex">
            <van-icon
              name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/order/market.png"
              size="25"
            />
            <div @click="goIndex">随便逛逛</div>
          </div>
          <div class="bottom-right" @click="goShop">
            <van-icon
              name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/order/list.png"
              size="25"
            />
            <div @click="goShop">再来一单</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 待接单 -->
    <div v-if="orderData.status == 1">
      <div class="status">
        <span>待接单</span>
      </div>
    </div>
    <!-- 订单待配送 -->
    <div v-if="orderData.status == 2">
      <div class="status">
        <span>订单待配送</span>
        <!-- <span>{{expectFinishDuration}}</span> -->
      </div>
    </div>

    <!-- 配送中 -->
    <div v-if="orderData.status == 3">
      <div class="status">
        <span>订单配送中</span>
        <!-- <span>{{expectFinishDuration}}</span> -->
      </div>
    </div>

    <div v-if="orderData.doRefund == true&&orderData.orderRefund.refundStatus<3">
      <div class="status">
        <span>退款中</span>
      </div>
    </div>
    <div v-else-if="orderData.status == 6">
      <div class="status">
        <span>退款中</span>
      </div>
    </div>

    <div v-if="false">
      <div class="status">
        <span>商家拒绝接单</span>
        <van-icon
          name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/order/right.png"
          size="11"
        />
      </div>
      <div class="cancel-cell">
        <div class="cancel-status">
          <span class="gray">您的订单已取消</span>
        </div>
        <div class="cancel-bottom">
          <span class="reason red">
            原因：暂无该商品，拒绝接单
          </span>
        </div>
      </div>
    </div>

    <div v-if="orderData.status == 41||orderData.status == 4">
      <div class="status">
        <span>订单已完成</span>
      </div>
      <div class="cancel-cell">
        <div class="cancel-status">
          <span>感谢您对点滴平台的信任，期待您的再次光临。</span>
        </div>
        <div class="cancel-bottom">
          <div class="bottom-left newwidth" @click="goIndex">
            <van-icon
              name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/order/market.png"
              size="25"
            />
            <div>随便逛逛</div>
          </div>
          <div class="bottom-center newwidth" @click="CallPhone(orderData.orderAddress.marketMobile)">
            <van-icon
              name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/order/bigtel.png"
              size="25"
            />
            <div>联系商家</div>
          </div>
          <div class="bottom-right newwidth" @click="goShop">
            <van-icon
              name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/order/list.png"
              size="25"
            />
            <div @click="goShop">再来一单</div>
          </div>
        </div>
      </div>
    </div>

  </div>
</template>

<script>
export default {
  components: {
  },
  filters: {
    cancelType(val) {
      if (val == 1) {
        return '用户取消'
      } else if (val == 2) {
        return '用户退款'
      } else if (val == 3) {
        return '商户拒绝'
      } else if (val == 4) {
        return '付款超时'
      } else if (val == 5) {
        return '商户接单超时'
      }
    }
  },
  props: {
    orderData: {
      type: Object,
      default: function() {
        return {}
      }
    }
  },
  data() {
    return {
      expectFinishDuration: ''
    }
  },
  mounted() {
    // this.expectFinishDuration = parseInt(this.orderData.orderDeliver.expectFinishDuration / 60)
  },
  methods: {
    fatherMethod() {
      this.show = false
    },
    // 拨打电话
    CallPhone(data) {
      AlipayJSBridge.call('CallPhone', {
        phoneNum: data
      }, function(result) {})
    },
    goShop() {
      this.$store.state.cart.cartData[0].goodsList = []
      this.$router.push({
        name: 'ShoppingMallIndex',
        query: {
          id: this.orderData.marketId
        }
      })
    },
    goIndex() {
      this.$router.push({
        name: 'Classify',
        query: {
          isTakeaway: true
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
	.home {
		.status {
			font-size: 44px;
			font-family: PingFangSC;
			color: #222;
			margin: 20px 0 40px 40px;
			>span:nth-of-type(1) {
				margin-right: 18px;
			}
		}
		.cancel-cell {
			width: 710px;
			margin: 0 auto 12px;
			background-color: #fff;
			border-radius: 20px;
			color: #000010;
			font-size: 32px;
			.cancel-status {
				height: 103px;
				line-height: 103px;
				border-bottom: 1px solid #efefef;
				color: #000010;
				font-size: 30px;
				padding-left: 24px;
			}
			.cancel-bottom {
				display: flex;
				justify-content: space-between;
				color: #000010;
				padding-bottom:34px;
				.reason {
					display: inline-block;
					font-size: 28px;
					margin: 32px 0 0 24px;
				}
				.bottom-left,
				.bottom-right,
				.bottom-center {
					width: 50%;
					padding-top: 28px;
					text-align: center;
					>div {
						font-size: 24px;
					}
				}
			}
		}
	}

	.newwidth {
		width: 25% !important;
	}
	.red {
		color: #fb4c58;
	}
	.gray {
		color: #4c4c57;
	}
</style>
