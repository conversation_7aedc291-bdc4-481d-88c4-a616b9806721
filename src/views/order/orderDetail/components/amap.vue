<template>
  <div class="home">
    <!-- <div class="top" @click="close">
      <van-icon name="arrow-left" style="margin-left: .2rem;" />
    </div> -->
    <div id="container" />
    <!-- <div class="box">
      <div @click="goTest()" class="location">
        <img
          src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/address/locate.png"
          alt=""
        />
      </div> -->
    <!-- <div class="card">
        <div class="time">
          预计<span>{{ mapdata.orderDeliver.expectFinishTime }}</span
          >送达
        </div>
        <div class="callShop" @click="callCouur">
          <van-icon name="phone" class="phoneIcon" />联系骑手
        </div>
      </div> -->
  </div>
</template>

<script>
import { takeoutcouLoc } from '@/api/order'
export default {
  props: {
    mapdata: {
      type: Object,
      default: function() {}
    }
  },
  data() {
    return {
      longitude: '',
      latitude: '',
      times: '',
      map: {}
    }
  },
  mounted() {
    this.addMap()
  },
  methods: {
    addMap() {
      let self = this
      // 创建一个起点 Icon
      var startIcon = new AMap.Icon({
        // 图标尺寸
        size: new AMap.Size(100, 100),
        // 图标的取图地址
        image:
          'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/index/peisomng.png'
      })
      // 创建一个终点 icon
      var endIcon = new AMap.Icon({
        size: new AMap.Size(100, 100),
        image:
          'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/index/myactive.png'
        // imageSize: new AMap.Size(20, 30)
      })
      // 创建地图实例

      takeoutcouLoc(this.mapdata.orderNo)
        .then((res) => {
          // return
          if (res.status == 200) {
            self.longitude = res.data.longitude
            self.latitude = res.data.latitude
            self.map = new AMap.Map('container', {
              zoom: 14,
              zooms: [3, 18], // 缩放级别范围
              center: [self.longitude, self.latitude],
              resizeEnable: true
            })

            // 将 icon 传入 marker，骑手位置
            var startMarker = new AMap.Marker({
              position: new AMap.LngLat(self.longitude, self.latitude),
              icon: startIcon,
              offset: new AMap.Pixel(-25, -50)
            })

            // 将 icon 传入 marker,用户位置
            var endMarker = new AMap.Marker({
              position: new AMap.LngLat(
                self.$store.state.location.longitude,
                self.$store.state.location.latitude
              ),
              icon: endIcon,
              offset: new AMap.Pixel(-25, -50)
            })

            // 将 markers 添加到地图
            self.map.add([startMarker, endMarker])
          } else {
            self.$toast('地图加载失败')
          }
        })
    },
    close() {
      this.$emit('fatherMethod')
    },
    callCouur() {
      AlipayJSBridge.call(
        'CallPhone',
        {
          phoneNum: this.mapdata.orderDeliver.courierMobile
        },
        function(result) {}
      )
    },
    goTest() {
      this.map.panTo([this.longitude, this.latitude])
    }
  }
}
</script>

<style>
.amap-icon img {
  width: 90px;
  height: 90px;
}
</style>

<style lang="scss" scoped>
.home {
  width: 100%;
  height: 600px;
  margin-bottom: 20px;
  .top {
    width: 100%;
    height: 100px;
    position: fixed;
    top: 30px;
    z-index: 9999;
    line-height: 100px;
    font-size: 50px;
  }

  #container {
    height: 100%;
    width: 100%;
  }

  .box {
    width: 100%;
    height: 300px;
    position: fixed;
    bottom: 0;
    z-index: 99999;
    .location {
      position: absolute;
      bottom: 350px;
      right: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 64px;
      height: 64px;
      background-color: #fff;
      border-radius: 50%;
      img {
        width: 48px;
        height:48px;
      }
    }
    .card {
      width: 90%;
      height: 200px;
      border-radius: 10px;
      background-color: #fff;
      margin: 0 auto;
      .time {
        width: 90%;
        height: 100px;
        margin: 0 auto;
        font-size: 35px;
        line-height: 100px;
        border-bottom: 1px solid #eeeeee;
        span {
          color: #fb4c58;
          margin-left: 10px;
          margin-right: 10px;
        }
      }
      .callShop {
        width: 90%;
        height: 100px;
        margin: 0 auto;
        font-size: 35px;
        line-height: 100px;
        text-align: center;
        .phoneIcon {
          position: relative;
          top: 4.5px;
          right: 10px;
        }
      }
    }
  }
}
</style>
