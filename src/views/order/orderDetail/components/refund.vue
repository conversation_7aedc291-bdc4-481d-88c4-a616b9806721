<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: zhaoyuxin
 * @Date: 2021-06-01 15:32:18
 * @LastEditors: zhaoyuxin
 * @LastEditTime: 2021-06-01 16:11:48
-->
<template>
  <div v-if="orderData.orderRefund!=null" class="home">
    <div class="content">
      <div class="refund-left">
        <span>退款金额:</span>
        <span>{{ orderData.actualPay }}元</span>
      </div>
      <div v-if="orderData.orderRefund.refundStatus == 3" class="refund-right">
        <span>退款成功</span>
      </div>
      <div v-if="orderData.orderRefund.refundStatus<3" class="refund-right">
        <span>退款中</span>
      </div>
      <div v-if="orderData.orderRefund.refundStatus == 4" class="refund-right">
        <span>退款拒绝</span>
      </div>
    </div>
    <div class="content">
      <div class="refund-left">
        <span>退款理由:</span>
      </div>
      <div class="refund-right1">
        <span>{{ orderData.orderRefund.refundContent.refundReason }}</span>
      </div>
    </div>
    <div v-if="orderData.orderRefund.refundStatus == 4" class="content">
      <div class="refund-left">
        <span>拒绝理由:</span>
      </div>
      <div class="refund-right1">
        <span>{{ orderData.orderRefund.refuseReason }}</span>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    orderData: {
      type: Object,
      default: function() {
        return {}
      }
    }
  },
  data() {
    return {}
  },
  mounted() {},
  methods: {}
}
</script>

<style lang="scss" scoped>
	.home{
		width: 710px;
		background-color: #fff;
		margin: 0 auto 12px;
		border-radius: 20px;
	}
	.content {
		display: flex;
		justify-content: space-between;
		align-items: center;
		width: 710px;
		min-height: 106px;
		margin: 0 auto;
		background-color: #fff;
		font-size:28px;
		padding: 0 24px;
		box-sizing: border-box;
		color: #2c2c2c;
		border-radius: 20px;
		.refund-left {
			>span:nth-of-type(2) {
                color: #333;
				font-size: 32px;
				margin-left: 20px;
			}
		}
		.refund-right {
			>span:nth-of-type(1) {
				color: #65b05c;
				margin-right: 20px;
			}
			.van-icon {
				top: 4px;
			}
		}
		.refund-right1 {
			width: 75%;
			text-align: right;
			color: #000010;
			>span:nth-of-type(1) {
				color: #000;
				margin-right: 20px;
			}
			.van-icon {
				top: 4px;
			}
		}
	}
</style>
