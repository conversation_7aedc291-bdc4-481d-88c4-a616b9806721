<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-06-01 16:57:45
 * @LastEditors: 孙立政
 * @LastEditTime: 2021-08-26 15:01:51
-->
<template>
  <!-- 等待支付-->
  <div class="content">
    <div class="status">
      <span>订单等待付款</span>
    </div>
    <div class="tips">
      <van-icon name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/order/loud.png" size="15" />
      <span>超过15分钟未支付，订单将自动取消</span>
    </div>
    <div class="cancel-cell">
      <div v-if="orderData.deliveryType!=2" class="cancel-status">
        <span>预计</span>
        <span class="red">{{ orderData.orderDeliver.expectFinishDuration|time }}</span>
        <span>分钟送达</span>
      </div>
      <div class="cancel-bottom">
        <div class="bottom-left" @click="cancelOrder">
          <van-icon name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/order/cancel.png" size="25" />
          <div>取消订单</div>
        </div>
        <div class="bottom-center" @click="payOrder(orderData)">
          <van-icon name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/order/pay.png" size="25" />
          <div>立即支付</div>
        </div>
        <div class="bottom-right" @click="CallPhone(orderData.orderAddress.marketMobile)">
          <van-icon name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/order/bigtel.png" size="25" />
          <div>联系商家</div>
        </div>
      </div>
      <div style="height:15px" />
    </div>
  </div>
</template>

<script>
import {
  cancelOrder
} from '@/api/order'
export default {
  props: {
    orderData: {
      type: Object,
      default: function() {
        return {}
      }
    }
  },
  data() {
    return {}
  },
  mounted() {},
  methods: {
    // 拨打电话
    CallPhone(data) {
      AlipayJSBridge.call('CallPhone', {
        phoneNum: data
      }, function(result) {})
    },
    // 支付订单
    payOrder(item) {
      this.$router.push({ name: 'Payment', query: {
        orderNo: item.orderNo,
        actualPay: item.actualPay,
        marketSn: item.marketSn
      }})
    },
    cancelOrder() {
      let self = this
      this.$dialog.confirm({
        title: '确认',
        message: '是否取消订单'
      }).then(() => {
        cancelOrder(this.$route.query.orderNo).then((res) => {
          if (res.status == 200) {
            self.$toast('取消成功')
            this.$emit('reloadMethod')
            // setTimeout(() => {
            //   // 从缓存中获取
            //   // history.go(0)
            //   // 从服务端获取
            //   location.reload()
            // }, 1000)
          } else {
            self.$toast(res.message)
          }
        })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
	.content {
		.status {
			display: flex;
			align-items: center;
			font-size: 44px;
			font-family: PingFangSC;
			color: #000010;
			opacity: 0.84;
			margin: 20px 0 40px 44px;
			>span:nth-of-type(1) {
				margin-right: 18px;
			}
			.van-icon {
				top: 2px;
			}
		}
		.tips {
			display: flex;
			align-items: center;
			width: 710px;
			height: 76px;
			margin: 0 auto 12px;
			background-color: #fff;
			border-radius: 20px;
			font-size: 28px;
			padding: 0 26px;
			box-sizing: border-box;
			color: #4c4c57;
			>span {
				margin-left:26px;
			}
		}
		.cancel-cell {
			width: 710px;
			margin: 0 auto 12px;
			background-color: #fff;
			border-radius: 20px;
			color: #000010;
			font-size: 32px;
			.cancel-status {
				height: 103px;
				line-height: 103px;
				color: #000010;
				font-size: 44px;
				padding-left: 24px;
			}
			.cancel-bottom {
				display: flex;
				color: #333;
				.bottom-left,
				.bottom-right,
				.bottom-center {
					width: 33.33%;
					padding-top: 28px;
					text-align: center;
					>div {
						font-size: 24px;
					}
				}
			}
		}
	}

	.red {
		color: #fb4c58;
		margin-right: 14px;
		margin-left: 14px;
	}
</style>
