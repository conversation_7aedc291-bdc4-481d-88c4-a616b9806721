<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-05-31 14:29:33
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-11-24 10:55:11
-->
<template>
  <!-- 我的订单 -->
  <div class="homess">
    <NavHeight bgc="#fff" />
    <!-- 导航栏 -->
    <Nav />
    <!-- 标签栏 -->
    <Tab @condition="changeRest" />
    <div v-if="list.length==0 || this.$store.getters.getUserId == null" class="line" />
    <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
      <van-list
        v-model="loading"
        :finished="finished"
        finished-text=""
        @load="onLoad"
      >
        <OrderCard v-for="(item,index) in list" :key="index" :list="item" @clickHandler3="mageOrder" />
      </van-list>
    </van-pull-refresh>
    <Skeleton v-if="list.length == 0&&!emptyType&&$store.getters.getUserId != null" type="order" />

    <Empty v-if="emptyType" msg="您还没有相关订单" msg1="去下一单试试吧" type="去逛逛" />
    <Empty v-if="this.$store.getters.getUserId == null" msg="请登录后查看相关订单哦~" msg1="去登录一下试试吧" type="去登录" />
    <!-- 退款弹出 -->
    <van-dialog v-model="refundShow" title="退款" show-cancel-button @confirm="sureRefund">
      <div class="tips">注：如该订单使用了优惠券，退款后优惠券将不予返还</div>
      <van-field
        v-model="contentMsg"
        rows="3"
        maxlength="50"
        show-word-limit
        autosize
        type="textarea"
        placeholder="请输入退款原因"
      />
    </van-dialog>
    <div style="height:60px" />
  </div>
</template>

<script>
import Nav from './components/nav'
import Tab from './components/tab'
import Empty from './components/empty.vue'
import OrderCard from '@/components/OrderCard/index'
import { orderList, cancelOrder,	sureRefund, selfEmployedCancel, complete } from '@/api/order'
import Skeleton from '@/components/Skeleton'
export default {
  components: {
    Nav,
    Tab,
    OrderCard,
    Empty,
    Skeleton
  },
  data() {
    return {
      loading: false,
      finished: false,
      refreshing: false,
      list: [],
      page: 1,
      tabIndex: 0,
      orderNo: '',
      contentMsg: '',
      refundShow: false,
      emptyType: false
    }
  },
  created() {
    this.$store.state.Index.bannerIndex = 2
    this.$store.state.tabbar.index = 2
    this.tabIndex = this.$route.query.activeIdx
  },
  mounted() {
    document.body.scrollTop = 0
    document.documentElement.scrollTop = 0
    // this.onLoad()
  },
  methods: {
    getList(type) {
      let data = {
        pageNum: this.page++,
        pageSize: 10,
        search: {
          queryStatus: this.tabIndex,
          orderType: this.tabIndex == 1 ? 7 : null
        }
      }
      orderList(data).then((res) => {
        this.loading = false
        if (res.status == 200) {
          if (res.data.list.length == 0 && this.list.length == 0) {
            this.emptyType = true
          } else {
            this.emptyType = false
          }
          // type==2 通过点击tab获取第一页数据
          if (type == 2) {
            // let cache = this.$store.state.order.orderData
            // if (cache.length > 0 || JSON.stringify(res.data.list) != JSON.stringify(cache)) {
            //   this.list = res.data.list
            //   this.$store.state.order.orderData = res.data.list
            // }
            this.list = res.data.list
            this.$store.state.order.orderData = res.data.list
          } else {
            this.list.push(...res.data.list)
            this.$store.state.order.orderData.push(...res.data.list)
          }
          if (res.data.list.length == 0) {
            this.finished = true
          }
        } else {
          this.finished = true
        }

        // type==1通过下拉刷新请求的数据
        if (type == 1) {
          setTimeout(() => {
            this.refreshing = false
          }, 300)
        }
      })
    },
    onLoad() {
      this.getList()
    },
    onRefresh() {
      this.page = 1
      this.list = []
      this.getList(1)
    },
    changeRest(data) {
      this.list = []
      this.page = 1
      this.loading = false
      this.finished = false
      this.tabIndex = data
      this.getList(2)
    },
    mageOrder(type, data) {
      if (type == 1) { // 立即支付
        console.log(type, data)
        this.$store.state.market.marketData.payradio = ''
        this.$router.push({
          name: 'Payment',
          query: {
            orderNo: data.orderNo,
            actualPay: data.actualPay,
            marketSn: data.marketSn,
            marketId: data.marketId,
            payAsync: data.payAsync,
            orderType: data.paymentChannel
          }
        })
      } else if (type == 2) { // 评价
        this.$router.push({
          name: 'PublishEvalute',
          query: {
            marketId: data.marketId,
            orderNo: data.orderNo,
            deliverPart: data.orderDeliver.deliverPart,
            deliveryType: data.deliveryType
          }
        })
      } else if (type == 3) { // 退款
        this.orderNo = data.orderNo
        this.refundShow = true
        this.contentMsg = ''
      } else if (type == 4) { // 取消
        this.$dialog
          .confirm({
            title: '确认',
            message: '是否取消订单',
            confirmButtonColor: '#6095f0'
          })
          .then(() => {
            if (data.paymentChannel == 12) {
              // 自营商城订单
              selfEmployedCancel(data.orderNo)
                .then((res) => {
                  if (res.status == 200) {
                    this.$toast('取消成功')
                    setTimeout(() => {
                      this.page = 1
                      this.getList(2)
                    }, 1000)
                  } else {
                    this.$toast(res.message)
                  }
                })
            } else {
              // 外卖订单
              cancelOrder(data.orderNo)
                .then((res) => {
                  if (res.status == 200) {
                    this.$toast('取消成功')
                    setTimeout(() => {
                      this.page = 1
                      this.getList(2)
                    }, 1000)
                  } else {
                    this.$toast(res.message)
                  }
                })
            }
          })
      } else if (type == 5) { // 确认收货
        this.$dialog
          .confirm({
            title: '确认',
            message: '是否确认收货',
            confirmButtonColor: '#6095f0'
          })
          .then(() => {
            complete(data.orderNo)
              .then((res) => {
                if (res.status == 200) {
                  this.$toast('成功')
                  setTimeout(() => {
                    this.page = 1
                    this.getList(2)
                  }, 1000)
                } else {
                  this.$toast(res.message)
                }
              })
          })
      }
    },
    // 确认退款
    sureRefund() {
      if (this.contentMsg == '') {
        this.$toast('请输入退款原因！')
        return false
      }
      let data = {
        orderNo: this.orderNo,
        refundImages: [],
        refundReason: this.contentMsg
      }
      sureRefund(data)
        .then((res) => {
          if (res.status == 200) {
            this.$toast('退款申请成功')
            setTimeout(() => {
              this.page = 1
              this.getList(2)
            }, 1000)
          } else {
            this.$toast(res.message)
          }
        })
    }
  }
}
</script>

<style scoped lang="scss">
.line {
  height: 30px;
  width: 100%;
  background: #f5f5f5;
}
.tips{
  text-align: center;
  color: red;
  font-size: 20px;
}
</style>
