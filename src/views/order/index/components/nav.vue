<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-05-31 14:29:33
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-09-26 13:50:19
-->
<template>
  <div class="home">
    <div class="fixed">
      <van-nav-bar
        title="我的订单"
        left-text=""
        right-text=""
        :placeholder="true"
        :border="false"
      />
      <!-- <div v-if="$store.state.isMenuMarket" class="market_in">
        <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/order/status/orderin.png" alt="" @click="goRules">
      </div>
      <div class="market_in">
        <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/shop/fineFood/setmail.png" alt="" @click="goSetMailOrder">
      </div> -->
    </div>
    <!-- <div style="height:43px" /> -->
    <div v-if="$store.state.isMenuMarket" style="height:45px" />
    <div v-else style="height:45px" />
  </div>
</template>

<script>
export default {
  data() {
    return {

    }
  },
  created() {

  },
  mounted() {

  },
  methods: {
    goRules() {
      this.$router.push({
        name: 'MarketOrder'
      })
    },
    goSetMailOrder() {
      this.$router.push({
        name: 'SetMealorderList'
      })
    }
  }
}
</script>

<style scoped lang="scss">
    .home {
      .fixed {
        position: fixed;
        left: 0;
        // top: 0;
        width: 100%;
        z-index: 10;
      }
      ::v-deep .van-nav-bar__title {
        color: #222;
        font-size: 36px;
        font-family: PingFangSC-Medium;
      }
      .market_in{
        width: 100%;
        height: 83px;
        background-color: #fff;
        img{
          width: 100%;
          height: 83px;
          float: left;
        }
      }
    }
</style>
