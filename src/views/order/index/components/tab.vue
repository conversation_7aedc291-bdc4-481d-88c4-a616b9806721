<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-05-22 16:44:42
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-05-20 18:25:26
-->
<template>
  <div class="home">
    <ul class="tab">
      <li v-for="(item,index) in list" :key="item.id" class="tab-item" :class="currentIndex==index?'active':''">
        <span :data-current="index" @click="clickIndex($event,item.id)">{{ item.title }}</span>
        <div :class="[currentIndex==index?'tab-item-icon':'tab-null-icon']" />
      </li>
    </ul>
    <div style="height:37px" />
  </div>
</template>

<script>
export default {
  data() {
    return {
      list: [
        {
          id: 0,
          title: '全部'
        },
        // {
        //   id: 0,
        //   title: '待付款'
        // },
        // {
        //   id: 1,
        //   title: '配送中'
        // },
        {
          id: 1,
          title: '待评价'
        },
        {
          id: 2,
          title: '退款/售后'
        },
        {
          id: 3,
          title: '异常订单'
        }
      ],
      currentIndex: 0
    }
  },
  created() {
    // console.log('activeIdx' + this.$route.query.activeIdx)
    if (this.$route.query.activeIdx) {
      this.currentIndex = this.$route.query.activeIdx
    }

    // console.log(this.currentIndex)
  },
  mounted() {

  },
  methods: {
    clickIndex(e) {
      let current = e.target.dataset.current
      if (this.currentIndex == current) {
        return false
      } else {
        this.currentIndex = current
        this.$router.push({ path: this.$route.path, query: { activeIdx: current }})
      }
      this.$emit('condition', current)
    }
  }
}
</script>

<style scoped lang="scss">
    .home {
        .tab {
          position: fixed;
          left: 0;
          z-index: 11;
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: space-around;
            height: 74px;
            color: #333;
            background-color: #fff;
            margin-top: 2px;
            padding-top: 3px;
            .tab-item{
              width: 33%;
              text-align: center;
                font-size: 28px;
                font-family: PingFangSC;
            }
            .active {
                font-weight: 500;
                font-family: PingFangSC-Medium;
            }
            .tab-item-icon,.tab-null-icon {
                width: 45px;
                height: 15px;
                margin: 3px auto 0;
            }
            .tab-item-icon {
                background-image: url(https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/user/classify/tab-active.png);
                background-size: 100%;
            }
        }

    }
</style>
