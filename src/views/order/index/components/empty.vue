<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: zhaoyuxin
 * @Date: 2021-06-29 11:54:13
 * @LastEditors: zhaoyuxin
 * @LastEditTime: 2021-07-20 16:48:41
-->
<template>
  <div class="home">
    <div class="emptyImg">
      <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/empty/empty.png" alt="">
      <div class="msg">{{ msg }}</div>
      <div class="msg1">{{ msg1 }}</div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    msg: {
      type: String,
      default: '暂无更多'
    },
    msg1: {
      type: String,
      default: ''
    },
    type: {
      type: String,
      default: ''
    }
  },
  data() {
    return {

    }
  },
  computed: {
  },
  created() {
  },
  mounted() {

  },
  methods: {
    goNexts() {
      // this.$router.push('/')
      // if (this.type == '去登录') {
      //   this.$router.push({
      //     name: 'PwdLogin'
      //   })
      // } else {
      //   this.$router.push('/')
      // }
    }
  }
}
</script>

<style scoped lang="scss">
    .home {
        position: fixed;
        width: 100%;
        height: 100vh;
        // top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: #fff;
        z-index:-100;

        .emptyImg {
            position: absolute;
            left: 50%;
            top: 43%;
            transform: translate(-50%,-50%);
            width: 400px;
            height: 300px;
            margin: 0 auto;
            img {
                width: 100%;
                height: 100%;
            }
        }
        .msg {
            font-size: 30px;
            font-family:PingFangSC-Medium;
            font-weight: 500;
            color: #333333;
            text-align: center;
            margin-top: 24px;
        }
        .msg1 {
            font-size: 26px;
            font-family: PingFangSC;
            text-align: center;
            color: #999999;
            margin-top: 16px;
        }
        .btn {
            width: 230px;
            height: 80px;
            line-height: 80px;
            text-align: center;
            font-size: 32px;
            border: 2px solid #169d1b;
            border-radius: 14px;
            color: #169d1b;
            font-family: PingFangSC-Medium;
            font-weight: 500;
            margin: 36px auto 0;
        }
    }
</style>
