<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2022-05-31 17:57:58
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-07-14 15:09:11
-->
<template>
  <div class="home">
    <div class="d_img">
      <img
        :src="item.adPictureUrl != '' ? item.adPictureUrl : item.pictureUrl"
        alt=""
      >
      <img v-if="item.activityStatus==3" class="d_off" src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/home/<USER>/activity/off.png" alt="">
    </div>
    <div class="d_title">{{ item.adGoodsName }}</div>
    <div class="d_guiji">
      <div class="d_guiji_or">￥{{ item.pintuanPrice }}</div>
      <div>拼团价</div>
      <div class="d_guiji_or">￥{{ item.pintuanPrice }}</div>
    </div>
    <div class="d_btn" :class="item.activityStatus==3?'d_off_btn':''">
      <span>最低￥</span>
      <span class="d_btn_price">{{ item.adPrice }}</span>
      <span>起/{{ item.adUnit }}</span>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    item: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  data() {
    return {}
  },
  created() {},
  mounted() {},
  methods: {}
}
</script>

<style scoped lang="scss">
.home {
  width: 224px;
  height: 330px;
  background: #ffffff;
  border-radius: 8px;
  // margin-top: 100px;
  .d_img {
    width: 224px;
    height: 180px;
    position: relative;
    img {
      width: 224px;
      height: 180px;
      float: left;
      border-top-left-radius: 8px;
      border-top-right-radius: 8px;
    }
    .d_off{
      position: absolute;
      top: 0;
      left: 0;
      // z-index: 5;
    }
  }
  .d_title {
    font-size: 26px;
    color: #454545;
    font-family: PingFangSC-Medium;
    padding: 7px;
  }
  .d_guiji {
    width: 213px;
    height: 44px;
    background-image: url(https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/market/index/guiji.png);
    background-size: 100% 100%;
    padding: 2px;
    display: flex;
    justify-content: space-between;
    font-size: 18px;
    color: #a6a6a6;
    .d_guiji_or {
      margin-top: 3px;
    }
  }
  .d_btn {
    min-width: 174px;
    max-width: 200px;
    height: 48px;
    line-height: 44px;
    text-align: center;
    color: #fff;
    border-radius: 17px;
    font-size: 20px;
    background-image: url(https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/market/index/btnbj.png);
    background-size: 100% 100%;
    margin: 0 auto;
    letter-spacing: 1px;
    overflow: hidden;
    .d_btn_price {
      font-size: 30px;
      position: relative;
      top: 2px;
    }
  }
  .d_off_btn{
    height: 38px;
    border-radius: 19px;
    background: #dddddd;
    margin-top: 5px;
    line-height: 35px;
  }
}
</style>
