<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2021-05-29 11:34:50
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-09-27 21:53:23
-->
<template>
  <div class="home">
    <!-- <div class="bannerIm1" @click="goActieve1">
      <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/home/<USER>/activity/20240430-51/banner.png">
    </div> -->

    <van-swipe class="banner" :autoplay="3000" touchable @change="onChange">
      <van-swipe-item v-for="(item, index) in list" :key="index">
        <img
          class="bannerImg"
          :src="item.image + '?x-oss-process=image/resize,w_700/format,jpg/quality,q_99'"
          @click="goActieve(item)"
        >
      </van-swipe-item>
    </van-swipe>
  </div>
</template>

<script>
import { getBannerList } from '@/api/index'
export default {
  data() {
    return {
      list: this.$store.state.Index.banner
    }
  },
  created() {
    this.getBannerList()
  },
  methods: {
    getBannerList() {
      getBannerList().then((res) => {
        if (res.status == 200) {
          let data = this.$store.state.Index.banner
          if (data == '' || JSON.stringify(res.data) != JSON.stringify(data)) {
            this.list = res.data

            // this.list.unshift({
            //   image: 'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/home/<USER>/activity/banner-20240430.png',
            //   url: 'nameiyun'
            // })

            this.$store.state.Index.banner = this.list
          }
        }
      })
    },
    // 跳转
    goActieve(val) {
      this.$store.state.cart.cartData[0].goodsList = []
      this.$store.state.market.marketData.remark = ''
      if (val.url != '' && val.url != null) {
        if (val.url == 'activity') {
          // this.$toast('即将上线，敬请期待')
          // this.$router.push({
          //   name: 'ShoppingMallActieve',
          //   query: {
          //     id: 880
          //   }
          // })
        } else if (val.url == 'fdc') {
          this.$router.push({
            name: 'Fdc'
          })
        } else if (val.url == 'fdc2') {
          this.$router.push({
            name: 'FdcTwo'
          })
        } else if (val.url == 'fdc3') {
          this.$router.push({
            name: 'Fdc3'
          })
        } else if (val.url == 'fdc4') {
          if (this.$store.getters.getUserId == null) {
            this.$router.push({ name: 'PwdLogin' })
            return
          }
          this.$router.push({
            name: 'ShoppingMallIndex',
            query: {
              id: 1138
            }
          })
          return
        } else if (val.url == 'nameiyun') {
          if (this.$store.getters.getUserId == null) {
            this.$router.push({ name: 'PwdLogin' })
            return
          }
          let urls = 'https://emallh5.namek.com.cn/emall/pages/index/index?tenant=scxddwlkjyxzrgs'

          let userNo = this.$store.getters.getUserNo
          let statusHeight = this.$store.getters.getStatusHeight
          var u = navigator.userAgent
          var isiOS = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/) // ios终端
          if (isiOS) {
            statusHeight = 0
          }
          let url = urls + '&statusHeight=' + statusHeight + '&navigationBarHeight=' + this.$store.getters.getNavigationBarHeight + '&userNo=' + userNo
          AlipayJSBridge.call('pushWindow', {
            url: url,
            param: {
              readTitle: true,
              showOptionMenu: false
            },
            passData: {}
          })
        } else {
          this.$router.push({
            name: 'Shop',
            query: {
              id: val.url,
              st: 'index'
            }
          })
        }
      }
    },
    onChange(index) {
      // this.$store.state.Index.bannerIndex = index
    },
    goActieve1() {
      this.$router.push({
        name: 'Activity51'
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.home {
  .banner {
    width: 710px;
    height: 188px;
    margin: 0 auto;
    margin-top: 20px;
    border-radius: 15px;
    margin-bottom: 30px;

    ::v-deep .van-swipe__indicator {
      width: 20px;
      height: 2px;
      border-radius: 0;
    }

    ::v-deep .van-swipe__indicator--active {
      background: #74e165;
    }
  }

  .bannerIm1 {
    width: 710px;
    height: 230px;
    border-radius: 15px;
    margin: 0 auto;
    margin-top: 30px;
    margin-bottom: 30px;
    img{
      width: 100%;
      height: 100%;
      border-radius: 15px;

    }
  }

  .bannerImg {
    width: 710px;
    height: 188px;
    border-radius: 15px;
  }
}
</style>
