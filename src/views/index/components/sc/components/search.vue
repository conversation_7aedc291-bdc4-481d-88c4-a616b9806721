<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-05-22 15:18:58
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-10-25 11:25:56
-->
<template>
  <div class="home">
    <div class="container">
      <div class="search">
        <van-search v-model="keyword" placeholder="请输入商家或商品名称" show-action action-text="搜索" @focus="goSearch"	@search="goSearch">
          <template #action>
            <!-- <div class="searchBtn">搜索</div> -->
            <img class="searchBtn" :src="$store.state.Index.template.searchButton" alt="">
          </template>
        </van-search>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      keyword: ''
    }
  },
  created() {},
  mounted() {},
  methods: {
    goSearch() {
      // if (this.keyword.trim() == '') {
      //   this.$toast('请输入关键字')
      //   return false
      // }
      // this.$store.state.searchKey = this.keyword
      this.$router.push({
        name: 'Search'
      })
    }
  }
}
</script>

<style scoped lang="scss">
.home {
  .container {
    // height: 128px;
    .search {
        width: 710px;
        // height: 129px;
        border-radius: 44px 44px 0 0;
        margin: 0 auto;
        margin-top: 28px;
      ::v-deep .van-search {
          width: 702px;
          border-radius:34px;
          overflow: hidden;
          padding: 0;
      }
      ::v-deep .van-search__content {
          background-color: #fff;
      }
      ::v-deep .van-search__action {
          width: 130px;
          height: 56px;
          line-height: 56px;
          margin-right: 6px;
          font-size: 28px;
          color: #fff;
          // background: linear-gradient(90deg,#32d349 1%, #64e151 99%);
          padding: 0 24px;
          border-radius: 30px;
          overflow: hidden;
          img{
            width: 104px;
          height: 56px;
          }
      }
    }
  }
}
</style>
