<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2021-05-31 10:04:59
 * @LastEditors: 孙立政
 * @LastEditTime: 2023-06-21 10:08:32
-->
<template>
  <div v-if="temparr.length>0&&temparr1.length>0" class="actieve" :style="'background-image: url('+$store.state.Index.template.activityBackgroundTwo+')'">
    <div class="actieveTitle">
      <div>
        <img :src="$store.state.Index.template.activityOne" alt="">
      </div>
      <div>
        <img :src="$store.state.Index.template.activityTwo" alt="">
      </div>
    </div>
    <div class="actieveGoods">
      <!-- 天天特价 -->
      <div class="actieveGoodsLeft">
        <div v-for="(item,index) in temparr" :key="index" class="actieveGoodsList" @click="goShop(item,0)">
          <div class="actieveImg">
            <img :src="item.cover+'?x-oss-process=image/resize,w_700/format,jpg/quality,q_85'" alt="">
          </div>
          <div class="actieveGoodsTitle">
            {{ item.title | ellipsis(5) }}
          </div>
          <div class="actieveGoodsPrice">
            <div class="actieveGoodsPriceSymbol">￥</div>
            <div class="actieveGoodsPriceNum">{{ item.seckillPrice }}</div>
            <div v-if="item.seckillPrice!=item.oldPrice" class="actieveGoodsPriceOrl">￥{{ item.oldPrice }}</div>
          </div>
        </div>
      </div>
      <!-- 限时抢购 -->
      <div class="actieveGoodsRight">
        <div v-for="(item,index) in temparr1" :key="index" class="actieveGoodsList" @click="goShop(item,1)">
          <div class="actieveImg">
            <img :src="item.cover+'?x-oss-process=image/resize,w_700/format,jpg/quality,q_85'" alt="">
          </div>
          <div class="actieveGoodsTitle">
            {{ item.title | ellipsis(5) }}
          </div>
          <div class="actieveGoodsPrice">
            <div class="actieveGoodsPriceSymbol">￥</div>
            <div class="actieveGoodsPriceNum">{{ item.seckillPrice }}</div>
            <div v-if="item.seckillPrice!=item.oldPrice" class="actieveGoodsPriceOrl">￥{{ item.oldPrice }}</div>
          </div>
        </div>
        <!-- <div class="actieveGoodsList" @click="goShopMe">
          <div class="actieveImg">
            <img style="object-fit: cover;" src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/active/20240110/a1272f31c1c541f7a0f09c20998d6070.jpg" alt="">
          </div>
          <div class="actieveGoodsTitle">
            空调清洗
          </div>
          <div class="actieveGoodsPrice">
            <div class="actieveGoodsPriceNum2">低至￥89起</div>
          </div>
        </div> -->
      </div>
    </div>
    <div style="height:5px" />
  </div>
</template>

<script>
import { addData } from '@/utils/upLog.js'
import { getTejiaList, getYouhuiList } from '@/api/index'
export default {
  data() {
    return {
      temparr: this.$store.state.Index.activeTttj,
      temparr1: this.$store.state.Index.activeThzq
    }
  },
  created() {
    this.getTejiaList()
    this.getYouhuiList()
  },
  methods: {
    // 获取天天特价
    getTejiaList() {
      getTejiaList().then(res => {
        if (res.status == 200) {
          let cache = this.$store.state.Index.activeTttj

          if (cache == '' || JSON.stringify(res.data[0].seckillGoodsList.slice(0, 2)) != JSON.stringify(cache)) {
            if (res.data.length > 0) {
              if (res.data[0].seckillGoodsList.length > 0) {
                this.temparr = res.data[0].seckillGoodsList.slice(0, 2)
                this.$store.state.Index.activeTttj = res.data[0].seckillGoodsList.slice(0, 2)
              }
            }
          }
        }
      })
    },
    // 获取优惠专区
    getYouhuiList() {
      getYouhuiList().then(res => {
        if (res.status == 200) {
          let cache = this.$store.state.Index.activeThzq
          if (cache == '' || JSON.stringify(res.data.list.slice(0, 2)) != JSON.stringify(cache)) {
            if (res.data.list.length > 0) {
              this.temparr1 = res.data.list.slice(0, 2)
              this.$store.state.Index.activeThzq = res.data.list.slice(0, 2)
            }
          }
        }
      })
    },
    // 去店铺
    goShop(item, val) {
      // 记录优选店铺轨迹
      // 轨迹埋点
      if (val == 0) {
        addData(22)
      } else if (val == 1) {
        addData(23)
      }
      this.$store.state.cart.cartData[0].goodsList = []
      this.$router.push({
        name: 'Shop',
        query: {
          id: item.marketId
        }
      })
    },
    // 去店铺
    goShopMe() {
      this.$router.push({
        name: 'SetMeal',
        query: {
          id: 1965,
          type: 2
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.actieve{
    width: 710px;
    // background: linear-gradient(180deg,#ffe2e4, #ffffff 20%);
    background-size: 100% 100%;
    border-radius: 16px;
    margin: 0 auto;
    margin-top: 16px;
    padding-bottom: 20px;
    .actieveTitle{
      display: flex;
      div {
        width: 50%;
        margin-top: -6px;
        img{
          width: 136px;
          height: 34px;
          margin-left: 22px;
        }
      }
    }
    .actieveGoods{
        display: flex;
        margin-top: 10px;
        overflow: hidden;
        .actieveGoodsLeft, .actieveGoodsRight {
            width: 50%;
            margin-left: 22px;
            // height: 230px;
            display: flex;
            .actieveGoodsList{
                margin-right: 11px;
            }
        }
        .actieveImg{
            img {
                width: 151px;
                height: 151px;
                border-radius: 12px;

            }
        }
        .actieveGoodsTitle{
            font-size: 24px;
            font-weight: 500;
            font-family: PingFangSC-Medium;
            color: #333333;
        }
        .actieveGoodsPrice{
          display: flex;
          margin-top: 7px;
            .actieveGoodsPriceSymbol{
                font-size: 18px;
                color: #ff301e;
                font-family: PingFangSC-Medium;
                font-weight: 600;
                margin-top: 6px;
            }
            .actieveGoodsPriceNum{
                font-size: 30px;
                font-family: PingFangSC-Medium;
                font-weight: 500;
                color: #ff301e;
            }
            .actieveGoodsPriceNum2{
                font-size: 28px;
                font-family: PingFangSC-Medium;
                font-weight: 500;
                color: #ff301e;
            }
            .actieveGoodsPriceOrl{
                font-size: 22px;
                color: #999999;
                margin-left: 4px;
                margin-top: 6px;
                text-decoration:line-through;
            }
        }
    }
}
</style>
