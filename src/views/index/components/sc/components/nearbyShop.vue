<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2021-05-31 14:25:20
 * @LastEditors: 孙立政
 * @LastEditTime: 2023-02-16 14:46:35
-->
<template>
  <div class="nearby">
    <div class="nearbyTitle">
      附近的店
    </div>
    <div class="nearbyShopList" @click="setLog">
      <GoodsCard v-for="(item,index) in list" :key="index" type="1" :list="item" />
    </div>
  </div>
</template>

<script>
import GoodsCard from '@/components/GoodsCard'
import { getShopList } from '@/api/index'
import { addData } from '@/utils/upLog.js'
export default {
  components: {
    GoodsCard
  },
  data() {
    return {
      list: []
    }
  },
  created() {
    this.getShopList()
  },
  mounted() {
    let self = this
    AlipayJSBridge.call('LocationMsg', {}, function(result) {
      if (result.error == 1) {
        self.$toast('定位获取失败，请检查定位权限')
      }
      let locationdata = JSON.parse(result.locationMsg)
      self.$store.state.location.latitude = locationdata.latitude
      self.$store.state.location.longitude = locationdata.longitude
      self.$store.state.location.address = locationdata.address
      self.getShopList()
    })
  },
  methods: {
    // 获取优选店铺
    getShopList() {
      let data = {
        marketRequestVO: {
          page: 1,
          size: 10,
          latitude: this.$store.getters.getLocation.latitude ? this.$store.getters.getLocation.latitude : 28.592388,
          longitude: this.$store.getters.getLocation.longitude ? this.$store.getters.getLocation.longitude : 119.275865,
          regionId: this.$store.getters.getRegionId,
          orderBy: 3,
          isTakeaway: true
        }
      }
      getShopList(data).then((res) => {
        if (res.status == 200) {
          this.list = res.marketVOList
        }
      })
    },
    setLog() {
      // 记录附近店铺轨迹
      addData(25)
    }
  }
}
</script>

<style lang="scss" scoped>
.nearby{
    width: 710px;
    margin: 0 auto;
    margin-top: 25px;
    .nearbyTitle{
        height: 50px;
        font-size: 36px;
        font-family: PingFangSC-Medium;
        font-weight: 600;
        color: #333333;
        line-height: 50px;
    }
    .nearbyShopList{
      margin-top: 24px;
    }
}
</style>
