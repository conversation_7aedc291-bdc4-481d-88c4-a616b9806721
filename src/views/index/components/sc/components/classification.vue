<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2021-05-29 13:57:12
 * @LastEditors: 孙立政
 * @LastEditTime: 2023-03-16 10:03:41
-->
<template>
  <div class="classfy">
    <div>
      <ul class="classify">
        <!-- <li @click="goTo({name:'拼团'})">
          <div>
            <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/home/<USER>/classify/sc.png" alt="">
          </div>
        </li>
        <li @click="goTo({name:'外卖'})">
          <div>
            <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/home/<USER>/classify/gif1.gif" alt="">
          </div>
        </li> -->
        <li v-for="(item, index) in list" v-show="item.id != 73 && item.id != 14" :key="index" @click="goTo(item)">
          <div>
            <img :src="item.icon" alt="">
          </div>
        </li>
      </ul>
      <ul class="classifyD">
        <li v-for="(item, index) in listD" :key="index" @click="goToD(item)">
          <div>
            <img :src="item.icon" alt="">
          </div>
        </li>
      </ul>
    </div>

    <div v-if="ifOpen" class="adImgs" @click="funIfOpen">
      <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/school/zhxyyd.png" alt="">
    </div>
    <School v-if="ifOpen" @funIfOpen="funIfOpen" />
  </div>
</template>

<script>
import School from '@/components/Advert/school.vue'
import { getBindExist } from '@/api/school'
import { getCategory } from '@/api/index'
import { UserInfo } from '@/api/my'
// import { addData } from '@/utils/upLog.js'
// import { activityNo } from '@/config/die'

export default {
  components: {
    School
  },
  data() {
    return {
      list: [
        // {
        //   icon: 'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/home/<USER>/classify/20240402/icon11.png',
        //   name: '那美云店'
        // },
        {
          icon: 'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/home/<USER>/classify/20240402/icon13.png',
          name: '商城'
        },
        {
          icon: 'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/home/<USER>/classify/20240402/icon2.png',
          name: '外卖'
        },
        {
          icon: 'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/home/<USER>/classify/20240402/icon3.png?a=10',
          name: '美食',
          id: 12
        },
        {
          icon: 'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/home/<USER>/classify/20240402/icon4.png',
          name: '菜市场',
          id: 13
        },
        {
          icon: 'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/home/<USER>/classify/20240402/icon5.png',
          name: '疗休养',
          id: 15
        }
      ],
      listD: [
        {
          icon: 'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/home/<USER>/classify/20240402/icon6.png',
          name: '助农集市'
        },
        {
          icon: 'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/home/<USER>/classify/jtly.png',
          name: '点滴超市'
        },
        {
          icon: 'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/home/<USER>/classify/20240402/icon8.png',
          name: '智慧校园'
        },
        // {
        //   icon: this.$store.state.Index.template.iconEight,
        //   name: '智慧影院'
        // },
        {
          icon: 'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/home/<USER>/classify/20240402/icon9.png',
          name: '人事劳务'
        },
        {
          icon: 'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/home/<USER>/classify/20240402/icon10.png',
          name: '本地生活'
        }

      ],
      isExist: false,
      ifOpen: false
    }
  },
  created() {
    // this.getCategory()
    // this.getBindExist()
  },
  methods: {
    // 关闭智慧校园入口
    funIfOpen() {
      localStorage.setItem('schoolStatus', 1)
      this.ifOpen = false
    },
    // 获取分类
    getCategory() {
      getCategory().then((res) => {
        if (res.status == 200) {
          let data = this.$store.state.Index.classify
          if (data == '' || JSON.stringify(res.data) != JSON.stringify(data)) {
            let newdata = res.data
            for (let i = 0; i < newdata.length; i++) {
              if (newdata[i].id == 40) {
                newdata.splice(i, 1)
              }
              if (newdata[i].id == 41) {
                newdata.splice(i, 1)
              }
              if (newdata[i].id == 42) {
                newdata.splice(i, 1)
              }
            }
            this.list = newdata
            this.$store.state.Index.classify = newdata
          }
        }
      })
    },
    // 跳转对应类目
    goTo(item) {
      var u = navigator.userAgent
      var isiOS = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/) // ios终端
      // if (this.$store.getters.getUserId == null) {
      //   this.$router.push({ name: 'PwdLogin' })
      //   return
      // } else {
      // 记录目录轨迹
      // addData(1)
      if (item.name === '菜市场') {
        this.$router.push({
          name: 'Classify',
          query: {
            id: item.id,
            name: item.name
          }
        })
      } else if (item.name === '酒店') {
        this.$router.push({
          path: '/Classify',
          query: {
            id: item.id,
            name: item.name,
            cateId: 14,
            isTakeaway: null
          }
        })
      } else if (item.name === '美食') {
        this.$router.push({
          name: 'Classify',
          query: {
            id: item.id,
            name: item.name,
            cateId: 12,
            isTakeaway: null
          }
        })
      } else if (item.name === '外卖') {
        this.$router.push({
          path: '/Classify',
          query: {
            id: item.id,
            name: item.name,
            cateId: 0,
            isTakeaway: true
          }
        })
        // AlipayJSBridge.call('GetJurisdiction', function(result) {
        //   console.log(result)
        // })
      } else if (item.name === '商城') {
        // this.$router.push({
        //   name: 'MarketHome',
        //   query: {
        //     activityNo: activityNo
        //   }
        // })
        let userNo = this.$store.getters.getUserNo
        let phone = this.$store.getters.getPhone
        let query = ''
        if (userNo != null || phone != null && userNo != '' && phone != '') {
          query = `&query=userNo%3D${userNo}%26phone%3D${phone}`
        }
        console.log(query)
        let urls = `weixin://dl/business/?appid=wxf72e75902d97af48&path=pages/index/index` + query
        if (!isiOS) {
          window.location.href = urls
        } else {
          AlipayJSBridge.call('OpenAppByRouter', {
            urlStr: urls
          }, function(result) {})
        }
      } else if (item.name === '疗休养') {
        this.$router.push({
          path: '/rest/restDefault',
          query: {
            id: item.id,
            name: item.name
          }
        })
      } else if (item.name === '那美云店') {
        let urls = 'https://emallh5.namek.com.cn/emall/pages/index/index?tenant=scxddwlkjyxzrgs'
        let userNo = this.$store.getters.getUserNo
        let statusHeight = this.$store.getters.getStatusHeight
        if (isiOS) {
          statusHeight = 0
        }
        let url = urls + '&statusHeight=' + statusHeight + '&navigationBarHeight=' + this.$store.getters.getNavigationBarHeight + '&userNo=' + userNo
        AlipayJSBridge.call('pushWindow', {
          url: url,
          param: {
            readTitle: true,
            showOptionMenu: isiOS ? true : false
          },
          passData: {}
        })
      } else {
        this.$router.push({
          path: '/Classify',
          query: {
            id: item.id,
            name: item.name,
            cateId: 0,
            isTakeaway: null
          }
        })
      }

      // Tracker.click('CLICK_MAIN_ICON', { ext: { channel: 'h5' }})
      // }
      // 获取定位
      this.$store.commit('setLocation')
    },
    goToD(item) {
      var u = navigator.userAgent
      var isiOS = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/) // ios终端
      let userID = this.$store.getters.getUserId
      if (item.name === '助农集市') {
        // this.$toast('即将上线，敬请期待')
        // if (userID != 3402 && userID != 11485 && userID != 11721 && userID != 11949 && userID != 18302 && userID != 3393) {
        //   this.$toast('即将上线，敬请期待')
        //   return
        // }

        // this.$store.state.cart.cartData[0].goodsList = []
        // this.$store.state.market.marketData.remark = ''
        // this.$router.push({
        //   name: 'ShoppingMallIndex',
        //   query: {
        //     id: 1138
        //     // id: 880
        //   }
        // })
        let userNo = this.$store.getters.getUserNo
        let phone = this.$store.getters.getPhone
        let query = ''
        if (userNo != null || phone != null && userNo != '' && phone != '') {
          query = `&query=userNo%3D${userNo}%26phone%3D${phone}`
        }
        console.log(query)
        let urls = `weixin://dl/business/?appid=wxf72e75902d97af48&path=pages/index/index` + query
        if (!isiOS) {
          window.location.href = urls
        } else {
          AlipayJSBridge.call('OpenAppByRouter', {
            urlStr: urls
          }, function(result) {})
        }
      } else if (item.name === '人事劳务') {
        // this.$toast('即将上线，敬请期待')
        // 获取账户信息
        UserInfo(userID)
          .then((res) => {
            if (res.status == 200) {
              localStorage.setItem('username', res.data.username)
            }
          })
        this.$router.push({
          path: '/workIndex/home/<USER>'
        })
      } else if (item.name === '点滴超市') {
        // if (userID != 3402 && userID != 11485 && userID != 11721 && userID != 11949 && userID != 18302 && userID != 3393) {
        //   this.$toast('即将上线，敬请期待')
        //   return
        // }
        this.$router.push({
          path: '/Classify',
          query: {
            id: 73
            // id: 62
          }
        })
      } else if (item.name === '本地生活') {
        // this.$router.push({
        //   path: '/Classify',
        //   query: {
        //     id: 41,
        //     cateId: 41,
        //     name: '生活馆',
        //     isTakeaway: null
        //   }
        // })
        if (this.$store.getters.getUserId == null) {
          this.$router.push({ name: 'wxLogin2' })
          return
        }
        this.$router.push({
          name: 'External'
        })
      } else if (item.name === '智慧影院') {
        this.$router.push({
          name: 'Webview',
          query: {
            url: 'https://m.qianzhu8.com/cinema/main/movie?platformId=10390',
            type: 2
          }
        })
      } else if (item.name === '智慧校园') {
        this.$router.push({
          name: 'SchoolList'
        })
      }
      // 获取定位
      this.$store.commit('setLocation')
    },
    // 学校入口判断绑定关系
    getBindExist() {
      getBindExist().then(res => {
        if (res.status == 200) {
          console.log(res)
          if (res.data.isExist == true) {
            this.isExist = true
            if (localStorage.getItem('schoolStatus') != '1') {
              this.ifOpen = true
            }
          } else {
            this.isExist = false
          }
        }
      })
    },
    isSchoolStatus(val) {
      if (val.name == '智慧校园') {
        if (this.isExist == true) {
          return true
        } else {
          return false
        }
      } else if (val.name == '智慧影院') {
        if (this.isExist == true) {
          return false
        } else {
          return true
        }
      } else {
        return true
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.classfy {
  width: 710px;
  margin: 0 auto;
  height: auto;
  margin-top: 30px;
  margin-bottom: 30px;
  overflow: hidden;
  background-color: #fff;
  border-radius: 16px;

  .classify {
    width: 690px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    margin-top: 30px;

    li {
      width: 106px;
      flex-shrink: 0;
      text-align: center;

      img {
        width: 106px;
        height: 140px;
      }

      font-size: 25px;
      font-family: PingFangSC;
      font-weight: 400;
      color: #333333;

      .title {
        margin-top: 6px;
        font-weight: 400;
      }

      .ms_img {
        width: 113px;
        height: 92px;
      }
    }
  }

  .classifyD {
    width: 640px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    margin-top: 30px;
    margin-bottom: 25px;

    li {
      width: 85px;
      flex-shrink: 0;
      text-align: center;

      img {
        width: 85px;
        height: 115px;
      }

      font-size: 25px;
      font-family: PingFangSC;
      font-weight: 400;
      color: #333333;

      .title {
        margin-top: 6px;
        font-weight: 400;
      }
    }

    // li:nth-child(1) {
    //   img{
    //       width: 104px;
    //       height: 70px;
    //       margin-left: 12px;
    //   }
    // }
  }

  .adImgs {
    width: 676px;
    margin: 0 auto;
    position: relative;
    top: -120px;
    z-index: 1001;

    img {
      width: 676px;
      height: 378px;
    }
  }
}
</style>
