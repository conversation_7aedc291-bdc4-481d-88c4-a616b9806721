<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2021-05-29 11:34:50
 * @LastEditors: 孙立政
 * @LastEditTime: 2023-08-10 09:22:53
-->
<template>
  <div class="banner2" :style="{ top: topVal }">
    <van-swipe class="banner" :autoplay="5000" touchable indicator-color="#74E165" @change="onChange">
      <van-swipe-item v-for="(item, index) in list" :key="index">
        <img
          class="bannerImg"
          :src="item.image + '?x-oss-process=image/resize,w_700/format,jpg/quality,q_99'"
          @click="goActieve(item)"
        >
      </van-swipe-item>
    </van-swipe>
  </div>
</template>

<script>
import { initGetVirtualPhone } from '@/api/login'
import {
  nmyUrl
} from '@/config/die'
export default {
  data() {
    return {
      // list: this.$store.state.Index.banner
      list: [
        // {
        //   image: 'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/store/banner/20250305/38-3-dd.png',
        //   url: '1'
        // },
        // {
        //   image: 'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/home/<USER>/banner/20250303/38-dd.png',
        //   url: '99'
        // },
        {
          image: 'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/home/<USER>/banner/2025/0618/20250618.jpg',
          url: '99'
        },
        // {
        //   image: 'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/store/banner/2025/04/20250415.png',
        //   url: '10199'
        // },
        // {
        //   image: 'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/home/<USER>/banner/2025/0412/banner.jpeg',
        //   url: '10218'
        // },
        // {
        //   image: 'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/home/<USER>/banner/2025/0529/20250529.jpg',
        //   url: '100'
        // },
        // {
        //   image: 'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/home/<USER>/banner/2025/0604/20250604.jpg',
        //   url: '99'
        // },
        // {
        //   image: 'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/store/banner/2025/06/13/20250613.jpg',
        //   url: '10029'
        // },
        {
          image: 'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/home/<USER>/banner/2025/0620/shanmu.png',
          url: '1'
        },
        // {
        //   image: 'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/store/activity/20250514/20250527.jpg',
        //   url: '3'
        // },
        // {
        //   image: 'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/home/<USER>/banner/2025/0513/20250513.jpg',
        //   url: '88'
        // },
        // {
        //   image: 'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/home/<USER>/banner/2025/0321/2.png',
        //   url: '10185'
        // },
        // {
        //   image: 'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/home/<USER>/banner/2025/0321/1.png',
        //   url: '10183'
        // },
        // {
        //   image: 'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/store/banner/2025/03/12/d-1.png',
        //   url: '10165'
        // },
        // {
        //   image: 'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/store/banner/2025/03/12/d-2.png',
        //   url: '10164'
        // },
        {
          image: 'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/home/<USER>/activity/20250214/nameiyun-20250214.jpg',
          url: '2'
        }
        // {
        //   image: 'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/home/<USER>/activity/20240430-2.png',
        //   url: '2'
        // }
      ],
      isShowCouponList: [],
      topVal: '0px'
    }
  },
  created() {
    // 获取屏幕高度
    let topVal = Number(this.$store.getters.getStatusHeight) + 40 + 'px'
    this.topVal = '-' + topVal
    console.log(this.topVal)
  },
  methods: {
    getShare() {
      this.$toast('欢迎进店消费')
    },
    // 跳转
    async goActieve(val) {
      var u = navigator.userAgent
      var isiOS = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/) // ios终端
      // if (this.$store.getters.getUserId == null) {
      //   this.$router.push({ name: 'PwdLogin' })
      //   return
      // }

      if (val.url == '2') {
        const { status, message } = await initGetVirtualPhone({
          platId: 5
        })
        if (status === 401) {
          this.$toast('请先登录')
          return
        }
        if (status !== 200) {
          this.$toast('获取号码失败，请稍后再试')
          return
        }

        let userNo = this.$store.getters.getUserNo

        let statusHeight = this.$store.getters.getStatusHeight

        let url = nmyUrl + '&statusHeight=' + statusHeight + '&navigationBarHeight=' + this.$store.getters.getNavigationBarHeight + '&userNo=' + this.signNmy(userNo) + '&phone=' + this.signNmy(message)

        console.log(url)

        AlipayJSBridge.call('pushWindow', {
          url: url,
          param: {
            readTitle: true,
            showOptionMenu: false,
            transparentTitle: 'always'
          },
          passData: {}
        })
      } else if (val.url == '1') {
        let userNo = this.$store.getters.getUserNo
        let phone = this.$store.getters.getPhone
        let query = ''
        if (userNo != null && phone != null && userNo != '' && phone != '') {
          query = `&query=userNo%3D${userNo}%26phone%3D${phone}`
        }
        console.log(query)

        let urls = `weixin://dl/business/?appid=wxf72e75902d97af48&path=pages/goods/list-sm` + query
        if (!isiOS) {
          window.location.href = urls
        } else {
          AlipayJSBridge.call('OpenAppByRouter', {
            urlStr: urls
          }, function(result) {})
        }
      } else if (val.url == '3') {
        let userNo = this.$store.getters.getUserNo
        let phone = this.$store.getters.getPhone
        let query = ''
        if (userNo != null || phone != null && userNo != '' && phone != '') {
          query = `&query=userNo%3D${userNo}%26phone%3D${phone}`
        }
        console.log(query)
        let urls = `weixin://dl/business/?appid=wxf72e75902d97af48&path=pages/activity/index` + query
        if (!isiOS) {
          window.location.href = urls
        } else {
          AlipayJSBridge.call('OpenAppByRouter', {
            urlStr: urls
          }, function(result) {})
        }
      } else if (val.url == '10029') {
        let userNo = this.$store.getters.getUserNo
        let phone = this.$store.getters.getPhone
        let query = ''
        if (userNo != null && phone != null && userNo != '' && phone != '') {
          query = `&query=userNo%3D${userNo}%26phone%3D${phone}%26categoryId%3D10029`
        }
        console.log(query)

        let urls = `weixin://dl/business/?appid=wxf72e75902d97af48&path=pages/goods/list` + query
        if (!isiOS) {
          window.location.href = urls
        } else {
          AlipayJSBridge.call('OpenAppByRouter', {
            urlStr: urls
          }, function(result) {})
        }
      } else if (val.url == '99') {
        return
      } else if (val.url == '88') {
        this.$router.push({
          name: 'SetMeal',
          query: {
            id: 1490,
            type: 2
          }
        })
      } else if (val.url == '100') {
        this.$router.push({
          name: 'SetMeal',
          query: {
            id: 167,
            type: 2
          }
        })
      } else {
        let userNo = this.$store.getters.getUserNo
        let phone = this.$store.getters.getPhone
        let goodsId = val.url
        let query = '&query=goodsId%3D' + goodsId
        if (userNo != null && phone != null && userNo != '' && phone != '') {
          query = query + `%26userNo%3D${userNo}%26phone%3D${phone}`
        }
        console.log(query)
        let urls = `weixin://dl/business/?appid=wxf72e75902d97af48&path=pages/goods/detail` + query
        if (!isiOS) {
          window.location.href = urls
        } else {
          AlipayJSBridge.call('OpenAppByRouter', {
            urlStr: urls
          }, function(result) {})
        }
      }
    },
    onChange(index) {
      this.$store.state.Index.bannerIndex = index
    },
    // 判断当前登录的手机号是否在数组中
    isShowCoupon() {
      if (this.isShowCouponList.length == 0) {
        return true
      }
      let phone = this.$store.getters.getPhone
      let isShow = false
      this.isShowCouponList.forEach(item => {
        if (item == phone) {
          isShow = true
        }
      })
      return isShow
    },
    // 加密
    signNmy(val) {
      const crypto = require('crypto')

      // 加密秘钥和iv，可以自行更改
      const secretKey = '92f33d7ce63483f5'
      const iv = '572019c19836877d'

      const cipher = crypto.createCipheriv('aes-128-cbc', secretKey, iv)

      let encrypted = cipher.update(val, 'utf8', 'hex')
      encrypted += cipher.final('hex')

      return encrypted
    }
  }
}
</script>

<style>
.van-image-preview__close-icon--top-right {
  top: 100px;
  font-size: 60px;
}
</style>

<style lang="scss" scoped>
.banner2 {
  position: relative;
  z-index: 1;
  ::v-deep .van-swipe__indicator {
    width: 25px;
    height: 3px;
  }

  .banner {
    width: 750px;
    height: 850px;
    margin: 0 auto;
    border-radius: 6px;
  }

  .bannerImg {
    width: 100%;
    height: 100%;
    border-radius: 6px;
  }
}
</style>
