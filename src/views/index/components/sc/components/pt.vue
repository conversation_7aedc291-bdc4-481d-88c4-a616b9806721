<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2021-06-02 17:41:44
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-08-03 18:25:16
-->
<template>
  <div v-if="displayStatus" class="advertise">
    <div class="goods_list" :style="'background-image: url('+ backPictureUrl+')'" @click="goMarket">
      <div v-for="item in detailDTOList" :key="item.id" class="goods_item" @click.stop="goMarketDetil(item)">
        <PtGoods :item="item" />
      </div>
    </div>
  </div>
</template>

<script>
import PtGoods from './pt-goods.vue'
import { campaignBannerDetail } from '@/api/index'
import { activityNo } from '@/config/die'
export default {
  components: {
    PtGoods
  },
  data() {
    return {
      backPictureUrl: '',
      activityNo: '',
      detailDTOList: [],
      displayStatus: true
    }
  },
  created() {
    this.getlist()
  },
  methods: {
    getlist() {
      campaignBannerDetail({ regionId: 1, activityTypeNo: 2 }).then(res => {
        if (res.status == 200) {
          this.backPictureUrl = res.data.backPictureUrl
          this.activityNo = res.data.activityNo
          this.detailDTOList = res.data.detailDTOList
          this.displayStatus = res.data.displayStatus
        }
      })
    },
    goMarket() {
      this.$router.push({
        name: 'MarketPt',
        query: {
          activityNo: activityNo
        }
      })
    },
    goMarketDetil(item) {
      if (item.activityStatus == 3) {
        return
      }
      this.$router.push({
        name: 'MarketPtDetails',
        query: {
          id: item.activityJoinNo,
          goodsId: item.goodsId,
          activityNo: this.activityNo,
          activityJoinNo: item.activityJoinNo
        }
      })
    }

  }
}
</script>

<style lang="scss" scoped>
.advertise{
    width: 100%;
    overflow: hidden;
    img{
      width: 100%;
      height: 100%;
    }
    .goods_list{
      width: 710px;
      height: 452px;
      margin: 0 auto;
      margin-top: 18px;
    //   background-image: url(https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/market/bjtest.png);
      background-size: 100% 100%;
      overflow: hidden;
      padding-left: 20px;
      overflow-x: auto;
      display: flex;
      border-radius: 13px;
    }
    .goods_item{
      height: 352px;
      margin-top: 100px;
      margin-right: 17px;

    }
}
</style>
