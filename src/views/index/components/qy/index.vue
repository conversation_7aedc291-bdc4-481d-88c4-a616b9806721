<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2021-05-25 14:22:27
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-09-09 14:40:22
-->
<template>
  <div class="home">
    <!-- 动态背景 -->
    <div class="bannerRoll">
      <NavHeight bgc="rgba(0,0,0,0)" />
      <Nav />
      <Search v-if="false" />
      <Banner v-if="false" />

      <div class="nhj_btn" @click="getPhone" />

      <Banner2 v-if="true" />
    </div>
    <Classification />

    <!-- 活动 -->
    <Actieve />

    <!-- 附近的店 -->
    <NearbyShop />
    <!-- <div>
      <div class="advertise">
        <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/home/<USER>/active/20240318-03.png" alt="">
      </div>
    </div> -->

    <div style="height:150px" />

    <!-- <van-dialog v-model="tipsStatus" title="公告" @confirm="close">
      <div class="tips">
        <div>
          因浙江省省农行在2025年06月10-17日期间会对支付系统进行升级，可能会影响交易性能，如遇到交易失败或无法交易，请及时反馈给当地运营或稍后重试。带来不便，敬请谅解！
        </div>
        <div>
          联系人及电话：
        </div>
        <div>
          吴亚玲 13566993383（676383）
        </div>
      </div>
    </van-dialog> -->
  </div>
</template>

<script>

import Nav from './components/nav'
import Search from './components/search'
import Banner from './components/banner'
import Classification from './components/classification'
import NearbyShop from './components/nearbyShop'
import Actieve from './components/actieve'
import Banner2 from './components/banner2'

import logger from '@/utils/aliLog'

export default {
  components: {
    Nav,
    Search,
    Banner,
    Classification,
    NearbyShop,
    Actieve,
    Banner2
  },
  data() {
    return {
      bannerIndex: this.$store.state.Index.bannerIndex,
      tipsStatus: false
    }
  },
  created() {},
  mounted() {
    // 回到顶部
    document.body.scrollTop = 0
    document.documentElement.scrollTop = 0

    logger.sum('庆元-日活')
    // if (sessionStorage.getItem('tips-20250612') != '1') {
    //   this.tipsStatus = true
    // }
  },
  methods: {
    // 拨打电话
    getPhone() {
      console.log('拨打电话')
      AlipayJSBridge.call(
        'CallPhone',
        {
          phoneNum: '0578-6035366'
        },
        function(result) { }
      )
      // this.$router.push({
      //   path: '/market/details',
      //   query: {
      //     id: '119'
      //   }
      // })
      // this.$router.push('/market/home')
    },
    close() {
      this.tipsStatus = false
      sessionStorage.setItem('tips-20250612', '1')
    }
  }
}
</script>

<style lang="scss" scoped>
.home{
  width: 100%;
  .tips{
    padding: 30px;
  }
  .advertise{
    width: 710px;
    margin: 0 auto;
    margin-top: 30px;
    img{
        width: 100%;
        height: 100%;
    }
  }
  .bannerRoll{
    width: 100%;
    height: 1020px;
    background-size: 100% 100%;
    overflow: hidden;
    position: relative;
    .nhj_btn{
      width: 100%;
      height: 700px;
      position: absolute;
      bottom: 50px;
    }
  }
}
</style>
