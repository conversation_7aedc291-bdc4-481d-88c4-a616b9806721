<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-05-22 15:18:58
 * @LastEditors: 孙立政
 * @LastEditTime: 2021-10-29 14:23:57
-->
<template>
  <div class="home">
    <div class="container">
      <div class="search" @click="goSearch">
        <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/index/lq/index/search.png" alt="">
        请输入商家或商品名称
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      keyword: ''
    }
  },
  created() {},
  mounted() {},
  methods: {
    goSearch() {
      // this.$toast('即将上线，敬请期待')

      // if (this.keyword.trim() == '') {
      //   this.$toast('请输入关键字')
      //   return false
      // }
      // this.$router.push({
      //   name: 'AgencyList',
      //   query: {
      //     attrTitles: '全部',
      //     keyword: this.keyword
      //   }
      // })
      // this.$store.state.searchKey = this.keyword
      this.$router.push({
        name: 'Search'
      })
    }
  }
}
</script>

<style scoped lang="scss">
.home {
  .container {
    .search {
        width: 710px;
        border-radius: 32px;
        margin: 0 auto;
        margin-top: 28px;
        height: 64px;
        background-color: #F5F5F5;
        font-size: 28px;
        font-family: PingFangSC;
        line-height: 64px;
        color: #999999;
        img{
          width: 34px;
          height: 34px;
          margin-left: 24px;
          margin-right: 14px;
          position: relative;
          top: 6px;
        }
    }
  }
}
</style>
