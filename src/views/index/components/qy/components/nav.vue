<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2021-05-29 10:15:25
 * @LastEditors: 孙立政
 * @LastEditTime: 2023-06-14 14:30:44
-->
<template>
  <div class="home">
    <div class="nav">
      <div class="navLeft" @click="goRegion">
        <div>
          <img v-if="isColor()" class="navAmap" src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/index/nav/amap.png" alt="">
          <img v-else class="navAmap" src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/home/<USER>/amap-b.png" alt="">
        </div>
        <div v-if="isColor()" class="navName1">
          {{ $store.getters.getRegionName|ellipsis(3) }}
        </div>
        <div v-else class="navName">
          {{ $store.getters.getRegionName|ellipsis(3) }}
        </div>
        <div>
          <img v-if="isColor()" class="navDown" src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/index/nav/down.png" alt="">
          <img v-else class="navDown" src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/home/<USER>/down-b.png" alt="">
        </div>
      </div>
      <div class="navRight">
        <img v-if="isColor()" class="navCode" src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/index/nav/code.png" alt="" @click="goCode">
        <img v-if="isColor()" class="navScan" src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/index/nav/scan.png" alt="" @click="goScan">
        <img v-if="!isColor()" class="navCode" src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/home/<USER>/code-b.png" alt="" @click="goCode">
        <img v-if="!isColor()" class="navScan" src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/home/<USER>/acan-b.png" alt="" @click="goScan">
      </div>
      <div v-if="tipsOff" class="tips" @click="closeTips">
        <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/region/qiehuan.png" alt="">
      </div>
    </div>

    <div v-if="height>60" class="navSearch" :style="styleVar">
      <div class="top" :style="styleVar" />
      <div class="search">
        <van-search v-model="keyword" placeholder="请输入商家或商品" show-action action-text="搜索" @search="goSearch" @focus="goSearch">
          <template #action>
            <div class="searchBtn" @click.stop="goSearch">搜索</div>
          </template>
        </van-search>
      </div>
    </div>
  </div>
</template>

<script>
// import { getScan } from '@/api/scan'
export default {
  data() {
    return {
      keyword: '',
      height: 0,
      tipsOff: false
    }
  },
  computed: {
    styleVar() {
      return {
        '---nav-height': this.$store.getters.getStatusHeight + 'px'
      }
    }
  },
  mounted() {
    if (localStorage.getItem('tipsOff') != 1) {
      this.tipsOff = true
    }
    window.addEventListener('scroll', this.handleScrollx, true)
  },
  destroyed() {
    window.removeEventListener('scroll', this.handleScrollx, true)
  },
  methods: {
    isColor() {
      return false
    },
    // 关闭提示
    closeTips() {
      localStorage.setItem('tipsOff', 1)
      this.tipsOff = false
    },
    // 付款码
    goCode() {
      this.$toast('敬请期待')
      // if (this.$store.getters.getUserId == null) {
      //   this.$router.push({ name: 'PwdLogin' })
      // } else {
      //   this.$router.push('/scan/qrCode')
      // }
    },
    // 扫码
    goScan() {
      // this.$toast('敬请期待')
      let self = this
      if (this.$store.getters.getUserId == null) {
        this.$router.push({ name: 'wxLogin2' })
      } else {
        AlipayJSBridge.call('QrCode', {}, function(result) { // 解析扫码数据
          if (result.error == 1) {
            self.$toast({
              duration: 5000, // 持续展示 toast
              forbidClick: true,
              message: 'APP启动失败,请关闭进程重新打开使用'
            })
          }
          if (self.GetQueryString('seatNo', result.qr_code_url)) {
            let statusHeight = self.$store.getters.getStatusHeight
            AlipayJSBridge.call('pushWindow', {
              url: result.qr_code_url + `&statusHeight=${statusHeight}&token=${self.$store.getters.getToken}`,
              param: {
                readTitle: true,
                showOptionMenu: false,
                transparentTitle: 'always'
              },
              passData: {}
            })
            return
          }
          let code = result.qr_code_url
          if (code.slice(0, 4) == 'http') { // 判断是否为url
            code = self.GetQueryString('code', code)
          } else {
            this.$message({
              message: '请扫描正确的二维码',
              type: 'warning'
            })
          }

          self.$router.push({
            path: '/scan/index',
            query: {
              code: code,
              regionId: 6
            }
          })
          // let data = {
          //   code: code
          // }
          // getScan(data).then((res) => {
          //   if (res.status == 200) {
          //     self.$router.push({
          //       path: '/scan/index',
          //       query: {
          //         code: code,
          //         regionId: 6
          //       }
          //     })
          //   }
          // })
        })
      }
    },
    GetQueryString(name, url) {
      var reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)')
      let newurl = url.split('\?')
      newurl = '?' + newurl[1]
      var r = newurl.substr(1).match(reg)
      if (r != null) return unescape(r[2])
      return null
    },
    // 搜搜
    goSearch() {
      this.$router.push({
        name: 'Search'
      })
    },
    // 获取页面滚动距离
    handleScrollx() {
      let top = document.documentElement.scrollTop || document.body.scrollTop || window.pageYOffset
      this.height = top
    },
    // 大区
    goRegion() {
      localStorage.setItem('tipsOff', 1)
      this.tipsOff = false
      this.$router.push({
        name: 'Region'
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.home{
    .nav{
        width: 710px;
        margin: 0 auto;
        height: 78px;
        line-height: 78px;
        display: flex;
        justify-content: space-between;
        position: relative;
        z-index: 999;
        .tips{
          position: absolute;
          top: 70px;
          left: 30px;
          z-index: 2;
          img{
            width: 208px;
            height: 68px;
          }
        }
        .navLeft {
            width: 80%;
            display: flex;
            font-size: 34px;
            font-weight: 600;
            font-family: PingFangSC-Medium;
            color: #fff;

            .navAmap{
                width: 38px;
                height: 38px;
                margin-top: 18px;
            }
            .navName1{
                margin-left: 3px;
                color: #fff;
            }
            .navName{
                margin-left: 3px;
                color: #000;
            }
            .navDown{
                width: 20px;
                height: 20px;
                margin-left: 7px;
            }
        }
        .navRight {
            width: 20%;
            display: flex;
            justify-content: space-between;
            .navCode{
                margin-left: 20px;
            }
            .navCode, .navScan{
                width: 44px;
                height: 44px;
                margin-top: 16px;
            }
        }
    }
    .navSearch{
      width: 100%;
      // height: 150px;
      height: calc(110px + var(---nav-height));
      background-color: #fff;
      // overflow: hidden;
      position: fixed;
      top: 0;
      z-index: 999;
      .top {
        height: var(---nav-height);
      }
      .search {
        width: 710px;
        border-radius: 44px 44px 0 0;
        margin: 0 auto;
        ::v-deep .van-search {
            width: 702px;
            border-radius:34px;
            overflow: hidden;
            padding: 0;
            border: 2px solid #64e151;
            margin-top: 15px;
        }
        ::v-deep .van-search__content {
            background-color: #fff;
        }
        ::v-deep .van-search__action {
            width: 104px;
            height: 64px;
            line-height: 64px;
            margin-right: 2px;
            font-size: 28px;
            color: #fff;
            background: linear-gradient(90deg,#32d349 1%, #64e151 99%);
            padding: 0 24px;
            border-radius: 30px;
            overflow: hidden;
        }
      }
    }
}
</style>
