<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2021-05-29 13:57:12
 * @LastEditors: 孙立政
 * @LastEditTime: 2023-06-14 16:28:12
-->
<template>
  <div class="classifcationy">
    <div>
      <ul class="classify">
        <li v-for="(item,index) in list" :key="index" @click="goTo(item)">
          <div>
            <img :src="item.icon" alt="">
          </div>
        </li>
      </ul>
      <ul class="classifyD">
        <li v-for="(item,index) in list2" :key="index" @click="goTo(item)">
          <div>
            <img :src="item.icon" alt="">
          </div>
        </li>
      </ul>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      list: [],
      list2: []
    }
  },
  created() {
    this.getCategory()
  },
  methods: {
    // 获取分类
    getCategory() {
      this.list = [
        { 'id': 55, 'icon': 'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/home/<USER>/class/icon1.png', 'isOpen': true, 'level': 1, 'name': '食堂', 'sort': 3, 'regionSn': 'Q855765', 'capitalPoolId': 1, 'pid': 0 },
        { 'id': 117, 'icon': 'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/home/<USER>/class/icon2.png', 'isOpen': true, 'level': 1, 'name': '美食', 'sort': 2, 'regionSn': 'Q855765', 'capitalPoolId': 1, 'pid': 0 },
        { 'id': 118, 'icon': 'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/home/<USER>/class/icon3.png', 'isOpen': true, 'level': 1, 'name': '美食', 'sort': 3, 'regionSn': 'Q855765', 'capitalPoolId': 1, 'pid': 0 },
        { 'id': 119, 'icon': 'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/home/<USER>/class/icon4.png', 'isOpen': true, 'level': 1, 'name': '菜市场', 'sort': 3, 'regionSn': 'Q855765', 'capitalPoolId': 1, 'pid': 0 },
        { 'id': 999, 'icon': 'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/home/<USER>/class/icon5.png', 'isOpen': true, 'level': 1, 'name': '菜市场', 'sort': 3, 'regionSn': 'Q855765', 'capitalPoolId': 1, 'pid': 0 }
      ]
      this.list2 = [
        { 'id': 109, 'icon': 'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/home/<USER>/class/icon6.png', 'isOpen': true, 'level': 1, 'name': '超市', 'sort': 5, 'regionSn': 'Q855765', 'capitalPoolId': 1, 'pid': 0 },
        { 'id': 122, 'icon': 'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/home/<USER>/class/icon7.png', 'isOpen': true, 'level': 1, 'name': '超市', 'sort': 5, 'regionSn': 'Q855765', 'capitalPoolId': 1, 'pid': 0 },
        { 'id': 123, 'icon': 'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/home/<USER>/class/icon8.png?a=1', 'isOpen': true, 'level': 1, 'name': '生活馆', 'sort': 5, 'regionSn': 'Q855765', 'capitalPoolId': 1, 'pid': 0 },
        { 'id': 124, 'icon': 'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/home/<USER>/class/icon9.png', 'isOpen': true, 'level': 1, 'name': '疗休养', 'sort': 5, 'regionSn': 'Q855765', 'capitalPoolId': 1, 'pid': 0 },
        { 'id': 666, 'icon': 'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/home/<USER>/class/icon11.png', 'isOpen': true, 'level': 1, 'name': '酒店', 'sort': 5, 'regionSn': 'Q855765', 'capitalPoolId': 1, 'pid': 0 }
      ]
    },
    // 跳转对应类目
    goTo(item) {
      // var u = navigator.userAgent
      // var isiOS = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/) // ios终端
      if (item.id == 118 || item.id == 123 || item.id == 119 || item.id == 122) { // 美食
        this.$router.push({
          name: 'Classify',
          query: {
            id: item.id,
            name: item.name
          }
        })
      } else if (item.id === 999) {
        this.$router.push({
          name: 'AgencyIndexQY'
        })
      } else if (item.id === 666) {
        this.$router.push({
          name: 'Canteen',
          query: {
            type: 0
          }
        })
      } else if (item.id === 55) {
        this.$toast('即将上线，敬请期待')
        // this.$router.push('/market/home')
        // let userNo = this.$store.getters.getUserNo
        // let phone = this.$store.getters.getPhone
        // let query = ''
        // if (userNo != null && phone != null && userNo != '' && phone != '') {
        //   query = `&query=userNo%3D${userNo}%26phone%3D${phone}`
        // }
        // console.log(query)

        // let urls = `weixin://dl/business/?appid=wxf72e75902d97af48&path=pages/index/index` + query
        // if (!isiOS) {
        //   window.location.href = urls
        // } else {
        //   AlipayJSBridge.call('OpenAppByRouter', {
        //     urlStr: urls
        //   }, function(result) {})
        // }
      } else {
        this.$toast('即将上线，敬请期待')
      }
      // 获取定位
      this.$store.commit('setLocation')
    }
  }
}
</script>

<style lang="scss" scoped>
.home{
    overflow: hidden;
    .classify{
        width: 690px;
        margin: 0 auto;
        display: flex;
        justify-content: space-between;
        margin-top:35px;
        li {
            width: 108px;
            flex-shrink: 0;
            text-align: center;
            img{
                width: 108px;
                height: 155px;
            }
            font-size: 24px;
            font-family: PingFangSC;
            font-weight: 400;
            color: #333333;
        }
    }
    .classifyD{
      width: 690px;
      margin: 0 auto;
      display: flex;
      justify-content: space-between;
      margin-top: 32px;
      li {
          width: 108px;
          flex-shrink: 0;
          text-align: center;
          img{
            width: 88px;
            height: 112px;
          }
          font-size: 24px;
          font-family: PingFangSC;
          font-weight: 400;
          color: #333333;
      }
    }
}
</style>
