<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2021-05-31 10:04:59
 * @LastEditors: 孙立政
 * @LastEditTime: 2023-06-15 13:47:54
-->
<template>
  <div class="actieve">
    <div class="left" @click="goClass(152)">
      <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/home/<USER>/active/20240426/a1.png" alt="">
    </div>
    <div class="right">
      <div @click="goClass(154)">
        <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/home/<USER>/active/20240426/a2.png" alt="">
      </div>
      <div @click="goClass(155)">
        <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/home/<USER>/active/20240426/a3.png" alt="">
      </div>

    </div>

    <div style="height:5px" />
  </div>
</template>

<script>
export default {
  data() {
    return {
    }
  },
  created() {
  },
  methods: {
    goClass(val) {
      if (val != 155 && val != 154) {
        this.$toast('即将上线，敬请期待')
        return
      }
      // this.$toast('即将上线，敬请期待')
      // return
      this.$router.push({
        name: 'Classify',
        query: {
          id: val,
          name: '福利',
          cateId: val
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.actieve {
  width: 710px;
  margin: 0 auto;
  margin-top: 30px;
  display: flex;
  justify-content: space-between;
  .left {
    width: 260px;
    height: 360px;
    img {
      width: 100%;
      height: 100%;
    }
  }
  .right {
    width: 435px;
    margin-left: 15px;
    img {
      width: 100%;
      height: 100%;
    }
  }

}
</style>
