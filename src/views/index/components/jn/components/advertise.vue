<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2021-06-02 17:41:44
 * @LastEditors: 孙立政
 * @LastEditTime: 2023-06-20 16:48:46
-->
<template>
  <div class="advertise">
    <div class="advertiseBox2" @click="goTo(99)">
      <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/home/<USER>/banner/20250402/a202504502.png" alt="">
    </div>
    <div class="advertiseBox2" @click="goTo(85)">
      <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/home/<USER>/activity/20250401/a/shfw.png" alt="">
    </div>
    <div class="advertiseBox2" @click="goTo(109)">
      <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/home/<USER>/activity/20250401/a/bmcs.png" alt="">
    </div>
    <div class="advertiseBox" @click="goShop">
      <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/home/<USER>/advertise/20230905.png" alt="">
    </div>
  </div>
</template>

<script>
export default {
  methods: {
    goTo(val) {
      if (val == 99) {
        this.$toast('即将上线，敬请期待')
      }
      if (val == 85) {
        this.$router.push({
          name: 'Classify',
          query: {
            id: 85,
            name: '生活馆'
          }
        })
      }
      if (val == 109) {
        this.$router.push({
          name: 'Classify',
          query: {
            id: 109,
            name: '超市'
          }
        })
      }
    },
    goShop() {
      AlipayJSBridge.call('CallPhone', {
        phoneNum: '0578-5625806'
      }, function(result) {})
      // if (this.$store.getters.getUserId == null) {
      //   this.$router.push({ name: 'PwdLogin' })
      //   return
      // }
      // this.$router.push({ name: 'ActivityJyz' })
      // this.$store.state.searchKey = '加油站'
      // // 搜索
      // this.$router.push({
      //   path: '/results',
      //   query: {}
      // })
    }
  }
}
</script>

<style lang="scss" scoped>
.advertise{
    width: 100%;
    // background: #fff;
    margin-top: 20px;
    .advertiseBox{
        width: 710px;
        height: 255px;
        margin: 0 auto;
        img{
            width: 100%;
            height: 100%;
            border-radius: 15px;
        }
    }
    .advertiseBox2{
        width: 710px;
        margin: 0 auto;
        img{
            width: 100%;
            height: 100%;
            border-radius: 13px;
            border: 1px solid #eee;
        }
    }
}
</style>
