<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2021-05-31 10:04:59
 * @LastEditors: 孙立政
 * @LastEditTime: 2023-06-15 13:47:54
-->
<template>
  <div class="actieve">
    <div class="actieve_one">
      <div class="a_card" style="background: linear-gradient(180deg, rgba(229, 234, 255, 1) 0%, rgba(255, 255, 255, 1) 30%,rgba(255, 255, 255, 1) 100%);">
        <div class="a_card_top">
          <div class="a_card_name">特惠专区</div>
          <div>
            <img class="a_card_icon" style="width: 32px;" src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/home/<USER>/act1.png" alt="">
          </div>
        </div>
        <div class="a_card_goods_list">
          <div v-for="(item,index) in temparr" :key="index" class="a_card_goods_item" @click="goShop(item,0)">
            <div>
              <img class="a_card_goods_img" :src="item.cover" alt="">
            </div>
            <div class="a_card_goods_price">
              <span class="price_icon">￥</span>
              <span>{{ item.seckillPrice }}</span>
            </div>
          </div>
        </div>
      </div>
      <div class="a_card" style="background: linear-gradient(180deg, rgba(197, 255, 219, 1) 0%,rgba(255, 255, 255, 1) 30%, rgba(255, 255, 255, 1) 100%);">
        <div class="a_card_top">
          <div class="a_card_name">天天特价</div>
          <div>
            <img class="a_card_icon" style="width: 86px;" src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/home/<USER>/act2.png" alt="">
          </div>
        </div>
        <div class="a_card_goods_list">
          <div v-for="(item,index) in temparr1" :key="index" class="a_card_goods_item" @click="goShop(item,1)">
            <div>
              <img class="a_card_goods_img" :src="item.cover" alt="">
            </div>
            <div class="a_card_goods_price">
              <span class="price_icon">￥</span>
              <span>{{ item.seckillPrice }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- two -->
    <div class="actieve_two">
      <div class="a_card" style="background: linear-gradient(180deg, rgba(255, 255, 255, 1) 0%, rgba(255, 255, 255, 1) 100%);">
        <div class="a_card_top">
          <div class="a_card_name">火锅烤肉</div>
          <div class="a_card_name" style="margin-left: 8px;">西餐日料</div>
        </div>
        <div class="a_card_goods_list">
          <div class="a_card_goods_item" @click="goClass(48)">
            <div>
              <img class="a_card_goods_img" src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/home/<USER>/ab1.png" alt="">
            </div>
          </div>
          <div class="a_card_goods_item" @click="goClass(49)">
            <div>
              <img class="a_card_goods_img" src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/home/<USER>/ab2.png" alt="">
            </div>
          </div>
        </div>
      </div>
      <div class="a_card" style="background: linear-gradient(180deg, rgba(255, 255, 255, 1) 0%, rgba(255, 255, 255, 1) 100%);">
        <div class="a_card_top">
          <div class="a_card_name">甜品奶茶</div>
          <div class="a_card_name" style="margin-left: 8px;">水果特产</div>
        </div>
        <div class="a_card_goods_list">
          <div class="a_card_goods_item" @click="goClass(51)">
            <div>
              <img class="a_card_goods_img" src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/home/<USER>/ab3.png" alt="">
            </div>
          </div>
          <div class="a_card_goods_item" @click="goClass(66)">
            <div>
              <img class="a_card_goods_img" src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/home/<USER>/ab4.png" alt="">
            </div>
          </div>
        </div>
      </div>
    </div>

    <div style="height:5px" />
  </div>
</template>

<script>
import { addData } from '@/utils/upLog.js'
import { getTejiaList, getYouhuiList } from '@/api/index'
export default {
  data() {
    return {
      temparr: this.$store.state.Index.activeTttj,
      temparr1: this.$store.state.Index.activeThzq
    }
  },
  created() {
    this.getTejiaList()
    this.getYouhuiList()
  },
  methods: {
    // 获取天天特价
    getTejiaList() {
      getTejiaList().then(res => {
        if (res.status == 200) {
          let cache = this.$store.state.Index.activeTttj

          if (cache == '' || JSON.stringify(res.data[0].seckillGoodsList.slice(0, 2)) != JSON.stringify(cache)) {
            if (res.data.length > 0) {
              if (res.data[0].seckillGoodsList.length > 0) {
                this.temparr = res.data[0].seckillGoodsList.slice(0, 2)
                this.$store.state.Index.activeTttj = res.data[0].seckillGoodsList.slice(0, 2)
              }
            }
          }
        }
      })
    },
    // 获取优惠专区
    getYouhuiList() {
      getYouhuiList().then(res => {
        if (res.status == 200) {
          let cache = this.$store.state.Index.activeThzq
          if (cache == '' || JSON.stringify(res.data.list.slice(0, 2)) != JSON.stringify(cache)) {
            if (res.data.list.length > 0) {
              this.temparr1 = res.data.list.slice(0, 2)
              this.$store.state.Index.activeThzq = res.data.list.slice(0, 2)
            }
          }
        }
      })
    },
    goClass(val) {
      this.$router.push({
        name: 'Classify',
        query: {
          id: 46,
          name: '美食',
          cateId: val
        }
      })
    },
    // 去店铺
    goShop(item, val) {
      // 记录优选店铺轨迹
      // 轨迹埋点
      if (val == 0) {
        addData(22)
      } else if (val == 1) {
        addData(23)
      }
      this.$store.state.cart.cartData[0].goodsList = []
      this.$router.push({
        name: 'Shop',
        query: {
          id: item.marketId
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.actieve{
    width: 710px;
    margin: 0 auto;
    margin-top: 20px;

    .actieve_one{
      display: flex;
      justify-content: space-between;
    }
    .actieve_two{
      display: flex;
      justify-content: space-between;
    }
    .a_card{
      width: 346px;
      height: 282px;
      border-radius: 12px;
      overflow: hidden;
      .a_card_top{
        height: 70rpx;
        display: flex;
        margin-top: 16px;
        margin-left: 20px;
        .a_card_name{
          font-size: 33px;
          font-family: PingFangSC-Medium;
          color: rgba(51, 51, 51, 1);
        }
        .a_card_icon{
          height: 32px;
          display: block;
          margin-left: 8px;
          margin-top: 5px;
        }
      }
      .a_card_goods_list{
        width: 90%;
        margin: 0 auto;
        display: flex;
        justify-content: space-between;
        margin-top: 20px;
        .a_card_goods_item{
          width: 144px;
          text-align: center;
          .a_card_goods_img{
            width: 130px;
            height: 130px;
            display: block;
            border-radius: 50%;
            margin: 0 auto;
            object-fit: cover;
          }
          .a_card_goods_price{
            font-size: 32px;
            font-family: PingFangSC-Medium;
            color: #FF301E;
            margin-top: 13px;
            margin-left: 10px;
            .price_icon{
              font-size: 22px;
              margin-left: -15px;
            }
          }
        }
      }

    }

    .actieve_two{
      margin-top: 18px;
      .a_card{
        width: 346px;
        height: 232px;
      }
      .a_card_top{
        .a_card_name{
          width: 165px;
          text-align: left;
        }
      }
    }
}
</style>
