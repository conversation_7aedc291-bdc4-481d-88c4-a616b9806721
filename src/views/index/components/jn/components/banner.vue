<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2021-05-29 11:34:50
 * @LastEditors: 孙立政
 * @LastEditTime: 2023-08-10 09:22:53
-->
<template>
  <div class="home">
    <van-swipe class="banner" :autoplay="8000" touchable indicator-color="#74E165" @change="onChange">
      <van-swipe-item v-for="(item, index) in list" :key="index">
        <img class="bannerImg" :src="item.image+'?x-oss-process=image/resize,w_700/format,jpg/quality,q_99'" @click="goActieve(item)">
      </van-swipe-item>
    </van-swipe>
  </div>
</template>

<script>
import { getBannerList } from '@/api/index'
export default {
  data() {
    return {
      // list: this.$store.state.Index.banner
      list: [
        {
          image: 'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/home/<USER>/banner/banner.png'
        }
      ]
    }
  },
  created() {
    // this.getBannerList()
  },
  methods: {
    getBannerList() {
      getBannerList().then((res) => {
        if (res.status == 200) {
          let data = this.$store.state.Index.banner
          if (data == '' || JSON.stringify(res.data) != JSON.stringify(data)) {
            console.log(666)
            this.list = res.data
            this.$store.state.Index.banner = res.data
          }
        }
      })
    },
    // 跳转
    goActieve(val) {
      if (val.url == '202300711') {
        this.$router.push({ name: 'ActivityLqNsh' })
      }
    },
    onChange(index) {
      this.$store.state.Index.bannerIndex = index
    }
  }
}
</script>

<style>
.van-image-preview__close-icon--top-right{
    top: 100px;
    font-size: 60px;
}
</style>

<style lang="scss" scoped>
.home{
    ::v-deep .van-swipe__indicator{
      width: 25px;
      height: 3px;
    }
    .banner{
        width: 710px;
        height: 292px;
        margin: 0 auto;
        margin-top: 89px;
        border-radius: 20px;
    }
    .bannerImg{
        width: 710px;
        height: 292px;
        border-radius: 15px;
    }
}
</style>
