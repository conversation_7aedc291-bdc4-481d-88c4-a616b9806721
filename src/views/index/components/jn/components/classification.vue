<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2021-05-29 13:57:12
 * @LastEditors: 孙立政
 * @LastEditTime: 2023-06-14 16:28:12
-->
<template>
  <div class="jn_classify">
    <div>
      <ul class="classify">
        <li v-for="(item, index) in list" :key="index" @click="goTo(item)">
          <div>
            <img :src="item.icon" alt="">
          </div>
        </li>
      </ul>
      <ul class="classify">
        <li v-for="(item, index) in list2" :key="index" @click="goTo(item)">
          <div>
            <img :src="item.icon" alt="">
          </div>
        </li>
      </ul>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      list: [],
      list2: []
    }
  },
  created() {
    this.getCategory()
  },
  methods: {
    // 获取分类
    getCategory() {
      this.list = [
        { 'id': 55, 'icon': 'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/home/<USER>/class/icon1.png', 'isOpen': true, 'level': 1, 'name': '食堂', 'sort': 3, 'regionSn': 'Q855765', 'capitalPoolId': 1, 'pid': 0 },
        { 'id': 46, 'icon': 'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/home/<USER>/class/icon2.png', 'isOpen': true, 'level': 1, 'name': '美食', 'sort': 2, 'regionSn': 'Q855765', 'capitalPoolId': 1, 'pid': 0 },
        { 'id': 83, 'icon': 'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/home/<USER>/class/icon8.png', 'isOpen': true, 'level': 1, 'name': '美食', 'sort': 3, 'regionSn': 'Q855765', 'capitalPoolId': 1, 'pid': 0 },
        { 'id': 84, 'icon': 'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/home/<USER>/class/icon4.png', 'isOpen': true, 'level': 1, 'name': '菜市场', 'sort': 3, 'regionSn': 'Q855765', 'capitalPoolId': 1, 'pid': 0 }
      ]
      this.list2 = [
        { 'id': 109, 'icon': 'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/home/<USER>/class/icon5.png', 'isOpen': true, 'level': 1, 'name': '超市', 'sort': 5, 'regionSn': 'Q855765', 'capitalPoolId': 1, 'pid': 0 },
        { 'id': 85, 'icon': 'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/home/<USER>/class/icon9.png', 'isOpen': true, 'level': 1, 'name': '生活馆', 'sort': 5, 'regionSn': 'Q855765', 'capitalPoolId': 1, 'pid': 0 },
        { 'id': 15, 'icon': 'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/home/<USER>/class/icon7.png', 'isOpen': true, 'level': 1, 'name': '疗休养', 'sort': 5, 'regionSn': 'Q855765', 'capitalPoolId': 1, 'pid': 0 },
        { 'id': 52, 'icon': 'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/home/<USER>/class/icon3.png', 'isOpen': true, 'level': 1, 'name': '酒店', 'sort': 5, 'regionSn': 'Q855765', 'capitalPoolId': 1, 'pid': 0 }
      ]
    },
    // 跳转对应类目
    goTo(item) {
      if (item.id == 15) {
        // !!!生产是否更新？
        this.$toast('即将上线，敬请期待')
        // this.$router.push({
        //   path: '/rest/longquan/index',
        //   query: {}
        // })
      } else if (item.id == 52) {
        this.$toast('即将上线，敬请期待')

        // this.$router.push({
        //   path: '/Classify',
        //   query: {
        //     id: item.id,
        //     name: item.name,
        //     cateId: item.id,
        //     isTakeaway: null
        //   }
        // })
      } else if (item.id == 83) { // 美食
        // this.$toast('即将上线，敬请期待')

        this.$router.push({
          name: 'Classify',
          query: {
            id: item.id,
            name: item.name
          }
        })
      } else if (item.id == 109) { // 超市
        // this.$toast('即将上线，敬请期待')

        this.$router.push({
          name: 'Classify',
          query: {
            id: item.id,
            name: item.name
          }
        })
      } else if (item.id == 84) { // 菜市场
        // this.$toast('即将上线，敬请期待')

        this.$router.push({
          name: 'Classify',
          query: {
            id: item.id,
            name: item.name
          }
        })
      } else if (item.id == 85) {
        // this.$toast('即将上线，敬请期待')

        this.$router.push({
          name: 'Classify',
          query: {
            id: item.id,
            name: item.name
          }
        })
      } else if (item.id == 46) {
        this.$toast('即将上线，敬请期待')

        // this.$router.push({
        //   path: '/Classify',
        //   query: {
        //     id: item.id,
        //     name: item.name,
        //     cateId: item.id,
        //     isTakeaway: null
        //   }
        // })
      } else if (item.id == 55) {
        this.$toast('即将上线，敬请期待')

        // this.$router.push({
        //   path: '/canteen',
        //   query: {
        //     id: item.id,
        //     name: item.name,
        //     cateId: item.id,
        //     isTakeaway: null
        //   }
        // })
      } else if (item.name === '外卖') {
        this.$toast('即将上线，敬请期待')
        // this.$router.push({
        //   path: '/Classify',
        //   query: {
        //     id: item.id,
        //     name: item.name,
        //     cateId: 0,
        //     isTakeaway: true
        //   }
        // })
      } else if (item.id == 0) {
        this.$toast('即将上线，敬请期待')
      } else if (item.id == 52) {
        this.$toast('即将上线，敬请期待')

        // this.$router.push({
        //   path: '/Classify',
        //   query: {
        //     id: item.id,
        //     name: item.name,
        //     cateId: item.id,
        //     isTakeaway: null
        //   }
        // })
      }

      // 获取定位
      this.$store.commit('setLocation')
    }
  }
}
</script>

<style lang="scss" scoped>
.jn_classify {
  width: 710px;
  margin: 0 auto;
  background: #fff;
  margin-top: 20px;
  border-radius: 20px;
  padding-bottom: 20px;

  overflow: hidden;

  .classify {
    width: 650px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    margin-top: 25px;

    li {
      width: 108px;
      flex-shrink: 0;
      text-align: center;

      img {
        width: 108px;
        height: 135px;
      }

      font-size: 24px;
      font-family: PingFangSC;
      font-weight: 400;
      color: #333333;
    }
  }

  .classifyD {
    width: 690px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    margin-top: 32px;

    li {
      width: 108px;
      flex-shrink: 0;
      text-align: center;

      img {
        width: 66px;
        height: 66px;
      }

      font-size: 24px;
      font-family: PingFangSC;
      font-weight: 400;
      color: #333333;
    }
  }
}
</style>
