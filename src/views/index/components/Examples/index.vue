<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2021-05-25 14:22:27
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-09-09 14:40:22
-->
<template>
  <div class="home">
    <!-- 动态背景 -->
    <div class="bannerRoll" style="background-image: url(https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/index/actieve/zq/zqbj.png)">
      <NavHeight bgc="rgba(0,0,0,0)" />
      <!-- 头部 -->
      <Nav />
      <!-- 搜索 -->
      <Search />
      <!-- banner -->
      <Banner />
      <!-- 分类 -->
      <Classification />

    </div>

    <!-- 公告 -->
    <News />

    <!-- 天天特价/限时抢购 -->
    <Actieve />
    <!-- 优选店铺 -->
    <Optimization />
    <!-- 附近的店 -->
    <NearbyShop />

    <Bottom />

    <div style="height:200px" />
  </div>
</template>

<script>

import Nav from './components/nav'
import Search from './components/search'
import Banner from './components/banner'
import Classification from './components/classification'
import News from './components/news'
import Actieve from './components/actieve'
import Optimization from './components/optimization'
import NearbyShop from './components/nearbyShop'
import Bottom from './components/bottom'

export default {
  components: {
    Nav,
    Search,
    Banner,
    Classification,
    News,
    Actieve,
    Optimization,
    NearbyShop,
    Bottom
  },
  data() {
    return {
      bannerIndex: this.$store.state.Index.bannerIndex
    }
  },
  created() {},
  mounted() {
    // 回到顶部
    document.body.scrollTop = 0
    document.documentElement.scrollTop = 0
  },
  methods: {
  }
}
</script>

<style lang="scss" scoped>
.home{
  width: 100%;
  .advertiseZq{
    width: 730px;
    height: 260px;
    margin: 0 auto;
    margin-top: 24px;
    img{
        width: 100%;
        height: 100%;
    }
  }
  .bannerRoll{
    width: 100%;
    height: auto;
    background-size: 100% 100%;
    overflow: hidden;
  }
}
</style>
