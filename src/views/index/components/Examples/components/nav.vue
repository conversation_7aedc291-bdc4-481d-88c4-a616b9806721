<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2021-05-29 10:15:25
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-09-08 15:09:43
-->
<template>
  <div class="home">
    <div class="nav">
      <div class="navLeft" @click="goRegion">
        <div>
          <img class="navAmap" src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/index/nav/amap.png" alt="">
        </div>
        <div class="navName">
          {{ $store.getters.getRegionName|ellipsis(3) }}
        </div>
        <div>
          <img class="navDown" src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/index/nav/down.png" alt="">
        </div>
      </div>
      <div class="navRight">
        <img class="navCode" src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/index/nav/code.png" alt="" @click="goCode">
        <img class="navScan" src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/index/nav/scan.png" alt="" @click="scanPrivacy">
      </div>
      <div v-if="tipsOff" class="tips" @click="closeTips">
        <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/region/qiehuan.png" alt="">
      </div>
    </div>

    <div v-if="height>60" class="navSearch" :style="styleVar">
      <div class="top" :style="styleVar" />
      <div class="search">
        <van-search v-model="keyword" placeholder="请输入商家或商品" show-action action-text="搜索" @search="goSearch" @focus="goSearch">
          <template #action>
            <div class="searchBtn" @click.stop="goSearch">搜索</div>
          </template>
        </van-search>
      </div>
    </div>

  </div>
</template>

<script>
export default {
  components: {
  },
  data() {
    return {
      keyword: '',
      height: 0,
      tipsOff: false,
      privacyShow: false,
      privacyForm: {
        cancelName: '不允许',
        confirmName: '允许',
        title: '相机权限',
        content: '帮您实现扫码二维码、拍照发表评价或上传信息认证等功能'
      }
    }
  },
  computed: {
    styleVar() {
      return {
        '---nav-height': this.$store.getters.getStatusHeight + 'px'
      }
    }
  },
  created() {},
  mounted() {
    window.addEventListener('scroll', this.handleScrollx, true)
  },
  destroyed() {
    window.removeEventListener('scroll', this.handleScrollx, true)
  },
  methods: {
    // 关闭提示
    closeTips() {
      localStorage.setItem('tipsOff', 1)
      this.tipsOff = false
    },
    // 付款码
    goCode() {
      this.$store.commit('setIsPrivacy', 0)
    },
    scanPrivacy() {
      this.$store.commit('setIsPrivacy', 0)
    },
    // 搜搜
    goSearch() {
      this.$store.commit('setIsPrivacy', 0)
    },
    // 获取页面滚动距离
    handleScrollx() {
      let top = document.documentElement.scrollTop || document.body.scrollTop || window.pageYOffset
      this.height = top
    },
    // 大区
    goRegion() {
      this.$store.commit('setIsPrivacy', 0)
    }
  }
}
</script>

<style lang="scss" scoped>
.home{
    .nav{
        width: 710px;
        margin: 0 auto;
        height: 78px;
        line-height: 78px;
        display: flex;
        justify-content: space-between;
        position: relative;
        .tips{
          position: absolute;
          top: 70px;
          left: 30px;
          z-index: 2;
          img{
            width: 208px;
            height: 68px;
          }
        }

        .navLeft {
            width: 80%;
            display: flex;
            font-size: 34px;
            font-weight: 600;
            font-family: PingFangSC-Medium;
            color: #f7fbff;
            .navAmap{
                width: 38px;
                height: 38px;
                margin-top: 18px;
            }
            .navName{
                margin-left: 3px;
            }
            .navDown{
                width: 20px;
                height: 20px;
                margin-left: 7px;
                position: relative;
                top: -2px;
            }
        }
        .navRight {
            width: 20%;
            display: flex;
            justify-content: space-between;
            .navCode{
                margin-left: 20px;
            }
            .navCode, .navScan{
                width: 44px;
                height: 44px;
                margin-top: 16px;
            }
        }
    }
    .navSearch{
      width: 100%;
      // height: 150px;
      height: calc(110px + var(---nav-height));
      background-color: #fff;
      // overflow: hidden;
      position: fixed;
      top: 0;
      z-index: 999;
      .top {
        height: var(---nav-height);
      }
      .search {
        width: 710px;
        border-radius: 44px 44px 0 0;
        margin: 0 auto;
        ::v-deep .van-search {
            width: 702px;
            border-radius:34px;
            overflow: hidden;
            padding: 0;
            border: 2px solid #64e151;
            margin-top: 15px;
        }
        ::v-deep .van-search__content {
            background-color: #fff;
        }
        ::v-deep .van-search__action {
            width: 104px;
            height: 64px;
            line-height: 64px;
            margin-right: 2px;
            font-size: 28px;
            color: #fff;
            background: linear-gradient(90deg,#32d349 1%, #64e151 99%);
            padding: 0 24px;
            border-radius: 30px;
            overflow: hidden;
        }
      }
    }
}
</style>
