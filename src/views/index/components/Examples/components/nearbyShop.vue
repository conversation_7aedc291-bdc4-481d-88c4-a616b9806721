<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2021-05-31 14:25:20
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-09-08 15:08:23
-->
<template>
  <div class="nearby">
    <div class="nearbyTitle">
      附近的店
    </div>
    <div class="nearbyShopList" @click="setLog">
      <GoodsCard v-for="(item,index) in list" :key="index" type="1" :list="item" />
    </div>
  </div>
</template>

<script>
import GoodsCard from '@/components/GoodsCard'
import { getShopList } from '@/api/index'
export default {
  components: {
    GoodsCard
  },
  data() {
    return {
      list: []
    }
  },
  created() {
    this.getShopList()
  },
  mounted() {
  },
  methods: {
    // 获取优选店铺
    getShopList() {
      let data = {
        marketRequestVO: {
          page: 1,
          size: 10,
          latitude: this.$store.getters.getLocation.latitude ? this.$store.getters.getLocation.latitude : 28.592388,
          longitude: this.$store.getters.getLocation.longitude ? this.$store.getters.getLocation.longitude : 119.275865,
          regionId: this.$store.getters.getRegionId,
          orderBy: 3,
          isTakeaway: true
        }
      }
      getShopList(data).then((res) => {
        if (res.status == 200) {
          if (res.marketVOList.length > 0) {
            this.list = res.marketVOList
          }
        }
      })
    },
    setLog() {
      this.$store.commit('setIsPrivacy', 0)
    }
  }
}
</script>

<style lang="scss" scoped>
.nearby{
    width: 710px;
    margin: 0 auto;
    margin-top: 25px;
    .nearbyTitle{
        height: 50px;
        font-size: 36px;
        font-family: PingFangSC-Medium;
        font-weight: 600;
        color: #333333;
        line-height: 50px;
    }
    .nearbyShopList{
      margin-top: 24px;
    }
}
</style>
