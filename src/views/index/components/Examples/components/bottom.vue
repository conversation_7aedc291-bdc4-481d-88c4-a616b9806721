<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2022-09-08 15:11:05
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-09-08 15:16:22
-->
<template>
  <div class="bottom" @click="goPrivacy">
    <div>
      欢迎使用点滴，您同意《点滴隐私政策》后，可查看更多服务信息
    </div>
    <div class="btn">
      查看吃喝玩乐全部服务
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {

    }
  },
  methods: {
    goPrivacy() {
      this.$store.commit('setIsPrivacy', 0)
    }
  }
}
</script>

<style scoped lang="scss">
    .bottom {
        width: 100%;
        height: 320px;
        background: #fff;
        position: fixed;
        bottom: 0;
        font-size: 35px;
        padding: 50px;
        text-align: center;
        .btn{
            width: 100%;
            height: 80px;
            background: #39cf3f;
            border-radius: 20px;
            line-height: 80px;
            margin-top: 50px;
            color: #fff;
            font-size: 30px;
        }
    }
</style>
