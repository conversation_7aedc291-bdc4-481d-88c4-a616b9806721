<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2021-05-29 11:34:50
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-09-08 15:05:57
-->
<template>
  <div class="home">
    <van-swipe class="banner" :autoplay="20000" touchable @change="onChange">
      <van-swipe-item v-for="(item, index) in list" :key="index">
        <img class="bannerImg" :src="item.image+'?x-oss-process=image/resize,w_700/format,jpg/quality,q_99'" @click="goActieve(item)">
      </van-swipe-item>
    </van-swipe>
  </div>
</template>

<script>
import { getBannerList } from '@/api/index'
export default {
  data() {
    return {
      list: this.$store.state.Index.banner
    }
  },
  created() {
    this.getBannerList()
  },
  methods: {
    getBannerList() {
      getBannerList().then((res) => {
        if (res.status == 200) {
          let data = this.$store.state.Index.banner
          if (data == '' || JSON.stringify(res.data) != JSON.stringify(data)) {
            this.list = res.data
            this.$store.state.Index.banner = res.data
          }
        }
      })
    },
    // 跳转
    goActieve(val) {
      this.$store.commit('setIsPrivacy', 0)
    },
    onChange(index) {
      // this.$store.state.Index.bannerIndex = index
    }
  }
}
</script>

<style lang="scss" scoped>
.home{
    .banner{
        width: 710px;
        height: 188px;
        margin: 0 auto;
        margin-top: 32px;
        border-radius: 15px;
        ::v-deep .van-swipe__indicator{
          width: 20px;
          height: 2px;
          border-radius: 0;
        }
         ::v-deep .van-swipe__indicator--active{
          background: #74e165;
        }
    }
    .bannerImg{
        width: 710px;
        height: 188px;
        border-radius: 15px;
    }
}
</style>
