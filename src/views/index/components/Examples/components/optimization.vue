<!--
 * @Descripttion: 附近的店
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2021-05-31 10:04:59
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-09-08 15:08:41
-->
<template>
  <div class="actieve">
    <div class="actieveTitle">
      <div>
        <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/index/actieve/shop.png" alt="">
      </div>
    </div>
    <div class="actieveShop">
      <div v-for="(item,index) in data" :key="index" class="actieveShopList" @click="goShop(item)">
        <div class="actieveShopImg">
          <img :src="item.cover+'?x-oss-process=image/resize,w_700/format,jpg/quality,q_80'" alt="">
        </div>
        <div class="actieveShopTitle">{{ item.marketName | ellipsis(8) }}</div>
        <div class="actieveShopBottom">
          <div v-if="item.marketTags.length > 0" class="actieveShopTag">
            <div v-for="(list, indexs) in item.marketTags.slice(0, 2)" :key="indexs">{{ list.tagTitle }}</div>
          </div>
          <div class="actieveShopBtn">
            进店
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getShopList } from '@/api/index'
export default {
  data() {
    return {
      data: this.$store.state.Index.preferred
    }
  },
  created() {
    this.getShopList()
  },
  methods: {
    // 获取优选店铺
    getShopList() {
      let data = {
        marketRequestVO: {
          page: 1,
          size: 10,
          latitude: this.$store.getters.getLocation.latitude ? this.$store.getters.getLocation.latitude : 28.592388,
          longitude: this.$store.getters.getLocation.longitude ? this.$store.getters.getLocation.longitude : 119.275865,
          regionId: this.$store.getters.getRegionId,
          orderBy: 3,
          isTakeaway: true
        }
      }
      getShopList(data).then((res) => {
        if (res.status == 200) {
          let cache = this.$store.state.Index.activeTttj
          if (res.marketVOList.length > 0) {
            this.data = res.marketVOList.slice(2, 4)
          }

          if (cache == '' || JSON.stringify(res.marketVOList.slice(2, 4)) != JSON.stringify(cache)) {
            if (res.marketVOList.length > 0) {
              this.data = res.marketVOList.slice(2, 4)
              this.$store.state.Index.preferred = res.marketVOList.slice(2, 4)
            }
          }
        }
      })
    },
    // 去店铺
    goShop(item) {
      this.$store.commit('setIsPrivacy', 0)
    }
  }
}
</script>

<style lang="scss" scoped>
.actieve {
    width: 710px;
    // height: 432px;
    background: linear-gradient(180deg,#fffbe2, #ffffff 15%);
    border-radius: 16px;
    margin: 0 auto;
    margin-top: 16px;
    padding-bottom: 28px;
    .actieveTitle {
        div {
            width: 50%;
            img {
                width: 136px;
                height: 34px;
                margin-left: 22px;
            }
        }
    }
    .actieveShop {
        display: flex;
        margin-left: 20px;
        .actieveShopList{
          margin-right: 16px;
        }
        .actieveShopImg {
          margin-top: 8px;
          width: 326px;
          height: 220px;
          img {
            width: 326px;
            height: 220px;
            border-radius: 11px;
            object-fit: cover;
          }
        }
        .actieveShopTitle{
          font-size: 30px;
          font-weight: 400;
          color: #333333;
          font-family: PingFangSC;
          margin-top: 10px;
        }
        .actieveShopTag{
          display: flex;
          div {
            width: 90px;
            height: 36px;
            text-align: center;
            background: #FFF0EF;
            border-radius: 8px;
            line-height: 36px;
            color: #FF4534;
            font-size: 24px;
            margin-right: 10px;
            font-family: PingFangSC;
            font-weight: 400;
          }
        }
        .actieveShopBottom{
          display: flex;
          justify-content: space-between;
          margin-top: 15px;
        }
        .actieveShopBtn{
          width: 68px;
          height: 40px;
          background: #39cf3f;
          border-radius: 6px;
          text-align: center;
          line-height: 40px;
          color: #fff;
          font-size: 20px;
          font-weight: 500;
          font-family: PingFangSC-Medium;
        }
    }
}
</style>
