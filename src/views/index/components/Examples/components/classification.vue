<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2021-05-29 13:57:12
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-09-08 15:10:38
-->
<template>
  <div class="classfy">
    <div>
      <ul class="classify">
        <li @click="goTo({name:'拼团'})">
          <div>
            <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/home/<USER>/classify/sc.png" alt="">
          </div>
          <div class="title">拼团</div>
        </li>
        <li @click="goTo({name:'外卖'})">
          <div>
            <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/index/classify/1.png" alt="">
          </div>
          <div class="title">外卖</div>
        </li>
        <li v-for="(item,index) in list" v-show="item.id!=73&&item.id!=14" :key="index" @click="goTo(item)">
          <div>
            <img :src="item.icon" alt="">
          </div>
          <div class="title">{{ item.name }}</div>
        </li>
      </ul>
      <ul class="classifyD">
        <li v-for="(item,index) in listD" v-show="isSchoolStatus(item)" :key="index" @click="goToD(item)">
          <div>
            <img :src="item.icon" alt="">
          </div>
          <div class="title">{{ item.name }}</div>
        </li>
      </ul>

    </div>

    <div v-if="ifOpen" class="adImgs" @click="funIfOpen">
      <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/school/zhxyyd.png" alt="">
    </div>

    <School v-if="ifOpen" @funIfOpen="funIfOpen" />
  </div>
</template>

<script>
import School from '@/components/Advert/school.vue'
import { getBindExist } from '@/api/school'
import { getCategory } from '@/api/index'

export default {
  components: {
    School
  },
  data() {
    return {
      list: this.$store.state.Index.classify,
      listD: [
        {
          icon: 'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/index/classify/1-8.png',
          name: '助农集市'
        },
        {
          icon: 'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/index/classify/l-2.png',
          name: '点滴超市'
        },
        {
          icon: 'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/home/<USER>/classify/zhxy.png',
          name: '智慧校园'
        },
        {
          icon: 'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/index/classify/1-9.png',
          name: '智慧影院'
        },
        {
          icon: 'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/index/classify/1-7.png',
          name: '人事劳务'
        },
        {
          icon: 'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/index/classify/1-10.png',
          name: '本地生活'
        }

      ],
      isExist: false,
      ifOpen: false
    }
  },
  created() {
    this.getCategory()
    this.getBindExist()
  },
  methods: {
    // 关闭智慧校园入口
    funIfOpen() {
      localStorage.setItem('schoolStatus', 1)
      this.ifOpen = false
    },
    // 获取分类
    getCategory() {
      getCategory().then((res) => {
        if (res.status == 200) {
          let data = this.$store.state.Index.classify
          if (data == '' || JSON.stringify(res.data) != JSON.stringify(data)) {
            let newdata = res.data
            for (let i = 0; i < newdata.length; i++) {
              if (newdata[i].id == 40) {
                newdata.splice(i, 1)
              }
              if (newdata[i].id == 41) {
                newdata.splice(i, 1)
              }
              if (newdata[i].id == 42) {
                newdata.splice(i, 1)
              }
            }
            this.list = newdata
            this.$store.state.Index.classify = newdata
          }
        }
      })
    },
    // 跳转对应类目
    goTo(item) {
      this.$store.commit('setIsPrivacy', 0)
    },
    goToD(item) {
      this.$store.commit('setIsPrivacy', 0)
    },
    // 学校入口判断绑定关系
    getBindExist() {
      getBindExist().then(res => {
        if (res.status == 200) {
          console.log(res)
          if (res.data.isExist == true) {
            this.isExist = true
            if (localStorage.getItem('schoolStatus') != '1') {
              this.ifOpen = true
            }
          } else {
            this.isExist = false
          }
        }
      })
    },
    isSchoolStatus(val) {
      if (val.name == '智慧校园') {
        if (this.isExist == true) {
          return true
        } else {
          return false
        }
      } else if (val.name == '智慧影院') {
        if (this.isExist == true) {
          return false
        } else {
          return true
        }
      } else {
        return true
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.classfy{
    height: auto;
    margin-top: 10px;
    margin-bottom: 30px;
    overflow: hidden;

    .classify{
        width: 690px;
        margin: 0 auto;
        display: flex;
        justify-content: space-between;
        margin-top: 30px;
        li {
            width: 92px;
            flex-shrink: 0;
            text-align: center;
            img{
                width: 92px;
                height: 92px;
            }
            font-size: 25px;
            font-family: PingFangSC;
            font-weight: 400;
            color: #333333;
            .title{
              margin-top: 6px;
              font-weight: 400;
            }
        }
    }
    .classifyD{
      width: 690px;
      margin: 0 auto;
      display: flex;
      justify-content: space-between;
      margin-top: 21px;
      li {
          width: 92px;
          flex-shrink: 0;
          text-align: center;
          img{
              width: 66px;
              height: 66px;
          }
          font-size: 25px;
          font-family: PingFangSC;
          font-weight: 400;
          color: #333333;
          .title{
            margin-top: 6px;
            font-weight: 400;
          }
      }
      li:nth-child(1) {
        img{
            width: 104px;
            height: 70px;
            margin-left: 12px;
        }
      }
    }
    .adImgs{
      width: 676px;
      margin: 0 auto;
      position: relative;
      top: -120px;
      z-index: 1001;
      img{
        width: 676px;
        height: 378px;
      }
    }
}
</style>
