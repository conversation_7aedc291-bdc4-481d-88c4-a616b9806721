<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2021-05-25 14:22:27
 * @LastEditors: 孙立政
 * @LastEditTime: 2023-07-28 10:19:54
-->
<template>
  <div class="home">
    <!-- 动态背景 -->
    <div class="bannerRoll">

      <div class="background-body" :style="'margin-top: '+$store.getters.getStatusHeight+'px;'">
        <!-- 头部 -->
        <Nav />
        <!-- 搜索 -->
        <Search v-if="false" />
        <!-- banner -->
        <Banner2 v-if="true" :istesting="isTesting()" />
      </div>

    </div>

    <!-- 公告 -->
    <News v-if="false" />
    <!-- 分类 -->
    <Classification :istesting="isTesting()" />

    <!-- 中部广告 -->
    <Advertise2 v-if="true" />

    <!-- 天天特价/限时抢购 -->
    <Actieve v-if="false" />

    <Banner v-if="false" />

    <!-- 中部广告 -->
    <Advertise />

    <!-- 招商 -->
    <!-- <Living /> -->

    <!-- 附近的店 -->
    <NearbyShop />

    <div style="height:100px" />

    <van-dialog v-model="bulletinShow" title="公告" @confirm="bulletinFun">
      <div style="padding: 15px;text-align: left;">
        &nbsp;&nbsp;&nbsp;&nbsp;2023年7月27日22:00-2023年7月28日09:30期间因系统故障导致支付功能无法正常使用，经技术人员紧急修复，该功能已于2023年7月28日09:30恢复正常，由此给用户造成的不便，敬请谅解。
      </div>
      <div style="text-align: right;margin-right: 10px;margin-bottom: 10px;">
        <p>龙泉市易生活智慧商贸发展有限责任公司</p>
        <p>2023年7月28日</p>
      </div>
    </van-dialog>

    <Advert v-if="ifOpen" @funIfOpen="funIfOpen" />

  </div>
</template>

<script>
import Nav from './components/nav'
import Search from './components/search'
import Banner2 from './components/banner2'
import Banner from './components/banner'
import Classification from './components/classification'
import Advertise from './components/advertise'
import Advertise2 from './components/advertise2'
// import Living from './components/living'

import Actieve from './components/actieve'
import NearbyShop from './components/nearbyShop'

import { findTbSkinStartUp, getPhpConfig2 } from '@/api/index'

import Advert from '@/components/Advert/lq-open.vue'

import logger from '@/utils/aliLog'

export default {
  components: {
    Nav,
    Search,
    Banner,
    Banner2,
    Classification,
    Actieve,
    NearbyShop,
    Advertise,
    Advert,
    Advertise2
  },
  data() {
    return {
      bannerIndex: this.$store.state.Index.bannerIndex,
      ifOpen: false,
      tipsStatus: false,
      bulletinShow: false,
      isShowTest: [],
      vdloading: true,
      video: null
    }
  },
  created() {
    this.getPhpConfig()
    this.startAppInfo()
    this.$store.state.Index.bannerIndex = 0
    this.$store.state.tabbar.index = 0
    this.$nextTick(() => {
      this.video = this.$refs.video // 获取视频元素的引用
      this.playVideo() // 在页面创建时播放视频
    })
  },
  destroyed() {
    this.pauseVideo() // 在页面销毁时暂停视频
  },
  mounted() {
    // var video = document.getElementById('video')
    // video.play()
    // video.addEventListener('canplay', () => {
    //   this.vdloading = false
    // })
    // 回到顶部
    document.body.scrollTop = 0
    document.documentElement.scrollTop = 0

    // if (this.is0728() && localStorage.getItem('a20230728') != '1') {
    //   this.bulletinShow = true
    // }

    // if (localStorage.getItem('lq-20240918') == '1') {
    //   this.ifOpen = false
    // } else {
    //   this.ifOpen = true
    // }
    logger.sum('龙泉-日活')
  },
  methods: {
    playVideo() {
      if (this.video) {
        this.video.play()
      }
    },
    pauseVideo() {
      if (this.video) {
        this.video.pause()
      }
    },
    // 关闭活动入口
    funIfOpen() {
      localStorage.setItem('lq-20240918', 1)
      this.ifOpen = false
      localStorage.removeItem('lq-advertopen00')
    },
    // 启动图
    startAppInfo() {
      findTbSkinStartUp(3).then(res => {
        if (res.status === 200) {
          if (res.data) {
            if (localStorage.getItem('startAppInfo') != res.data.startUpSkin) {
              localStorage.setItem('startAppInfo', res.data.startUpSkin)
              AlipayJSBridge.call('startAppInfo', { imgUrl: res.data.startUpSkin, typetime: res.data.startUpSkin }, function(result) {
                console.log('APP启动图：' + result)
              })
            }
          }
        }
      })
    },
    imUrl(row) {
      return 'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/index/background/bjlist4.png'
    },
    close() {
      this.tipsStatus = false
    },
    bulletinFun() {
      this.bulletinShow = false
      localStorage.setItem('a20230728', '1')
    },
    is0728() {
      // 创建当前日期的Date对象
      var today = new Date()

      // 获取当天的年、月、日
      var year = today.getFullYear()
      var month = today.getMonth() + 1 // 月份从0开始，需要加1
      var day = today.getDate()

      // 将年、月、日转为字符串格式，并格式化成八位数，比较是否和指定日期相等
      var currentDate = year.toString() +
        (month < 10 ? '0' + month.toString() : month.toString()) +
        (day < 10 ? '0' + day.toString() : day.toString())

      // 判断是否是指定日期
      if (currentDate === '20230728') {
        return true
      } else {
        return false
      }
    },
    // 获取php配置
    getPhpConfig() {
      getPhpConfig2().then(res => {
        if (res.status === 200) {
          this.isShowTest = res.data.testUser.longQuan
        }
      })
    },
    // 判断当前登录的手机号是否在数组中
    isTesting() {
      let phone = this.$store.getters.getPhone
      let isShow = false
      this.isShowTest.forEach(item => {
        if (item == phone) {
          isShow = true
        }
      })
      return isShow
    }
  }
}
</script>

<style lang="scss" scoped>
.home {
  width: 100%;
  .bannerRoll {
    width: 100%;
    height: 950px;
    background-color: #fff;
    // background-image: url(https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/home/<USER>/banner/20240326/65bd049ec06fd_01.png);
    // background-size: 100% 100%;
    // background-repeat: no-repeat;
    overflow: hidden;
    position: relative;

    border-bottom-right-radius: 10px;
    border-bottom-left-radius: 10px;

    .background-body {
      width: 100%;
      height: 100%;
      position: absolute;
      top: 0;
      left: 0;
      z-index: 9;
      // margin-top: 50px;
    }

    .background-video {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
    }

    .background-video video {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
    .video-placeholder {
  position: relative;
  width: 100%;
  height: 100%;
  background-color: #000; /* 可以根据设计需求设置占位颜色 */
}

.video-placeholder img {
  display: block;
  width: 100%;
  height: 100%;
  object-fit: cover;
}
    .background-bottom{
      position: absolute;
      bottom: 0;
      left: 0;
      width: 100%;
      height: 100px;
      background: linear-gradient(180deg,rgba(255,255,255,0) 0%,rgba(255,255,255,1) 100%);
    }
  }

}
</style>
