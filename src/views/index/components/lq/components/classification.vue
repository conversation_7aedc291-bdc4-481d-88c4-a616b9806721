<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2021-05-29 13:57:12
 * @LastEditors: 孙立政
 * @LastEditTime: 2023-06-14 16:28:12
-->
<template>
  <div class="home">
    <div :style="'margin-top:'+topHeight+'px'">
      <ul class="classify">
        <li v-for="(item,index) in list" :key="index" @click="goTo(item)">
          <div>
            <img :src="item.icon" alt="">
          </div>
          <div>
            {{ item.name }}
          </div>
        </li>
        <li @click="goTo({name:'外卖'})">
          <div>
            <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/home/<USER>/class/20240708/waimai.png" alt="">
          </div>
          <div>
            外卖
          </div>
        </li>
      </ul>
      <ul class="classifyD">
        <li v-for="(item,index) in list2" :key="index" @click="goTo(item)">
          <div>
            <img :src="item.icon" alt="">
          </div>
          <div>
            {{ item.name }}
          </div>
        </li>
      </ul>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    istesting: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      list: [],
      list2: [],
      topHeight: 0
    }
  },
  created() {
    this.getCategory()
  },
  mounted() {
    var u = navigator.userAgent
    var isiOS = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/) // ios终端
    if (isiOS) {
      this.topHeight = 20
    } else {
      this.topHeight = 15
    }
  },
  methods: {
    // 获取分类
    getCategory() {
      this.list = [
        { 'id': 99, 'icon': 'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/home/<USER>/class/20240708/shangcheng.png', 'isOpen': true, 'level': 1, 'name': '商城', 'sort': 2, 'regionSn': 'Q855765', 'capitalPoolId': 1, 'pid': 0 },
        { 'id': 101, 'icon': 'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/home/<USER>/class/20240708/gfjs.png', 'isOpen': true, 'level': 1, 'name': '共富集市', 'sort': 2, 'regionSn': 'Q855765', 'capitalPoolId': 1, 'pid': 0 },
        { 'id': 46, 'icon': 'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/home/<USER>/class/20240708/shitang.png', 'isOpen': true, 'level': 1, 'name': '美食', 'sort': 2, 'regionSn': 'Q855765', 'capitalPoolId': 1, 'pid': 0 },
        { 'id': 55, 'icon': 'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/home/<USER>/class/20240708/chaoshi.png', 'isOpen': true, 'level': 1, 'name': '食堂', 'sort': 3, 'regionSn': 'Q855765', 'capitalPoolId': 1, 'pid': 0 }
      ]
      this.list2 = [
        { 'id': 67, 'icon': 'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/home/<USER>/class/20240708/caishichang.png', 'isOpen': true, 'level': 1, 'name': '菜市场', 'sort': 3, 'regionSn': 'Q855765', 'capitalPoolId': 1, 'pid': 0 },
        { 'id': 56, 'icon': 'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/home/<USER>/class/20240708/chaoshi2.png', 'isOpen': true, 'level': 1, 'name': '超市', 'sort': 5, 'regionSn': 'Q855765', 'capitalPoolId': 1, 'pid': 0 },
        { 'id': 57, 'icon': 'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/home/<USER>/class/20231227/icon6.png', 'isOpen': true, 'level': 1, 'name': '生活馆', 'sort': 5, 'regionSn': 'Q855765', 'capitalPoolId': 1, 'pid': 0 },
        { 'id': 15, 'icon': 'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/home/<USER>/class/20240708/liaoxiuyang.png', 'isOpen': true, 'level': 1, 'name': '疗休养', 'sort': 5, 'regionSn': 'Q855765', 'capitalPoolId': 1, 'pid': 0 },
        { 'id': 52, 'icon': 'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/home/<USER>/class/20240708/jiudian.png', 'isOpen': true, 'level': 1, 'name': '酒店', 'sort': 5, 'regionSn': 'Q855765', 'capitalPoolId': 1, 'pid': 0 }
      ]
    },
    // 跳转对应类目
    goTo(item) {
      // var u = navigator.userAgent
      // var isiOS = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/) // ios终端
      if (item.id == 15) {
        // !!!生产是否更新？
        // this.$toast('即将上线，敬请期待')
        this.$router.push({
          path: '/rest/longquan/index',
          query: {}
        })
      } else if (item.id == 101) {
        if (this.istesting) {
          // this.$router.push('/market/home')
          this.$router.push({ name: 'ActivityLq20250408' })
        } else {
          this.$toast('即将上线，敬请期待')
        }
      } else if (item.id == 99) { // 商城
        this.$router.push({ name: 'MarketHome' })
        // this.$toast('即将上线，敬请期待')
      } else if (item.id == 52) {
        // this.$toast('即将上线，敬请期待')

        this.$router.push({
          path: '/Classify',
          query: {
            id: item.id,
            name: item.name,
            cateId: item.id,
            isTakeaway: null
          }
        })
      } else if (item.id == 67) {
        this.$toast('即将上线，敬请期待')

        // this.$router.push({
        //   name: 'Classify',
        //   query: {
        //     id: item.id,
        //     name: item.name
        //   }
        // })
      } else if (item.id == 56) {
        // this.$toast('即将上线，敬请期待')

        this.$router.push({
          name: 'Classify',
          query: {
            id: item.id,
            name: item.name
          }
        })
      } else if (item.id == 57) {
        // this.$toast('即将上线，敬请期待')

        this.$router.push({
          name: 'Classify',
          query: {
            id: item.id,
            name: item.name
          }
        })
      } else if (item.id == 46) {
        // this.$toast('即将上线，敬请期待')

        this.$router.push({
          path: '/Classify',
          query: {
            id: item.id,
            name: item.name,
            cateId: item.id,
            isTakeaway: null
          }
        })
      } else if (item.id == 55) {
        // this.$toast('即将上线，敬请期待')

        this.$router.push({
          path: '/canteen',
          query: {
            id: item.id,
            name: item.name,
            cateId: item.id,
            isTakeaway: null
          }
        })
      } else if (item.name === '外卖') {
        this.$toast('即将上线，敬请期待')
        // this.$router.push({
        //   path: '/Classify',
        //   query: {
        //     id: item.id,
        //     name: item.name,
        //     cateId: 0,
        //     isTakeaway: true
        //   }
        // })
      } else if (item.id == 0) {
        this.$toast('即将上线，敬请期待')
      } else if (item.id == 52) {
        // this.$toast('即将上线，敬请期待')

        this.$router.push({
          path: '/Classify',
          query: {
            id: item.id,
            name: item.name,
            cateId: item.id,
            isTakeaway: null
          }
        })
      }

      // 获取定位
      this.$store.commit('setLocation')
    }
  }
}
</script>

<style lang="scss" scoped>
.home{
    background: linear-gradient(180deg,#ffffff, #ffffff 89%, rgba(255,255,255,0.00));
    overflow: hidden;
    .classify{
        width: 690px;
        margin: 0 auto;
        display: flex;
        justify-content: space-between;
        li {
            width: 108px;
            flex-shrink: 0;
            text-align: center;
            img{
                width: 108px;
                height: 108px;
            }
            font-size: 26px;
            font-family: PingFangSC;
            font-weight: 500;
            color: #333333;
        }
    }
    .classifyD{
      width: 690px;
      margin: 0 auto;
      display: flex;
      justify-content: space-between;
      margin-top: 32px;
      li {
          width: 100px;
          flex-shrink: 0;
          text-align: center;
          img{
            width: 100px;
            height: 100px;
          }
          font-size: 26px;
          font-family: PingFangSC;
          font-weight: 400;
          color: #333333;
      }
    }
}
</style>
