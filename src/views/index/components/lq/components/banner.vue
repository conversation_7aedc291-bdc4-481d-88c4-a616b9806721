<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2021-05-29 11:34:50
 * @LastEditors: 孙立政
 * @LastEditTime: 2023-08-10 09:22:53
-->
<template>
  <div class="home">
    <van-swipe class="banner" :autoplay="6000" touchable indicator-color="#74E165" @change="onChange">
      <van-swipe-item v-for="(item, index) in list" :key="index">
        <img
          class="bannerImg"
          :src="item.image + '?x-oss-process=image/resize,w_700/format,jpg/quality,q_99'"
          @click="goActieve(item)"
        >
      </van-swipe-item>
    </van-swipe>
  </div>
</template>

<script>
import { ImagePreview } from 'vant'
import { getBannerList } from '@/api/index'
export default {
  data() {
    return {
      list: this.$store.state.Index.banner
      // list: []
    }
  },
  created() {
    this.getBannerList()
  },
  methods: {
    getBannerList() {
      getBannerList().then((res) => {
        if (res.status == 200) {
          let data = this.$store.state.Index.banner
          if (data == '' || JSON.stringify(res.data) != JSON.stringify(data)) {
            this.list = res.data
            this.$store.state.Index.banner = res.data
            // 在首位插入
            // this.list.unshift({
            //   image: 'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/home/<USER>/class/20231227/test.png?a=1',
            //   url: ''
            // })
          }
        }
      })
    },
    // 跳转
    goActieve(val) {
      if (val.url == '20230609') {
        ImagePreview({
          images: ['https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/home/<USER>/67985250dc940a446b23d74cf283db9c.jpg'],
          showIndex: false,
          closeable: true
        })
      }
      if (val.url == '202300711') {
        this.$router.push({ name: 'ActivityLqNsh' })
      }
      if (val.url == '202300720') {
        this.$router.push({ name: 'ActivityLqPay' })
      }
      if (val.url == '202300810') {
        this.$router.push({ name: 'ActivityLqLf' })
      }
      if (val.url == '20230928') {
        this.$router.push({ name: 'ActivityLq20230928' })
      }
    },
    onChange(index) {
      this.$store.state.Index.bannerIndex = index
    }
  }
}
</script>

<style>
.van-image-preview__close-icon--top-right {
  top: 100px;
  font-size: 60px;
}
</style>

<style lang="scss" scoped>
.home {
  ::v-deep .van-swipe__indicator {
    width: 25px;
    height: 3px;
  }

  .banner {
    width: 710px;
    height: 220px;
    margin: 0 auto;
    margin-top: 25px;
    border-radius: 15px;
  }

  .bannerImg {
    width: 710px;
    height: 220px;
    border-radius: 15px;
  }
}
</style>
