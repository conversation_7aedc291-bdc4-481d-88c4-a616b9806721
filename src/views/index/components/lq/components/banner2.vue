<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2021-05-29 11:34:50
 * @LastEditors: 孙立政
 * @LastEditTime: 2023-08-10 09:22:53
-->
<template>
  <div class="banner2" :style="{ top: topVal }">
    <van-swipe class="banner" :autoplay="5000" touchable indicator-color="#74E165" @change="onChange">
      <van-swipe-item v-for="(item, index) in list" :key="index">
        <img
          class="bannerImg"
          :src="item.image + '?x-oss-process=image/resize,w_700/format,jpg/quality,q_99'"
          @click="goActieve(item)"
        >
      </van-swipe-item>
    </van-swipe>
  </div>
</template>

<script>
export default {
  props: {
    istesting: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      // list: this.$store.state.Index.banner
      list: [
        // {
        //   image: 'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/active/lq-38-20240305/20240306-003.jpg',
        //   url: '99'
        // },
        // {
        //   image: 'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/active/lq-38-20240305/20240308-02_02.png',
        //   url: '6'
        // },
        // {
        //   image: 'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/home/<USER>/activity/20240918/banner.png',
        //   url: '7'
        // }
        // {
        //   image: 'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/home/<USER>/banner/20250307/1.jpg',
        //   url: '99'
        // },
        // {
        //   image: 'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/home/<USER>/banner/2025/0403/lq-qingming.jpg',
        //   url: '99'
        // },
        // {
        //   image: 'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/home/<USER>/banner/2025/0508/banner.jpg',
        //   url: '99'
        // },
        {
          image: 'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/home/<USER>/banner/2025/0618/20250618-1.jpg',
          url: '99'
        },
        {
          image: 'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/home/<USER>/banner/20250401/banner2.png',
          url: '5'
        },
        {
          image: 'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/home/<USER>/banner/20250127/20250407.jpg?time=' + new Date().getTime(),
          url: '99'
        }
        // {
        //   image: 'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/home/<USER>/banner/20240104/b3.png',
        //   url: '4'
        // }
      ],
      isShowCouponList: [],
      topVal: '0px'
    }
  },
  watch: {
    istesting: {
      handler(newVal) {
        this.updateBannerList()
      },
      immediate: true
    }
  },
  created() {
    // 获取屏幕高度
    let topVal = Number(this.$store.getters.getStatusHeight) + 40 + 'px'
    this.topVal = '-' + topVal
    console.log(this.topVal)
  },
  mounted() {
    // this.updateBannerList()
  },
  methods: {
    updateBannerList() {
      if (this.istesting) {
      // 检查是否已经添加过，避免重复添加
        const existingBanner = this.list.find(item => item.url === '1')
        if (!existingBanner) {
          this.list.unshift({
            image: 'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/active/lq-20250408/banner.png',
            url: '1'
          })
        }
      }
    },
    // 跳转
    goActieve(val) {
      if (this.$store.getters.getUserId == null) {
        this.$router.push({ name: 'wxLogin2' })
        return
      }
      if (val.url == '1') {
        this.$router.push({ name: 'ActivityLq20250408' })
      }
      if (val.url == '2') {
        this.$router.push({
          path: '/Classify',
          query: {
            id: 46,
            name: '美食',
            cateId: 46,
            isTakeaway: null
          }
        })
      }
      if (val.url == '3') {
        this.$router.push({
          path: '/Classify',
          query: {
            id: 46,
            name: '美食',
            cateId: 46,
            isTakeaway: null
          }
        })
      }
      if (val.url == '4') {
        this.$router.push({
          path: '/rest/longquan/index',
          query: {}
        })
      }
      if (val.url == '5') {
        this.$router.push({ name: 'ActivityLqPay' })
      }
      if (val.url == '6') {
        this.$router.push({ name: 'ActivityLq38' })
      }
      if (val.url == '7') {
        this.$router.push({ name: 'ActivityLq20240918' })
      }
    },
    onChange(index) {
      this.$store.state.Index.bannerIndex = index
    },
    // 判断当前登录的手机号是否在数组中
    isShowCoupon() {
      if (this.isShowCouponList.length == 0) {
        return true
      }
      let phone = this.$store.getters.getPhone
      let isShow = false
      this.isShowCouponList.forEach(item => {
        if (item == phone) {
          isShow = true
        }
      })
      return isShow
    }
  }
}
</script>

<style>
.van-image-preview__close-icon--top-right {
  top: 100px;
  font-size: 60px;
}
</style>

<style lang="scss" scoped>
.banner2 {
  position: relative;
  z-index: 1;
  ::v-deep .van-swipe__indicator {
    width: 25px;
    height: 3px;
  }

  .banner {
    width: 100%;
    height: 950px;
    margin: 0 auto;
    border-radius: 6px;
  }

  .bannerImg {
    width: 100%;
    height: 100%;
    border-radius: 6px;
  }
}
</style>
