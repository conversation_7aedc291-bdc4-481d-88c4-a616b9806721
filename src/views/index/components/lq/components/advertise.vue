<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2021-06-02 17:41:44
 * @LastEditors: 孙立政
 * @LastEditTime: 2023-06-20 16:48:46
-->
<template>
  <div class="advertise">
    <div class="advertiseBox" @click="goShop">
      <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/home/<USER>/20230420.png" alt="">
    </div>
  </div>
</template>

<script>
export default {
  methods: {
    goShop() {
      if (this.$store.getters.getUserId == null) {
        this.$router.push({ name: 'wxLogin2' })
        return
      }
      this.$router.push({ name: 'ActivityJyz' })
      // this.$store.state.searchKey = '加油站'
      // // 搜索
      // this.$router.push({
      //   path: '/results',
      //   query: {}
      // })
    }
  }
}
</script>

<style lang="scss" scoped>
.advertise{
    width: 100%;
    height: 255px;
    // background: #fff;
    .advertiseBox{
        width: 710px;
        height: 255px;
        margin: 0 auto;
        img{
            width: 100%;
            height: 100%;
            border-radius: 15px;
        }
    }
}
</style>
