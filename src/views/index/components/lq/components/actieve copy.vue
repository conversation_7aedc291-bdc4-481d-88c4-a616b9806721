<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2021-05-31 10:04:59
 * @LastEditors: 孙立政
 * @LastEditTime: 2023-06-12 15:42:28
-->
<template>
  <div class="actieve">
    <div class="actieveTitle">
      <div>
        <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/index/actieve/lqthzq.png" alt="">

      </div>
      <div>
        <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/index/actieve/lqday.png" alt="">
      </div>
    </div>
    <div class="actieveGoods">

      <!-- 天天特价 -->
      <div class="actieveGoodsLeft">
        <div v-for="(item,index) in temparr" :key="index" class="actieveGoodsList" @click="goShop(item,0)">
          <div class="actieveImg">
            <img :src="item.cover+'?x-oss-process=image/resize,w_700/format,jpg/quality,q_85'" alt="">
          </div>
          <div class="actieveGoodsTitle">
            {{ item.title | ellipsis(6) }}
          </div>
          <div class="actieveGoodsPrice">
            <div class="actieveGoodsPriceSymbol">￥</div>
            <div class="actieveGoodsPriceNum">{{ item.seckillPrice }}</div>
            <div v-if="item.seckillPrice!=item.oldPrice" class="actieveGoodsPriceOrl">￥{{ item.oldPrice }}</div>
          </div>
        </div>
      </div>
      <!-- 限时抢购 -->
      <div class="actieveGoodsRight">
        <div v-for="(item,index) in temparr1" :key="index" class="actieveGoodsList" @click="goShop(item,1)">
          <div class="actieveImg">
            <img :src="item.cover+'?x-oss-process=image/resize,w_700/format,jpg/quality,q_85'" alt="">
          </div>
          <div class="actieveGoodsTitle">
            {{ item.title | ellipsis(6) }}
          </div>
          <div class="actieveGoodsPrice">
            <div class="actieveGoodsPriceSymbol">￥</div>
            <div class="actieveGoodsPriceNum">{{ item.seckillPrice }}</div>
            <div v-if="item.seckillPrice!=item.oldPrice" class="actieveGoodsPriceOrl">￥{{ item.oldPrice }}</div>
          </div>
        </div>
      </div>
    </div>
    <div style="height:5px" />
  </div>
</template>

<script>
import { addData } from '@/utils/upLog.js'
import { getTejiaList, getYouhuiList } from '@/api/index'
export default {
  data() {
    return {
      temparr: this.$store.state.Index.activeTttjLq,
      temparr1: this.$store.state.Index.activeThzqLq
    }
  },
  created() {
    this.getTejiaList()
    this.getYouhuiList()
  },
  methods: {
    // 获取天天特价
    getTejiaList() {
      getTejiaList().then(res => {
        if (res.status == 200) {
          let cache = this.$store.state.Index.activeTttjLq

          if (cache == '' || JSON.stringify(res.data[0].seckillGoodsList.slice(0, 2)) != JSON.stringify(cache)) {
            if (res.data.length > 0) {
              if (res.data[0].seckillGoodsList.length > 0) {
                this.temparr = res.data[0].seckillGoodsList.slice(0, 2)
                this.$store.state.Index.activeTttjLq = res.data[0].seckillGoodsList.slice(0, 2)
              }
            }
          }
        }
      })
    },
    // 获取优惠专区
    getYouhuiList() {
      getYouhuiList().then(res => {
        if (res.status == 200) {
          let cache = this.$store.state.Index.activeThzqLq
          if (cache == '' || JSON.stringify(res.data.list.slice(0, 2)) != JSON.stringify(cache)) {
            if (res.data.list.length > 0) {
              this.temparr1 = res.data.list.slice(0, 2)
              this.$store.state.Index.activeThzqLq = res.data.list.slice(0, 2)
            }
          }
        }
      })
    },
    // 去店铺
    goShop(item, val) {
      // 记录优选店铺轨迹
      // 轨迹埋点
      if (val == 0) {
        addData(22)
      } else if (val == 1) {
        addData(23)
      }
      this.$store.state.cart.cartData[0].goodsList = []
      this.$router.push({
        name: 'Shop',
        query: {
          id: item.marketId
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.actieve{
    width: 710px;
    // height: 340px;
    background: #fff;
    border-radius: 16px;
    margin: 0 auto;
    margin-top: 16px;
    .actieveTitle{
        display: flex;
        div {
            width: 50%;
            img{
                width: 136px;
                height: 34px;
                margin-left: 22px;
            }
        }
    }
    .actieveGoods{
        display: flex;
        margin-top: 10px;
        .actieveGoodsLeft, .actieveGoodsRight {
            width: 50%;
            margin-left: 22px;
            // height: 230px;
            display: flex;
            .actieveGoodsList{
                margin-right: 11px;
            }
        }
        .actieveImg{
            img {
                width: 151px;
                height: 151px;
                border-radius: 12px;
            }
        }
        .actieveGoodsTitle{
            font-size: 24px;
            font-weight: 500;
            font-family: PingFangSC-Medium;
            color: #333333;
        }
        .actieveGoodsPrice{
          display: flex;
          margin-top: 7px;
            .actieveGoodsPriceSymbol{
                font-size: 18px;
                color: #ff301e;
                font-family: PingFangSC-Medium;
                font-weight: 600;
                margin-top: 6px;
            }
            .actieveGoodsPriceNum{
                font-size: 30px;
                font-family: PingFangSC-Medium;
                font-weight: 500;
                color: #ff301e;
            }
            .actieveGoodsPriceOrl{
                font-size: 22px;
                color: #999999;
                margin-left: 4px;
                margin-top: 6px;
                text-decoration:line-through;
            }
        }
    }
}
</style>
