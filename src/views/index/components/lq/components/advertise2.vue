<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2021-06-02 17:41:44
 * @LastEditors: 孙立政
 * @LastEditTime: 2023-06-20 16:48:46
-->
<template>
  <div class="advertise">
    <div v-if="false" class="advertiseBox2" @click="goTo(15)">
      <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/home/<USER>/activity/20250401/a/lxy.png" alt="">
    </div>
    <div class="advertiseBox2" @click="goTo(57)">
      <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/home/<USER>/activity/20250401/a/shfw.png" alt="">
    </div>
    <div class="advertiseBox2" @click="goTo(56)">
      <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/home/<USER>/activity/20250401/a/bmcs.png" alt="">
    </div>
  </div>
</template>

<script>
export default {
  methods: {
    goTo(val) {
      if (val == 15) {
        this.$router.push({
          path: '/rest/longquan/index',
          query: {}
        })
      }
      if (val == 57) {
        this.$router.push({
          name: 'Classify',
          query: {
            id: 57,
            name: '生活馆'
          }
        })
      }
      if (val == 56) {
        this.$router.push({
          name: 'Classify',
          query: {
            id: 56,
            name: '超市'
          }
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.advertise{
    width: 100%;
    margin-top: 30px;
    .advertiseBox2{
        width: 710px;
        margin: 0 auto;
        img{
            width: 100%;
            height: 100%;
            border-radius: 13px;
        }
    }
}
</style>
