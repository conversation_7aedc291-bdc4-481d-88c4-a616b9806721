<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2021-05-29 15:14:30
 * @LastEditors: 孙立政
 * @LastEditTime: 2023-02-04 09:40:23
-->
<template>
  <div class="news">
    <div class="newsLogo">
      <img :src="$store.state.Index.template.notice" alt="">
    </div>
    <div class="newsMsg" @click="goNotice">
      <van-notice-bar :scrollable="false" background="#fff" color="#999999">
        <van-swipe vertical class="notice-swipe" :autoplay="6000" :show-indicators="false" :touchable="false">
          <van-swipe-item v-for="(item,index) in text" :key="index">{{ item.articleName }}</van-swipe-item>
        </van-swipe>
      </van-notice-bar>
    </div>
    <div class="newsRight">
      <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/index/news/right.png" alt="">
    </div>
  </div>
</template>

<script>
import { getTbTripArticle } from '@/api/news'
export default {
  data() {
    return {
      text: []
    }
  },
  created() {
    this.getTbTripArticle()
  },
  methods: {
    getTbTripArticle() {
      getTbTripArticle(this.$store.getters.getRegionId).then(res => {
        if (res.status == 200) {
          if (res.data != null) {
            if (res.data.length > 0) {
              this.text = res.data
            }
          }
        }
      })
    },
    goNotice() {
      this.$router.push({
        name: 'Notice'
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.news{
    width: 710px;
    height: 64px;
    line-height: 64px;
    display: flex;
    background-color: #fff;
    border-radius: 13px;
    margin: 0 auto;
    // margin-top: 10px;
    margin-top: -12px;
    img {
        width: 100%;
        height: 100%;
    }
    .newsLogo{
        width: 25%;
        height: 44px;
        margin-left: 16px;
        img {
            width: 164px;
            height: 44px;
            position: relative;
        }
    }
    .newsMsg{
        width: 65%;
        font-size: 24px;
        color: #999999;
        font-family: PingFangSC;
        font-weight: 400;
        .notice-swipe{
          width: 300px;
          height: 40px;
          line-height: 40px;
          margin-top: 5px;
        }
        ::v-deep .van-notice-bar{
          height: 60px;
        }
    }
    .newsRight{
        width: 18px;
        height: 16px;
        img{
          position: relative;
          top: -15px;
        }
    }
}
</style>
