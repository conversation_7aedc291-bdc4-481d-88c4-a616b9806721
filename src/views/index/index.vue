<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2021-07-07 11:17:56
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-12-30 09:17:01
-->
<template>
  <div class="home">
    <!-- 大区切换目前根据大区id来判断 -->
    <Sc v-if="$store.getters.getRegionId == 1" />
    <Jn v-else-if="$store.getters.getRegionId == 6" />
    <Lq v-else-if="$store.getters.getRegionId == 3" />
    <Qy v-else-if="$store.getters.getRegionId == 7" />

    <!-- 隐私协议弹出 -->
    <Privacy v-if="privacyOff" />
    <!-- 定位变更弹出 -->
    <Dialog :show="logShow" :msg="regionData.msg" @close="close" @confirm="confirm" />
    <!-- 定位提示 -->
    <div v-if="locationMsgStatus" class="location" @click="setLoctions">
      <div class="close" @click.stop="closeLocation" />
    </div>
  </div>
</template>

<script>
import Lq from './components/lq'
import Sc from './components/sc'
import Jn from './components/jn'
import Qy from './components/qy'
// 隐私协议
import Privacy from '@/components/Privacy'
import { version, rel } from '@/config/settings'
import { addAppVersion, findTbSkinStartUp } from '@/api/index'
import Dialog from '@/components/Dialog'
import { lq, sc, jn } from '@/utils/cityXY/index.js'
export default {
  components: {
    Lq, Sc, Jn, Qy, Privacy, Dialog
  },
  data() {
    return {
      privacyOff: false,
      logShow: false,
      regionData: {
        regionId: null,
        regionName: null,
        msg: null
      },
      locationMsgStatus: false
    }
  },
  created() {
    // this.startAppInfo()
    var u = navigator.userAgent
    var isiOS = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/) // ios终端
    if (localStorage.getItem('privacyOff') != 1) {
      if (!isiOS) {
        this.privacyOff = true
      }
    }
    if (sessionStorage.getItem('LocationMsgType') != 1) {
      this.LocationMsg()
    }
    if (localStorage.getItem('token')) {
      this.getLocation()
    }
    this.addAppVersion()
    document.removeEventListener('mobSever', function(e) {})
  },
  methods: {
    startAppInfo() {
      findTbSkinStartUp().then(res => {
        if (res.status === 200) {
          console.log(res.data)
          if (localStorage.getItem('startAppInfo') != res.data.startUpSkin) {
            localStorage.setItem('startAppInfo', res.data.startUpSkin)
            AlipayJSBridge.call('startAppInfo', { imgUrl: res.data.startUpSkin, typetime: res.data.startUpSkin }, function(result) {
              console.log('APP启动图：' + result)
            })
          }
        }
      })
    },
    // app版本上报
    addAppVersion() {
      var u = navigator.userAgent
      var isiOS = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/) // ios终端
      let data = {
        userId: localStorage.getItem('userId'), // 用户ID
        regionId: this.$store.getters.getRegionId, // 大区id
        deviceType: isiOS ? 'IOS' : 'Android', // 手机型号
        networkEnvironment: 'h5', // 网络状态
        version: version, // 版本号
        deviceId: '', // 手机唯一标识
        infVersion: rel
      }
      let newdata = JSON.stringify(data)
      addAppVersion(newdata).then((res) => {})
    },
    // 区域切换
    confirm() {
      this.$store.commit('setRegionName', this.regionData.regionName)
      this.$store.commit('setRegionId', this.regionData.regionId)
      this.logShow = false
    },
    // 取消区域切换
    close() {
      this.logShow = false
    },
    // 区域判断
    LocationMsg() {
      let self = this
      sessionStorage.setItem('LocationMsgType', 1)
      let myVar = setInterval(function() {
        AlipayJSBridge.call('LocationMsg', {}, function(result) {
          if (result.locationMsg) {
            clearInterval(myVar)
            let locationdata = JSON.parse(result.locationMsg)
            let isPointInRingLq = AMap.GeometryUtil.isPointInRing([locationdata.longitude, locationdata.latitude], lq())
            let isPointInRingSc = AMap.GeometryUtil.isPointInRing([locationdata.longitude, locationdata.latitude], sc())
            let isPointInRingJn = AMap.GeometryUtil.isPointInRing([locationdata.longitude, locationdata.latitude], jn())
            if (isPointInRingLq == true && self.$store.getters.getRegionId != 3) {
              self.regionData.msg = '发现您所在的城市发生变化，是否切换到 龙泉市'
              self.regionData.regionId = 3
              self.regionData.regionName = '龙泉市'
              self.logShow = true
            }
            if (isPointInRingSc == true && self.$store.getters.getRegionId != 1) {
              self.regionData.msg = '发现您所在的城市发生变化，是否切换到 遂昌县'
              self.regionData.regionId = 1
              self.regionData.regionName = '遂昌县'
              self.logShow = true
            }
            if (isPointInRingJn == true && self.$store.getters.getRegionId != 6) {
              self.regionData.msg = '发现您所在的城市发生变化，是否切换到 景宁'
              self.regionData.regionId = 6
              self.regionData.regionName = '景宁县'
              self.logShow = true
            }
          }
        })
      }, 1000)
    },
    // 检查定位信息
    getLocation() {
      let self = this
      AlipayJSBridge.call('LocationMsg', {}, function(result) {
        let locationdata = JSON.parse(result.locationMsg)
        if (locationdata.latitude == 0.0 || locationdata == 0) {
          self.locationMsgStatus = true
        }
      })
    },
    // 跳转定位
    setLoctions() {
      this.locationMsgStatus = false
      AlipayJSBridge.call('SetLoctions', {}, function(result) { })
    },
    // 关闭定位提示
    closeLocation() {
      this.locationMsgStatus = false
    }
  }
}
</script>

<style scoped lang="scss">
.location{
  width: 680px;
  height: 84px;
  background-image: url(https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/region/dwbj.png);
  background-size: 100% 100%;
  margin-left: 30px;
  position: fixed;
  bottom: 140px;
  left: 0;
  z-index: 2;
  .close{
    float: right;
    width: 50px;
    height: 84px;

  }
}
</style>
