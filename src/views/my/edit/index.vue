<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-06-04 11:51:57
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-07-01 15:40:06
-->
<template>
  <!-- 个人中心 -->
  <div class="content">
    <NavHeight bgc="#fff" />
    <!-- 头部 -->
    <van-nav-bar left-arrow :border="false">
      <template #title>
        <div class="topTitle">设置</div>
      </template>
      <template #left>
        <van-icon name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/user/icon/arrow-black.png" style="cursor:pointer" size="20" @click="goBack" />
      </template>
    </van-nav-bar>

    <div class="editList">
      <ul class="list">
        <li class="item" @click="goTo('PersonInfo')">
          <div>
            <div class="itemName">个人信息</div>
            <div class="itemTag">头像、名称、收货地址</div>
          </div>
          <div>
            <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/my/edit/right.png" alt="">
          </div>
        </li>
        <li class="item" @click="goTo('Account')">
          <div>
            <div class="itemName">账号与安全</div>
            <div class="itemTag">修改密码、修改手机号码、账号管理</div>
          </div>
          <div>
            <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/my/edit/right.png" alt="">
          </div>
        </li>
      </ul>

      <ul class="list">
        <li class="item itemList" @click="telShow = true">
          <div>
            <div class="itemName">客服帮助</div>
          </div>
          <div>
            <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/my/edit/right.png" alt="">
          </div>
        </li>
        <li class="item itemList" @click="goTo('FeedBack')">
          <div>
            <div class="itemName">意见反馈</div>
          </div>
          <div>
            <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/my/edit/right.png" alt="">
          </div>
        </li>
      </ul>

      <ul class="list">
        <li class="item itemList" @click="goTo('sdk')">
          <div>
            <div class="itemName">第三方SDK名单</div>
          </div>
          <div>
            <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/my/edit/right.png" alt="">
          </div>
        </li>
      </ul>

      <ul class="list">
        <li class="item itemList" @click="goTo('AboutUs')">
          <div>
            <div class="itemName">关于点滴</div>
          </div>
          <div>
            <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/my/edit/right.png" alt="">
          </div>
        </li>
      </ul>

    </div>

    <div class="btn" @click="loginShow = true">退出账号</div>

    <!-- 退出弹框 -->
    <van-popup
      v-model="loginShow"
      round
      :style="{
        height: '160px',
      }"
    >
      <div class="popLoginBox">
        <div class="title">亲！确定要退出登录吗？</div>
        <div class="popBtn">
          <div class="cancel" @click="cancel">取消</div>
          <div class="confirm" @click="logout">确认</div>
        </div>
      </div>
    </van-popup>

    <!-- 电话弹框 -->
    <van-popup
      v-model="telShow"
      round
      :style="{
        width: '57%',
        height: '',
      }"
    >
      <div class="popTelBox">
        <div v-for="(item,index) in phoneList" :key="index" @click="CallPhone(item.cvalue)">{{ item.cname }}</div>
      </div>
    </van-popup>
  </div>
</template>

<script>
import { getCustomServicePhone } from '@/api/my'
import { logout } from '@/api/login'
export default {
  components: {
  },
  data() {
    return {
      loginShow: false,
      telShow: false,
      phoneList: []
    }
  },
  created() {
    this.getCustomServicePhone()
  },
  methods: {
    // 根据大区获取客服电话
    getCustomServicePhone() {
      getCustomServicePhone(this.$store.getters.getRegionId).then(res => {
        if (res.status == 200) {
          this.phoneList = res.data
        }
      })
    },
    // 跳转对应页面
    goTo(data) {
      this.$router.push({
        name: data
      })
    },
    cancel() {
      this.loginShow = false
    },
    // 退出
    logoutBtn() {
      this.loginShow = false
      localStorage.removeItem('token')
      localStorage.removeItem('userId')
      localStorage.removeItem('headImg')
      localStorage.removeItem('advert')
      localStorage.removeItem('isWxBind')
      localStorage.removeItem('phone')
      this.$store.state.My.headImg = ''
      this.$store.state.token = ''
      this.$store.state.userId = ''
      this.$store.state.phone = ''
      this.$store.state.order.orderList = []
      this.$store.state.My.blance = 0
      this.$store.state.market.marketData.balance = ''
      this.$store.state.market.marketData.payradio = ''
      this.$store.state.market.marketData.postFee = 0
      this.$router.push({ name: 'wxLogin2' })
      AlipayJSBridge.call('UserLogout', {}, function(result) {})
    },
    // 退出登录
    logout() {
      logout().then((res) => {
        if (res.status == 200) {
          this.logoutBtn()
        }
      })
    },
    // 打电话
    CallPhone(data) {
      AlipayJSBridge.call('CallPhone', {
        phoneNum: data
      }, function(result) {})
    },
    goBack() {
      this.$router.go(-1)
    }
  }
}
</script>

  <style lang="scss" scoped>
  .content {
    font-family: PingFangSC;
    .editList{
      .list{
        width: 100%;
        background-color: #fff;
        margin-top: 20px;
        .item{
          width: 686px;
          height: 128px;
          display: flex;
          justify-content: space-between;
          font-size: 32px;
          margin: 0 auto;
          .itemName{
            color: #222222;
            font-size: 32px;
            margin-top: 20px;
          }
          .itemTag{
            font-size: 26px;
            color: #999999;
          }
          img{
            width: 30px;
            height: 30px;
            margin-top: 49px;
          }
        }
        .itemList{
          height: 88px;
          img{
            margin-top: 32px;
          }
        }
      }

    }
    .topTitle{
      color: #222222;
      font-size: 36px;
      font-family: PingFangSC;
    }
    .btn {
      width: 452px;
      height: 84px;
      line-height: 84px;
      text-align: center;
      color: #ffffff;
      font-size: 32px;
      margin: 0 auto;
      background: linear-gradient(45deg, #71d774 0%, #5ecc52 100%);
      border-radius: 12px;
      margin-top: 400px;
    }
    .popLoginBox {
      position: relative;
      width: 561px;
      height: 282px;
      padding-top: 80px;
      box-sizing: border-box;
      .title {
        color: #333340;
        font-size: 36px;
        text-align: center;
      }
    }
    .popBtn {
      position: fixed;
      left: 0;
      bottom: 0px;
      display: flex;
      width: 561px;
      height: 97px;
      line-height: 97px;
      font-size: 32px;
      justify-content: space-between;
      border-top: 1px solid #cfcece;
      text-align: center;
      .cancel {
        width: 50%;
        border-right: 1px solid #cfcece;
        color: #999999;
      }
      .confirm {
        width: 50%;
        color: #6095f0;
      }
    }
    .popTelBox {
			overflow: hidden;
			>div {
				height: 102px;
				line-height: 102px;
				text-align: center;
				font-size: 32px;
				color: #000010;
				opacity: 0.7;
			}

			>div:not(:nth-of-type(3)) {
				border-bottom: 1px solid #e5e5e5;
			}
		}

		::v-deep .van-popup--center.van-popup--round {
			border-radius: 16px;
		}
  }
  </style>
