<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-06-04 11:51:57
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-08-22 14:21:28
-->
<template>
  <!-- 个人中心 -->
  <div class="content">
    <NavHeight bgc="#fff" />
    <!-- 头部 -->
    <van-nav-bar title="" left-text="" left-arrow :border="false">
      <template #title>
        <div>个人信息</div>
      </template>
      <template #left>
        <van-icon name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/user/icon/arrow-black.png" size="20" @click="goBack" />
      </template>
    </van-nav-bar>
    <div class="personInfo">
      <div v-if="false" class="item line1">
        <span>头像</span>
        <input
          v-if="isShow"
          ref="input"
          type="file"
          accept="image/*"
          style="opacity: 0;"
          class="change_avatar"
          name="upload_file"
          @change="changeImg()"
        >
        <div class="avatar">
          <img v-if="$store.state.My.headImg" :src="$store.state.My.headImg" alt="">
          <img v-else :src="defaultImg" alt="">
        </div>
        <h5-cropper v-if="false" ref="cropper" class="upbtn" :option="option" @getbase64Data="getbase64Data" />
      </div>
      <div class="item line2">
        <span>名称</span>
        <div class="rightName" @click="usernameShow = true">
          <span style="margin-right:14px">{{ nickName }}</span>
          <van-icon
            name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/my/edit/right.png"
            size="11"
          />
        </div>
      </div>
      <div class="item line2" @click="goAddress">
        <span>收货地址</span>
        <div class="rightName">
          <span style="margin-right:14px">修改/添加</span>
          <van-icon
            name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/my/edit/right.png"
            size="11"
          />
        </div>
      </div>
    </div>
    <div class="btn" @click="loginShow = true">退出账号</div>

    <!-- 修改用户名弹框 -->
    <van-popup
      v-model="usernameShow"
      round
      :style="{
        height: '200px',
      }"
    >
      <div class="popBox">
        <div class="title">修改用户名（限制15字符以内）</div>
        <input
          v-model="username"
          type="text"
          maxlength="15"
          placeholder="请输入用户名"
        >
        <div class="popBtn">
          <div class="cancel" @click="cancel">取消</div>
          <div class="confirm" @click="confirm">确认</div>
        </div>
      </div>
    </van-popup>

    <!-- 退出弹框 -->
    <van-popup
      v-model="loginShow"
      round
      :style="{
        height: '160px',
      }"
    >
      <div class="popLoginBox">
        <div class="title">亲！确定要退出登录吗？</div>
        <div class="popBtn">
          <div class="cancel" @click="cancel">取消</div>
          <div class="confirm" @click="logout">确认</div>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script>
import OSS from 'ali-oss'
import H5Cropper from '@/components/CropperH5/cropper'
import { UserInfo, editAccountName, editPic } from '@/api/my'
import { logout } from '@/api/login'
export default {
  components: { H5Cropper },
  data() {
    return {
      usernameShow: false,
      loginShow: false,
      username: '',
      headImg: '',
      telephone: '',
      nickName: '',
      imgData: [], // 选择图片保存数组
      type: '',
      maxSize: 2 * 1024,
      defaultImg: 'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/my/touxiang.png',
      option: { // 配置
        autoCropWidth: '400',
        autoCropHeight: '400',
        full: true
      },
      isShow: true
    }
  },
  mounted() {
    this.getUser()
    if (this.$store.getters.getUserId == 3402) {
      this.isShow = false
    }
  },
  methods: {
    getbase64Data(data) {
      this.compress(data)
    },
    cancel() {
      this.usernameShow = false
      this.loginShow = false
    },
    confirm() {
      if (this.username.trim() == '') {
        this.$toast('用户名不能为空')
        return false
      }
      let data = {
        id: this.$store.getters.getUserId,
        nickName: this.username
      }
      editAccountName(data)
        .then((res) => {
          if (res.status == 200) {
            this.$toast('修改用户名成功')
            setTimeout(() => {
              this.getUser()
            }, 1000)
          } else {
            this.$toast(res.message)
          }
        })
      this.usernameShow = false
    },
    logoutBtn() {
      this.loginShow = false
      localStorage.removeItem('token')
      localStorage.removeItem('userId')
      localStorage.removeItem('headImg')
      localStorage.removeItem('advert')
      localStorage.removeItem('isWxBind')
      localStorage.removeItem('phone')
      this.$store.state.My.headImg = ''
      this.$store.state.token = ''
      this.$store.state.userId = ''
      this.$store.state.phone = ''
      this.$store.state.order.orderList = []
      this.$store.state.My.blance = 0
      this.$store.state.market.marketData.balance = ''
      this.$store.state.market.marketData.payradio = ''
      this.$store.state.market.marketData.postFee = 0

      this.$router.push({ name: 'wxLogin2' })
      AlipayJSBridge.call('UserLogout', {}, function(result) {})
    },
    // 退出登录
    logout() {
      logout().then((res) => {
        if (res.status == 200) {
          this.logoutBtn()
        }
      })
    },
    getUser() {
      let data = this.$store.getters.getUserId
      UserInfo(data)
        .then((res) => {
          if (res.status == 200) {
            this.headImg = res.data.headImg
            this.telephone = res.data.telephone
            this.nickName = res.data.nickName
          } else {
            this.$toast(res.message)
          }
        })
    },
    // 图片change事件
    changeImg() {
      // 上传图片事件
      var files = this.$refs.input.files
      this.beforeRead(files[0])
    },
    beforeRead(file) {
      this.type = file.type
      this.uploadFile(file)
      // let size = file.size / 1024
      // // 如果图片大于2M进行压缩，
      // if (size <= this.maxSize) {
      //   // 直接上传
      //   this.uploadFile(file)
      // } else {
      //   // 对图片进行压缩
      //   this.imgPreview(file)
      // }
    },
    // 将图片转成 base64 格式
    imgPreview(file) {
      let self = this
      // 看支持不支持FileReader
      if (!file || !window.FileReader) return
      let reader = new FileReader()
      reader.readAsDataURL(file)
      reader.onloadend = function() {
        // 此处的this是reader
        let result = this.result
        self.compress(result)
      }
    },
    // 压缩图片
    compress(base64) {
      let cvs = document.createElement('canvas')
      let img = document.createElement('img')
      img.crossOrigin = 'anonymous'
      img.src = base64
      // 图片偏移值
      // eslint-disable-next-line no-unused-vars
      let offetX = 0
      img.onload = () => {
        if (img.width > 800) {
          cvs.width = 800
          cvs.height = (img.height * 800) / img.width
          offetX = (img.width - 800) / 2
        } else {
          cvs.width = img.width
          cvs.height = img.height
        }
        // eslint-disable-next-line no-unused-vars
        let ctx = cvs.getContext('2d').drawImage(img, 0, 0, cvs.width, cvs.height)
        let imageData = cvs.toDataURL(this.type, 0.95)
        this.convertBase64UrlToBlob(imageData)
      }
    },
    // 将base64转为文件流
    convertBase64UrlToBlob(imageData) {
      let filename = ''
      let arr = imageData.split(',')
      let mime = arr[0].match(/:(.*?);/)[1]
      if (!filename) {
        filename = `${new Date().getTime()}.${mime.substr(mime.indexOf('/') + 1)}`
      }
      let bstr = atob(arr[1])
      let n = bstr.length
      let u8arr = new Uint8Array(n)
      while (n--) {
        u8arr[n] = bstr.charCodeAt(n)
      }
      this.uploadFile(new File([u8arr], filename, {
        type: mime
      }))
    },
    // 图片上传接口
    uploadFile(file) {
      let self = this
      this.$toast.loading({
        duration: 0, // 持续展示 toast
        forbidClick: true,
        message: '上传中'
      })
      var formData = new FormData()
      // 添加文件流
      formData.append('file', file)

      let client = new OSS({
        region: 'oss-cn-hangzhou',
        accessKeyId: 'LTAI4GHzemXAwKszXKiniT9s',
        accessKeySecret: '******************************',
        bucket: 'diandi-video'
      })
      let now = Date.parse(new Date())
      let names = now + file.name
      client.put('face/' + names, file)
        .then(function(res) {
          self.$toast.clear()
          self.ups(res.url + '?x-oss-process=image/resize,w_700/format,jpg/quality,q_30')
          return false
        })
    },
    ups(url) {
      let self = this
      let data = {
        id: this.$store.getters.getUserId,
        headImg: url
      }
      editPic(data)
        .then((res) => {
          if (res.status == 200) {
            self.$toast('头像更换成功')
            setTimeout(function() {
              self.$store.commit('My/setHeadImg', url)
              self.getUser()
            }, 300)
          } else {
            self.$toast(res.message)
          }
        })
    },
    upload1() {
      // 点击触发按钮
      this.$refs.input.dispatchEvent(new MouseEvent('click'))
    },
    goAddress() {
      this.$router.push({
        name: 'Address'
      })
    },
    goBack() {
      this.$router.go(-1)
    }
  }
}
</script>

  <style lang="scss" scoped>
  .content {
    font-family: PingFangSC;
    .personInfo {
      width: 100%;
      background-color: #fff;
      padding: 20px 37px 0;
      box-sizing: border-box;
      .item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 32px;
        .avatar {
          width: 90px;
          height: 90px;
          border-radius: 45px;
          overflow: hidden;
          img {
            width: 100%;
            height: 100%;
          }
        }
        > span {
          color: #222;
        }
        .rightName{
          color: #999;
        }
      }
      .line1{
        position: relative;
        .change_avatar{
          position: absolute;
          right: 0;
        }
      }
      .upbtn{
        position: absolute;
      }
      .line1,.line3 {
        height: 126px;
      }
      .line2 {
        height: 100px;
      }
      ::v-deep .van-icon {
        top: 2px;
      }
    }
    .btn {
      width: 452px;
      height: 84px;
      line-height: 84px;
      text-align: center;
      color: #ffffff;
      font-size: 32px;
      margin: 0 auto;
      background: linear-gradient(45deg, #71d774 0%, #5ecc52 100%);
      border-radius: 12px;
      margin-top: 400px;
    }
    .popBox {
      position: relative;
      width: 561px;
      height: 32px;
      padding-top:48px;
      box-sizing: border-box;

      .title {
        color: #7f7f87;
        font-size: 30px;
        padding-left: 42px;
      }

      ::v-deep input {
        font-size: 32px;
        margin-left: 40px;
        margin-top: 38px;
        border: none;
      }
    }
    .popLoginBox {
      position: relative;
      width: 561px;
      height: 282px;
      padding-top: 80px;
      box-sizing: border-box;
      .title {
        color: #333340;
        font-size: 36px;
        text-align: center;
      }
    }
    .popBtn {
      position: fixed;
      left: 0;
      bottom: 0px;
      display: flex;
      width: 561px;
      height: 97px;
      line-height: 97px;
      font-size: 32px;
      justify-content: space-between;
      border-top: 1px solid #cfcece;
      text-align: center;
      .cancel {
        width: 50%;
        border-right: 1px solid #cfcece;
        color: #999999;
      }
      .confirm {
        width: 50%;
        color: #6095f0;
      }
    }
  }
  </style>
