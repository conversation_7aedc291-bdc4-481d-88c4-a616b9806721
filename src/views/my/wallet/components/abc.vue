<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2021-11-15 09:36:37
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-09-03 15:22:22
-->
<template>
  <div class="ccb-home">
    <div v-if="item.accountType == 1" class="item-btn">
      <div v-if="accountType == 1" class="item-recharge" @click="privacyShow = true">签约</div>
      <div v-if="accountType == 2" class="item-recharge" @click="activationABC(item)">银联激活</div>
      <!-- <div v-if="accountType == 2" class="item-recharge" @click="getBlance(item)">银联激活</div> -->
      <div v-if="accountType == 3" style="font-size: 16px;color: #daac22">激活中</div>
      <div v-if="accountType == 4" style="font-size: 16px;color: #daac22">已签约</div>
      <div v-if="accountType == 5" style="font-size: 16px;color: #daac22" />
      <!-- <div v-if="accountType == 4&& item.regionId == 7" class="item-recharge" @click="agentUnSignReq(item)">解约</div> -->
    </div>

    <!-- 验证码弹框 -->
    <van-popup v-model="codeLog" round :style="{ height: '260px', }" :close-on-click-overlay="false">
      <div class="popBox">
        <van-field v-model="cardNo" :disabled="isDisCardNo" center clearable :border="false" label="签约卡号" placeholder="请输入" />
        <div style="height: 5px;" />
        <van-field v-model="verifyCode" center clearable :border="false" label="验证码" placeholder="请输入验证码">
          <template #button>
            <div>
              <div v-if="showCode" class="codeLoginFormBtn" @click="agentSignReq">发送验证码</div>
              <div v-if="!showCode" class="codeLoginFormBtns">已发送{{ count }}s</div>
            </div>
          </template>
        </van-field>
        <div class="contract_tips">
          <div>注意事项：</div>
          <div>
            请确保银行预留信息(姓名、手机号、身份证)与点滴系统预留一致
          </div>
        </div>
        <div class="popBtn">
          <div class="cancel" @click="codeLog = false">取消</div>
          <div class="confirm" @click="confirm">确认</div>
        </div>
      </div>
    </van-popup>

    <van-popup v-model="privacyShow" round position="bottom" :style="{ height: '50%' }">
      <div class="abc_box">
        <div class="ccb_title">
          服务协议
        </div>
        <div class="ccb_privacy">
          <div class="ccb_privacy_name">
            <div style="text-align: center;">
              中国农业银行股份有限公司网络商户快捷支付服务三方协议
            </div>
            尊敬的客户，欢迎您使用快捷支付服务。为了维护您的权益，请在签署本协议前，仔细
            阅读本协议各条款（特别是含有黑体字标题或黑体字文字的条款），注意本协议涉及数量和
            质量、价款或者费用、履行期限和方式、安全注意事项和风险警示、售后服务、民事责任、
            管辖等内容的条款。请关注个人金融信息保护的相关法律法规，了解自己的权利义务，合理
            审慎提供个人金融信息，维护自身合法权益。如您有疑问或不明之处，请咨询您的律师和有
            关专业人士。如需进行业务咨询和投诉，请拨打农业银行客服热线：95599。
            本协议已经由   {{ item.regionId == 7?"庆元县旅游集散中心有限公司":"景宁畲族自治县景宜商贸有限公司" }}    （即为甲方）和中国农
            业银行股份有限公司经办机构（即为乙方）共同签署，您（即为丙方）点击确认即接受协议
            各条款，本协议生效。

            第一条 快捷支付方式说明
            （一）丙方为甲方电子商务平台上的实名注册客户，持有甲方注册账户。
            （二）丙方为乙方电子银行客户或预留手机号的银行卡客户，并且丙方需在其电子银行
            注册账户中或预留手机号的银行卡账户中指定账户作为扣款账户。
            （三）丙方按照甲方和乙方提供的操作流程将其在甲方的注册账户和在乙方的扣款账户
            进行绑定。
            （四）丙方进行网上支付时，如果选择使用快捷支付方式的，扣款只需提供其在甲方的
            注册账户及安全认证信息。甲方根据客户的实名注册信息和客户选择快捷支付方式的信息向
            乙方发出扣款指令，乙方按照甲方的扣款指令从丙方的扣款账户中进行扣款。
            第二条 甲方的权利和义务
            （一）甲方应当在乙方开立结算账户。
            （二）甲方应当为丙方使用快捷支付方式提供使用指引。
            （三）丙方选择使用快捷支付方式后，甲方应当及时、真实、完整、准确、合法和有效
            地向乙方发送扣款指令。甲方不得篡改丙方指令，不得假冒丙方指令，也不得增加扣款金额；
            甲方如实施上述行为，致使乙方提供扣款等支付结算服务被丙方提出权利主张的，由甲方负
            责处理并承担全部责任，由此给丙方、乙方造成的损失由甲方负责赔偿。
            （四）甲方负责向丙方提供商品或者服务、业务查询、咨询、投诉、打印及发送发票等
            电子商务服务。
            （五）甲方应当保证交易数据的安全性、保密性、完整性和不可抵赖性，并负责处理因
            安全认证不足而产生的差错交易及风险交易，承担相关责任。
            （六）甲方通过非柜面渠道向非同名银行账户和支付账户转账的日累计限额、笔数和年
            累计限额等，已在甲乙双方之间另行签订的相关协议中约定，各方将按其约定执行。
            第三条 乙方的权利和义务
            （一）乙方按照甲方的扣款指令实施扣款行为，将丙方款项划转至甲方在乙方开立的账
            户，为丙方和甲方提供支付结算服务。乙方对甲方发送的扣款指令不做实质性审查。
            （二）乙方对以下情况不承担责任：
            1.甲方或丙方账户信息验证不成功；
            2.甲方或丙方账户的存款余额或信用额度不足；
            3.甲方或丙方支付金额高于其自设或付款账户开户行、中国银联或其他银行卡组织及机

            1
            构、乙方设置的限额；
            4.甲方或丙方账户处于止付、挂失、未激活、冻结、过期或销户等非正常状态；
            5.甲方或丙方未能按照乙方的有关业务规定正确操作的；
            6.乙方接收到的指令存在迟延、虚假、信息不明、乱码、不完整或内容有误等问题；
            7.甲方或丙方在乙方系统非交易时间发出的指令；
            8.电子商务平台的交易方之间或交易方与平台之间的纠纷，包括但不限于甲方与丙方之
            间产生的纠纷；
            9.因甲方或丙方处理不善致使乙方提供的商户软件、证书或密码泄露造成损失和纠纷
            的；
            10.由于人民银行交易系统、银行卡清算机构交易系统技术问题或其他非因乙方的原因
            而导致的交易延缓；
            11.其他不可抗力或者不可归责于乙方的情形。
            第四条 丙方的权利和义务
            （一）丙方应当按照甲方和乙方提供的指引，正确指定扣款账户，注册实名账户，并将
            实名账户和扣款账户绑定。
            （二）乙方执行扣款指令后，丙方不能要求变更或者撤销扣款指令。
            （三）丙方同意，其使用甲方的实名注册账户并选择快捷支付方式的，乙方即可实施扣
            款行为。丙方应当妥善保管本人在甲方的实名注册账户信息及相关密码，不得向他人透露，
            因丙方泄露实名注册账户信息和密码造成的资金损失，甲乙双方不承担责任。
            （四）丙方应妥善保管本人在乙方的银行卡关键信息（密码、有效期、CVN2 码、绑定
            的手机号等）、网上银行证书及其密码、身份证件信息，不得泄露上述信息。因丙方保管不
            善造成的损失，甲乙双方不承担责任。
            （五）丙方知悉并同意，对于甲方电子商务平台和乙方网上支付系统而言，使用丙方拥
            有的相关账户、证书和密码即视为丙方行为，行为后果由丙方承担。
            （六）丙方应当按照甲方电子商务平台公布的收费标准向甲方支付快捷支付服务的各项
            费用。
            （七）丙方通过非柜面渠道向非同名银行账户和支付账户转账的日累计限额、笔数和年
            累计限额等，已由乙丙双方另行约定，各方将按其约定执行。
            （八）甲方或丙方使用服务时，支付限额须同时遵守乙方、扣款账户开户行及中国银联
            或其他银行卡组织的规定。乙方有权根据有关法律法规、反洗钱监管要求和业务经营需要等
            设置和调整快捷支付的金额限额，并在其网站或客户端相关页面及时发布公告。
            第五条 客户信息保护
            （一）乙方基于快捷支付业务办理、履行合同及开展风险管理的需要，将自本协议签署
            之日起，收集、存储、使用、加工个人基本信息（包括身份信息、联系方式）、账户信息（账
            户号）、交易信息（扣款记录）。对于需要丙方同意的个人信息处理行为，乙方将通过合法方
            式取得丙方授权同意。各方业务关系终止后，乙方将严格依照法律法规的规定和本协议的约
            定处理丙方信息。
            （二）丙方理解，上述个人信息中涉及的敏感个人信息包括身份证件号码、联系方式、
            账户信息、交易信息，一旦泄露、非法提供或滥用可能危害人身和财产安全。如丙方同意授
            权的，乙方将通过加密安全的合法方式处理上述敏感个人信息。中国农业银行将仅在甲乙双
            方约定的使用目的、方式及信息种类等范围内处理甲方个人信息，并对甲方个人信息安全储
            存、严格保密。
            （三）为履行本协议及为丙方开通快捷支付服务及提供交易风险短信验证服务所必需，
            甲方需将其姓名、银行卡号、手机号码、身份证件号码发送至乙方（联系方式及乙方个人信

            2
            息保护规则请以乙方官方渠道公布为准）。如丙方不同意可能导致乙方无法向甲方或丙方提
            供本协议相关的服务或履行乙方需向甲方或丙方承担的义务。
            （四）甲乙双方承诺将严格遵守法律法规规定，在处理丙方信息时，遵循合法、正当、
            必要和诚信的原则，并在为丙方提供协议约定的服务所必需的前提下及丙方授权的范围、内
            容和期限内收集、存储、使用、加工、传输、提供、公开、删除丙方信息。不泄露、篡改、
            毁损丙方信息，不非法买卖、提供或者公开丙方信息，不收集、使用、加工、传输与所提供
            服务或办理业务无关的丙方信息。
            （五）甲乙双方承诺将使用符合法律法规要求的安全防护措施保护丙方提供的个人信
            息，防止数据遭到未经授权的访问、公开披露、使用、修改、损坏或丢失。
            （六）甲乙双方恪守对丙方信息的保密义务，并严格按照法律法规规定及双方约定的范
            围和用途处理其信息，若发生个人信息泄露事件，甲乙双方将按照法律法规要求及时采取有
            效补救措施，并及时将事件相关情况以电子邮件、信函、电话或推送通知等方式告知丙方，
            难以逐一告知的，甲乙双方将通过公告告知。业务关系终止后，甲乙双方将严格依照法律法
            规的规定和与丙方的约定处理丙方信息，并依法承担违规责任。
            （七）在不违反法律法规或监管规定的前提下，丙方有权通过甲方网点、电子银行等渠
            道查阅及复制向甲方提供的个人信息，如发现信息有错误的，丙方有权要求修改或删除已提
            供的个人信息。
            （八）本协议项下与客户信息保护相关的通用规则，如丙方行使法律规定权利的方式和
            程序等，请参见中国农业银行在官方网站和掌上银行 APP 发布并不定时更新的《中国农业
            银行股份有限公司隐私政策（个人版）》的相关内容。
            第六条 异常账务处理
            （一）丙方发现扣款金额与其交易金额不符的，应当及时向甲方进行查询或者投诉，由
            甲方和乙方核对扣款信息。
            （二）确因甲方安全认证不足或其他等原因造成丙方账户扣款差错的，由甲方负责补偿
            丙方资金损失。
            第七条 争议解决
            本协议履行过程中，如果发生争议，可由三方协商解决，协商不成，任何一方有权向乙
            方所在地有管辖权的人民法院提起诉讼。
            在诉讼期间，本协议不涉及争议的条款仍需履行。
            第八条 协议终止
            （一）丙方有权在线解除本协议。丙方解除本协议的，本协议终止，甲乙双方不再为丙
            方提供快捷支付服务。
            （二）甲方和乙方协商一致拟终止业务合作关系的，应当由甲方提前十五个工作日在其
            网站上公告。公告期满，本协议自动终止，甲方和乙方将不再为丙方办理快捷支付服务。
          </div>
        </div>

        <div class="ccb_btn">
          <div class="ccb_radio">
            <van-checkbox v-model="privacyCecked" style="width: 130px;">
              我已阅读并同意
            </van-checkbox>
            <span class="link" @click="privacyShow = true">《服务协议》</span>
          </div>
          <div class="btns">
            <van-button class="btn" type="default" @click="privacyShow = false">拒 绝</van-button>
            <van-button class="btn" type="primary" @click="agentSignHandle">同 意</van-button>
          </div>

        </div>
      </div>
    </van-popup>

    <!-- 银联弹出 -->
    <van-popup v-model="unionPayCodeLog" round :style="{ height: '130px', }">
      <div class="popBox">
        <van-field v-model="unionPayForm.codeNum" center clearable :border="false" label="验证码" placeholder="请输入验证码">
          <template #button>
            <div>
              <div v-if="unionPayForm.showCode" class="codeLoginFormBtn" @click="activation">发送验证码</div>
              <div v-if="!unionPayForm.showCode" class="codeLoginFormBtns">已发送{{ unionPayForm.count }}s</div>
            </div>
          </template>
        </van-field>
        <div class="popBtn">
          <div class="cancel" @click="unionPayCodeLog = false">取消</div>
          <div class="confirm" @click="unionPayConfirm">确认</div>
        </div>
      </div>
    </van-popup>

    <Loading :show="loadingShow" />
  </div>
</template>

<script>
import Loading from '@/components/Loading/index'
import { capitalist } from '@/api/my'
import { unionPayCreateAcc } from '@/api/bank'
import { openAccountSmsSend } from '@/api/bank/nsh'
import { fundAbcAccount, agentSignReq, agentSignResend, agentSignSubmit, agentUnSignReq } from '@/api/bank/abc'
export default {
  components: {
    Loading
  },
  props: {
    item: {
      type: Object,
      default: function() {
        return {}
      }
    }
  },
  data() {
    return {
      codeLog: false,
      codeNum: '',
      count: '',
      showCode: true,
      accountType: 0,
      loadingShow: false,
      cardNo: '',
      isDisCardNo: false,
      verifyCode: '',
      privacyShow: false,
      privacyCecked: false,
      unionPayCodeLog: false,
      unionPayForm: {
        smsId: '',
        smsCode: '',
        count: '',
        showCode: true,
        timer: null,
        codeNum: ''
      }
    }
  },
  mounted() {
    if (this.item.regionName === '身心疗养基金' || this.item.regionName === '员工福祉基金' || this.item.regionName === '女神呵护基金' || this.item.regionName === '员工生日礼遇') {
      this.accountType = 5
      return
    }
    if (this.item.payChannel == 2) {
      this.fundAccountQuery('UnionPay', this.item)
    }
  },
  methods: {
    //   获取状态
    getBlance() {
      capitalist(this.$store.getters.getUserId).then((res) => {
        if (res.status == 200) {
          if (res.data.length != 0) {
            this.fundAccountQuery('UnionPay', this.item)
            // for (let i = 0; i < res.data.length; i++) {
            //   if (res.data[i].regionId == 7 || res.data[i].regionId == 6 && res.data[i].payChannel == 2 && res.data[i].capitalPoolId == 12) {
            //     this.fundAccountQuery('UnionPay', res.data[i])
            //   } else {
            //     this.loadingShow = false
            //   }
            // }
            // res.data.map((item) => {
            //   if (item.regionId == 7 || item.regionId == 6 && item.payChannel == 2) {
            //     this.fundAccountQuery('UnionPay', item)
            //   } else {
            //     this.loadingShow = false
            //   }
            // })
          }
        }
      })
    },
    // 查询开户类型
    fundAccountQuery(data, item) {
      //       status：状态 0-- 待开户，1--银行开户中，2--银行已开户，3---银联开户中（农行），4---银联已开户（农行）
      // 0、1：展示"签约"按钮
      // 2：展示"银联激活"按钮
      // 3：展示"激活中"提示

      let queryVO = {
        'capitalAccountId': item.id,
        'userId': this.$store.getters.getUserId
      }
      fundAbcAccount(queryVO).then(res => {
        if (item.id == 45896) {
          console.log(res.data)
          console.log(363636)
        }
        if (res.data != null) {
          if (res.data.status == 0 || res.data.status == 1) {
            this.accountType = 1
          } else if (res.data.status == 2) {
            this.accountType = 2
          } else if (res.data.status == 3) {
            this.accountType = 3
          } else {
            this.accountType = 4
          }

          if (res.data.remark != '') {
            this.cardNo = res.data.remark
            // this.isDisCardNo = true
          }
        } else {
          this.accountType = 1
        }
      })
    },
    // 签约
    agentSignHandle() {
      if (this.privacyCecked == false) {
        this.$toast('请阅读并同意服务协议')
        return
      }
      this.codeLog = true
      this.privacyShow = false
    },
    // 验证码
    agentSignReq() {
      let self = this
      agentSignReq({
        'capitalAccountId': this.item.id,
        'cardNo': this.cardNo
      }).then(res => {
        if (res.status == 200) {
          this.$toast('发送成功')
          this.smsId = res.message
          const TIME_COUNT = 60
          this.count = TIME_COUNT
          this.showCode = false
          this.timer = setInterval(() => {
            if (self.count > 0 && self.count <= TIME_COUNT) {
              self.count--
            } else {
              self.showCode = true
              clearInterval(self.timer)
              self.timer = null
            }
          }, 1000)
        }
      })
    },
    // 重发
    agentSignResend() {
      let self = this
      agentSignResend({
        'capitalAccountId': this.item.id,
        'cardNo': this.cardNo,
        'mobileNo': '***********',
        'accName': '王远庆',
        'certificateNo': '******************'
      }).then(res => {
        if (res.status == 200) {
          this.$toast('发送成功')
          this.smsId = res.data.smsId
          const TIME_COUNT = 60
          this.count = TIME_COUNT
          this.showCode = false
          this.timer = setInterval(() => {
            if (self.count > 0 && self.count <= TIME_COUNT) {
              self.count--
            } else {
              self.showCode = true
              clearInterval(self.timer)
              self.timer = null
            }
          }, 1000)
        }
      })
    },
    // 输入短信验证码，激活
    confirm() {
      this.$toast.loading({
        duration: 0,
        forbidClick: true,
        message: '请求中'
      })
      let queryVO = {
        'capitalAccountId': this.item.id,
        'orderNo': this.smsId,
        'verifyCode': this.verifyCode
      }
      agentSignSubmit(queryVO).then(res => {
        if (res.status == 200 && res.data == true) {
          this.$toast('操作成功')
          this.accountType = 3
          this.codeLog = false
          this.getBlance()
          this.$emit('getBlanceMethod')
        } else {
          this.$toast(res.data.respMsg)
        }

        setTimeout(() => {
          this.$toast.clear()
        }, 2000)
      })
    },
    // 激活农行银联
    activationABC(item) {
      let self = this
      this.capitalAccountId = item.capitalAccountId
      this.capitalPoolId = item.capitalPoolId

      this.$toast.loading({
        message: '',
        duration: 1,
        forbidClick: true
      })
      this.unionPayForm.codeNum = ''
      this.unionPayForm.codeLog = true
      let queryVO = {
        'bank': 'ABC',
        // eslint-disable-next-line no-undef
        'clientIp': '127.0.0.1',
        'deviceId': localStorage.getItem('deviceId') ? localStorage.getItem('deviceId') : 'test',
        'latitude': this.$store.getters.getLocation.latitude,
        'longitude': this.$store.getters.getLocation.longitude,
        'userId': localStorage.getItem('userId'),
        'capitalAccountId': item.capitalAccountId
      }
      openAccountSmsSend(queryVO).then(res => {
        this.$toast.clear()
        if (res.data != null) {
          if (res.status == 200) {
            this.$toast('发送成功')
            this.unionPayForm.smsId = res.data.smsId
            const TIME_COUNT = 60
            this.unionPayForm.count = TIME_COUNT
            this.unionPayCodeLog = true
            this.unionPayForm.showCode = false
            this.unionPayForm.timer = setInterval(() => {
              if (self.unionPayForm.count > 0 && self.unionPayForm.count <= TIME_COUNT) {
                self.unionPayForm.count--
              } else {
                self.unionPayForm.showCode = true
                clearInterval(self.unionPayForm.timer)
                self.unionPayForm.timer = null
              }
            }, 1000)
          }
        }
      })
    },
    // 输入短信验证码，银联开户
    unionPayConfirm() {
      this.$toast.loading({
        duration: 0,
        forbidClick: true,
        message: '请求中'
      })
      let queryVO = {
        'bank': 'ABC',
        // eslint-disable-next-line no-undef
        'clientIp': '127.0.0.1',
        'deviceId': localStorage.getItem('deviceId') ? localStorage.getItem('deviceId') : 'test',
        'latitude': this.$store.getters.getLocation.latitude,
        'longitude': this.$store.getters.getLocation.longitude,
        'poolId': this.$store.getters.getRegionId,
        'smsCode': this.unionPayForm.codeNum,
        'smsId': this.unionPayForm.smsId,
        'capitalAccountId': this.capitalAccountId
      }
      unionPayCreateAcc(queryVO).then(res => {
        this.$toast.clear()
        if (res.status == 200) {
          if (res.data.respCode == '00000') {
            this.$toast('操作成功')
            this.getBlance()
            this.unionPayCodeLog = false
          } else {
            this.$toast(res.data.respMsg)
          }
        }
      })
    },
    // 解约
    agentUnSignReq(item) {
      this.$dialog.confirm({
        title: '提示',
        message: '是否解约？'
      }).then(() => {
        agentUnSignReq({
          'capitalAccountId': item.capitalAccountId,
          'bank': 'ABC'
        }).then(res => {
          if (res.status == 200) {
            this.$toast('解约成功')
            this.getBlance()
            this.$emit('getBlanceMethod')
          }
        })
      }).catch(() => {
        // on cancel
      })
    }
  }
}
</script>

<style scoped lang="scss">
.ccb-home {
  .item-btn {
    display: flex;
  }

  .item-recharge {
    width: 166px;
    height: 62px;
    line-height: 62px;
    background-color: #FDCD3C;
    border-radius: 31px;
    font-size: 28px;
    font-family: PingFangSC-Medium;
    font-weight: 500;
    color: #222222;
    text-align: center;
    margin-right: 5px;
  }

  .codeLoginFormBtn {
    width: 150px;
    height: 50px;
    text-align: center;
    line-height: 50px;
    background: linear-gradient(90deg, #40d243, #1fc432);
    border-radius: 12px;
    font-size: 26px;
    font-weight: 500;
    color: #ffffff;
    font-family: PingFangSC;
  }

  .codeLoginFormBtns {
    width: 160px;
    height: 50px;
    text-align: center;
    line-height: 50px;
    border-radius: 12px;
    font-size: 26px;
    font-weight: 500;
    color: #ffffff;
    background: #dcdcdc;
    font-family: PingFangSC;
  }

  .popBox {
    position: relative;
    width: 620px;
    height: 190px;
    padding-top: 48px;
    box-sizing: border-box;

    .title {
      color: #7f7f87;
      font-size: 30px;
      padding-left: 42px;
    }

    ::v-deep .van-field__label {
      width: 100px;
      margin-right: 15px;
      font-size: 28px;
      font-weight: bold;
    }

    .contract_tips {
      font-size: 28px;
      padding: 25px;
      margin-top: -15px;
      color: #ff1929;
    }
  }

  .popBtn {
    position: fixed;
    left: 0;
    bottom: 0px;
    display: flex;
    width: 620px;
    height: 97px;
    line-height: 97px;
    font-size: 32px;
    justify-content: space-between;
    border-top: 1px solid #cfcece;
    text-align: center;

    .cancel {
      width: 50%;
      border-right: 1px solid #cfcece;
      color: #999999;
    }

    .confirm {
      width: 50%;
      color: #6095f0;
    }
  }

  .abc_box {
    width: 92%;
    margin: 0 auto;

    .ccb_title {
      height: 100px;
      line-height: 100px;
      font-size: 40px;
      font-family: PingFangSC-Medium;

      img {
        width: 50px;
        height: 50px;
        position: relative;
        top: 10px;
        margin-left: -10px;
      }
    }

    .ccb_privacy {
      margin-top: 20px;
    }

    .ccb_privacy_name {
      max-height: 500px;
      overflow-y: auto;
      font-size: 30px;
      margin-bottom: 10px;
      font-family: PingFangSC-Medium;
      padding-bottom: 50px;
    }

    .ccb_privacy_mag {
      font-size: 30px;
    }

    .ccb_radio {
      height: 60px;
      line-height: 60px;
      font-size: 28px;
      margin-top: 30px;
      display: flex;
    }

    .link {
      color: #3c51f0;
    }

    .ccb_btn {
      width: 92%;
      height: 230px;
      margin: 0 auto;

      background-color: #ffffff;
      position: absolute;
      bottom: 20px;
      .btns{
        display: flex;
        justify-content: space-between;
        margin-top: 20px;
      }
      .btn {
        width: 280px;
        border-radius: 10px;
        font-size: 30px;
      }
    }
  }
}</style>
