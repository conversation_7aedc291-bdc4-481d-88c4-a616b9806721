<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2021-11-15 09:48:05
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-10-24 17:30:36
-->
<template>
  <div class="nsh-home">
    <div v-if="item.accountType == 1" class="item-btn">
      <div v-if="nshType == 1&&item.payChannel==2" class="item-recharge" @click="clickNshOn(item)">开通</div>
      <div v-if="nshType == 2&&item.payChannel==2" class="item-recharge" @click="activationNsh(item)">激活</div>
      <div v-if="nshType != 1&&item.payChannel==2" class="item-recharge" @click="clickNshOn(item)">进入</div>
    </div>

    <!-- 验证码弹框 -->
    <van-popup
      v-model="codeLog"
      round
      :style="{
        height: '130px',
      }"
    >
      <div class="popBox">
        <van-field v-model="codeNum" center clearable :border="false" label="验证码" placeholder="请输入验证码">
          <template #button>
            <div>
              <div v-if="showCode" class="codeLoginFormBtn" @click="activationNsh">发送验证码</div>
              <div v-if="!showCode" class="codeLoginFormBtns">已发送{{ count }}s</div>
            </div>

          </template>
        </van-field>
        <div class="popBtn">
          <div class="cancel" @click="codeLog = false">取消</div>
          <div class="confirm" @click="confirmNsh">确认</div>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script>
import { capitalist } from '@/api/my'
import { rcbHomePage, findUnionRcbAccount, openAccountSmsSend } from '@/api/bank/nsh'
import { unionPayCreateAcc } from '@/api/bank'
export default {
  props: {
    item: {
      type: Object,
      default: function() {
        return {}
      }
    }
  },
  data() {
    return {
      codeLog: false,
      codeNum: '',
      count: '',
      showCode: true,
      smsId: '',
      nshType: 0,
      capitalAccountId: null,
      capitalPoolId: null
    }
  },
  mounted() {
    // this.getBlance()
    this.findUnionRcbAccount(this.item.capitalAccountId)
  },
  methods: {
    // 获取状态
    getBlance() {
      capitalist(this.$store.getters.getUserId).then((res) => {
        if (res.status == 200) {
          if (res.data.length != 0) {
            for (let index = 0; index < res.data.length; index++) {
              if (res.data[index].payChannel == 2) {
                this.findUnionRcbAccount(res.data[index].capitalAccountId)
              }
            }
          }
        }
      })
    },
    // 打开农商sdk
    clickNshOn() {
      let self = this

      const timernsh = window.setInterval(() => {
        self.$emit('getBlanceMethod')
        // self.getBlance()
        self.findUnionRcbAccount(self.item.capitalAccountId)
      }, 2000)

      this.$once('hook:beforeDestroy', () => {
        window.clearInterval(timernsh)
      })

      rcbHomePage(this.$store.getters.getUserId + '/1').then(res => {
        if (res.data == null) {
          this.$toast('开户异常，请重新开户')
          return
        }
        let data = {
          redirectUrl: res.data.redirectUrl
        }
        AlipayJSBridge.call('NSBBizvoke', data, function(result) {
          console.log('---------农商行----------')
          console.log(result)
          console.log('---------end----------')
        })
      })
    },
    // 查询农商
    findUnionRcbAccount(id) {
      let data = {
        'userId': this.$store.getters.getUserId,
        'capitalAccountId': id
      }
      findUnionRcbAccount(data).then(res => {
        if (res.status == 200) {
          if (res.data == null) {
            this.nshType = 1
            return
          }
          if (res.data.status == 1) {
            this.nshType = 2
          } else {
            this.nshType = 3
          }
        }
      })
    },
    // 激活农商银联
    activationNsh(item) {
      let self = this
      this.capitalAccountId = item.capitalAccountId
      this.capitalPoolId = item.capitalPoolId

      this.$toast.loading({
        message: '',
        duration: 1,
        forbidClick: true
      })
      this.codeNum = ''
      this.codeLog = true
      let queryVO = {
        'bank': 'RCB',
        // eslint-disable-next-line no-undef
        'clientIp': '127.0.0.1',
        'deviceId': localStorage.getItem('deviceId') ? localStorage.getItem('deviceId') : 'test',
        'latitude': this.$store.getters.getLocation.latitude,
        'longitude': this.$store.getters.getLocation.longitude,
        'userId': localStorage.getItem('userId'),
        'capitalAccountId': item.capitalAccountId
      }
      openAccountSmsSend(queryVO).then(res => {
        this.$toast.clear()
        if (res.data != null) {
          if (res.status == 200) {
            this.$toast('发送成功')
            this.smsId = res.data.smsId
            const TIME_COUNT = 60
            this.count = TIME_COUNT
            this.showCode = false
            this.timer = setInterval(() => {
              if (self.count > 0 && self.count <= TIME_COUNT) {
                self.count--
              } else {
                self.showCode = true
                clearInterval(self.timer)
                self.timer = null
              }
            }, 1000)
          }
        }
      })
    },
    // 确认激活
    confirmNsh() {
      this.$toast.loading({
        duration: 0,
        forbidClick: true,
        message: '请求中'
      })
      // unionPayCreateAcc
      let queryVO = {
        'bank': 'RCB',
        'capitalAccountId': this.capitalAccountId,
        // eslint-disable-next-line no-undef
        'clientIp': '127.0.0.1',
        'deviceId': localStorage.getItem('deviceId') ? localStorage.getItem('deviceId') : 'test',
        'latitude': this.$store.getters.getLocation.latitude,
        'longitude': this.$store.getters.getLocation.longitude,
        'poolId': this.capitalPoolId,
        'smsCode': this.codeNum,
        'smsId': this.smsId,
        'userId': localStorage.getItem('userId')
      }
      unionPayCreateAcc(queryVO).then(res => {
        this.$toast.clear()
        if (res.status == 200) {
          if (res.data.respCode == '00000') {
            this.$toast('操作成功')
            this.getBlance()
            this.codeLog = false
            this.$emit('getBlanceMethod')
          } else {
            this.$toast(res.data.respMsg)
          }
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
.nsh-home {
  .item-btn{
    display: flex;
  }
    .item-recharge {
        width: 166px;
        height: 62px;
        line-height: 62px;
        background-color: #FDCD3C;
        border-radius: 31px;
        font-size: 28px;
        font-family:PingFangSC-Medium;
        font-weight: 500;
        color: #222222;
        text-align: center;
        margin-right: 5px;
    }
    .codeLoginFormBtn{
        width: 160px;
        height: 50px;
        text-align: center;
        line-height: 50px;
        background: linear-gradient(90deg,#40d243, #1fc432);
        border-radius: 12px;
        font-size: 26px;
        font-weight: 500;
        color: #ffffff;
        font-family: PingFangSC;
    }
    .codeLoginFormBtns{
        width: 160px;
        height: 50px;
        text-align: center;
        line-height: 50px;
        border-radius: 12px;
        font-size: 26px;
        font-weight: 500;
        color: #ffffff;
        background: #dcdcdc;
        font-family: PingFangSC;
    }
    .popBox {
        position: relative;
        width: 561px;
        height: 32px;
        padding-top:48px;
        box-sizing: border-box;

        .title {
          color: #7f7f87;
          font-size: 30px;
          padding-left: 42px;
        }

        ::v-deep .van-field__label{
          width: 100px;
          margin-right: 0;
        }
      }
    .popBtn {
        position: fixed;
        left: 0;
        bottom: 0px;
        display: flex;
        width: 561px;
        height: 97px;
        line-height: 97px;
        font-size: 32px;
        justify-content: space-between;
        border-top: 1px solid #cfcece;
        text-align: center;
        .cancel {
          width: 50%;
          border-right: 1px solid #cfcece;
          color: #999999;
        }
        .confirm {
          width: 50%;
          color: #6095f0;
        }
      }
}
</style>
