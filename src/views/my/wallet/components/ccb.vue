<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2021-11-15 09:36:37
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-09-03 15:22:22
-->
<template>
  <div class="ccb-home">
    <div v-if="item.accountType == 1" class="item-btn">
      <!-- <div v-if="$store.getters.getUserId == 11949" class="item-recharge" @click="goRecharge(item)">充值</div> -->
      <div v-if="accountType == 1" class="item-recharge" @click="clickOn(item)">开通</div>
      <div v-if="accountType == 2" class="item-recharge" @click="activation(item)">激活</div>
      <div v-if="accountType == 3" class="item-recharge" @click="clickOn(item)">进入</div>
    </div>

    <!-- 验证码弹框 -->
    <van-popup v-model="codeLog" round :style="{ height: '130px', }">
      <div class="popBox">
        <van-field v-model="codeNum" center clearable :border="false" label="验证码" placeholder="请输入验证码">
          <template #button>
            <div>
              <div v-if="showCode" class="codeLoginFormBtn" @click="activation">发送验证码</div>
              <div v-if="!showCode" class="codeLoginFormBtns">已发送{{ count }}s</div>
            </div>
          </template>
        </van-field>
        <div class="popBtn">
          <div class="cancel" @click="codeLog = false">取消</div>
          <div class="confirm" @click="confirm">确认</div>
        </div>
      </div>
    </van-popup>
    <Loading :show="loadingShow" />
  </div>
</template>

<script>
import Loading from '@/components/Loading/index'
import { capitalist } from '@/api/my'
import { fundAccountQuery, ccbAccRecord, unionPayCreateAcc, ccbAccQuery } from '@/api/bank'
import { prod } from '@/utils/bank'
export default {
  components: {
    Loading
  },
  props: {
    item: {
      type: Object,
      default: function() {
        return {}
      }
    }
  },
  data() {
    return {
      codeLog: false,
      codeNum: '',
      count: '',
      showCode: true,
      accountType: 0,
      loadingShow: true
    }
  },
  created() {
    this.getBlance()
  },
  methods: {
    //   获取状态
    getBlance() {
      capitalist(this.$store.getters.getUserId).then((res) => {
        if (res.status == 200) {
          if (res.data.length != 0) {
            res.data.map((item) => {
              if (item.regionId == 1 && item.payChannel == 2) {
                // this.ccbAccQuery('CCB')
                this.ccbAccQuery()
              } else {
                this.loadingShow = false
              }
            })
          }
        }
      })
    },
    // 查询建行账户
    ccbAccQuery() {
      ccbAccQuery().then(res => {
        this.loadingShow = false
        if (res.status == 200) {
          // 返回建行信息后查询  银联
          if (res.data == null) {
            this.accountType = 1
          } else {
            this.fundAccountQuery('UnionPay')
          }
        }
      })
    },
    // ccbAccQuery(data) {
    //   let queryVO = {
    //     'bankType': data,
    //     'userId': this.$store.getters.getUserId
    //   }
    //   fundAccountQuery(queryVO).then(res => {
    //     this.loadingShow = false
    //     // this.$toast.clear()
    //     if (res.data != null) {
    //       if (res.data.status == 2) {
    //         this.fundAccountQuery('UnionPay')
    //       } else {
    //         this.accountType = 1
    //       }
    //     } else {
    //       this.accountType = 1
    //     }
    //   })
    // },
    // 查询开户类型
    fundAccountQuery(data) {
      // this.$toast.loading({
      //   message: '',
      //   duration: 1,
      //   forbidClick: true
      // })
      // CCB---建行,UnionPay---银联
      let queryVO = {
        'bankType': data,
        'userId': this.$store.getters.getUserId
      }
      fundAccountQuery(queryVO).then(res => {
        // this.$toast.clear()
        if (res.data != null) {
          if (res.data.status == 2) {
            this.accountType = 3
          } else {
            this.accountType = 2
          }
        } else {
          this.accountType = 2
        }
      })
    },
    // 开通-打开建行sdk
    clickOn() {
      let self = this

      const timerccb = window.setInterval(() => {
        self.$emit('getBlanceMethod')
        self.getBlance()
      }, 2000)

      this.$once('hook:beforeDestroy', () => {
        window.clearInterval(timerccb)
      })

      AlipayJSBridge.call('CCBBizInvoke', prod(), function(result) {
        console.log('---------建行开户----------')
        console.log(result)
        console.log('---------建行开户end----------')
      })
    },
    // 建行银联开户-激活
    activation() {
      this.bankType = 1
      let self = this
      this.$toast.loading({
        message: '',
        duration: 1,
        forbidClick: true
      })
      this.codeNum = ''
      this.codeLog = true
      let queryVO = {
        'bank': 'CCB',
        // eslint-disable-next-line no-undef
        'clientIp': '127.0.0.1',
        deviceId: localStorage.getItem('deviceId') ? localStorage.getItem('deviceId') : 'test',
        'latitude': this.$store.getters.getLocation.latitude,
        'longitude': this.$store.getters.getLocation.longitude,
        'poolId': this.$store.getters.getRegionId
      }
      ccbAccRecord(queryVO).then(res => {
        this.$toast.clear()
        if (res.data != null) {
          if (res.status == 200) {
            this.$toast('发送成功')
            this.smsId = res.data.smsId
            const TIME_COUNT = 60
            this.count = TIME_COUNT
            this.showCode = false
            this.timer = setInterval(() => {
              if (self.count > 0 && self.count <= TIME_COUNT) {
                self.count--
              } else {
                self.showCode = true
                clearInterval(self.timer)
                self.timer = null
              }
            }, 1000)
          }
        }
      })
    },
    // 输入短信验证码，银联开户
    confirm() {
      this.$toast.loading({
        duration: 0,
        forbidClick: true,
        message: '请求中'
      })
      let queryVO = {
        'bank': 'UnionPay',
        // eslint-disable-next-line no-undef
        'clientIp': '127.0.0.1',
        'deviceId': localStorage.getItem('deviceId') ? localStorage.getItem('deviceId') : 'test',
        'latitude': this.$store.getters.getLocation.latitude,
        'longitude': this.$store.getters.getLocation.longitude,
        'poolId': this.$store.getters.getRegionId,
        'smsCode': this.codeNum,
        'smsId': this.smsId
      }
      unionPayCreateAcc(queryVO).then(res => {
        this.$toast.clear()
        if (res.status == 200) {
          if (res.data.respCode == '00000') {
            this.$toast('操作成功')
            this.getBlance()
            this.codeLog = false
          } else {
            this.$toast(res.data.respMsg)
          }
        }
      })
    },
    // 充值（旧）
    goRecharge(item) {
      this.$router.push({
        name: 'Recharge',
        query: {
          regionId: item.regionId,
          capitalAccountId: item.capitalAccountId,
          capitalPoolId: item.capitalPoolId
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
.ccb-home {
  .item-btn{
    display: flex;
  }
    .item-recharge {
        width: 166px;
        height: 62px;
        line-height: 62px;
        background-color: #FDCD3C;
        border-radius: 31px;
        font-size: 28px;
        font-family:PingFangSC-Medium;
        font-weight: 500;
        color: #222222;
        text-align: center;
        margin-right: 5px;
    }
    .codeLoginFormBtn{
        width: 160px;
        height: 50px;
        text-align: center;
        line-height: 50px;
        background: linear-gradient(90deg,#40d243, #1fc432);
        border-radius: 12px;
        font-size: 26px;
        font-weight: 500;
        color: #ffffff;
        font-family: PingFangSC;
    }
    .codeLoginFormBtns{
        width: 160px;
        height: 50px;
        text-align: center;
        line-height: 50px;
        border-radius: 12px;
        font-size: 26px;
        font-weight: 500;
        color: #ffffff;
        background: #dcdcdc;
        font-family: PingFangSC;
    }
    .popBox {
        position: relative;
        width: 561px;
        height: 32px;
        padding-top:48px;
        box-sizing: border-box;

        .title {
          color: #7f7f87;
          font-size: 30px;
          padding-left: 42px;
        }

        ::v-deep .van-field__label{
          width: 100px;
          margin-right: 0;
        }
      }
    .popBtn {
        position: fixed;
        left: 0;
        bottom: 0px;
        display: flex;
        width: 561px;
        height: 97px;
        line-height: 97px;
        font-size: 32px;
        justify-content: space-between;
        border-top: 1px solid #cfcece;
        text-align: center;
        .cancel {
          width: 50%;
          border-right: 1px solid #cfcece;
          color: #999999;
        }
        .confirm {
          width: 50%;
          color: #6095f0;
        }
      }
}
</style>
