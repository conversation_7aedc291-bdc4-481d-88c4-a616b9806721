<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-06-03 15:39:48
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-01-28 17:07:02
-->
<template>
  <div class="home">
    <NavHeight bgc="#fff" />
    <div class="top">
      <van-nav-bar title="" left-text="" left-arrow>
        <template #left>
          <van-icon
            name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/user/icon/arrow-black.png"
            size="18"
            @click="goBack"
          />
        </template>
      </van-nav-bar>
    </div>
    <div class="occupHeight" />
    <div class="wallet">
      <ul v-if="false" class="wallet-title">
        <li>我的钱包</li>
        <li v-if="regionList.length>0" @click="goBill">资金详情</li>
      </ul>
      <div class="wallet-amount">
        <div class="wallet-amount-1">总资产(元)</div>
        <div class="wallet-amount-2">￥{{ balance }}</div>
      </div>
      <ul class="wallet-region">
        <li v-for="(item, index) in regionList" :key="index" class="region-item">
          <div class="region-item-top">
            <div class="item-name">
              <div>{{ item.regionName }}</div>
            </div>

            <CCB v-if="item.regionId == 1" ref="onCcb" :item="item" @getBlanceMethod="getBlance" />
            <NSH v-if="item.regionId == 3" ref="onNsh" :item="item" @getBlanceMethod="getBlance" />
            <ABC v-if="item.regionId == 6" ref="onAbc" :item="item" @getBlanceMethod="getBlance" />
            <ABC v-if="item.regionId == 7" ref="onAbc" :item="item" @getBlanceMethod="getBlance" />

          </div>
          <div class="region-item-amount">
            <div class="item-amount-1">￥{{ item.balance }}</div>
            <!-- <div class="item-amount-2">专项补贴（元）</div> -->
            <div class="item-amount-2" @click="goBill(item)">资金详情</div>
          </div>
        </li>
      </ul>
    </div>
    <Loading :show="loadingShow" />
  </div>
</template>

<script>
import { capitalist } from '@/api/my'
import BigNumber from 'bignumber.js'
import Loading from '@/components/Loading/index'
import CCB from './components/ccb.vue'
import NSH from './components/nsh.vue'
import ABC from './components/abc.vue'
export default {
  components: {
    CCB,
    NSH,
    ABC,
    Loading
  },
  data() {
    return {
      regionList: [],
      balance: 0,
      loadingShow: true,
      nemsg: ''
    }
  },
  created() {

  },
  mounted() {
    this.getBlance()
    AlipayJSBridge.call('GetNsh', this.form, function(result) {
      console.log(result)
    })
  },
  methods: {
    //   获取余额
    getBlance() {
      let data = this.$store.getters.getUserId
      capitalist(data).then((res) => {
        this.loadingShow = false
        if (res.status == 200) {
          if (res.data.length != 0) {
            this.regionList = res.data
            let sum = 0
            res.data.map((item) => {
              sum = BigNumber(sum).plus(Number(item.balance))
            })
            this.balance = sum
            this.nemsg = res.data[0]
          }
        } else {
          this.$toast(res.message)
        }
      })
    },
    // 去资金详情（旧）
    goBill(item) {
      this.$router.push(
        {
          name: 'Bill',
          query: {
            id: item.capitalAccountId,
            capitalAccount: item.capitalPoolId
          }})
    },
    goBack() {
      this.$router.push('my')
    }
  }
}
</script>

<style scoped lang="scss">
    .home {
      width: 100%;
      height: 100%;
      // background-color: #fff;

      .popBox {
        position: relative;
        width: 561px;
        height: 32px;
        padding-top:48px;
        box-sizing: border-box;

        .title {
          color: #7f7f87;
          font-size: 30px;
          padding-left: 42px;
        }

        ::v-deep .van-field__label{
          width: 100px;
          margin-right: 0;
        }
      }
      .popBtn {
        position: fixed;
        left: 0;
        bottom: 0px;
        display: flex;
        width: 561px;
        height: 97px;
        line-height: 97px;
        font-size: 32px;
        justify-content: space-between;
        border-top: 1px solid #cfcece;
        text-align: center;
        .cancel {
          width: 50%;
          border-right: 1px solid #cfcece;
          color: #999999;
        }
        .confirm {
          width: 50%;
          color: #6095f0;
        }
      }
        .top {
            position: fixed;
            left: 0;
            // top: 0;
            width: 100%;
            background-color: #fff;
        }
        .occupHeight {
            height: 92px;
        }
        .wallet {
            .wallet-title {
                display: flex;
                align-items: center;
                justify-content: space-between;
                height: 120px;
                padding: 0 20px;
                background-color: #fff;
                li:first-child {
                    font-size: 40px;
                    font-family:PingFangSC-Medium;
                }
                li:last-child {
                    font-size: 32px;
                    font-family:PingFangSC;
                    color: #222;
                }
            }
            .wallet-amount {
                width: 710px;
                height: 210px;
                background-image: url(https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/my/wallet-amount.png);
                background-size: 100%;
                background-repeat: no-repeat;
                margin: 0 auto 20px;
                color: #fff;
                padding-left: 30px;
                margin-top: 20px;
                .wallet-amount-1 {
                    font-size: 28px;
                    font-family:PingFangSC;
                    padding-top: 44px;
                    margin-bottom: 18px;
                }
                .wallet-amount-2 {
                    font-size: 58px;
                    font-family:PingFangSC-Medium;
                }
            }
            .wallet-region {
                padding-bottom: 20px;
                .region-item {
                    width: 710px;
                    height: 238px;
                    background-color: #fff;
                    border-radius: 16px;
                    margin: 20px auto 0;
                    .region-item-top {
                        display: flex;
                        align-items: center;
                        justify-content: space-between;
                        height: 120px;
                        border-bottom: 2px solid #f4f4f4;
                        margin: 0 20px;
                        .item-name {
                            font-family: PingFangSC-Medium;
                            font-weight: 500;
                            color: #333333;
                            font-size: 38px;
                        }
                    }
                    .region-item-amount {
                        display: flex;
                        justify-content: space-between;
                        margin: 0 20px;
                        .item-amount-1 {
                            font-family:PingFangSC-Medium;
                            font-weight: 700;
                            color: #222222;
                            margin-top: 30px;
                            font-size: 44px;
                        }
                        .item-amount-2 {
                          line-height: 130px;
                            font-family:PingFangSC;
                            font-weight: 400;
                            color: #716f6f;
                            margin-bottom: 6px;
                            font-size: 28px;
                            text-decoration: underline;
                        }
                    }
                }
            }
        }
    }
</style>
