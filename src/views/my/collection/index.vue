<template>
  <div class="content">
    <NavHeight bgc="#fff" />
    <div class="top">
      <div class="fixed">
        <van-nav-bar title="我的收藏" left-text="" left-arrow>
          <template #left>
            <van-icon
              name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/user/icon/arrow-black.png"
              size="18"
              @click="goBack"
            />
          </template>
        </van-nav-bar>
      </div>
      <div style="height:46px" />
    </div>

    <div class="nearbyShopList">
      <div v-for="(item, index) in list" :key="index">
        <GoodsCard type="1" :list="item.market" />
        <div class="cancelCollect">
          <van-button class="button" type="default" size="mini" @click="cancelCollect(item.id)">取消收藏</van-button>
        </div>
      </div>
    </div>

    <van-empty v-if="list.length === 0" description="暂无收藏" />
  </div>
</template>

<script>
import GoodsCard from '@/components/GoodsCard'
import { marketCollectList, marketUnCollect } from '@/api/shop'
export default {
  components: {
    GoodsCard
  },
  data() {
    return {
      list: []
    }
  },
  created() {
    this.getShopList()
  },
  methods: {
    // 获取优选店铺
    getShopList() {
      let data = {
        pageNum: 1,
        pageSize: 100,
        latitude: this.$store.getters.getLocation.latitude ? this.$store.getters.getLocation.latitude : 28.592388,
        longitude: this.$store.getters.getLocation.longitude ? this.$store.getters.getLocation.longitude : 119.275865,
        regionId: this.$store.getters.getRegionId,
        type: 1
      }
      marketCollectList(data).then((res) => {
        if (res.status == 200) {
          this.list = res.data.list
        }
      })
    },
    // 取消收藏
    cancelCollect(id) {
      marketUnCollect({
        id
      }).then((res) => {
        if (res.status == 200) {
          this.$toast('取消收藏')
          this.getShopList()
        }
      })
    },

    // 返回上一页
    goBack() {
      this.$router.go(-1)
    }
  }
}
</script>

<style scoped lang="scss">
.content::before {
    // 利用伪元素设置整个页面的背景色
    content: " ";
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    z-index: -100;
    min-height: 100%;
    background-color: #f5f5f5;
}

.content {
    .cancelCollect{
      width: 95%;
      height: 80px;
      display: flex;
      justify-content: flex-end;
      align-items: center;
      margin: 0 auto;
      margin-top: -30px;
      background-color: #fff;
      margin-bottom: 20px;
      border-radius: 10px;
      .button{
        margin-right: 30px;
      }

    }

    .top {
        .fixed {
            position: fixed;
            left: 0;
            // top: 0;
            z-index: 2;
            width: 100%;
            background-color: #fff;
        }

        ::v-deep .van-nav-bar__title {
            font-size: 36px;
            color: #222;
        }
    }

    ::v-deep .van-nav-bar__title {
        font-size: 34px;
        color: #000010;
    }

    ::v-deep .van-nav-bar {
        background: none;
    }

    ::v-deep .van-nav-bar__right {
        font-size: 30px;
    }

    .banner {
        width: 93%;
        margin: 0 auto;
        margin-top: 18px;

        .image {
            width: 100%;
            height: 280px;
        }
    }

    .nearbyShopList {
        margin-top: 24px;
    }
}
</style>
