<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-06-03 14:15:29
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-11-07 09:53:53
-->
<template>
  <!-- 意见反馈 -->
  <div class="content">
    <NavHeight bgc="linear-gradient(45deg, #71d774 0%, #5ecc52 100%)" />
    <div class="top">
      <van-nav-bar title="" left-text="" left-arrow>
        <template #title>
          <div>个人信息收集清单</div>
        </template>
        <template #left>
          <van-icon
            name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/common/arrowlbft.png"
            size="18"
            @click="goback"
          />
        </template>
      </van-nav-bar>
    </div>
    <div>
      <iframe
        id="iframe"
        ref="vueIframe"
        src="https://open-web.diandiandidi.top/detailedList/detailedList.html"
        frameborder="0"
        scrolling="auto"
      />
    </div>

  </div>
</template>

<script>
import {
  version,
  rel
} from '@/config/settings'
export default {
  components: {},
  data() {
    return {
      version: version,
      rel: rel
    }
  },
  mounted() {},
  methods: {
    goback() {
      this.$router.go(-1)
    }
  }
}
</script>

    <style lang="scss" scoped>
    .list{
      width: 100%;
      height: 100px;
      line-height: 100px;
      font-size: 30px;
      text-align: center;
      border-bottom: 1px solid #eee;
    }
    .content::before {
      // 利用伪元素设置整个页面的背景色
      content: " ";
      position: absolute;
      left: 0;
      right: 0;
      top: 0;
      bottom: 0;
      z-index: -100;
      min-height: 100%;
      background-color: #fff;
    }
    .content {
      width: 100%;
      box-sizing: border-box;
      .top {
        color: #fff;
        background: linear-gradient(45deg, #71d774 0%, #5ecc52 100%);
        ::v-deep .van-nav-bar {
          background: none;
        }
        ::v-deep .van-nav-bar__title {
          color: #fff;
        }
      }
      #iframe{
          width: 100%;
          height: 85vh
      }
    }
    </style>

