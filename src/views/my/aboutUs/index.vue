<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-06-03 14:15:29
 * @LastEditors: 孙立政
 * @LastEditTime: 2023-05-23 13:59:32
-->
<template>
  <!-- 意见反馈 -->
  <div class="content">
    <NavHeight bgc="linear-gradient(45deg, #71d774 0%, #5ecc52 100%)" />
    <div class="top">
      <van-nav-bar title="" left-text="" left-arrow>
        <template #title>
          <div>关于我们</div>
        </template>
        <template #left>
          <van-icon
            name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/common/arrowlbft.png"
            size="18"
            @click="goback"
          />
        </template>
      </van-nav-bar>
    </div>
    <div class="center">
      <div class="logo" @click="test">
        <img
          src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/my/logo.png"
          alt=""
        >

      </div>
      <div @click="test2">点滴畅享生活</div>
      <div>Version {{ version }}</div>
    </div>
    <div class="bottom">
      <div class="blue" @click="goUseragt">《用户协议》</div>
      <div class="blue" @click="goPrivacy">《隐私协议》</div>
      <div>
        杭州奈坦网络科技有限公司 版权所有
        <br>
        Copyright 2020-2021 Natan. All Rights Reserved.H5-{{ rel }}
      </div>

    </div>

  </div>
</template>

<script>
import {
  version,
  rel
} from '@/config/settings'
export default {
  components: {},
  data() {
    return {
      version: version,
      rel: rel
    }
  },
  mounted() {},
  methods: {
    test() {
      AlipayJSBridge.call('startNsh', {
        openurl: 'BKnLsuVG2ondqWefTvrRlMf+wW4ywuhkk6IvV9HLOJ9Mo8OlBozkWLZllgsEVv49sfCUXxo/zB6r9K9xQrb6nFvna3JqB6sD282OlnFF2kfsslYXAdwKCXcWJafKX0lBp2nMgwUAQz6yrPvr00aRMwFLPd/yp/Cf8b6i1gNqNzIA8SWyPur0q588Y1TzkGySOCnxqI40l8rQQsCDmtJcCQ=='
      }, function(result) {})
    },
    test2() {
      AlipayJSBridge.call('OpenAppByRouter', {
        urlStr: 'weixin://dl/business/?appid=wxf72e75902d97af48&path=pages/index/index&query=userNo%3D2020102122042575732%26phone%3D18757156043'
      }, function(result) {})
    },
    goback() {
      this.$router.go(-1)
    },
    goPrivacy() {
      this.$router.push('/privacy')
    },
    goUseragt() {
      this.$router.push('/useragt')
    }
  }
}
</script>

  <style lang="scss" scoped>
  .list{
    width: 100%;
    height: 100px;
    line-height: 100px;
    font-size: 30px;
    text-align: center;
    border-bottom: 1px solid #eee;
  }
  .content::before {
    // 利用伪元素设置整个页面的背景色
    content: " ";
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    z-index: -100;
    min-height: 100%;
    background-color: #fff;
  }
  .content {
    width: 100%;
    box-sizing: border-box;
    .top {
      color: #fff;
      background: linear-gradient(45deg, #71d774 0%, #5ecc52 100%);
      ::v-deep .van-nav-bar {
        background: none;
      }
      ::v-deep .van-nav-bar__title {
        color: #fff;
      }
    }
    .center {
      text-align: center;
      .logo {
        width: 186px;
        height: 186px;
        margin: 0 auto;
        margin-top: 20%;
        img{
          width: 100%;
          height: 100%;
        }
      }
      > div:nth-of-type(2) {
        font-size: 48px;
        font-family: PingFangSC;
        color: #333340;
        margin-top: 10%;
        margin-bottom: 6px;
      }
      > div:nth-of-type(3) {
        color: #333340;
        font-size: 32px;
      }
    }
    .bottom {
      width: 100%;
      position: relative;
      left: 0;
      bottom: -400px;
      font-size: 24px;
      text-align: center;
      .blue {
        margin-bottom: 12px;
        color: #5b6a91;
      }
      > div:nth-of-type(3) {
        margin-top: 14px;
        color: #b2b2b2;
      }
    }
  }
  ::v-deep .van-hairline--bottom {
    border-bottom-width: 0;
  }
  ::v-deep .van-hairline--bottom::after {
    border-bottom-width: 0;
  }
  </style>

