<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2021-06-01 15:12:51
 * @LastEditors: 孙立政
 * @LastEditTime: 2021-12-03 14:36:00
-->
<template>
  <div class="orderBar">
    <div class="title">我的订单</div>
    <div class="orderBarStatus">

      <div class="item" @click="goOrder(0)">
        <div>
          <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/my/tabicon/myicon1.png" alt="">
        </div>
        <div>待支付</div>
      </div>
      <div class="item" @click="goOrder(0)">
        <div>
          <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/my/tabicon/myicon2.png" alt="">
        </div>
        <div>进行中</div>
      </div>
      <div class="item" @click="goOrder(1)">
        <div>
          <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/my/tabicon/myicon3.png" alt="">
        </div>
        <div>待评价</div>
      </div>
      <div class="item" @click="goOrder(2)">
        <div>
          <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/my/tabicon/myicon4.png" class="oem" alt="">
        </div>
        <div>退款/售后</div>
      </div>
      <div class="line">
        <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/my/oright.png" alt="">
      </div>
      <div class="item" @click="goOrder(0)">
        <div>
          <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/my/tabicon/myicon5.png" alt="">
        </div>
        <div>全部订单</div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  methods: {
    goOrder(type) {
      this.$store.state.tabbar.index = 2
      this.$store.state.order.tabActive = type
      this.$store.state.order.orderList = []
      this.$router.push({
        name: 'Order',
        query: {
          activeIdx: type
        }
      })
    }
  }

}
</script>

<style lang="scss" scoped>

.orderBar{
  width: 710px;
  height: 240px;
  margin: 0 auto;
  margin-top: 25px;
  border-radius: 16px;
  background-color: #fff;
  box-sizing: border-box;
  overflow: hidden;
  font-family: PingFangSC;
  box-shadow: 0px 2px 26px 11px rgba(0,0,0,0.04);
}
.title{
  width: 100%;
  height: 100px;
  line-height: 100px;
  font-size: 38px;
  color: #222;
  font-family: PingFangSC-Medium;
  padding-left: 30px;
}
.orderBarStatus {
    display: flex;
    justify-content: space-between;
    .line {
        width: 16px;
        height: 92px;
        img{
            width: 16px;
            height: 92px;
        }
    }

    .item {
        display: flex;
        flex-direction: column;
        align-items: center;
        width: 20%;
        color: #222;
        img{
            width: 50px;
            height: 50px;
            margin-top: 13px;
        }
        .oem{
            width: 54px;
            height: 50px;
            margin-top: 13px;
        }
        >div {
            font-size: 24px;
            margin-top: 8px;
        }
    }
}
</style>
