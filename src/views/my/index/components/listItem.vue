<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2021-06-01 15:32:50
 * @LastEditors: 孙立政
 * @LastEditTime: 2023-06-06 10:05:50
-->
<template>
  <div class="listItem">
    <div class="title">我的功能</div>
    <ul class="listItemUl">
      <li v-for="(item,index) in list" v-show="isShowFace(item)" :key="index" @click="goDetail(index)">
        <div class="listItemLeft">
          <div>
            <img :src="item.icon" alt="">
          </div>
          <div class="listItemLeftName">
            {{ item.name }}
            <span v-if="item.name=='优惠券'&&couponCount>0" style="margin-left: -6px;">
              <span style="color: #FF301E;font-size: 14px;">{{ couponCount }}</span>张
            </span>
          </div>
        </div>
      </li>
      <li v-if="false">
        <router-link to="/external/searchNumber">
          <div class="listItemLeft">
            <div>
              <van-icon name="phone" size="24" />
            </div>
            <div class="listItemLeftName">
              拨号台
            </div>
          </div>
        </router-link>

      </li>
    </ul>

    <!-- 电话弹框 -->
    <van-popup
      v-model="telShow"
      round
      :style="{
        width: '57%',
        height: '',
      }"
    >
      <div class="popTelBox">
        <div class="" @click="CallPhone('18757156043')">技术支持</div>
        <div class="" @click="CallPhone($store.getters.getRegionId == 1?'0578-8176992‬':'0578-7339309')">商户入驻</div>
        <div class="" @click="CallPhone($store.getters.getRegionId == 1?'0578-8176992‬':'0578-7339309')">投诉维权</div>
      </div>
    </van-popup>
  </div>
</template>

<script>
import { myCouponCount } from '@/api/coupon'
import {
  getUserFace
} from '@/api/my/face'
export default {
  props: {
    info: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      list: [
        { icon: 'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/myV2/pinglun.png', name: '我的评论', path: 'MyEvalute' },
        // { icon: 'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/myV2/time.png', name: '我的发布', path: 'ExternalOrder' },
        { icon: 'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/myV2/foodhous.png', name: '我的食堂', path: 'Canteen' },
        { icon: 'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/myV2/coupon.png', name: '优惠券', path: 'Coupon' },
        { icon: 'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/shop/favorite-b.png', name: '我的收藏', path: 'Collection' },
        { icon: 'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/myV2/face2.png', name: '人脸录入', path: 'Face' },
        { icon: 'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/my/list/kefu.png', name: '联系客服', path: 'Help' }
      ],
      telShow: false,
      couponCount: 0
    }
  },
  created() {
    this.myCouponCount()
  },
  methods: {
    myCouponCount() {
      myCouponCount({
        regionId: this.$store.getters.getRegionId
      }).then(res => {
        if (res.status == 200) {
          this.couponCount = res.data
        }
      })
    },
    isShowFace(item) {
      if (item.name == '人脸录入') {
        if (this.$store.state.accountType == 1) {
          return true
        } else {
          return false
        }
      } else {
        return true
      }
    },
    // 跳转对应页面
    goDetail(index) {
      console.log(index)
      if (index == 1) {
        if (this.info.canteenFlag == true) {
          this.$dialog.alert({
            title: '提示',
            message: '即将跳转农行掌银充值，请确认已安装农行掌银APP',
            confirmButtonText: '确认',
            confirmButtonColor: '#6095F0',
            closeOnClickOverlay: true
          }).then(() => {
            var u = navigator.userAgent
            var isiOS = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/) // ios终端
            const urls = 'bankabc://%7B%22method%22%3A%22jumpToSharedProduct%22%2C%22param%22%3A%221fc60e4e85ce769fdba0baa2c540ac4dd55bb5033744e8aada10fc9a91d4192642e16425ef14be4029d528991f21f7400d32995fbd16c1d993ad294e0000ac1e39a0e79b8d3bf9dec3f1f25988ff32134b37c3f144e906cec4f426caa974498a856f35eddd326819dfb903011675b23c%22%2C%22type%22%3A%221%22%7D'
            if (!isiOS) {
              window.location.href = urls
            } else {
              AlipayJSBridge.call('OpenAppByRouter', {
                urlStr: urls
              }, function(result) {})
            }
          })
        } else {
          this.$router.push('/canteen')
        }
      } else if (index == 4) {
        if (this.$store.getters.getUserId == null) {
          this.$router.push('/wxLogin2')
          return
        }
        getUserFace().then((res) => {
          if (res.status == 200) {
            if (res.data == null) {
              this.$router.push('/my/face')
            } else {
              this.$router.push('/my/faceOk')
            }
          } else {
            this.$toast(res.message)
          }
        })
      } else if (index == 5) {
        let lq = 'https://work.weixin.qq.com/kfid/kfc8b655be76167a6af'
        let sc = 'https://work.weixin.qq.com/kfid/kfc84bcbe69dd0c27dc'
        let jn = 'https://work.weixin.qq.com/kfid/kfc887432f04fec9dad'
        let qy = 'https://work.weixin.qq.com/kfid/kfcbb2e529cb8f0c456'
        let url = ''
        if (this.$store.getters.getRegionId == 1) {
          url = sc
        } else if (this.$store.getters.getRegionId == 3) {
          url = lq
        } else if (this.$store.getters.getRegionId == 6) {
          url = jn
        } else if (this.$store.getters.getRegionId == 7) {
          url = qy
        }
        var u = navigator.userAgent
        var isiOS = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/)
        if (isiOS) {
          window.open(url)
          // window.location.href = 'https://work.weixin.qq.com/kfid/kfc84bcbe69dd0c27dc'
        } else {
          AlipayJSBridge.call('OpenUrlForAPP', { openurl: url }, function(result) {})
        }
      } else {
        if (this.$store.getters.getUserId || index == 11) {
          let path = this.list[index].path
          this.$router.push({
            name: path,
            query: {
              type: 0
            }
          })
        } else {
          this.$router.push({
            path: '/wxLogin2'
          })
        }
      }
    },
    // 打电话
    CallPhone(data) {
      AlipayJSBridge.call('CallPhone', {
        phoneNum: data
      }, function(result) {})
    }
  }
}
</script>

<style lang="scss" scoped>
.listItem{
    width: 706px;
    height: 446px;
    margin: 0 auto;
    margin-top: 21px;
    background: #ffffff;
    border-radius: 16px;
    box-shadow: 0px 2px 26px 11px rgba(0,0,0,0.04);
    overflow: hidden;
    .title{
      width: 100%;
      height: 100px;
      line-height: 100px;
      font-size: 38px;
      color: #222;
      font-family: PingFangSC-Medium;
      padding-left: 30px;
    }
    .listItemUl {
        width: 662px;
        height: auto;
        margin: 0 auto;
        margin-top: 10px;
        display: flex;
        justify-content: space-between;
            display: -webkit-flex;
            flex-direction: row;
            flex-wrap: wrap;

        li {
            width: 33%;
            text-align: center;
            margin-bottom: 73px;
        }
        .listItemLeft{
            img {
                width: 48px;
                height: 48px;
                margin-left: 13px;
                position: relative;
                top: 8px;
            }
            font-size: 28px;
            font-family: PingFangSC;
            font-weight: 400;
            color: #222222;
            .listItemLeftName{
                margin-left: 15px;
                margin-top: 13px;
                color: #666666;
            }
        }
    }
}
.popLoginBox {
        position: relative;
        width: 561px;
        height: 282px;
        padding-top: 70px;
        box-sizing: border-box;
        .title {
          color: #333340;
          font-size: 36px;
          text-align: center;
        }
		}
		.popBtn {
        position: absolute;
        left: 0;
        bottom: 0;
        display: flex;
        width: 561px;
        height: 97px;
        line-height: 97px;
        font-size: 32px;
        justify-content: space-between;
        border-top: 1px solid #cfcece;
        text-align: center;
        .cancel {
          width: 50%;
          border-right: 1px solid #cfcece;
          color: #999999;
        }
        .confirm {
          width: 50%;
          color: #6095f0;
        }
		}
    .popTelBox {
			overflow: hidden;
			>div {
				height: 102px;
				line-height: 102px;
				text-align: center;
				font-size: 32px;
				color: #000010;
				opacity: 0.7;
			}

			>div:not(:nth-of-type(3)) {
				border-bottom: 1px solid #e5e5e5;
			}
		}

		::v-deep .van-popup--center.van-popup--round {
			border-radius: 16px;
		}
</style>
