<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2021-06-01 14:50:54
 * @LastEditors: 孙立政
 * @LastEditTime: 2021-12-03 14:14:16
-->
<template>
  <div class="base">
    <div class="baseNav">
      <div />
      <div>
        <img class="baseNavCodeIcon" src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/my/edit/edit.png" alt="" @click="goEdit">
        <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/my/edit/msg.png" alt="" @click="goNotice">
      </div>
    </div>

    <div class="baseInfo">
      <div @click="goPerson">
        <img v-if="$store.state.My.headImg" :src="$store.state.My.headImg" alt="">
        <img v-else src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/my/anio.png" alt="">
      </div>
      <div class="baseInfoName">
        <span v-if="status" @click="goPerson">{{ $store.state.My.nickName==null?'点滴用户':$store.state.My.nickName }}</span>
        <span v-else @click="goLogin">登录/注册</span>
      </div>
    </div>

    <div class="baseBlance">
      <div class="baseBlanceBox">
        <div>
          <span class="baseBlanceMy">我的钱包</span>
          <span class="baseBlanceNum">￥{{ $store.state.My.blance }}</span>
        </div>
        <div v-if="$store.getters.getRegionId == 3" class="baseBlancedetile" style="display: flex;">
          <div v-if="payBtnShow" @click="getNSHBlance">充值</div>
          <div style="margin-left: 10px;color: #f2f2f2" @click="goDetail">详情</div>
        </div>
        <div v-else class="baseBlancedetile" @click="goDetail">
          详情
        </div>
        <!-- <div class="baseBlancedetile" @click="goDetail">
          详情
        </div> -->
      </div>
    </div>
  </div>
</template>

<script>
import BigNumber from 'bignumber.js'
import { UserInfo, capitalist } from '@/api/my'
import { findUnionRcbAccount, rcbHomePage } from '@/api/bank/nsh'
export default {
  data() {
    return {
      userData: '',
      blance: '',
      status: '',
      msgList: [],
      payBtnShow: false,
      walletList: [],
      nshType: 1
    }
  },
  created() {
    this.status = localStorage.getItem('token')
    if (this.$store.getters.getUserId) {
      this.getUserInfo()
    }
  },
  mounted() {
    AlipayJSBridge.call('GetNsh', this.form, function(result) {
      console.log(result)
    })
  },
  methods: {
    // 判断是否显示充值按钮
    async isPayBtnShow() {
      if (this.$store.getters.getRegionId == 3 && this.walletList.length >= 1) {
        for (let i = 0; i < this.walletList.length; i++) {
          if (this.walletList[i].regionId == 3) {
            let rcbAccount = await this.findUnionRcbAccount(this.walletList[i].capitalAccountId)
            if (rcbAccount != null && rcbAccount.status == 2) {
              this.payBtnShow = true
            } else {
              this.payBtnShow = false
            }
            return
          }
        }
      } else {
        this.payBtnShow = false
      }
    },
    // 获取账户信息
    getUserInfo() {
      let self = this
      let data = this.$store.getters.getUserId
      UserInfo(data).then((res) => {
        if (res.status == 200) {
          this.userData = res.data
          if (self.$store.state.My.nickName != res.data.nickName) {
            self.$store.state.My.nickName = res.data.nickName
          }
          if (self.$store.state.My.telephone != res.data.telephone) {
            self.$store.state.My.telephone = res.data.telephone
          }
          if (self.$store.state.My.headImg != res.data.headImg) {
            self.$store.state.My.headImg = res.data.headImg
          }
          this.$emit('refInfo', res.data)
          // 获取用户余额
          this.getBalance()
        } else if (res.status == 401) {
          self.$store.state.My.headImg = ''
          self.status = ''
        }
      })
    },
    // 获取余额
    getBalance() {
      let self = this
      let data = this.$store.getters.getUserId
      let balance = 0
      capitalist(data).then((res) => {
        if (res.status == 200) {
          if (res.data.length == 0) {
            self.blance = 0
          } else {
            res.data.map((item) => {
              balance = BigNumber(balance).plus(Number(item.balance))
            })
            self.blance = balance

            self.$store.state.accountType = res.data[0].accountType

            if (self.$store.state.My.blance != balance) {
              self.$store.state.My.blance = balance
            }
            self.walletList = res.data
            self.isPayBtnShow()
          }
        }
      })
    },
    // 钱包
    goDetail() {
      if (this.$store.getters.getUserId) {
        this.$router.push({
          name: 'Wallet'
        })
      } else {
        this.$router.push({
          path: '/wxLogin2'
        })
      }
    },
    // 去个人中心
    goPerson() {
      if (this.$store.getters.getUserId) {
        this.$router.push({
          name: 'PersonInfo'
        })
      } else {
        this.$router.push({
          path: '/wxLogin2'
        })
      }
    },
    // 付款码
    goCode() {
      if (this.$store.getters.getUserId == null) {
        this.$router.push({ name: 'wxLogin2' })
      } else {
        this.$router.push('/scan/qrCode')
      }
    },
    // 设置
    goEdit() {
      this.$router.push({
        name: 'Edit'
      })
    },
    // 公告
    goNotice() {
      this.$router.push({
        name: 'Notice'
      })
    },
    // 登录
    goLogin() {
      this.$router.push({
        name: 'wxLogin2'
      })
    },
    // 查询农商开户情况
    async findUnionRcbAccount(id) {
      let query = {
        'userId': this.$store.getters.getUserId,
        'capitalAccountId': id
      }
      const { data } = await findUnionRcbAccount(query)
      return data
    },
    getNSHBlance() {
      rcbHomePage(this.$store.getters.getUserId + '/1').then(res => {
        if (res.data == null) {
          this.$toast('开户异常，请重新开户')
          return
        }
        let data = {
          redirectUrl: res.data.redirectUrl
        }
        AlipayJSBridge.call('NSBBizvoke', data, function(result) {
          console.log('---------农商行----------')
          console.log(result)
          console.log('---------end----------')
        })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.base{
    font-family: PingFangSC;
    width: 100%;
    height: 351px;
    background-image: url(https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/my/bj.png);
    background-size: 100% 100%;
    .baseNav{
        width: 710px;
        height: 50px;
        margin: 0 auto;
        display: flex;
        justify-content: space-between;
        img{
            width: 44px;
            height: 44px;
        }
        .baseNavCodeIcon{
            margin-right: 30px;
        }
    }
    .baseInfo{
        width: 710px;
        height: 100px;
        margin: 0 auto;
        display: flex;
        font-size: 32px;
        font-weight: 500;
        color: #ffffff;
        margin-top: 36px;
        img {
            width: 100px;
            height: 100px;
            border-radius: 50%;
        }
        .baseInfoName{
            margin-left: 20px;
            line-height: 100px;
            font-family: PingFangSC-Medium
        }
    }
    .baseBlance{
        width: 710px;
        height: 124px;
        margin: 0 auto;
        background-image: url(https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/my/blanc.png);
        background-size: 100% 100%;
        margin-top: 46px;
        line-height: 100px;
        color: #fbe8d3;
        .baseBlanceBox{
            width: 90%;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .baseBlanceMy{
            font-size: 32px;
        }
        .baseBlanceNum{
            font-size: 40px;
            margin-left: 20px;
            position: relative;
            top: 5px;
        }
        .baseBlancedetile{
            font-size: 32px;
            margin-top: 10px;
        }
    }
}
</style>
