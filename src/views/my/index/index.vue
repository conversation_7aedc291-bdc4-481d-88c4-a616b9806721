<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2021-05-25 14:22:27
 * @LastEditors: 孙立政
 * @LastEditTime: 2021-12-06 11:43:56
-->
<template>
  <div>
    <NavHeight bgi="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/my/bj.png" />
    <Base @refInfo="refInfo" />
    <OrderBar />
    <ListItem :info="info" />
    <div style="height: 80px" />
  </div>
</template>

<script>
import Base from './components/base'
import OrderBar from './components/orderBar'
import ListItem from './components/listItem'

export default {
  components: {
    Base,
    OrderBar,
    ListItem
  },
  data() {
    return {
      info: {}
    }
  },
  created() {
    this.$store.state.Index.bannerIndex = 3
    this.$store.state.tabbar.index = 3
  },
  mounted() {
    // 回到顶部
    document.body.scrollTop = 0
    document.documentElement.scrollTop = 0

    // uid
    AlipayJSBridge.call(
      'UserLogin',
      {
        userId: localStorage.getItem('userId'),
        token: localStorage.getItem('token')
      },
      function(result) {}
    )
  },
  methods: {
    refInfo(info) {
      this.info = info
    }
  }
}
</script>

<style>

</style>
