<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-06-03 18:20:31
 * @LastEditors: 孙立政
 * @LastEditTime: 2021-07-26 10:51:02
-->
<template>
  <div class="content">
    <NavHeight bgc="#fff" />
    <div class="top">
      <van-nav-bar title="" left-text="" left-arrow>
        <template #title>
          <div>账单详情</div>
        </template>
        <template #left>
          <van-icon name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/common/arrowlwft.png" size="18" @click="goBack" />
        </template>
        <template #right>
          <span v-if="current != 0" @click="dateShow = true">{{ time }}</span>
        </template>
      </van-nav-bar>
    </div>
    <div class="bill">
      <!-- <div class="icon">
				美食
			</div> -->
      <!-- <div>消费订单</div> -->
      <div class="bill-money">{{ detaildata.amount }}</div>
      <div class="bill-status">交易成功</div>
    </div>
    <div class="bill-info">
      <!-- <div class="line">
				<span>支付方式</span>
				<span>微信支付</span>
			</div> -->
      <div class="line">
        <span>交易时间</span>
        <span>{{ detaildata.createTime }}</span>
      </div>
      <div v-if="false" class="line">
        <span>交易单号</span>
        <span>353467655444532</span>
      </div>
      <div v-if="false" class="line">
        <span>商户单号</span>
        <span>34554456867943325</span>
      </div>
    </div>
    <Loading :show="loadingShow" />
  </div>
</template>

<script>
import { billDetail } from '@/api/my'
import Loading from '@/components/Loading/index'
export default {
  components: {
    Loading
  },
  data() {
    return {
      loadingShow: true,
      current: '',
      ids: '',
      detaildata: '',
      time: ''
    }
  },
  created() {
    this.current = this.$route.query.current
    this.ids = this.$route.query.id
    console.log(this.current)
  },
  mounted() {
    // 获取账单详情
    this.getBillDetaill()
  },
  methods: {
    getBillDetaill() {
      let geturl
      if (this.current == 0) {
        geturl = 'injectionRecord'
      } else {
        geturl = 'CapitalFlowRecord'
      }
      let data = {
        id: this.ids
      }
      billDetail(data, geturl).then((res) => {
        this.loadingShow = false
        if (res.status == 200) {
          this.detaildata = res.data
        } else {
          this.$toast(res.message)
        }
      })
    },
    goBack() {
      this.$router.go(-1)
    }
  }
}
</script>

<style lang="scss" scoped>
.content::before {
    // 利用伪元素设置整个页面的背景色
    content: " ";
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    z-index: -100;
    min-height: 100%;
    background-color: #fff;
}
.content {
    .bill {
        text-align: center;
        margin-top: 100px;
        .bill-money {
            font-size: 72px;
            font-weight: bold;
            color: #000010;
        }
        .bill-status {
            color: #7f7f87;
            font-size:26px;
            margin-bottom: 80px;
        }
    }
    .bill-info {
        width:710px;
        margin: 0 auto;
        font-size: 30px;
        padding-top: 50px;
        border-top: 1px solid #ededed;
        color: #96969c;
        .line {
            display: flex;
            justify-content: space-between;
            padding: 10px  0;
        }
    }
}
</style>
