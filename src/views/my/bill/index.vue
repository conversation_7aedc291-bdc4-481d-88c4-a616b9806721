<template>
  <div class="content">
    <NavHeight bgc="#fff" />
    <div class="statusTop">
      <van-nav-bar title="" left-text="" left-arrow>
        <template #title>
          <div>账单详情</div>
        </template>
        <template #left>
          <van-icon name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/common/arrowlwft.png" size="18" @click="goBack" />
        </template>
        <template #right>
          <!-- <span v-if="current != 0" @click="dateShow = true">{{ time }}</span> -->
        </template>
      </van-nav-bar>
    </div>
    <div class="occupyHeight" />
    <!-- tabs -->
    <div class="tabs-box">
      <div class="tabs">
        <van-tabs background="#fff" line-width="23px" title-inactive-color="#000010" title-active-color="#2C2C2C" @click="clickTab">
          <van-tab v-for="item in list" :key="item.index" v-model="current" :title="item.title" />
        </van-tabs>
      </div>
    </div>
    <div v-if="current ==1||current==2" class="bill">
      <div v-for="(item, index) in itemData" :key="index" class="bill-list" @click="goBillDetail(item)">
        <div class="list1">
          <span v-if="current == 1">{{ item.payeeName }}</span>
          <span v-else>{{ item.payeeName }}</span>
          <span v-if="current == 1">-￥{{ item.amount }}</span>
          <span v-else>+{{ item.amount }}</span>
        </div>
        <div class="list2">
          {{ item.createTime }}
        </div>
      </div>
    </div>
    <div v-else class="bill">
      <div v-for="(item, index) in itemData" :key="index" class="bill-list newheight" @click="goBillDetail(item)">
        <div class="list">
          <span>{{ item.createTime }}</span>
          <span v-if="item.operateType == 1">+{{ item.amount }}</span>
          <span v-if="item.operateType != 1&&item.flowComment!='充值'" style="color: red;">-￥{{ item.amount }}</span>
          <span v-if="item.flowComment=='充值'" style="color: red;">+￥{{ item.amount }}</span>
        </div>
      </div>
    </div>
    <van-popup v-model="dateShow" position="bottom" :style="{ width: '100%', height: '6.3rem', }">
      <van-datetime-picker
        v-model="currentDate"
        type="year-month"
        title="选择年月"
        :min-date="minDate"
        :max-date="maxDate"
        @confirm="confirmBtn"
        @cancel="cancelBtn"
      />
    </van-popup>
    <Loading :show="loadingShow" />
  </div>
</template>

<script>
import {
  Allowance,
  otherMoney
} from '@/api/my'
import {
  formatDate
} from '@/utils/date'
import Loading from '@/components/Loading/index'
export default {
  components: {
    Loading
  },
  data() {
    return {
      loadingShow: true,
      message: '账单详情',
      time: '',
      minDate: new Date(2018, 0, 1),
      maxDate: new Date(2045, 0, 1),
      currentDate: new Date(),
      dateShow: false,
      capitalAccount: '',
      pages: 1,
      current: 0,
      itemData: [],
      total: '',
      list: [{
        index: 1,
        title: '补贴'
      },
      {
        index: 2,
        title: '消费'
      },
      {
        index: 3,
        title: '退款'
      },
      {
        index: 4,
        title: '充值'
      }
      ]
    }
  },
  created() {
    this.capitalAccount = this.$route.query.capitalAccount
    this.capitalAccountId = this.$route.query.id
    this.time = formatDate(new Date(Date.now()), 'yyyy-MM')
  },
  mounted() {
    // 获取补贴
    this.getAllowance()
    // 获取其他金额
    // this.getOtherMoney()
  },
  beforeRouteLeave(to, from, next) {
    if (to.name === 'billDetail') {
      from.meta.keepAlive = true
      next()
    } else {
      from.meta.keepAlive = false
      this.$destroy()
      next()
    }
  },
  methods: {
    onLoad() {
      if (this.current != 0) {
        this.getOtherMoney()
      } else {
        this.getAllowance()
      }
    },
    confirmBtn(value) {
      this.time = formatDate(new Date(value), 'yyyy-MM')
      this.pages = 1
      this.getOtherMoney()
      this.dateShow = false
    },
    cancelBtn() {
      this.dateShow = false
    },
    clickTab(e) {
      this.current = e
      this.pages = 1
      if (this.current != 0) {
        this.getOtherMoney()
      } else {
        this.getAllowance()
      }
    },
    getAllowance() {
      let self = this
      this.loadingShow = true
      self.itemData = []
      let data = {
        page: 1,
        size: 10000,
        capitalAccountId: this.capitalAccount
      }
      Allowance(data)
        .then((res) => {
          this.loadingShow = false
          if (res.status == 200) {
            self.itemData = res.data.list
          } else {
            self.$toast(res.message)
          }
        })
    },
    getOtherMoney() {
      let self = this
      this.loadingShow = true
      self.itemData = []
      let data = {
        page: 1,
        size: 100000,
        searchTime: self.time + '-00',
        transactionType: this.current == 3 ? 5 : this.current,
        capitalPoolId: Number(this.capitalAccount)
      }
      otherMoney(data)
        .then((res) => {
          this.loadingShow = false
          if (res.status == 200) {
            self.itemData = res.data.rows
          } else {
            self.$toast(res.message)
          }
        })
    },
    goBillDetail(item) {
      console.log(item)
      this.$router.push({
        name: 'BillDetail',
        query: {
          id: item.id,
          current: this.current
        }
      })
    },
    goBack() {
      this.$router.go(-1)
    }
  }
}
</script>

<style lang="scss" scoped>
	.content::before {
		// 利用伪元素设置整个页面的背景色
		content: " ";
		position: absolute;
		left: 0;
		right: 0;
		top: 0;
		bottom: 0;
		z-index: -100;
		min-height: 100%;
		background-color: #fff;
	}

	.content {
		.statusTop {
			width: 100%;
			height: 92px;
			position: fixed;
			left: 0;
			z-index: 1;
			background-color: #fff;
		}
        .occupyHeight {
            height: 92px;
        }
		.tabs-box {
			width: 100%;
			position: fixed;
			left: 0;
			z-index: 1;
			padding-top: 20px;
			background-color: #fff;
		}

		.tabs {
			font-size: 28px;
			color: #2c2c2c;
			::v-deep .van-tabs__line {
				background-color: #37c204;
			}
		}

		.bill {
			width:100%;
			background-color: #fff;
			margin-top: 108px;
		}

		.bill-list {
			height: 158px;
			margin: 0 20px;
			padding-top: 34px;
			box-sizing: border-box;
			border-bottom: 1px solid #ededed;

			.list {
				display: flex;
				justify-content: space-between;

				>span:nth-of-type(1) {
					font-size: 30px;
					color: #66666f;
				}

				>span:nth-of-type(2) {
					font-size: 36px;
					color: #37c204;
				}
			}

			.list1 {
				display: flex;
				justify-content: space-between;
				font-size: 32px;
				color: #000010;

				>span:nth-of-type(2) {
					font-size:36px;
					color: #37c204;
				}
			}

			.list2 {
				font-size: 24px;
				color: #66666f;
                margin-top: 20px;
			}
		}

		.newheight {
			height: 100px;
		}
	}
	::v-deep .van-hairline--bottom {
		border-bottom-width: 0;
	}

	::v-deep .van-hairline--bottom::after {
		border-bottom-width: 0;
	}

	.null {
		width: 100%;
		text-align: center;
		img {
			width: 340px;
			height: 220px;
			margin-top: 20px;
		}
	}
</style>
