<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2021-07-07 11:29:51
 * @LastEditors: 孙立政
 * @LastEditTime: 2021-07-26 11:06:45
-->
<template>
  <div class="home">
    <NavHeight bgc="linear-gradient(45deg, #71d774 0%, #5ecc52 100%)" />
    <div class="top">
      <van-nav-bar title="" left-text="" left-arrow>
        <template #title>
          <div>分享</div>
        </template>
        <template #left>
          <van-icon
            name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/common/arrowlbft.png"
            size="18"
            @click="goback"
          />
        </template>
      </van-nav-bar>
    </div>

    <div class="scanCode">
      <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/logo/scan.png" alt="">
    </div>

    <div v-if="false" class="share-btn" @click="onShare = true">
      其他分享方式
    </div>

    <van-popup v-model="onShare" position="bottom" :style="{ height: '30%' }">
      <div class="shareList">
        <div class="shareListIcon">
          <div class="shareListIconLetf" @click="getShare('wechat')">
            <div>
              <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/share/weixin.png" alt="">
            </div>
            <div>微信</div>
          </div>
          <div class="shareListIconRight" @click="getShare('wechattimeline')">
            <div>
              <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/share/pyq.png" alt="">
            </div>
            <div>朋友圈</div>
          </div>
        </div>
        <div class="close" @click="onShare = false">取消</div>
      </div>
    </van-popup>
  </div>
</template>

<script>
export default {
  data() {
    return {
      onShare: false,
      form: {
        sharePlatform: '',
        title: '点滴',
        content: '点滴畅享生活',
        redirectUrl: '',
        contentType: 'url',
        url: 'http://www.diandiandidi.top/app/index.html',
        imgUrl: 'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/logo/logo.jpg'
      }
    }
  },
  created() {

  },
  mounted() {

  },
  methods: {
    getShare(val) {
      this.form.sharePlatform = val
      AlipayJSBridge.call('Share', this.form, function(result) {
        console.log(result)
      })
    },
    goback() {
      this.$router.go(-1)
    }
  }
}
</script>

<style scoped lang="scss">
    .home {
        .top {
            color: #fff;
            background: linear-gradient(45deg, #71d774 0%, #5ecc52 100%);
            ::v-deep .van-nav-bar {
                background: none;
            }
            ::v-deep .van-nav-bar__title {
                color: #fff;
            }
        }
        .van-popup{
          background-color: rgba(0,0,0,0);
        }
        .shareList{
          width: 90%;
          height: 92%;
          background-color: #fff;
          margin: 0 auto;
          border-radius: 15px;
          position: relative;
          overflow: hidden;
          .shareListIcon{
            display: flex;
            font-size: 28px;
            text-align: center;
            width: 90%;
            margin: 0 auto;
            margin-top: 13%;
            img{
              width: 100px;
              height: 100px;
              margin-bottom: 10px;
            }
            .shareListIconLetf{
              width: 50%;
            }
            .shareListIconRight{
              width: 50%;
            }
          }
          .close{
            width: 100%;
            height: 150px;
            text-align: center;
            line-height: 150px;
            font-size: 35px;
            font-weight: PingFangSC-Medium;
            position: absolute;
            bottom: 0;
          }
        }
        .scanCode{
          width: 400px;
          height: 400px;
          margin: 0 auto;
          margin-top: 100px;
          img{
            width: 100%;
            height: 100%;
          }
        }
        .share-btn{
            width: 300px;
            height: 100px;
            line-height: 100px;
            text-align: center;
            background: linear-gradient(45deg, #71d774 0%, #5ecc52 100%);
            font-size: 30px;
            margin: 0 auto;
            margin-top: 100px;
            border-radius: 50px;
            color: #fff;
        }
    }
</style>
