<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-06-23 14:28:47
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-10-09 11:25:57
-->
<template>
  <div class="content" :style="styleVar">
    <NavHeight bgc="#fff" />
    <NavBar title="优惠说明" />
    <!-- 外卖规则 -->
    <div>
      <div class="header">
        一、什么是优惠券？
      </div>
      <div class="rules">
        点滴优惠券是一种电子优惠券，可在下单时用于减免支付金额。在支付下单时，如果订单满足优惠券使用条件，则自动选择最优的优惠券使用，一张优惠券最多只能被使用一次。<br>
        (1)无门槛券：订单金额（不含运费等，下同）需大于优惠券可减免金额时，方可使用。<br>
        (2)满减券：订单金额需满足优惠券满减的门槛时，方可使用。<br>
        (3)折扣券：订单符合折扣券使用条件时，订单金额可以按照券面展示的折扣力度进行减免，并且减免的金额不超过最高可减免金额。<br>
        (4)免单券：适用于特权免单商品。<br>
      </div>
      <div class="header">
        二、优惠券的使用条件是什么？
      </div>
      <div class="rules">
        订单金额大于等于1元才可使用优惠券，且优惠后的实际支付金额必须大于0元（免单券除外）。具体各类优惠券的使用条件会在券面上进行展示，也可以点击券面上的箭头查看优惠券详情。 <br>
        (1)仅限XXX店内商品使用：是指必须在指定店铺内购买指定商品方可使用该优惠券。 <br>
        (2)仅限XXX店内商品使用：是指必须在指定店铺内购买指定商品方可使用该优惠券。 <br>
        (3)仅限点滴严选使用：是指仅在购买点滴严选商品时方可使用该优惠券。<br>
        (4)仅限点滴团购使用：是指仅在购买点滴团购商品时方可使用该优惠券。 <br>
        (5)全场通用：点滴所有场景可用。 <br>
      </div>
      <div class="header">
        三、如何获得优惠券？
      </div>
      <div class="rules">
        (1)部分店铺页以及商品详情页可领取店铺优惠券<br>
        (2)参与活动获得优惠券<br>
      </div>
      <div class="header">
        四、优惠券可以叠加使用嘛？
      </div>
      <div class="rules">
        在满足使用条件的情况下，平台优惠券和店铺优惠券可以叠加使用，但最多各使用一张。<br>
      </div>
      <div class="header">
        五、下单的时候用了优惠券，但后来又退货了，优惠券会返还吗？
      </div>
      <div class="rules">
        （1）优惠券在订单退款、退货操作后将失效，不予返还。
      </div>
    </div>
  </div>
</template>

<script>
import NavBar from '@/components/Navbar'
export default {
  components: {
    NavBar
  },
  data() {
    return {
    }
  },
  computed: {
    styleVar() {
      return {
        '---nav-height': this.$store.getters.getStatusHeight + 'px'
      }
    }
  },
  created() {
  },
  mounted() {

  },
  methods: {

  }
}
</script>

  <style scoped lang="scss">
      .content {
          background-color: #F9F9FA;
          font-size: 28px;
          padding-bottom: 30px;
          padding-top: calc(80px + var(---nav-height));
          .header {
              color: #222;
            //   height: 76px;
            //   line-height: 76px;
              padding: 30px 0 16px 0;
              padding-left: 20px;
              font-family:PingFangSC-Medium;
              font-weight: 500;
              font-size: 32px;
          }
          .rules {
              width: 710px;
              box-sizing: border-box;
              padding: 20px;
              color: #333;
              background-color: #f4f4f4;
              margin: auto;
              border-radius: 16px;
              line-height: 42px;
              font-family: PingFangSC;
          }
      }
  </style>
