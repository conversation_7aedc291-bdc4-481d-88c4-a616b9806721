<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2021-06-15 10:41:27
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-10-11 15:23:10
-->
<template>
  <div class="coupon">
    <NavHeight bgc="#fff" />
    <NavBar title="我的优惠券" right-name="优惠说明" @rightClick="goRules" />

    <div v-if="myCouponForUseList.length>0">
      <div class="tabs">
        已领取，待使用
      </div>
      <div class="cardList">
        <CouponCard v-for="item in myCouponForUseList" :key="item.couponId" :item="item" />
      </div>
    </div>

    <div v-if="myCouponCanPickList.length>0">
      <div class="tabs1" :class="myCouponForUseList.length === 0?'tabs2':''">
        待领取
      </div>
      <div class="cardList">
        <CouponCard v-for="item in myCouponCanPickList" :key="item.couponId" :item="item" />
      </div>
    </div>

    <Empty v-if="myCouponCanPickList.length == 0&&myCouponForUseList.length == 0" msg="您当前没有优惠券哦~" msg1="去看看有哪些好东西吧" type="去逛逛" />

    <div v-if="myCouponCanPickList.length > 0||myCouponForUseList.length > 0" style="height: 80px;" />
    <div class="bottom" @click="goCouponHistory">
      历史优惠券
    </div>
  </div>
</template>

<script>
import NavBar from '@/components/Navbar'
import CouponCard from './components/CouponCard.vue'
import { myCouponV2 } from '@/api/coupon'
export default {
  components: {
    NavBar,
    CouponCard
  },
  data() {
    return {
      active: 1,
      couponList: [],
      myCouponCanPickList: [],
      myCouponForUseList: []
    }
  },
  computed: {
    styleVar() {
      return {
        '---nav-height': this.$store.getters.getStatusHeigh + 'px'
      }
    }
  },
  created() {
    this.getCouponList()
  },
  methods: {
    getCouponList() {
      let data = {
        regionId: this.$store.getters.getRegionId
      }
      myCouponV2(data).then(res => {
        if (res.status == 200) {
          this.myCouponCanPickList = res.data.myCouponCanPickList
          this.myCouponForUseList = res.data.myCouponForUseList
        }
      })
    },
    goCouponHistory() {
      this.$router.push('/my/couponHistory')
    },
    onClick(name, title) {
      this.getCouponList()
    },
    goRules() {
      this.$router.push('/my/rules')
    }
  }
}
</script>

<style scoped lang="scss">
    .coupon {
      // background-color: #f5f5f6;
        .tabs{
          width: 100%;
          margin-top: 110px;
          font-size: 33px;
          color: #333333;
          font-weight: 400;
          padding-left: 26px;
        }
        .tabs1{
          width: 100%;
          margin-top: 30px;
          font-size: 33px;
          color: #333333;
          font-weight: 400;
          padding-left: 26px;
        }
        .tabs2{
          margin-top: 110px;
        }
        .cardList{
            margin-top: 23px;
            overflow: hidden;
        }
        ::v-deep .van-tab--active{
          font-size: 30px;
          font-family: PingFangSC-Medium;
        }
        .bottom{
          width: 100%;
          height: 114px;
          line-height: 114px;
          font-size: 40px;
          text-align: center;
          position: fixed;
          bottom: 0;
          background-color: #fff;
          border-top: 1px solid #ededed;
        }
    }
</style>
