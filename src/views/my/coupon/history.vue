<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2021-06-15 10:41:27
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-10-11 15:24:53
-->
<template>
  <div class="coupon">
    <NavHeight bgc="#fff" />
    <NavBar title="历史优惠券" />

    <div class="cardList">
      <CouponHistoryCard v-for="(item,index) in couponList" v-show="item.usageStatus == 2||item.usageStatus == 3||item.usageStatus == 4" :key="index" :item="item" />
    </div>

    <Empty v-if="couponList.length==0" msg="您当前没有优惠券哦~" msg1="去看看有哪些好东西吧" type="去逛逛" />

  </div>
</template>

<script>
import NavBar from '@/components/Navbar'
import CouponHistoryCard from './components/CouponHistoryCard.vue'
import { historicalCouponList } from '@/api/coupon'
export default {
  components: {
    NavBar,
    CouponHistoryCard
  },
  data() {
    return {
      couponList: []
    }
  },
  computed: {},
  created() {
    this.getCouponList()
  },
  methods: {
    getCouponList() {
      let data = {
        pageNum: 1,
        pageSize: 1000
      }
      historicalCouponList(data).then(res => {
        if (res.status == 200) {
          this.couponList = res.data.list
        }
      })
    },
    goRules() {
      this.$router.push('/my/rules')
    }
  }
}
</script>

  <style scoped lang="scss">
      .coupon {
        // background-color: #f5f5f6;
          .cardList{
            margin-top: 110px;
              overflow: hidden;
          }
          ::v-deep .van-tab--active{
            font-size: 30px;
            font-family: PingFangSC-Medium;
          }
      }
  </style>
