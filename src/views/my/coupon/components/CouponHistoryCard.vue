<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2022-08-05 11:23:35
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-10-11 09:20:32
-->
<template>
  <div class="coupon_card">
    <div class="left disable_bg">
      <div>
        <span>￥</span>
        <span>{{ item.preferentialAmount }}</span>
      </div>
      <div>满{{ item.useThreshold }}元可用</div>
    </div>
    <div class="center disable_bg">
      <div :class="item.invalidTime===null?'no_invalidTime':''">{{ item.couponName }}</div>
      <div>{{ item.applyMarket===0?'全部外卖店铺可用':'部分店铺可用' }}</div>
      <div v-if="item.invalidTime">{{ item.invalidTime }}到期</div>
    </div>
    <div class="right">
      <img v-if="item.usageStatus===2||item.usageStatus===3" src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/coupon/invalid1.png" alt="">
      <img v-if="item.usageStatus===4" src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/coupon/invalid2.png" alt="">
      <!-- <div v-if="item.usageStatus===2||item.usageStatus===3" class="coupun_btn disable"> 已使用 </div> -->
      <!-- <div v-if="item.usageStatus===4" class="coupun_btn disable"> 已失效 </div> -->
    </div>
  </div>
</template>

<script>
import { couponPick } from '@/api/coupon'
export default {
  props: {
    item: {
      type: Object,
      default: function() {
        return {}
      }
    }
  },
  data() {
    return {}
  },
  methods: {
    pick(item) {
      // let data = {
      //   couponId: item.couponId,
      //   regionId: this.$store.getters.getRegionId
      // }
      couponPick(item.couponManagementId).then(res => {
        if (res.status === 200) {
          this.$toast('领取成功')
          this.$router.push({
            path: '/Classify',
            query: { name: '外卖', cateId: 0, isTakeaway: true }
          })
        }
      })
    },
    goClass() {
      this.$router.push({
        path: '/Classify',
        query: { name: '外卖', cateId: 0, isTakeaway: true }
      })
    }
  }
}
</script>

<style scoped lang="scss">
.disable_bg{
  opacity: 0.6;
}
.coupon_card {
  width: 706px;
  height: 200px;
  background-color: #fff;
  border-radius: 16px;
  display: flex;
  justify-content: space-between;
  position: relative;
  margin: 0 auto;
  margin-bottom: 20px;
  .left {
    width: 180px;
    text-align: center;
    color: #ff301e;
    div:nth-of-type(1) {
      margin-top: 35px;
      height: 68px;
      font-family: PingFangSC-Medium;
      span:nth-of-type(1) {
        font-size: 26px;
      }
      span:nth-of-type(2) {
        font-size: 67px;
      }
    }
    div:nth-of-type(2) {
      height: 23px;
      font-size: 22px;
      margin-top: 13px;
    }
  }
  .center {
    width: 300px;
    div:nth-of-type(1) {
      height: 35px;
      line-height: 35px;
      font-size: 35px;
      font-family: PingFangSC-Medium;
      color: #222222;
      margin-top: 43px;
    }
    div:nth-of-type(2) {
      height: 26px;
      font-size: 26px;
      color: #666666;
      margin-top: 6px;
    }
    div:nth-of-type(3) {
      height: 23px;
      font-size: 22px;
      color: #999999;
      margin-top: 15px;
    }
    .no_invalidTime{
      margin-top: 60px !important;
    }
  }
  .right {
    width: 190px;
    text-align: center;
    img{
      width: 140px;
      height: 140px;
      position: relative;
      top: 30px;
    }
    .coupun_btn{
        width: 160px;
        height: 64px;
        background: linear-gradient(90deg,#ff1e29, #ff5a25);
        border-radius: 32px;
        font-size: 28px;
        color: #ffffff;
        line-height: 64px;
        text-align: center;
        margin-top: 68px;
    }
    .disable{
        background: #d8d8d8;
    }

  }
}
</style>
