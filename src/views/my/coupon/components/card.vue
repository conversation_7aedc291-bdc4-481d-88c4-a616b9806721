<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2021-06-17 14:49:53
 * @LastEditors: 孙立政
 * @LastEditTime: 2021-12-30 21:27:29
-->
<template>
  <div class="home">
    <div v-for="(item,index) in list" :key="index" class="card">
      <div class="cardTop">
        <div class="cardTopUsed">
          <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/shoppingMall/index/used.png" alt="">
        </div>
        <div class="cardTopLeft">
          <div class="cardTypeCover" :class="activeIndex==0?'':'typeUsed'">
            全品类券
          </div>
          <div class="cardTopLeftMsg">
            <div class="cardTopLeftMsgCover">
              <img v-if="activeIndex==0" src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/my/coupon/coupon.png" alt="">
              <img v-else src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/my/coupon/nocoupon.png" alt="">
            </div>
            <div class="cardTopLeftMsgRight">
              <div class="cardTopLeftMsgName">{{ item.title }}</div>
              <div class="cardTopLeftMsgTime">有效期至{{ item.validEndTime }}</div>
            </div>
          </div>
        </div>
        <div class="cardTopRight">
          <div class="cardTopRightPrice" :class="activeIndex==0?'':'priceUsed'">
            <span class="cardTopRightPrice_1">￥</span>
            <span class="cardTopRightPrice_2">{{ item.usedAmount }}</span>
          </div>
          <div class="cardTopRightCondition">满￥{{ item.withAmount }}可用</div>
        </div>
      </div>
      <div class="cardCenter" />
      <div class="cardBottom">
        <div class="cardBottomLeft">仅限点滴商城使用</div>
        <div class="cardBottomRight" :class="activeIndex==0?'':'btnUsed'">
          <span v-if="item.status==0&&activeIndex != 2" @click="goIndex">去使用</span>
          <span v-if="item.status==1">已使用</span>
          <span v-if="activeIndex == 2">已过期</span>
        </div>
      </div>
    </div>
    <Empty v-if="list.length==0" msg="您当前没有优惠券哦~" msg1="去看看有哪些好东西吧" type="去逛逛" />

    <Loading :show="loadingShow" />
  </div>
</template>

<script>
import { myCouList } from '@/api/shoppingMall'
import Loading from '@/components/Loading/index'
export default {
  components: {
    Loading
  },
  props: {
    activeIndex: {
      type: [Number, String],
      default: function() {
        return null
      }
    }
  },
  data() {
    return {
      list: [],
      loadingShow: true
    }
  },
  created() {
    this.getCouList(0)
  },
  mounted() {

  },
  methods: {
    goIndex() {
      this.$router.push({
        name: 'ShoppingMallIndex',
        query: {
          id: 1138
        }
      })
    },
    getCouList(name) {
      this.list = []
      let data = {
        userId: this.$store.getters.getUserId,
        status: name == 2 ? 3 : name
      }
      myCouList(data).then((res) => {
        this.loadingShow = false
        if (res.status == 200) {
          this.list = res.data
        } else {
          this.$toast(res.message)
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
    .home {
        .card {
            width: 706px;
            background: #ffffff;
            border-radius: 16px;
            margin: 0 auto;
            margin-top: 16px;
            .cardTop {
                position: relative;
                display: flex;
                justify-content: space-between;
                padding-right: 32px;
              .cardTopUsed {
                  position: absolute;
                  right: 0;
                  top: 0;
                  width: 102px;
                  height: 102px;
                  z-index: -1;
                  img {
                    width: 100%;
                    height: 100%;
                  }
              }
                .cardTopLeft {
                    .cardTypeCover {
                        width: 124px;
                        height: 36px;
                        background-image: url(https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/my/coupon/type.png);
                        background-size: 100% 100%;
                        text-align: center;
                        line-height: 36px;
                        font-size: 22px;
                        font-weight: 500;
                        color: #ffffff;
                    }
                    .typeUsed {
                      background-image: url(https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/shoppingMall/index/tip3-gray.png);
                    }
                    .cardTopLeftMsg {
                        display: flex;
                        margin-top: 15px;
                        margin-left: 22px;
                        .cardTopLeftMsgCover{
                            img {
                                width: 70px;
                                height: 70px;
                            }
                        }
                        .cardTopLeftMsgName {
                            font-size: 30px;
                            font-weight: 500;
                            color: #222222;
                            margin-left: 16px;
                            font-family:PingFangSC-Medium;
                        }
                        .cardTopLeftMsgTime {
                            font-size: 22px;
                            font-weight: 400;
                            color: #666666;
                            margin-top: 4px;
                            margin-left: 16px;
                            font-family: PingFangSC;
                        }
                    }
                }
                .cardTopRight {
                    text-align: center;
                    .cardTopRightPrice {
                        color: #ff301e;
                        margin-top: 32px;
                        .priceUsed {
                          color: #d8d8d8;
                        }
                        .cardTopRightPrice_1 {
                            font-size: 26px;
                            font-weight: 500;
                        }
                        .cardTopRightPrice_2 {
                            font-size: 64px;
                            font-weight: 500;
                        }
                    }
                    .cardTopRightCondition {
                        font-size: 20px;
                        font-weight: 400;
                        color: #666666;
                    }
                }
            }
            .cardCenter {
                width: 662px;
                height: 1px;
                border: 1px dashed #ededed;
                margin: 0 auto;
                margin-top: 8px;
            }
            .cardBottom {
                width: 650px;
                margin: 0 auto;
                display: flex;
                justify-content: space-between;
                align-items: center;
                height: 74px;
                .cardBottomLeft {
                    width: 80%;
                    font-size: 20px;
                    color: #999999;
                    font-weight: 400;
                    font-family: PingFangSC;
                }
                .cardBottomRight {
                    width: 108px;
                    height: 48px;
                    background: linear-gradient(90deg,#ff1e29, #ff5a25);
                    border-radius: 32px;
                    font-size: 24px;
                    color: #ffffff;
                    font-weight: 400;
                    text-align: center;
                    line-height: 48px;
                }
                .btnUsed {
                  background: #d8d8d8;
                  color: #fff;
                }
            }

        }

    }
</style>
