<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-06-02 17:31:55
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-11-28 09:38:38
-->
<template>
  <div class="content">
    <NavHeight bgc="#fff" />
    <div class="top">
      <div class="fixed">
        <van-nav-bar title="我的食堂" left-text="" left-arrow>
          <template #left>
            <van-icon name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/user/icon/arrow-black.png" size="18" @click="goBack" />
          </template>
        </van-nav-bar>
      </div>
      <div style="height:46px" />
    </div>
    <div class="tag-content">
      <div v-for="item in itemData" :key="item.id" class="list">
        <div class="list-top">
          <van-icon class="phone" name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/my/shitang/shitang.png" size="17" />
          该食堂限额消费
          <span v-if="item.limitType == 0">无限额；本月已消费 </span>
          <span v-if="item.limitType == 1">{{ item.limitAmount }}元/月；本月已消费 </span>
          <span v-if="item.limitType == 2">{{ item.limitAmount }}元/年；本年已消费 </span>

          <span>{{ item.totalAmount }}元</span>
        </div>
        <div class="list-box">
          <div class="img">
            <img :src="item.cover +'?x-oss-process=image/resize,w_700/format,jpg/quality,q_50'" border-radius="7" class="image">
          </div>
          <div class="data">
            <div class="data1">
              <div v-if="id != 'hotel'" class="title">
                {{ item.marketName| ellipsis(8) }}
              </div>
              <div class="date">
                <span>
                  （ 营业时间：{{ item.openingHours }}~{{ item.closingHours }} ）
                </span>
              </div>
              <div class="address">
                <span style="float: left">
                  {{ item.address | ellipsis(15) }}
                </span>
              </div>
            </div>
          </div>
          <div class="moble">
            <van-icon class="phone" name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/my/shitang/call.png" size="32" @click="CallPhone(item.mobilePhone)" />
          </div>
        </div>
        <div class="bottom">
          <div v-show="item.id === 1185||item.id === 1187||item.id === 1190||item.id === 1331" class="tips">
            <div>消费提醒：</div>
            <div>1: 该消费限额逐月递增400元，最高限额4800元/年，年底额度清零</div>
            <div>2: 该消费额度与机关事务保障中心管辖范围内三家食堂共享</div>
          </div>
        </div>
        <div v-if="item.poolId === 12||item.poolId === 11" style="text-align: right;">
          <van-button type="primary" size="mini" style="margin-right: 20px;width: 80px;" @click="goToCCB">充 值</van-button>
        </div>
      </div>
      <div style="height: 100px;" />
    </div>
    <!-- <Empty v-if="itemData==null" msg="您还没有食堂哦~" /> -->
    <div v-if="itemData==null" class="empty">
      <div class="emptyImg">
        <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/empty/empty-canteen.png" alt="">
        <div class="msg">您还没有食堂哦~</div>
      </div>
    </div>
    <Loading :show="loadingShow" />
  </div>
</template>

<script>
import { myFoodList } from '@/api/my'
import Loading from '@/components/Loading/index'
export default {
  components: {
    Loading
  },
  data() {
    return {
      id: 1,
      itemData: [],
      loadingShow: true
    }
  },
  created() {
    this.getList()
  },
  mounted() {

  },
  methods: {
    goToCCB() {
      this.$dialog.alert({
        title: '提示',
        message: '即将跳转农行掌银充值，请确认已安装农行掌银APP',
        confirmButtonText: '确认',
        confirmButtonColor: '#6095F0',
        closeOnClickOverlay: true
      }).then(() => {
        var u = navigator.userAgent
        var isiOS = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/) // ios终端
        const urls = 'bankabc://%7B%22method%22%3A%22jumpToSharedProduct%22%2C%22param%22%3A%221fc60e4e85ce769fdba0baa2c540ac4dd55bb5033744e8aada10fc9a91d4192642e16425ef14be4029d528991f21f740ae1be29c4b00e30ba1e7f3060d20e3de6e6487d4c18cf9e701a66c12ed3d00a95c1bcbac585d8f02dcab2846718b67f5%22%2C%22type%22%3A%221%22%7D'
        if (!isiOS) {
          window.location.href = urls
        } else {
          AlipayJSBridge.call('OpenAppByRouter', {
            urlStr: urls
          }, function(result) {})
        }
      })
    },
    goBack() {
      this.$router.go(-1)
    },
    getList() {
      // 获取店铺列表
      myFoodList()
        .then(res => {
          this.loadingShow = false
          if (res.status == 200) {
            this.itemData = res.data
          }
        })
    },
    goShop(item) {
      this.$router.push({
        name: 'Shop',
        query: {
          id: item.id
        }
      })
    },
    // 拨打电话
    CallPhone(data) {
      AlipayJSBridge.call('CallPhone', {
        phoneNum: data
      }, function(result) {})
    }
  }
}
</script>

  <style lang="scss" scoped>
  .content::before {
    // 利用伪元素设置整个页面的背景色
    content: " ";
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    z-index: -100;
    min-height: 100%;
    background-color: #f5f5f5;
  }

  .content {
    background-color: #fff;
    .top {
        .fixed {
            position: fixed;
            left: 0;
            // top: 0;
            z-index: 2;
            width: 100%;
            background-color: #fff;
        }
        ::v-deep .van-nav-bar__title {
            font-size: 36px;
            color: #222;
        }
      }
    ::v-deep .van-nav-bar__title {
      font-size: 34px;
      color: #000010;
    }
    ::v-deep .van-nav-bar {
      background: none;
    }
    ::v-deep .van-nav-bar__right {
      font-size: 30px;
    }
    .banner {
      width: 93%;
      margin: 0 auto;
      margin-top: 18px;
      .image {
        width: 100%;
        height: 280px;
      }
    }
  }
  .tag-content {
    width: 100%;
    margin: 0 auto;
    margin-top: 48px;
    .list {
      width: 710px;
      border-radius: 16px;
      margin: 0 auto;
      background-color: #fff;
      margin-top: 45px;
      box-shadow: 0px 2px 16px 0px #e1e5f2;
      overflow: hidden;
      padding-bottom: 30px;
      .list-top{
        height: 70px;
        background: #feffad;
        border-radius: 16px 16px 0px 0px;
        font-size: 28px;
        line-height: 70px;
        color: #333;
        img{
          width: 36px;
          height: 36px;
          margin-left: 20px;
          position: relative;
          top: 8px;
        }
        span{
          color: #FF301E;
        }
      }
      .list-box{
        display: flex;
        margin-top: 25px;
      }
      .img {
        width: 152px;
        height: 152px;
        margin-left: 20px;
        margin-right: 16px;
        .image {
          width: 152px;
          height: 152px;
          border-radius: 10px;
        }
      }

      .bottom{
        display: flex;
        justify-content: space-between;
        padding: 0 20px;
        margin-top: 20px;
        div{
          font-size: 28px;
        }
        .tips{
          font-size: 23px;
          color: #FF301E;
          div{
            margin-top: 10px;
          }
        }
      }

      .data {
        width: 400px;
        height: 150px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        .title {
          font-family: PingFangSC-Medium;
          font-size: 35px;
          color: #222222;
          margin-top: 5px;
        }
        .date {
          font-size: 27px;
          margin-top:13px;
          margin-bottom: 13px;
          color: #666666;
        }

        .address {
          font-size: 27px;
          color: #666666;
        }
      }
      .moble{
        width: 100px;
        margin-top: 30px;
      }
    }
  }
  .empty {
    .emptyImg {
            position: absolute;
            left: 50%;
            top: 43%;
            transform: translate(-50%,-50%);
            width: 400px;
            height: 300px;
            margin: 0 auto;
            img {
                width: 100%;
                height: 100%;
            }
        }
        .msg {
            font-size: 30px;
            font-family:PingFangSC;
            font-weight: 500;
            color: #333333;
            text-align: center;
            margin-top: 24px;
        }
  }
  </style>
