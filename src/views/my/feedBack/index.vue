<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-06-03 14:47:41
 * @LastEditors: 孙立政
 * @LastEditTime: 2021-12-07 03:18:22
-->
<template>
  <!-- 意见反馈 -->
  <div class="content">
    <NavHeight bgc="linear-gradient(45deg, #71d774 0%, #5ecc52 100%)" />
    <div class="top">
      <van-nav-bar title="" left-text="" left-arrow>
        <template #title>
          <div>关于我们</div>
        </template>
        <template #left>
          <van-icon
            name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/common/arrowlbft.png"
            size="18"
            @click="goBack"
          />
        </template>
      </van-nav-bar>
    </div>
    <div style="height: 17px; background: #fff" />
    <!-- <div class="feedback-title">意见反馈</div> -->
    <div class="feedback-wrap">
      <van-field
        v-model="reason"
        style="margin-top: 10px"
        type="textarea"
        rows="3"
        placeholder="输入你要反馈的意见~"
        input-align="left"
        maxlength="100"
      />
      <!-- <van-uploader v-model="fileList" :before-delete="delImage"  :after-read="afterRead" multiple :max-count="3">
                  </van-uploader> -->

      <!-- <div class="bottom" v-if="imageList.length != 0">
          <div class="avatar" v-for="(item, index) in imageList" :key="index">
            <img :src="item" alt="" />
            <img
              @click="delUpload(index)"
              src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/my/del.png"
              alt=""
              class="delicon"
            />
          </div>

          <div class="avatar" @click="goUpload" v-if="imageList.length==1&&imageList[1]==undefined">
            <img
              src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/my/upload.png"
              alt=""
            />
          </div>
          <div class="avatar" @click="goUpload" v-if="imageList.length==2&&imageList[2]==undefined">
            <img
              src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/my/upload.png"
              alt=""
            />
          </div>

        </div> -->

      <!-- <div class="bottom" v-else>
          <div class="avatar" @click="goUpload">
            <img
              src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/my/upload.png"
              alt=""
            />
          </div>
        </div> -->
    </div>
    <div class="btn" @click="confirm">提交</div>
  </div>
</template>

<script>
import { feedback } from '@/api/my'
export default {
  components: {
  },
  data() {
    return {
      message: '意见反馈',
      reason: '',
      fileList: [],
      imageList: [],
      iosTop: false
    }
  },
  mounted() {
  },
  // // 离开页面清除缓存
  // beforeRouteLeave(to, form, next) {
  //   AlipayJSBridge.call(
  //     'CleanPic',
  //     {
  //       position: -1
  //     },
  //     function(result) {
  //     }
  //   )
  //   next()
  // },
  methods: {
    goUpload() {
      let self = this
      AlipayJSBridge.call(
        'ChoosePic',
        {
          type: 2,
          size: 3
        },
        function(result) {
          self.imageList = result.picImg
        }
      )
    },
    delUpload(index) {
      let self = this
      AlipayJSBridge.call(
        'CleanPic',
        {
          position: index
        },
        function(result) {
          if (result) {
            self.imageList.splice(index, 1)
          } else {
            self.$toast('图片删除失败')
          }
        }
      )
    },
    delImage(file, detail) {
      this.imageList.splice(detail.index, 1)
      this.fileList.splice(detail.index, 1)
    },
    confirm() {
      let self = this
      let data = {
        imageList: this.imageList,
        message: this.reason,
        regionId: this.$store.getters.getRegionId
      }
      if (self.reason.trim() == '') {
        self.$toast('请输入反馈意见')
        return false
      }
      feedback(data)
        .then((res) => {
          if (res.status == 200) {
            self.$toast('反馈成功')
            self.reason = ''
            self.imageList = []
            self.fileList = []
            AlipayJSBridge.call(
              'CleanPic',
              {
                position: -1
              },
              function(result) {
              }
            )
          } else {
            self.$toast(res.message)
          }
        })
    },
    goBack() {
      this.$router.go(-1)
    }
  }
}
</script>

  <style lang="scss" scoped>
  .content::before {
    // 利用伪元素设置整个页面的背景色
    content: " ";
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    z-index: -100;
    min-height: 100%;
    background-color: #fff;
  }

  .content {
    .top {
      color: #fff;
      background: linear-gradient(45deg, #71d774 0%, #5ecc52 100%);
      ::v-deep .van-nav-bar {
        background: none;
      }
      ::v-deep .van-nav-bar__title {
        color: #fff;
      }
    }
    .feedback-title {
      font-size: 32px;
      color: #333340;
      margin: 30px 0 30px 36px;
    }

    .feedback-wrap {
      position: relative;
      width: 678px;
      height: 388px;
      border: 2px solid #d4d4d4;
      border-radius:24px;
      margin: 0 auto;
      box-sizing: border-box;
      input {
        border: none;
      }

      input::placeholder {
        color: #bfbfc3;
        font-size: 28px;
      }

      .bottom {
        position: absolute;
        left: 22px;
        bottom: 20px;
        display: flex;
        height: 160px;
        z-index: 1;
      }
    }

    ::v-deep .van-cell::after {
      border-bottom: none;
      margin-bottom: -5px;
    }

    ::v-deep .van-cell {
      position: relative;
      top: -10px;
    }

    ::v-deep .van-field__word-limit {
      margin-top: 120px;
    }

    .btn {
      width: 452px;
      height: 84px;
      line-height:84px;
      text-align: center;
      color: #ffffff;
      font-size: 32px;
      margin: 0 auto;
      background: linear-gradient(45deg, #71d774 0%, #5ecc52 100%);
      border-radius: 12px;
      margin-top: 40px;
    }
    .avatar {
      position: relative;
      width: 130px;
      height: 130px;
      margin-right:20px;
      img {
        width: 100%;
        height: 100%;
      }
      .delicon {
        position: absolute;
        right: 0;
        top: 0;
        width: 36px;
        height: 36px;
      }
    }
  }
  </style>
