<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-06-03 17:17:18
 * @LastEditors: 孙立政
 * @LastEditTime: 2021-08-11 10:19:13
-->
<template>
  <!-- 为自己充值 -->
  <div class="content">
    <NavHeight bgc="#fff" />
    <!-- 头部 -->
    <van-nav-bar title="" left-text="" left-arrow :border="false">
      <template #title>
        <div>充值</div>
      </template>
      <template #left>
        <van-icon name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/user/icon/arrow-black.png" size="20" @click="goBack" />
      </template>
    </van-nav-bar>
    <div class="line" />
    <div class="pay">
      <div class="title">充值金额</div>
      <div class="inputBorder">
        <van-field
          v-model="amount"
          onkeyup="this.value=this.value.toString().match(/^\d+(?:\.\d{0,2})?/)"
          label=""
          left-icon="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/user/icon/yuan.png"
          placeholder="请输入充值金额"
          clearable
          type="number"
          maxlength="10"
        />
      </div>
    </div>
    <div class="title">选择支付方式</div>
    <van-radio-group v-model="paymentType">
      <div class="payment">
        <div v-if="paydata[0]==3||paydata[1]==3||paydata[2]==3||paydata[3]==3" class="payment-line" @click="paymentType = '3'">
          <div class="line-left">
            <van-icon name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/pay/weixinpay.png" size="23" />
            <span>微信支付</span>
          </div>
          <div class="line-right">
            <van-radio name="3" icon-size="19" checked-color="#5dcb4f" />
          </div>
        </div>
        <div v-if="paydata[0]==2||paydata[1]==2||paydata[2]==2||paydata[3]==2" class="payment-line" @click="paymentType = '2'">
          <div class="line-left">
            <van-icon name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/pay/alipay.png" size="23" />
            <span>支付宝支付</span>
          </div>
          <div class="line-right">
            <van-radio name="2" icon-size="19" checked-color="#5dcb4f" />
          </div>
        </div>
        <div v-if="paydata[0]==4||paydata[1]==4||paydata[2]==4||paydata[3]==4" class="payment-line" @click="paymentType = '4'">
          <div class="line-left">
            <van-icon name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/pay/dragon.png" size="26" />
            <span>龙支付</span>
          </div>
          <div class="line-right">
            <van-radio name="4" icon-size="19" checked-color="#5dcb4f" />
          </div>
        </div>
      </div>
    </van-radio-group>
    <div class="btn" @click="goPay">立即充值</div>
    <Loading :show="loadingShow" />
  </div>
</template>

<script>
import { ifPay, payBlance
} from '@/api/my'
import { payLoading } from '@/api/takeout'
import Loading from '@/components/Loading/index'
import { version } from '@/config/settings'
export default {
  components: {
    Loading
  },
  data() {
    return {
      content: '是否支付完成？',
      amount: '',
      paymentType: '',
      regionId: '',
      capitalAccountId: '',
      orderNo: '',
      paydata: '',
      loadingShow: true
    }
  },
  mounted() {
    this.ifPay()
  },
  methods: {
    goPay() {
      let self = this
      var u = navigator.userAgent
      var isiOS = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/) // ios终端
      if (self.amount == '') {
        self.$toast('请输入充值金额')
        return
      } else if (self.paymentType == '') {
        self.$toast('请输入充值方式')
        return
      } else if (self.amount < 0.02) {
        self.$toast('充值金额最低0.02元')
        return
      } else if (self.amount > 99999) {
        self.$toast('充值金额过大请确认')
        return
      }
      self.$toast.loading({
        duration: 0,
        forbidClick: true,
        message: '充值'
      })

      let data = {
        amount: self.amount,
        capitalPoolId: this.$route.query.capitalPoolId,
        capitalAccountId: this.$route.query.capitalAccountId,
        paymentType: self.paymentType,
        clientIp: '127.0.0.1',
        terminalSysVer: version
      }

      payBlance(data).then((res) => {
        if (res.status == 200) {
          self.$toast.clear()
          if (self.paymentType == 2) { // 支付宝
            if (!isiOS) {
              AlipayJSBridge.call('IsAvailable', {
                packageName: 'com.eg.android.AlipayGphone'
              }, function(result) {
                if (result.error == 1) {
                  self.$toast({
                    duration: 5000, // 持续展示 toast
                    forbidClick: true,
                    message: 'APP启动失败,请关闭进程重新打开使用'
                  })
                }
                if (result.available == true) {
                  let url = 'alipays://platformapi/startapp?saId=********&qrcode=' + res.data.thirdPartPayData.qrUrl
                  window.location.href = url
                  setTimeout(() => {
                    self.okPayMess()
                    self.orderNo = res.data.orderNo
                  }, 2000)
                } else {
                  self.$toast('未安装支付宝')
                }
              })
            } else {
              AlipayJSBridge.call('IsAvailable', {
                packageName: 'alipay://'
              }, function(result) {
                if (result.available == true) {
                  let url = 'alipays://platformapi/startapp?saId=********&qrcode=' + res.data.thirdPartPayData.qrUrl
                  window.location.href = url
                  setTimeout(() => {
                    self.okPayMess()
                    self.orderNo = res.data.orderNo
                  }, 2000)
                } else {
                  self.$toast('未安装支付宝')
                }
              })
            }
          } else if (self.paymentType == 3) { // 微信支付
            // 微信支付
            AlipayJSBridge.call(
              'WeChatPay', {
                nonceStr: res.data.thirdPartPayData.nonceStr,
                partnerid: res.data.thirdPartPayData.partnerid,
                prepayid: res.data.thirdPartPayData.prepayid,
                timeStamp: res.data.thirdPartPayData.timeStamp,
                packageStr: 'Sign=WXPay', // 固定值，以微信支付文档为主
                paySign: res.data.thirdPartPayData.paySign
              },
              function(result) {
                self.$toast('支付中')
                if (result.payResult == 'success') {
                  // 支付成功,跳转
                  self.$toast('支付成功')
                  self.amount = ''
                } else if (result.payResult == 'failed') {
                  // 支付失败跳转
                  self.$toast('支付失败')
                }
              }
            )
          } else if (self.paymentType == 4) { // 龙支付
            if (!isiOS) {
              AlipayJSBridge.call('IsAvailable', {
                packageName: 'com.ccb.loongpay'
              }, function(result) {
                if (result.error == 1) {
                  self.$toast({
                    duration: 5000, // 持续展示 toast
                    forbidClick: true,
                    message: 'APP启动失败,请关闭进程重新打开使用'
                  })
                }
                if (result.available == true) {
                  let nurl = res.data.thirdPartPayData.openAppUrl.slice(16)
                  let url = res.data.thirdPartPayData.lzfSchema + nurl
                  window.location.href = url
                  setTimeout(() => {
                    self.okPayMess()
                    self.orderNo = res.data.orderNo
                  }, 2000)
                } else {
                  self.$toast('未安装龙支付')
                }
              })
            } else {
              AlipayJSBridge.call('IsAvailable', {
                packageName: 'loongpayapp://payfrombrowser'
              }, function(result) {
                if (result.available == true) {
                  let nurl = res.data.thirdPartPayData.openAppUrl.slice(16)
                  let url = res.data.thirdPartPayData.lzfSchema + nurl
                  AlipayJSBridge.call('LongPay', {
                    urlStr: url
                  }, function(result) {})
                  setTimeout(() => {
                    self.okPayMess()
                    self.orderNo = res.data.orderNo
                  }, 2000)
                } else {
                  self.$toast('未安装龙支付')
                }
              })
            }
          }
        } else {
          self.$toast.clear()
          self.$toast(res.message)
        }
      })
    },
    //   确认支付提示
    okPayMess() {
      let self = this
      this.$dialog
        .confirm({
          title: '是否支付完成？',
          message: ''
        })
        .then(() => {
          self.appPay()
        })
        .catch(() => {
          self.appPay()
        })
    },
    // 加查支付
    appPay() {
      payLoading(self.orderNo).then(res => {
        if (res.status == 200) {
          if (res.data == true) {
            this.$toast('支付成功')
            this.amount = ''
          } else {
            this.$toast('支付失败')
          }
        } else {
          this.$toast(res.message)
        }
      })
    },
    goBack() {
      this.$router.go(-1)
    },
    ifPay() {
      ifPay().then((res) => {
        this.loadingShow = false
        if (res.status == 200) {
          this.paydata = res.data
        } else {
          this.$toast(res.message)
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
	.content {
        height: 100vh;
		background-color: #fff;
        ::v-deep .van-nav-bar__title {
            font-family:PingFangSC-Medium;
            font-weight: 500;
            color: #222222;
            font-size: 36px;
        }
        .line {
            background-color: #f5f5f5;
            height: 20px;
        }
		.title {
            font-size: 38px;
            font-family:  PingFangSC-Medium;
            font-weight: 500;
            color: #222222;
            margin: 48px 32px;
        }
        .inputBorder {
            width: 686px;
            height: 108px;
            border: 2px solid #333333;
            border-radius: 14px;
            margin:0  auto 16px;
            ::v-deep .van-field {
                // margin: 10px auto 0;
                margin-top: 10px;
                padding: 20px 28px;
            }
            ::v-deep .van-icon{
                font-size: 44px;
            }
            ::v-deep .van-cell__value--alone {
            font-size: 32px;
            }
        }

		::v-deep input::placeholder {
			font-size: 32px;
		}
		.payment {
			margin: -24px 32px 0;
			.payment-line {
				display: flex;
				justify-content: space-between;
				align-items: center;
				font-size: 32px;
				color: #000010;
				// height: 0.5rem;
				// line-height: 0.5rem;
				padding: 34px 0 34px 0;

				.line-left {
					display: flex;
					align-items: center;
					>span {
						margin-left: 24px;
                        color: #222222;
                        font-family: PingFangSC;
					}
				}

				.line-right {
					display: flex;
					align-items: center;

					>span {
						margin-right: 32px;
					}
				}

				::v-deep .van-radio__icon .van-icon {
					border: 1px solid #CCCCCC;
				}
			}
		}

		.btn {
            width: 452px;
            height: 88px;
            background: linear-gradient(90deg,#40d243, #1fc432);
            border-radius: 8px;
			line-height: 84px;
            font-size: 32px;
            font-family: PingFangSC-Medium;
            font-weight: 500;
			text-align: center;
			color: #ffffff;
			margin: 0 auto;
			margin-top: 224px;
		}

	}
</style>
