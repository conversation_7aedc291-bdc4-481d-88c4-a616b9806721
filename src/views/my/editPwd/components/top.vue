<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: zhaoyu<PERSON>
 * @Date: 2021-06-02 15:36:39
 * @LastEditors: 孙立政
 * @LastEditTime: 2021-08-12 10:43:09
-->
<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: zhaoyuxin
 * @Date: 2021-06-01 17:44:29
 * @LastEditors: zhaoyuxin
 * @LastEditTime: 2021-06-01 17:52:25
-->
<template>
  <div class="home">
    <van-nav-bar :title="message" left-text="" left-arrow>
      <template #left>
        <van-icon name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/user/icon/arrow-black.png" size="18" @click="goBack" />
      </template>
    </van-nav-bar>
  </div>
</template>

<script>
export default {
  props: {
    message: {
      type: String,
      default: ''
    }
  },
  data() {
    return {

    }
  },
  created() {

  },
  mounted() {

  },
  methods: {
    goBack() {
      if (this.$route.query.path == 'school') {
        this.$router.push('/index')
      } else {
        this.$router.go(-1)
      }
    }
  }
}
</script>

  <style scoped lang="scss">
      .home {
          ::v-deep .van-nav-bar__title {
              font-size: 36px;
              color: #222;
          }
      }
  </style>
