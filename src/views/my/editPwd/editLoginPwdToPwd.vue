<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-06-02 15:48:54
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-11-26 11:27:39
-->
<template>
  <div class="home">
    <NavHeight bgc="#fff" />
    <Top :message="message" />
    <div class="line20" />
    <van-field v-model="froms.tel" type="tel" label="" readonly placeholder="请输入手机号" clearable maxlength="11" />
    <van-field v-model="froms.oldPassword" type="password" label="" placeholder="请输入原密码" clearable maxlength="20" />
    <van-field v-model="froms.password" type="password" label="" placeholder="请输入登录密码" clearable maxlength="20" />
    <van-field v-model="froms.rpassword" type="password" label="" placeholder="请再次输入登录密码" clearable maxlength="20" />

    <!-- <van-field v-model="froms.password" disabled center clearable label="" placeholder="请输入登录密码" :type="hidden1?'password':'type'" maxlength="20" @click="pwd1">
      <template #button>
      </template>
    </van-field>
    <van-field v-model="froms.rpassword" disabled center clearable label="" placeholder="请再次输入登录密码" :type="hidden2?'password':'type'" maxlength="20" @click="pwd2">
      <template #button>
      </template>
    </van-field> -->
    <div class="submit" :class="['submit',showBtn?'green':'']" @click="submit">确认</div>

  </div>
</template>

<script>
import Top from './components/top'
import { editPwdLoginPwd } from '@/api/login'
export default {
  components: {
    Top
  },
  data() {
    return {
      message: '修改登录密码',
      froms: {
        tel: '',
        oldPassword: '',
        password: '',
        rpassword: ''
      },
      openIcon: 'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/my/pwd_open.png',
      closeIcon: 'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/my/pwd_close.png',
      hidden1: true,
      hidden2: true,
      show: true,
      count: '',
      showBtn: false,
      timer: '',
      defaultVal: '',
      length: 50,
      password: '',
      ischang: 1
    }
  },
  watch: {
    password(val) {
      if (this.ischang == 1) {
        this.froms.password = val
      } else {
        this.froms.rpassword = val
      }
    }
  },
  created() {
    this.froms.tel = localStorage.getItem('phone')
  },
  mounted() {
    AlipayJSBridge.call('OnScreenshot', {}, function(result) {
      console.log(result)
    })
  },
  destroyed() {
    AlipayJSBridge.call('OffScreenshot', {}, function(result) {
      console.log(result)
    })
  },
  methods: {
    pwd1() {
      this.ischang = 1
      this.password = ''
    },
    pwd2() {
      this.ischang = 2
      this.password = ''
    },
    // 确认
    submit() {
      let self = this
      if (!/^(?!([a-zA-Z]+|\d+)$)[a-zA-Z\d]{6,20}$/.test(this.froms.password)) {
        this.$toast('请输入6-20新密码同时包含数字和字母')
        return false
      }
      if (this.froms.password != this.froms.rpassword) {
        this.$toast('两次密码输入的不一致')
        return false
      }
      let data = {
        telephone: this.froms.tel,
        password: this.froms.password,
        oldPassword: this.froms.oldPassword
      }
      editPwdLoginPwd(data)
        .then((res) => {
          if (res.status == 200) {
            this.$toast('操作成功')
            setTimeout(() => {
              self.$router.push('/pwdLogin')
            }, 1000)
          } else {
            this.$toast(res.message)
          }
        })
    },
    change(type) {
      if (type == 1) {
        this.hidden1 = !this.hidden1
      } else {
        this.hidden2 = !this.hidden2
      }
    }
  }
}
</script>

<style scoped lang="scss">
    .home {
        ::v-deep .van-cell {
            padding: 21px 32px;
        }
        ::v-deep .van-button--primary {
            width: 160px;
            height: 56px;
            background: linear-gradient(90deg,#40d243, #1fc432);
            border-radius: 12px;
        }
        .submit {
            width: 686px;
            height: 88px;
            line-height: 88px;
            text-align: center;
            background: #dcdcdc;
            border-radius: 8px;
            color: #fff;
            font-size: 32px;
            margin: 127px auto 0;
        }
        .green {
            background: linear-gradient(90deg,#40d243, #1fc432);
        }
        .line20 {
            height:20px;
            background:#F5F5F5
        }
    }
</style>
