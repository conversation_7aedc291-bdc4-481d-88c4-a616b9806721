<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-06-04 11:51:57
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-11-26 11:23:41
-->
<template>
  <!-- 个人中心 -->
  <div class="content">
    <NavHeight bgc="#fff" />
    <!-- 头部 -->
    <van-nav-bar left-arrow :border="false">
      <template #title>
        <div class="topTitle">账号与安全</div>
      </template>
      <template #left>
        <van-icon name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/user/icon/arrow-black.png" size="20" @click="goBack" />
      </template>
    </van-nav-bar>

    <div class="editList">
      <ul class="list">
        <li class="item itemList" @click="goTo('EditLoginPwdToPwd')">
          <div>
            <div class="itemName">使用密码修改</div>
          </div>
          <div>
            <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/my/edit/right.png" alt="">
          </div>
        </li>
        <li class="item itemList" @click="goTo('EditLoginPwd')">
          <div>
            <div class="itemName">使用手机验证码修改</div>
          </div>
          <div>
            <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/my/edit/right.png" alt="">
          </div>
        </li>
      </ul>
    </div>
  </div>
</template>

<script>
export default {
  components: {
  },
  data() {
    return {}
  },
  created() {
    this.getWechatUnbinding()
  },
  mounted() {

  },
  methods: {
    goTo(path) {
      this.$router.push({
        name: path
      })
    },
    goBack() {
      this.$router.go(-1)
    }
  }
}
</script>

  <style lang="scss" scoped>
  .content {
    font-family: PingFangSC;
    .editListTitle{
      font-size: 30px;
      margin-top: 30px;
      margin-left: 35px;
    }
    .editList{
      .list{
        width: 100%;
        background-color: #fff;
        margin-top: 20px;
        .item{
          width: 686px;
          height: 128px;
          display: flex;
          justify-content: space-between;
          font-size: 32px;
          margin: 0 auto;
          .itemName{
            color: #222222;
            font-size: 32px;
            margin-top: 20px;
          }
          .itemTag{
            font-size: 26px;
            color: #999999;
          }
          .editListRight{
            display: flex;
            .watchatName{
              margin-top: 23px;
              margin-right: 15px;
              color: #999;
            }
            .checkedys{
              margin-top: 20px;
            }
          }
          img{
            width: 30px;
            height: 30px;
            margin-top: 49px;
          }
        }
        .itemList{
          height: 88px;
          img{
            margin-top: 32px;
          }
        }
      }

    }
    .topTitle{
      color: #222222;
      font-size: 36px;
      font-family: PingFangSC;
    }
  }
  </style>
