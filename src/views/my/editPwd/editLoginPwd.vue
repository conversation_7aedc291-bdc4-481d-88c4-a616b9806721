<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-06-02 15:48:54
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-11-26 11:07:46
-->
<template>
  <div class="home">
    <NavHeight bgc="#fff" />
    <Top :message="message" />
    <div class="line20" />
    <van-field v-model="froms.tel" type="tel" label="" readonly placeholder="请输入手机号" clearable maxlength="11" />
    <van-field v-model="froms.code" center type="digit" clearable label="" placeholder="请输入短信验证码" maxlength="6" @input="getShowBtn">
      <template #button>
        <van-button v-if="!show" size="small" type="default">已发送{{ count }}s</van-button>
        <van-button v-else size="small" type="primary" @click="getcode">发送验证码</van-button>
      </template>
    </van-field>
    <van-field v-model="froms.password" center clearable label="" placeholder="请输入登录密码" :type="hidden1?'password':'type'" maxlength="20">
      <template #button>
        <!-- <img :src="hidden1?closeIcon:openIcon" alt="" style="width:18px;height:14px" @click="change(1)"> -->
      </template>
    </van-field>
    <van-field v-model="froms.rpassword" center clearable label="" placeholder="请再次输入登录密码" :type="hidden2?'password':'type'" maxlength="20">
      <template #button>
        <!-- <img :src="hidden2?closeIcon:openIcon" alt="" style="width:18px;height:14px" @click="change(2)"> -->
      </template>
    </van-field>
    <div class="submit" :class="['submit',showBtn?'green':'']" @click="submit">确认</div>

    <div id="app" @dblclick="() => {return false;}">
      <div class="cus-keyboard">
        <Keyboard ref="showKeyboard" :length="length" :default-val="defaultVal" :text.sync="password" :keyboard-type="0" />
      </div>
    </div>
  </div>
</template>

<script>
import Top from './components/top'
import { resetLoginPwd, smsCode } from '@/api/login'
// import { UserInfo } from '@/api/my'
import Keyboard from '@/components/Keyboard'
export default {
  components: {
    Top, Keyboard
  },
  data() {
    return {
      message: '修改登录密码',
      froms: {
        tel: '',
        code: '',
        password: '',
        rpassword: ''
      },
      openIcon: 'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/my/pwd_open.png',
      closeIcon: 'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/my/pwd_close.png',
      hidden1: true,
      hidden2: true,
      show: true,
      count: '',
      showBtn: false,
      timer: '',
      defaultVal: '',
      length: 50,
      password: '',
      ischang: 1
    }
  },
  watch: {
    password(val) {
      if (this.ischang == 1) {
        this.froms.password = val
      } else {
        this.froms.rpassword = val
      }
    }
  },
  created() {
    this.getUserInfo()
  },
  mounted() {
    AlipayJSBridge.call('OnScreenshot', {}, function(result) {
      console.log(result)
    })
  },
  destroyed() {
    AlipayJSBridge.call('OffScreenshot', {}, function(result) {
      console.log(result)
    })
  },
  methods: {
    pwd1() {
      this.ischang = 1
      this.password = ''
      this.$refs.showKeyboard.show()
    },
    pwd2() {
      this.ischang = 2
      this.password = ''
      this.$refs.showKeyboard.show()
    },
    // 获取账户信息
    getUserInfo() {
      this.froms.tel = localStorage.getItem('phone')
      // let data = this.$store.getters.getUserId
      // UserInfo(data)
      //   .then((res) => {
      //     if (res.status == 200) {
      //       this.froms.tel = res.data.telephone
      //     }
      //   })
    },
    getcode() {
      var reg = /^1[3456789]\d{9}$/
      if (this.froms.tel == '') {
        this.$toast('手机号码不能为空！')
        return false
      }
      if (!reg.test(this.froms.tel)) {
        this.$toast('请输入有效的手机号码')
        return false
      }
      this.sendyzm()
    },
    // 发送验证码
    sendyzm() {
      let data = {
        tel: this.froms.tel,
        name: 'changePassword'
      }
      smsCode(data)
        .then((res) => {
          if (res.status == 200) {
            const TIME_COUNT = 60
            if (!this.timer) {
              this.count = TIME_COUNT
              this.show = false
              this.timer = setInterval(() => {
                if (this.count > 0 && this.count <= TIME_COUNT) {
                  this.count--
                } else {
                  this.show = true
                  this.ShowYzm = true
                  clearInterval(this.timer)
                  this.timer = null
                }
              }, 1000)
            }
            this.$toast('短信已发送，有效期5分钟')
          } else {
            this.$toast(res.message)
            this.show = true
            this.count = ''
            this.timer = null
          }
        })
    },
    // 确认
    submit() {
      let self = this
      if (this.froms.code == '') {
        this.$toast('请正确填写验证码')
        return false
      }
      if (!/^(?!([a-zA-Z]+|\d+)$)[a-zA-Z\d]{6,20}$/.test(this.froms.password)) {
        this.$toast('请输入6-20新密码同时包含数字和字母')
        return false
      }
      if (this.froms.password != this.froms.rpassword) {
        this.$toast('两次密码输入的不一致')
        return false
      }
      let data = {
        telephone: this.froms.tel,
        password: this.froms.password,
        code: this.froms.code
      }
      resetLoginPwd(data)
        .then((res) => {
          if (res.status == 200) {
            this.$toast('操作成功')
            setTimeout(() => {
              self.$router.push('/pwdLogin')
            }, 1000)
          } else {
            this.$toast(res.message)
          }
        })
    },
    getShowBtn() {
      if (this.froms.tel.length != 0 && this.froms.code.length != 0) {
        this.showBtn = true
      } else {
        this.showBtn = false
      }
    },
    change(type) {
      if (type == 1) {
        this.hidden1 = !this.hidden1
      } else {
        this.hidden2 = !this.hidden2
      }
    }
  }
}
</script>

<style scoped lang="scss">
    .home {
        ::v-deep .van-cell {
            padding: 21px 32px;
        }
        ::v-deep .van-button--primary {
            width: 160px;
            height: 56px;
            background: linear-gradient(90deg,#40d243, #1fc432);
            border-radius: 12px;
        }
        .submit {
            width: 686px;
            height: 88px;
            line-height: 88px;
            text-align: center;
            background: #dcdcdc;
            border-radius: 8px;
            color: #fff;
            font-size: 32px;
            margin: 127px auto 0;
        }
        .green {
            background: linear-gradient(90deg,#40d243, #1fc432);
        }
        .line20 {
            height:20px;
            background:#F5F5F5
        }
    }
</style>
