<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-06-02 15:48:54
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-03-09 10:18:29
-->
<template>
  <div class="cancel">
    <NavHeight bgc="#fff" />
    <Top :message="message" />
    <div class="box">
      <div class="top-title">
        <div>您正在申请</div>
        <div>注销点滴APP账号</div>
      </div>
      <div class="top-msg">
        你提交的注销申请生效前，点滴团队将进行以下验证，已保证你的账号、财产安全：
      </div>
      <div class="top-list">
        <div class="top-list-title">1、账户不存在未完成的订单：</div>
        <div class="top-list-msg">
          账户订单全部已完成，没有退货中、待收货、待自取等进行中的订单。
        </div>
      </div>
      <div class="top-list">
        <div class="top-list-title">2、账户相关财产已结清：</div>
        <div class="top-list-msg">
          账户余额为0；余额账户没有资金问题（待付款）；拥有二类账户不支持注销。
        </div>
      </div>
      <div class="formShopMsg">
        <van-checkbox v-model="checkedAgreement" />
        <div>
          同意<span style="color:#2E84DB" @click="goCancell">《点滴平台账户注销重要提示》</span>
        </div>
      </div>
      <div class="submit" :class="['submit',checkedAgreement?'green':'']" @click="setCancell">申请注销</div>
    </div>

  </div>
</template>

<script>
import Top from './components/top'
import { userLogout } from '@/api/my'
// var sensors = window['sensorsDataAnalytic201505']
export default {
  components: {
    Top
  },
  data() {
    return {
      message: '注销账号',
      checkedAgreement: false
    }
  },
  created() {
  },
  mounted() {

  },
  methods: {
    setCancell() {
      let self = this
      if (this.checkedAgreement == false) {
        this.$toast('不同意相关注销协议，无法申请！')
        return
      }
      this.$dialog.confirm({
        title: '确认',
        message: '是否确认注销点滴APP账户!'
      }).then(() => {
        userLogout().then((res) => {
          if (res.status == 200) {
            self.$toast('注销成功！')
            setTimeout(() => {
              self.logoutBtn()
            }, 1000)
          }
        })
      })

      // sensors.track('BuyProduct', {
      //   customData: 'cancellation',
      //   custom: 3
      // })
    },
    goCancell() {
      this.$router.push('/cancellationMsg')
    },
    logoutBtn() {
      localStorage.removeItem('token')
      localStorage.removeItem('userId')
      localStorage.removeItem('headImg')
      localStorage.removeItem('advert')
      localStorage.removeItem('phone')
      this.$store.state.My.headImg = ''
      this.$store.state.token = ''
      this.$store.state.userId = ''
      this.$store.state.phone = ''
      this.$store.state.order.orderList = []
      this.$store.state.My.blance = 0
      this.$store.state.market.marketData.balance = ''
      this.$store.state.market.marketData.payradio = ''
      this.$store.state.market.marketData.postFee = 0
      this.$router.push({ name: 'wxLogin2' })
      AlipayJSBridge.call('UserLogout', {}, function(result) {})
    }
  }
}
</script>

<style scoped lang="scss">
    .cancel {
        height: 100vh;
        background-color: #fff;
        .box{
            width: 85%;
            margin: 0 auto;
        }
        .top-title{
            font-family:PingFangSC-Medium;
            font-size: 50px;
            margin-top: 60px;
        }
        .top-msg{
            font-size: 28px;
            margin-top: 30px;
            color: #999;
        }
        .top-list{
            margin-top: 38px;
            .top-list-title{
                font-size: 36px;
            }
            .top-list-msg{
                font-size: 26px;
                margin-top: 15px;
                color: #999;
            }
        }
        .formShopMsg{
            display: flex;
            height: 70px;
            line-height: 70px;
            font-size: 28px;
            margin-top: 60px;
            ::v-deep .van-checkbox__icon .van-icon{
              width: 26px;
              height: 26px;
              line-height: 26px;
              margin-top: 6px;
              margin-right: 10px;
              font-size: 20px;
            }
            ::v-deep .van-checkbox__icon--checked .van-icon {
                color: #3DCF43;
                background-color: #fff;
                border-color: #3DCF43;
            }
        }
        .submit {
            width: 86%;
            height: 88px;
            line-height: 88px;
            text-align: center;
            background: #dcdcdc;
            border-radius: 8px;
            color: #fff;
            font-size: 32px;
            margin: 0 auto;
            margin-top: 80px;
        }
        .green {
            background: linear-gradient(90deg,#40d243, #1fc432);
        }
        .line20 {
            height:20px;
            background:#F5F5F5
        }
    }
</style>
