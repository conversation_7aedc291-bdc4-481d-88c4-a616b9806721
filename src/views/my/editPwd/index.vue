<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-06-04 11:51:57
 * @LastEditors: 孙立政
 * @LastEditTime: 2023-05-23 17:10:41
-->
<template>
  <!-- 个人中心 -->
  <div class="content">
    <NavHeight bgc="#fff" />
    <!-- 头部 -->
    <van-nav-bar left-arrow :border="false">
      <template #title>
        <div class="topTitle">账号与安全</div>
      </template>
      <template #left>
        <van-icon name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/user/icon/arrow-black.png" size="20" @click="goBack" />
      </template>
    </van-nav-bar>

    <div class="editList">
      <ul class="list">
        <li class="item itemList" @click="goTo('EditLoginPwdList')">
          <div>
            <div class="itemName">登录密码</div>
          </div>
          <div>
            <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/my/edit/right.png" alt="">
          </div>
        </li>
        <li class="item itemList" @click="goTo('EditPayPwd')">
          <div>
            <div class="itemName">支付密码</div>
          </div>
          <div>
            <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/my/edit/right.png" alt="">
          </div>
        </li>
      </ul>
    </div>
    <div class="editListTitle">推荐管理</div>
    <div class="editList">
      <ul class="list">
        <li class="item itemList">
          <div>
            <div class="itemName">个性化内容/广告推荐</div>
          </div>
          <div class="editListRight">
            <van-switch v-model="checkedys" class="checkedys" size="22px" @change="ysChange" />
          </div>
        </li>
      </ul>
    </div>
    <div class="editListTitle">绑定信息</div>
    <div class="editList">
      <ul class="list">
        <li class="item itemList">
          <div>
            <div class="itemName">微信账号</div>
          </div>
          <div class="editListRight" @click="setWechatUnbinding">
            <div class="watchatName">{{ watchatName }}</div>
            <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/my/edit/right.png" alt="">
          </div>
        </li>
      </ul>
    </div>
    <div class="editList">
      <ul class="list">
        <li class="item itemList" @click="otherBind">
          <div>
            <div class="itemName">天工后勤账号</div>
          </div>
          <div class="editListRight">
            <div class="watchatName">{{ acctSyncCheckStatus?'已绑定':'未绑定' }}</div>
            <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/my/edit/right.png" alt="">
          </div>
        </li>
      </ul>
    </div>
    <div class="editList">
      <ul class="list">
        <li class="item itemList" @click="cancellation()">
          <div>
            <div class="itemName">账号注销</div>
          </div>
          <div>
            <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/my/edit/right.png" alt="">
          </div>
        </li>
      </ul>
    </div>
  </div>
</template>

<script>
import { environment } from '@/api/index'
import { wechatUnbinding, getWechatUnbinding, accountSync, acctSyncCheck } from '@/api/login'
export default {
  components: {
  },
  data() {
    return {
      watchatName: '未绑定',
      unionId: '',
      checkedys: false,
      acctSyncCheckStatus: false
    }
  },
  created() {
    this.getWechatUnbinding()
    // 检查三方账号是否绑定
    this.acctSyncCheck()
  },
  mounted() {
    // this.bind()

    let self = this
    localStorage.setItem('isWxBind', 1)
    document.addEventListener('mobSever', function(e) {
      if (e.data.data) {
        self.$store.state.login.wx.nickname = e.data.data.nickname
        self.$store.state.login.wx.headimgurl = e.data.data.headimgurl
        self.$store.state.login.wx.openid = e.data.data.openid
        self.$store.state.login.wx.unionid = e.data.data.unionid
        self.$store.state.login.wx.loginNum = self.$store.state.login.wx.loginNum + 1
        console.log('安卓6666微信登录')
        // self.$router.push({ name: 'WxLogin' })
        self.$store.state.login.telephone = localStorage.getItem('phone')
        self.$router.push({ name: 'WxLoginCode', query: { telephone: localStorage.getItem('phone') }})
        self.$toast.clear()
        return false
      } else {
        self.$toast('微信登录失败')
      }

      console.log('====微信返回====')
      console.log(e)
      console.log('====微信end====')
    })
    console.log(localStorage.getItem('privacyStatus'))
    if (localStorage.getItem('privacyStatus') == 'true') {
      this.checkedys = false
    } else {
      this.checkedys = true
    }
  },
  methods: {
    // 隐私按钮
    ysChange(val) {
      let data = { 'userId': this.$store.getters.getUserId, 'note': val }
      environment(data).then((res) => {
        if (res.status == 200) {
          if (localStorage.getItem('privacyStatus') == 'true') {
            localStorage.setItem('privacyStatus', false)
          } else {
            localStorage.setItem('privacyStatus', true)
          }

          this.$toast(res.message)
        }
      })
    },
    // 绑定/解绑微信
    setWechatUnbinding() {
      if (this.unionId != '') {
        this.wechatUnbinding()
      } else {
        // this.$router.push({ name: 'WxLogin' })
        this.agree()
      }
    },
    // 解绑微信
    wechatUnbinding() {
      this.$dialog.confirm({
        message: '要解除和微信账号的绑定吗？',
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }).then(() => {
        wechatUnbinding(this.unionId).then(res => {
          if (res.status == 200) {
            this.$toast(res.message)
            this.getWechatUnbinding()
            AlipayJSBridge.call('RestartApp', {}, function(result) {})
          }
        })
      })
    },
    // 三方账号同步
    otherBind() {
      if (this.acctSyncCheckStatus) {
        this.$toast('已绑定成功，无需重复绑定')
        return false
      }
      this.$toast.loading({ duration: 0, forbidClick: true })
      accountSync().then(res => {
        this.$toast.clear()
        if (res.status == 200 && res.data == true) {
          this.$toast('绑定成功')
          this.acctSyncCheck()
        } else {
          this.$toast('绑定失败')
        }
      })
    },
    // 检查三方账号是否绑定
    acctSyncCheck() {
      acctSyncCheck().then(res => {
        if (res.status == 200) {
          this.acctSyncCheckStatus = res.data
        }
      })
    },
    // 查询微信绑定
    getWechatUnbinding() {
      getWechatUnbinding(localStorage.getItem('phone')).then(res => {
        console.log(res)
        if (res.status == 200 && res.data != null) {
          this.watchatName = res.data.nickName
          this.unionId = res.data.unionId
        } else {
          this.watchatName = '未绑定'
          this.unionId = ''
        }
      })
    },
    cancellation() {
      this.$router.push({
        name: 'Cancellation'
      })
    },
    goTo(data) {
      if (data == 'EditLoginPwdList') {
        this.$router.push({
          name: 'EditLoginPwdList'
        })
      } else {
        this.$router.push({
          name: data
        })
      }
    },
    cancel() {
      this.loginShow = false
    },
    goBack() {
      this.$router.go(-1)
    },
    agree() {
      let self = this
      this.$toast.loading({ duration: 0, forbidClick: true })
      AlipayJSBridge.call('WatchLogin', {}, function(result) {
        self.$toast.clear()
        console.log('====微信返回ios====')
        console.log(result)
        console.log('====微信end====')
        var u = navigator.userAgent
        var isiOS = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/) // ios终端
        if (isiOS) {
          if (result.success == true) {
            self.$store.state.login.wx.nickname = result.data.nickname
            self.$store.state.login.wx.headimgurl = result.data.headimgurl
            self.$store.state.login.wx.openid = result.data.openid
            self.$store.state.login.wx.unionid = result.data.unionid
            // self.$router.push({ name: 'WxLogin' })
            self.$store.state.login.telephone = localStorage.getItem('phone')
            self.$router.push({ name: 'WxLoginCode', query: { telephone: localStorage.getItem('phone') }})
          } else {
            self.$toast('登录失败')
          }
        }
      })
    },
    bind() {

    }
  }
}
</script>

  <style lang="scss" scoped>
  .content {
    font-family: PingFangSC;
    .editListTitle{
      font-size: 30px;
      margin-top: 30px;
      margin-left: 35px;
    }
    .editList{
      .list{
        width: 100%;
        background-color: #fff;
        margin-top: 20px;
        .item{
          width: 686px;
          height: 128px;
          display: flex;
          justify-content: space-between;
          font-size: 32px;
          margin: 0 auto;
          .itemName{
            color: #222222;
            font-size: 32px;
            margin-top: 20px;
          }
          .itemTag{
            font-size: 26px;
            color: #999999;
          }
          .editListRight{
            display: flex;
            .watchatName{
              margin-top: 23px;
              margin-right: 15px;
              color: #999;
            }
            .checkedys{
              margin-top: 20px;
            }
          }
          img{
            width: 30px;
            height: 30px;
            margin-top: 49px;
          }
        }
        .itemList{
          height: 88px;
          img{
            margin-top: 32px;
          }
        }
      }

    }
    .topTitle{
      color: #222222;
      font-size: 36px;
      font-family: PingFangSC;
    }
  }
  </style>
