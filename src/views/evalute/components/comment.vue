<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-04-24 14:52:58
 * @LastEditors: 孙立政
 * @LastEditTime: 2021-12-08 10:01:35
-->
<template>
  <div class="cell">
    <div class="comment">
      <!-- 骑手 -->
      <div v-if="form.orderNo&&$route.query.deliveryType!=2" class="comment-horse">
        <div class="horse-line1">
          <div v-if="deliverPart==2">您对骑手满意吗？</div>
          <div v-else>您对商家配送满意吗？</div>
          <div class="horse-line1-assess">匿名评价</div>
        </div>
        <div class="horse-line2">
          <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/user/takeout/take.png" alt="" class="horse-line-img">
          <div>
            <div class="horse-line2-name">{{ deliverPart==2?'骑手配送':'商家配送' }}</div>
            <div class="horse-line2-time">送达时间：{{ $route.query.updateTime }}</div>
          </div>
        </div>
        <div class="horse-line3">
          <div
            v-for="item in emojiList"
            :key="item.id"
            class="horse-line3-btn"
            :class="{activebg:form.evaluate==item.id}"
            @click="clickBtn(item.id)"
          >
            <img v-if="form.evaluate==item.id" :src="item.active" alt="" class="horse-line-emoji">
            <img v-else :src="item.default" alt="" class="horse-line-emoji">
            <span v-if="item.id==1">不满意</span>
            <span v-if="item.id==2">一般</span>
            <span v-if="item.id==3">满意</span>
          </div>
        </div>
      </div>
      <!-- 菜品外卖评价 -->
      <div class="comment-takeout">
        <div class="takeout-line1">
          <div>您对商家/菜品满意吗？</div>
          <div class="takeout-line1-assess">
            <van-checkbox v-model="form.isAnonymous" checked-color="#169d1b" label-position="left" icon-size="18px">匿名提交</van-checkbox>
          </div>
        </div>
        <div class="takeout-line2">
          <img :src="shopData.pic" alt="" class="takeout-line-img">
          <div>
            <div class="takeout-line2-name">{{ shopData.marketName }}</div>
          </div>
        </div>
        <div class="takeout-line3">
          <div class="takeout-line3-rate1">
            <div class="takeout-line3-label">总体评价</div>
            <van-rate
              v-model="form.score"
              class="starts"
              allow-half
              :size="24"
              color="#ffd21e"
              void-icon="star"
              void-color="#eee"
              @change="onChange1"
            />
            <div class="takeout-line3-label1">{{ form.score | satisfied }}</div>
          </div>

          <div v-for="(item,index) in form.tbMarketEvaluations" :key="index" class="takeout-line3-rate2">
            <div class="takeout-line3-label">{{ item.evaTypeName }}</div>
            <van-rate
              v-model="item.score"
              class="starts"
              allow-half
              :size="20"
              color="#ffd21e"
              void-icon="star"
              void-color="#eee"
            />
            <div class="takeout-line3-label1">{{ item.score | satisfied }}</div>
          </div>
        </div>
        <div class="takeout-line4">
          <van-field
            v-model="form.tbEvaluationContents[0].content"
            rows="7"
            autosize
            label=""
            type="textarea"
            maxlength="200"
            placeholder="请描述您的体验，您的评价是对商家最到的支持！"
            show-word-limit
          />
        </div>
        <div class="takeout-line5">
          <van-uploader
            v-model="pictures"
            :before-delete="afterDelete"
            :before-read="beforeRead"
            max-count="3"
            multiple="false"
          />
          <div class="takeout-line5-tips">最多上传3张图，有机会被评为推荐好评</div>
        </div>
      </div>
      <div class="btn" @click="goNext()">立即评价</div>
    </div>
    <!-- 提示弹框 -->
    <van-dialog v-model="show" title="您的评论已提交！" confirm-button-color="#1FC432" @confirm="confirmBtn">
      <div style="margin:15px auto;text-align:center">系统审核通过后将会在店铺评价里面显示</div>
    </van-dialog>
    <!-- 离开弹框 -->
    <van-dialog
      v-model="backshow"
      title="确定要离开？"
      :show-cancel-button="false"
      :show-confirm-button="false"
      @confirm="confirmBtn"
    >
      <div style="margin:15px auto;text-align:center">现在离开，已填写评价内容将会取消</div>
      <div class="backshow-btn">
        <div class="cancel" @click="cancel">取消</div>
        <div class="confirm" @click="confirm">确认</div>
      </div>
    </van-dialog>
  </div>
</template>

<script>
import OSS from 'ali-oss'
import { addEvaluate, marketEvaluationType } from '@/api/evaluate'
// import { upload } from '@/api/my'
import { getShopData } from '@/api/takeout'
export default {
  filters: {
    satisfied(value) {
      if (value > 0) {
        if (value <= 1) {
          return '非常差'
        } else if (value <= 2) {
          return '差'
        } else if (value <= 3) {
          return '一般'
        } else if (value <= 4) {
          return '满意'
        } else if (value <= 5) {
          return '非常满意'
        }
      }
    }
  },
  data() {
    return {
      shopData: {
        marketName: '',
        pic: '',
        type: ''
      },
      form: {
        evaTargetType: 1,
        evaluate: -1,
        isAnonymous: false,
        marketType: null,
        orderNo: this.$route.query.orderNo,
        orderType: 7,
        relatedId: this.$route.query.marketId,
        score: 0,
        tbEvaluationContents: [{ content: '' }],
        tbMarketEvaluations: [],
        pictures: []
      },
      pictures: [],
      deliverPart: this.$route.query.orderNo,
      emojiList: [{
        id: 1,
        default: 'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/review/angry.png',
        active: 'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/review/angry-active.png'
      },
      {
        id: 2,
        default: 'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/review/just.png',
        active: 'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/review/just-active.png'
      },
      {
        id: 3,
        default: 'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/review/happy.png',
        active: 'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/review/happy-active.png'
      }
      ],
      show: false,
      backshow: false,
      type: 1, // 假定1表示外卖，2表示非外卖
      imgType: '',
      maxSize: 0.5
    }
  },
  created() {
    this.getShopData()
  },
  mounted() {

  },
  methods: {
    // 获取店铺详情
    getShopData() {
      getShopData(this.$route.query.marketId).then((res) => {
        this.shopData = res.data
        this.form.marketType = res.data.type
      })
    },
    // 骑手满意度
    clickBtn(id) {
      this.form.evaluate = id
    },
    // 总体评分获取
    onChange1() {
      this.$toast.loading({
        duration: 0, // 持续展示 toast
        forbidClick: true,
        message: ''
      })
      let data = {
        marketType: this.shopData.type,
        orderType: this.$route.query.orderNo ? 7 : 3
      }
      marketEvaluationType(data).then(res => {
        this.$toast.clear()
        if (res.status == 200) {
          if (res.data != null) {
            let data = res.data
            for (let i = 0; i < data.length; i++) {
              data[i].evaTypeId = data[i].id
            }
            this.form.tbMarketEvaluations = data
          }
        }
      })
    },
    afterDelete(index) {
      const findIdx = this.pictures.findIndex((item) => item.url == index.url)
      this.pictures.splice(findIdx, 1)
      this.form.pictures.splice(findIdx, 1)
    },
    // 图片上传接口
    afterRead(file) {
      let self = this
      this.$toast.loading({
        duration: 0, // 持续展示 toast
        forbidClick: true,
        message: '上传中'
      })
      let formData = new FormData()
      formData.append('file', file)
      let client = new OSS({
        region: 'oss-cn-hangzhou',
        accessKeyId: 'LTAI4GHzemXAwKszXKiniT9s',
        accessKeySecret: '******************************',
        bucket: 'diandi-video'
      })
      let now = Date.parse(new Date())
      let names = now + file.name
      client.put('face/' + names, file)
        .then(function(res) {
          self.$toast.clear()

          self.pictures.push({
            url: res.url
          })
          self.form.pictures.push(res.url)
          return false
        })
    },
    beforeRead(file) {
      let self = this
      self.imgType = file.type
      let size = file.size / 1024
      if (size <= self.maxSize) {
        // 直接上传
        self.afterRead(file)
      } else {
        // 对图片进行压缩
        self.imgPreview(file)
      }
    },
    // 将图片转成 base64 格式
    imgPreview(file) {
      let self = this
      // 看支持不支持FileReader
      if (!file || !window.FileReader) return
      let reader = new FileReader()
      reader.readAsDataURL(file)
      reader.onloadend = function() {
        // 此处的this是reader
        let result = this.result
        self.compress(result)
      }
    },
    // 压缩图片
    compress(base64) {
      let cvs = document.createElement('canvas')
      let img = document.createElement('img')
      img.crossOrigin = 'anonymous'
      img.src = base64
      // 图片偏移值
      // eslint-disable-next-line no-unused-vars
      let offetX = 0
      img.onload = () => {
        if (img.width > 800) {
          cvs.width = 800
          cvs.height = (img.height * 800) / img.width
          offetX = (img.width - 800) / 2
        } else {
          cvs.width = img.width
          cvs.height = img.height
        }
        // eslint-disable-next-line no-unused-vars
        let ctx = cvs
          .getContext('2d')
          .drawImage(img, 0, 0, cvs.width, cvs.height)
        let imageData = cvs.toDataURL(this.imgType, 0.85)
        this.convertBase64UrlToBlob(imageData)
      }
    },
    // 将base64转为文件流
    convertBase64UrlToBlob(imageData) {
      let filename = ''
      let arr = imageData.split(',')
      let mime = arr[0].match(/:(.*?);/)[1]
      if (!filename) {
        filename = `${new Date().getTime()}.${mime.substr(
          mime.indexOf('/') + 1
        )}`
      }
      let bstr = atob(arr[1])
      let n = bstr.length
      let u8arr = new Uint8Array(n)
      while (n--) {
        u8arr[n] = bstr.charCodeAt(n)
      }
      this.afterRead(
        new File([u8arr], filename, {
          type: mime
        })
      )
    },
    confirmBtn() {
      this.$router.go(-1)
    },
    // 确认添加
    goNext() {
      // 处理图片
      this.form.pictures = this.form.pictures.toString()
      if (this.$route.query.orderNo) {
        this.form.orderType = 7
      } else {
        this.form.orderType = 3
      }
      // 提交
      console.log(this.isRule())
      if (!this.isRule()) {
        return
      }
      addEvaluate(this.form).then(res => {
        if (res.status == 200) {
          this.show = true
        } else {
          this.$toast(res.message)
        }
      })
    },
    isRule() {
      if (this.form.score == 0 || this.form.tbEvaluationContents[0].content == '') {
        if (this.form.evaluate < 0) {
          this.$toast('内容填写不完整')
          return false
        } else {
          return true
        }
      } else {
        return true
      }
    },
    cancel() {
      this.backshow = false
    },
    confirm() {
      this.backshow = false
    }
  }
}
</script>

<style scoped lang="scss">
.cell {
  padding-bottom: 40px;
	.comment {
		.comment-horse {
			width: 710px;
			background-color: #fff;
			border-radius:24px;
			margin: 20px auto;
			padding: 0 20px 20px;
			box-sizing: border-box;

			.horse-line1 {
				display: flex;
				justify-content: space-between;
				align-items: center;
				border-bottom: 1px solid #f4f4f4;
				height:93px;
				font-size: 34px;
				color: #222;
				font-family: PingFangSC, PingFangSC-Medium;

				.horse-line1-assess {
					font-size: 24px;
					color: #666;
				}
			}

			.horse-line2 {
				display: flex;
				margin-top: 40px;
				margin-bottom: 36px;

				.horse-line-img {
					width: 72px;
					height: 72px;
					margin-right: 12px;
				}

				.horse-line2-name {
					font-size: 30px;
					margin-right: 4px;
					margin-top: - 3px;
				}

				.horse-line2-time {
					font-size:22px;
					color: #666;
				}
			}

			.horse-line3 {
				display: flex;
				justify-content: space-between;

				.horse-line3-btn {
					display: flex;
					justify-content: center;
					align-items: center;
					width: 216px;
					height:72px;
					border-radius: 8px;
					background-color: #f5f6f7;
					font-size:26px;
					color: #333;

					.horse-line-emoji {
						width:60px;
						height: 60px;
					}
				}

				.activebg {
					background-color: #FFF9E0;
				}
			}
		}

		.comment-takeout {
			width: 710px;
			background-color: #fff;
			border-radius: 24px;
			margin: 20px auto;
			padding: 0 20px 40px;
			box-sizing: border-box;

			.takeout-line1 {
				display: flex;
				justify-content: space-between;
				align-items: center;
				border-bottom: 1px solid #f4f4f4;
				height: 93px;
				font-size: 34px;
				color: #222;
				font-family: PingFangSC, PingFangSC-Medium;

				.takeout-line1-assess {
					font-size: 24px;
					color: #666;
				}
			}

			.takeout-line2 {
				display: flex;
				margin-top: 40px;
				margin-bottom: 36px;
				height: 72px;
				line-height: 65px;
				font-size: 30px;

				.takeout-line-img {
					width: 72px;
					height:72px;
					margin-right: 12px;
				}

				.takeout-line2-time {
					font-size: 22px;
					color: #666;
				}
			}

			.takeout-line3 {
				margin-bottom: 32px;

				.takeout-line3-rate1,
				.takeout-line3-rate2 {
					.takeout-line3-label {
						display: inline-block;
						width: 140px;
						text-align: right;
						margin-right: 24px;
            font-size: 32px;
						font-family: PingFangSC, PingFangSC-Medium;
					}

					.takeout-line3-label1 {
						text-align: left;
						display: inline-block;
						font-size: 26px;
					}
					.starts{
						position: relative;
						top: -2px;
					}

					::v-deep .van-rate__item {
						top: 10px;
					}

					::v-deep .van-rate {
						width: 320px;
					}
				}
			}

			.takeout-line4 {
				margin-bottom: 30px;

				::v-deep .van-field {
					background-color: #f5f6f7;
					padding:24px 20px;
					border-radius: 24px;
				}
			}

			.takeout-line5 {
				.takeout-line5-tips {
					font-size: 20px;
					color: #ccc;
				}
			}

		}

		.btn {
			width:452px;
			height: 88px;
			line-height: 88px;
			text-align: center;
			color: #fff;
			background: linear-gradient(90deg, #40d243, #1fc432);
			border-radius: 7px;
      font-size: 34px;
			margin:100px auto;
		}
	}

	::v-deep .van-dialog__footer {
		padding: 0 0 4px 0;
	}

	::v-deep .van-hairline--top::after {
		border-top-width: 0;
	}

	::v-deep .van-dialog__header {
		font-size: 34px;
	}

	::v-deep .van-button__content {
		width: 320px;
		height: 80px;
		line-height: 80px;
		text-align: center;
		color: #fff;
		background: linear-gradient(90deg, #40d243, #1fc432);
		margin: 0 auto;
		border-radius: 7px;
	}

	.backshow-btn {
		display: flex;
		justify-content: center;

		.cancel,
		.confirm {
			width: 178px;
			height: 80px;
			line-height: 80px;
			border-radius: 7px;
			font-size: 28px;
			color: #333333;
			text-align: center;
			background-color: #F5F6F7;
		}

		.confirm {
			color: #fff;
			margin-left: 22px;
			background: linear-gradient(90deg, #40d243, #1fc432);
		}
	}
}
</style>
