<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-04-23 14:27:02
 * @LastEditors: 孙立政
 * @LastEditTime: 2021-07-09 21:33:10
-->
<template>
  <div class="cell">
    <div class="empty">
      <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/review/review-empty.png" alt="">
      <div class="tips">暂无评论，期待您的餐后评价~</div>
      <!-- <div class="btn" @click="goHome">去逛逛</div> -->
    </div>

  </div>
</template>

<script>
export default {
  data() {
    return {

    }
  },
  created() {

  },
  mounted() {

  },
  methods: {
    goHome() {
      this.$router.push('/')
    }
  }
}
</script>

<style scoped lang="scss">
    .cell {
        position: relative;
        .empty {
            position: absolute;
            left: 50%;
            top: 300px;
            transform: translate(-50%,0);
            width: 3.3rem;
            height: 230px;
            text-align: center;
           img {
               width: 253px;
               height: 137px;
           }
           .tips {
               font-size:24px;
               color: #999;
               margin-top: 60px;
           }
           .btn {
            width: 178px;
            height: 80px;
            background: linear-gradient(90deg,#40d243, #1fc432);
            border-radius:40px;
            font-size:28px;
            color: #fff;
            text-align: center;
            line-height: 80px;
            margin:60px auto 0;
        }
        }

    }
</style>
