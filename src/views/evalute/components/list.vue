<template>
  <!-- 评价内容 -->
  <div class="cell">
    <van-list v-if="temp.length!=0" v-model="loading" :finished="finished" finished-text="没有更多评价了~" @load="onLoad">
      <div v-for="(item,index) in temp" :key="index" class="review">
        <div class="review-avatar">
          <img :src="item.userImage" alt="">
        </div>
        <div class="review-content">
          <div class="content-line1">
            <div class="content-line1-user">{{ item.userName }}</div>
            <div class="content-line1-time">
              <span>{{ item.evaTime }}</span>
              <!-- <time-ago :refresh="60" :datetime="item.evaTime" locale="zh_CN" tooltip></time-ago> -->
            </div>
          </div>
          <div class="content-line2">
            <div class="content-line2-rate">商家
              <van-rate
                v-model="item.score"
                allow-half
                readonly
                :size="15"
                color="#ffd21e"
                void-icon="star"
                void-color="#eee"
              />
            </div>
            <!-- <div class="content-line2-horse">骑手：30分钟送达</div> -->
          </div>
          <div class="content-line3">
            <span v-for="tag in item.tbMarketEvaluations" :key="tag.id">{{ tag.evaTypeName }}:{{ tag.score }}星
            </span>
          </div>
          <div class="content-line4">
            {{ item.tbEvaluationContents[0].content }}
          </div>
          <div v-if="item.pictures!=''" class="content-line4">
            <img v-for="( imgList, indexs ) in item.pictures.split(',')" :key="indexs" :src="imgList" alt="" class="content-line4-img">
          </div>
          <div class="content-line5" @click="goShop(item)">
            <img :src="item.marketImage" alt="" class="content-line5-shopImg">
            <div class="content-line5-name">
              {{ item.marketName }}
            </div>
            <van-icon
              name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/review/arrow.png"
              size=".4rem"
              class="content-line5-arrow"
            />
          </div>
          <div class="content-line6">
            <div v-if="item.isAppendComment==false" class="content-line6-btn1" @click="goNextReview(item)">追评</div>
            <!-- <div class="content-line6-btn2">修改评论</div> -->
            <div class="content-line6-btn2" @click="delReview(item)">删除评论</div>
          </div>
          <div v-if="item.tbEvaluationContents.length" class="content-line7">
            <div v-for="(item1,idx) in item.tbEvaluationContents" :key="idx" class="content-line7-shop">
              <p v-if="idx!=0">
                <span v-if="item1.replierType==1" class="content-line7-reply">我的追评：</span>
                <span v-if="item1.replierType==2" class="content-line7-reply">商家回复：</span>
                <span class="content-line7-feedback">{{ item1.content }}</span>
              </p>
            </div>
          </div>
        </div>
      </div>
    </van-list>

    <!-- 追评弹框 -->
    <van-dialog v-model="show" title="我的追评" :show-cancel-button="false" :show-confirm-button="false">
      <van-field
        v-model="apppend.content"
        rows="7"
        autosize
        label=""
        type="textarea"
        placeholder="请描述您的要追评的内容"
        maxlength="100"
      />
      <div class="delshow-btn">
        <div class="cancel" @click="show = false">取消</div>
        <div class="confirm" @click="confirmBtn">确认</div>
      </div>
    </van-dialog>
    <!-- 删除弹框 -->
    <van-dialog v-model="delshow" title="确定要删除？" :show-cancel-button="false" :show-confirm-button="false">
      <div style="margin:.3rem auto;text-align:center">已填写的评价将会删除哦</div>
      <div class="delshow-btn">
        <div class="cancel" @click="delshow = false">取消</div>
        <div class="confirm" @click="confirm">确认</div>
      </div>
    </van-dialog>

    <!--空状态  -->
    <Empty v-if="temp.length==0" />

    <Loading :show="loadingShow" />
  </div>
</template>

<script>
import {
  queryMyEva,
  appendComment,
  deleteComment
} from '@/api/evaluate'
import Empty from '../components/empty'
import Loading from '@/components/Loading/index'
export default {
  components: {
    Empty,
    Loading
  },
  data() {
    return {
      temp: [],
      show: false,
      delshow: false,
      query: {
        'pageNum': 1,
        'pageSize': 10,
        'search': {
          'evaType': 1
        }
      },
      apppend: {
        content: '',
        evaId: ''
      },
      delId: null,
      loading: false,
      finished: false,
      flag: true,
      loadingShow: true
    }
  },
  created() {
    this.getList()
  },
  methods: {
    // 获取列表
    getList(type) {
      queryMyEva(this.query).then(res => {
        this.loadingShow = false
        if (res.status == 200) {
          if (type == 2) {
            this.temp = []
          }
          let newData = []
          res.data.list.forEach(item => {
            item.flag = true
            newData.push(item)
          })
          this.temp.push(...newData)
          this.loading = false

          // 数据全部加载完成
          if (this.query.pageNum == res.data.pages || res.data.pages == 0) {
            this.finished = true
          }
        } else {
          this.$toast(res.message)
        }
      })
    },
    // 分页加载
    onLoad() {
      this.query.pageNum = this.query.pageNum + 1
      this.getList()
    },
    // 打开追评
    goNextReview(item) {
      this.apppend.content = ''
      this.apppend.evaId = item.id
      this.show = true
    },
    confirmBtn() {
      const isEmoji = str => /(\ud83c[\udf00-\udfff])|(\ud83d[\udc00-\ude4f\ude80-\udeff])|[\u2600-\u2B55]/g
        .test(str)
      if (isEmoji(this.message)) {
        this.$toast('请勿输入表情')
        return false
      }
      appendComment(this.apppend).then(res => {
        if (res.status == 200) {
          this.$toast('追加成功')
          this.getList(2)
          // location.reload()
          this.show = false
        } else {
          this.$toast(res.message)
        }
      })
    },
    // 确认删除
    confirm() {
      this.delshow = false
      let data = {
        id: this.delId
      }
      deleteComment(data).then(res => {
        if (res.status == 200) {
          this.$toast('删除成功')
          this.getList(2)
          this.delshow = false
        } else {
          this.$toast(res.message)
        }
      })
    },
    // 打开删除
    delReview(item) {
      this.delId = item.id
      this.delshow = true
    },
    goShop(item) {
      this.$store.state.cart.cartData[0].goodsList = []
      this.$router.push({
        name: 'Shop',
        query: {
          id: item.relatedId
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
	.cell {
		.review {
			display: flex;
			width: 100%;
			margin-top: 20px;
			background-color: #fff;
			padding: 24px 30px  0 20px;
			box-sizing: border-box;

			.review-avatar {
				width: 78px;
				height: 78px;
				margin-right: 12px;

				img {
					width: 100%;
					height: 100%;
					border-radius: 50%;
				}
			}

			.review-content {
				.content-line1 {
					height: 40px;
					display: flex;
					justify-content: space-between;
					align-items: center;
					margin-bottom: 10px;

					.content-line1-user {
						font-size: 28px;
						font-family: PingFangSC, PingFangSC-Medium;
						font-weight: 500;
						color: #333;
					}

					.content-line1-time {
						font-size: 22px;
						color: #999;
					}
				}

				.content-line2 {
					display: flex;
					align-items: center;
					font-size: 22px;
					margin-bottom: 16px;

					.content-line2-rate {
						height: 50px;
						line-height:50px;
						margin-right: 17px;

						::v-deep .van-rate__item {
							top:4px;
						}
					}
				}

				.content-line3 {
					font-size:22px;
					color: #999;
					margin-bottom: 16px;
				}

				.content-line4 {
					font-size: 24px;
					color: #333;
					margin-bottom: 16px;
				}

				.content-line4 {
					margin-bottom: 24px;

					.content-line4-img {
						width: 128px;
						height: 128px;
						margin-right: 10px;
					}
				}

				.content-line5 {
          display: flex;
          align-items: center;
					position: relative;
					width: 610px;
					height: 110px;
					background-color: #f5f6f7;
					padding:10px;
					box-sizing: border-box;
					font-size: 28px;
					color: #333;
					margin-bottom: 24px;

					.content-line5-shopImg {
						width: 90px;
						height:90px;
						margin-right:10px;
					}

					.content-line5-arrow {
						position: absolute;
						right: 16px;
						top: 50px;
						width: 40px;
						height: 40px;
						transform: translateY(-50%);
					}
				}

				.content-line6 {
					display: flex;
					justify-content: flex-end;
					margin-bottom:24px;

					.content-line6-btn1,
					.content-line6-btn2 {
						height: 48px;
						line-height: 48px;
						border: 1px solid #cccccc;
						border-radius: 23px;
						font-size:24px;
						color: #454545;
						text-align: center;
					}

					.content-line6-btn1 {
						width: 98px;
						margin-right: 16px;
					}

					.content-line6-btn2 {
						width: 134px;
					}

					.content-line6-btn2:not(last-child) {
						margin-right:16px;
					}
				}

				.content-line7 {
					border-top: 1px solid #f4f4f4;
					font-size: 22px;
					padding-top: 23px;

					div:last-child {
						margin-bottom: 24px;
					}

					.content-line7-my {
						margin-top: 16px;
					}

					.content-line7-reply,
					.content-line7-myreply {
						color: #999
					}

				}
			}
		}

		::v-deep .van-field__control {
			background-color: #f5f6f7;
			padding:24px 20px;
			border-radius: 24px;
		}

		//  ::v-deep .van-hairline--top::after {
		//     border-top-width: 0;
		// }
		::v-deep .van-cell::after {
			border: none;
		}

		.delshow-btn {
			display: flex;
			justify-content: center;
			padding-bottom: 40px;
			padding-top: 10px;

			.cancel,
			.confirm {
				width: 178px;
				height: 80px;
				line-height: 80px;
				border-radius: 7px;
				font-size: 28px;
				color: #333333;
				text-align: center;
				background-color: #F5F6F7;
			}

			.confirm {
				color: #fff;
				margin-left: 22px;
				background: linear-gradient(90deg, #40d243, #1fc432);
			}
		}
	}
</style>
