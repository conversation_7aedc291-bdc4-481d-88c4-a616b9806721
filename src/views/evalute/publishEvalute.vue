<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-04-24 14:37:27
 * @LastEditors: 孙立政
 * @LastEditTime: 2021-12-03 14:40:44
-->
<template>
  <!-- 发表评价 -->
  <div class="content">
    <NavHeight bgc="#fff" />
    <Top message="发表评价" />
    <Comment />
  </div>
</template>

<script>
import Top from './components/top'
import Comment from './components/comment'
export default {
  components: {
    Top,
    Comment
  },
  data() {
    return {

    }
  },
  created() {

  },
  mounted() {
    // 回到顶部
    document.body.scrollTop = 0
    document.documentElement.scrollTop = 0
  },
  methods: {

  }
}
</script>

<style scoped lang="scss">
</style>
