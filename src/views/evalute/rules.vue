<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-04-27 14:31:51
 * @LastEditors: zhaoyuxin
 * @LastEditTime: 2021-06-22 17:53:16
-->
<template>
  <div class="content">
    <NavHeight bgc="#fff" />
    <Top message="规则说明" />
    <div>
      <div class="header">
        为了营造良好的评价体系，规范评价内容，平台会对评价内容进行审核，若遇到以下评价内容，平台会隐藏删除该评价，严重者会进行封号处理。
      </div>
      <div class="rules">
        1.反复发布无意义或恶意凑字数的评价。<br>
        2.出于商业及个人利益的广告性质的评价。 <br>
        3.存在违反相关法律、法规内容的评价。 <br>
        4.含有脏词、泄露他人隐私、存在色情淫秽等不当内容的评价。<br>
        5.商户通过优惠措施如折扣、送菜等利益诱惑或威胁的手段交换用户发布的评价。 <br>
        6.评价内容描述为听说的，来自他人的体验，或因被报道而引发的围观性评论，而非本人在该商户进行消费体验的评价，另包含上传图片为屏幕截图的相关评价图片。 <br>
        7.与商家相关的人员如亲友、员工等在其商家下的评价;其他出于明显商业利益的商家炒作性质评价等。 <br>
        8.其他违反规则的评价，如恶意注册/使用多设备多账号写评价，包括但不限于以牟利、炒作、套现、攻击等目的。
      </div>
    </div>
  </div>
</template>

<script>
import Top from './components/top'
export default {
  components: {
    Top
  },
  data() {
    return {
      flag1: false, // 外卖
      flag2: false // 非外卖
    }
  },
  created() {
  },
  mounted() {

  },
  methods: {

  }
}
</script>

<style scoped lang="scss">
.content::before {
  // 利用伪元素设置整个页面的背景色
  content: " ";
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: -100;
  min-height: 100%;
  background-color: #fff;
}
.content {
    font-size: 28px;
    .header {
        color: #333;
        font-size: 28px;
        padding: 20px;
        font-family: PingFangSC-Medium;
    }
    .rules {
        width: 710px;
        box-sizing: border-box;
        padding: 20px;
        color: #333;
        background-color: #f4f4f4;
        margin: auto;
        border-radius: 16px;
        line-height: 42px;
    }
}
  </style>

