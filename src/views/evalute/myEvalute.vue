<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-04-22 18:28:43
 * @LastEditors: zhaoyuxin
 * @LastEditTime: 2021-06-29 17:33:15
-->
<template>
  <div class="content">
    <NavHeight bgc="#fff" />
    <!-- 头部 -->
    <Top message="我的评价" />
    <!-- 去评价 -->
    <!-- <Unrated/> -->
    <!-- 评价列表 -->
    <List />
  </div>
</template>

<script>
import Top from './components/top'
import List from './components/list'
export default {
  components: {
    List,
    Top
  },
  data() {
    return {

    }
  },
  created() {

  },
  mounted() {

  },
  methods: {

  }
}
</script>

<style scoped lang="scss">
	.content {}
</style>
