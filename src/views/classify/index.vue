<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-05-22 15:16:46
 * @LastEditors: 孙立政
 * @LastEditTime: 2023-04-17 09:21:57
-->
<template>
  <div class="home">
    <NavHeight :bgc="getBgi(cateId)" />
    <!-- 根据分类id判断展示哪个分类 -->
    <Takeout v-if="cateId==0" />
    <FineFood v-if="cateId==12||cateId==46||cateId==83||cateId==118" />
    <Hotel v-if="cateId==14||cateId==52" />
    <FoodMarket v-if="cateId==13||cateId==67||cateId==119||cateId==84" />
    <Other v-if="cateId==40||cateId==41||cateId==42||cateId==55||cateId==56||cateId==57||cateId==109||cateId==85||cateId==122||cateId==123||cateId==155||cateId==154" />
    <!-- 龙泉首页banner进入 -->
    <otherLq v-if="cateId=='lqbanner'" />

    <!-- 生鲜 -->
    <Fresh v-if="cateId==62" />
    <!-- 江泰领悦 -->
    <Jtly v-if="cateId==73" />
  </div>
</template>

<script>
import Takeout from './components/takeout'
import FineFood from './components/fineFood/index.vue'
import Hotel from './components/hotel/index.vue'
import FoodMarket from './components/foodMarket'
import Other from './components/other'
import otherLq from './components/otherLq'
import Fresh from './components/fresh'
import Jtly from './components/jtly'
export default {
  components: {
    Takeout,
    FineFood,
    Hotel,
    FoodMarket,
    Other,
    Fresh,
    otherLq,
    Jtly
  },
  data() {
    return {
      cateId: ''
    }
  },
  created() {
    this.cateId = this.$route.query.id ? this.$route.query.id : 0
  },
  beforeRouteLeave(to, from, next) {
    console.log(to)
    if (to.path == '/shop' || to.path == '/fineFoodShopIndex' || to.path == '/hotelShopIndex' || to.path == '/setMeal') {
      from.meta.keepAlive = true
    } else {
      this.removeKeepAliveCache()
    }
    next()
  },
  methods: {
    removeKeepAliveCache() {
      if (this.$vnode && this.$vnode.data.keepAlive && this.$vnode.parent) {
        const tag = this.$vnode.tag
        let caches = this.$vnode.parent.componentInstance.cache
        let keys = this.$vnode.parent.componentInstance.keys
        for (let [key, cache] of Object.entries(caches)) {
          if (cache.tag === tag) {
            if (keys.length > 0 && keys.includes(key)) {
              keys.splice(keys.indexOf(key), 1)
            }
            delete caches[key]
          }
        }
      }
      this.$destroy()
    },
    getBgi(row) {
      if (row == 0) {
        return 'linear-gradient(270deg,#ff6a32 0%, #ff2b17 50%, #ff2b17 100%)'
      } else if (row == 12 || row == 46) {
        return 'linear-gradient(270deg,#fe9428 0%, #ff6610 50%, #ff6610)'
      } else if (row == 13 || row == 67 || row == 119 || row == 84) {
        return 'linear-gradient(270deg,#7de134 0%, #34ca03 50%, #34ca03)'
      } else if (row == 14 || row == 52) {
        return 'linear-gradient(270deg,#737aff 0%, #3b5bff 50%, #3b5bff)'
      } else if (row == 62) {
        return 'linear-gradient(270deg,#69d4ff 0%, #69d4ff 0%, #29bcff 0%, #0bb0ff)'
      } else {
        return 'linear-gradient(270deg,#fe9428 0%, #ff6610 50%, #ff6610)'
      }
    }
  }
}
</script>

<style scoped lang="scss">
</style>
