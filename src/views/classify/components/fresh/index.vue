<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-05-22 15:17:21
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-07-22 09:20:23
-->
<template>
  <div class="homes">
    <!-- 导航栏 -->
    <Nav @restSearch="restSearch" />
    <!-- 搜索框 -->
    <Search />
    <!-- 分类 -->
    <Category @condition="changeRest" />
    <!-- tab切换 -->
    <Tab @condition="changeRest" />
    <!-- 店铺列表 -->
    <!-- <van-list v-model="loading" :finished="finished" finished-text="" @load="onLoad"> -->
    <!-- <Empty v-if="listNull" msg="您还没有相关外卖哦~" msg1="去下看看其他的吧" type="去逛逛" /> -->
    <Skeleton v-if="list.length == 0" type="shop" />

    <GoodsCard v-for="(item,index) in list" v-else :key="index" type="4" :list="item" />
    <!-- </van-list> -->

    <Loading :show="loadingShow" />
  </div>
</template>

<script>
import Nav from './components/nav'
import Search from './components/search'
import Category from './components/category'
import Tab from './components/tab'
import GoodsCard from '../../../../components/GoodsCard'
import { MarketList } from '@/api/classify'
import Skeleton from '@/components/Skeleton'
import Loading from '@/components/Loading/index'
export default {
  components: {
    Nav,
    Search,
    Category,
    Tab,
    GoodsCard,
    Skeleton,
    Loading
  },
  data() {
    return {
      query: {
        marketRequestVO: {
          page: 1,
          size: 1000,
          latitude: this.$store.getters.getLocation.latitude,
          longitude: this.$store.getters.getLocation.longitude,
          regionId: this.$store.getters.getRegionId,
          categoryId: this.$route.query.id,
          orderBy: '',
          isTakeaway: true
        }
      },
      list: [],
      finished: false,
      loading: false,
      listNull: false,
      loadingShow: true
    }
  },
  created() {
    this.getList('')
  },
  methods: {
    onLoad() {
      // this.query.marketRequestVO.page++
      this.getList('')
    },
    // 获取列表
    getList(val) {
      if (val == 'rest') {
        this.loadingShow = false
      }
      MarketList(this.query).then((res) => {
        this.loadingShow = false
        if (res.status == 200) {
          this.loading = false
          // this.list.push(...res.marketVOList)

          this.list = res.marketVOList
          if (res.marketVOList.length == 0) {
            this.finished = true
            this.listNull = true
          }
        } else {
          this.finished = true
          this.$toast(res.message)
        }
      })
    },
    // 变化查询条件
    changeRest(data) {
      this.list = []
      this.query.marketRequestVO.page = 1
      if (data.type == 'tab') { // tab-综合/距离切换
        this.query.marketRequestVO.orderBy = data.orderBy
        this.query.marketRequestVO.filter = data.filter
      } else if (data.type == 'cate') { // 二级分类切换
        this.query.marketRequestVO.categoryId = data.categoryId
      }

      this.finished = false

      this.$toast.loading({
        message: '',
        duration: 1,
        forbidClick: true
      })

      // this.$toast.clear()
      this.getList()
    },
    // 重置查询条件
    restSearch() {
      this.list = []
      this.query = {
        marketRequestVO: {
          page: 1,
          size: 1000,
          latitude: this.$store.getters.getLocation.latitude,
          longitude: this.$store.getters.getLocation.longitude,
          regionId: this.$store.getters.getRegionId,
          categoryId: this.$route.query.id,
          orderBy: '',
          isTakeaway: true
        }
      }
      this.getList('rest')
    }
  }
}
</script>

<style scoped lang="scss">
.homes{
  background-color: #F7F7F7;
}
</style>
