<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-05-22 16:44:42
 * @LastEditors: 孙立政
 * @LastEditTime: 2021-11-18 20:32:37
-->
<template>
  <div class="home">
    <van-dropdown-menu active-color="#0BB0FF">
      <van-dropdown-item v-model="orderBy" :options="option" @change="clickIndex" />
    </van-dropdown-menu>
    <ul class="tab">
      <li v-for="(item,index) in list" :key="item.value" class="tab-item" :class="ifIndex(index)?'active':''">
        <span :data-current="index" @click="clickIndexList(item.value,index)">{{ item.text }}</span>
      </li>
    </ul>
  </div>
</template>

<script>
export default {
  data() {
    return {
      orderBy: 1,
      currentIndex: [],
      filter: [],
      option: [
        { text: '综合排序', value: 1 },
        { text: '销量优先', value: 2 },
        { text: '距离优先', value: 3 }
      ],
      list: [
        { text: '减配送费', value: 1 },
        { text: '0元起送', value: 2 }
      ]
    }
  },
  created() {

  },
  mounted() {

  },
  methods: {
    // tab
    clickIndex(e) {
      let data = {
        orderBy: this.orderBy,
        type: 'tab',
        filter: this.filter
      }
      this.$emit('condition', data)
    },
    // 标签
    clickIndexList(e, index) {
      if (this.currentIndex.includes(index)) {
        this.currentIndex.splice(this.currentIndex.indexOf(index), 1)
        this.filter.splice(this.filter.indexOf(e), 1)
      } else {
        this.currentIndex.push(index)
        this.filter.push(e)
      }
      let data = {
        orderBy: this.orderBy,
        type: 'tab',
        filter: this.filter
      }
      this.$emit('condition', data)
    },
    ifIndex(index) {
      return this.currentIndex.includes(index)
    }
  }
}
</script>

<style scoped lang="scss">
    .home {
      display: flex;
      margin-top: 20px;
      ::v-deep .van-dropdown-menu{
        .van-dropdown-menu__bar{
          background-color: rgba(0,0,0,0);
          box-shadow: 0px 0px 0px rgba(0,0,0,0);
          .van-dropdown-menu__item{
            -webkit-justify-content: left;
            justify-content: left;
            font-size: 30px;
            font-family: PingFangSC-Medium;
          }
        }
        .van-popup{
          background-color: #f9f9f9;
        }
        .van-cell{
          font-size: 26px;
          background-color: rgba(0,0,0,0);
        }
        .van-cell::after{
          border-bottom: 0px solid #ebedf0;
          font-family: PingFangSC;
        }
        .van-cell__value{
          opacity: 0;
        }
        .van-dropdown-item__content {
          border-bottom-left-radius: 16px;
          border-bottom-right-radius: 16px;
        }
      }

        .tab {
            display: flex;
            height: 85px;
            margin-left: 36px;
            margin-top: 28px;
            color: #333;
            .tab-item{
              width: 120px;
              height: 48px;
              font-size: 24px;
              margin-right: 16px;
              border-radius: 6px;
              background: #ededed;
              font-family: PingFangSC;
              text-align: center;
              line-height: 48px;
              color: #333333;
            }
            .active {
              background: #0BB0FF;
              color: #fff;
              font-family: PingFangSC;
            }
            .tab-item-icon {
                width: 45px;
                height: 15px;
                background-image: url(https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/user/classify/tab-active.png);
                background-size: 100%;
                margin: 3px auto 0;
            }
        }

    }
</style>
