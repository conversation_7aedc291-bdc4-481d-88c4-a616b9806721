<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-05-22 15:18:49
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-02-14 09:35:20
-->
<template>
  <!-- 导航栏 -->
  <div class="home">
    <div class="nav">
      <div class="nav-left" @click="goBack">
        <div class="back">
          <img class="back" src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/user/classify/arrow-left-white.png" alt="">
        </div>
        <div class="navLogo">
          <img class="navLogo" src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/user/classify/sxps2.png" alt="">
        </div>
      </div>
      <div class="nav-right">
        <van-icon class="locinion" name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/user/classify/location.png" size="13px" />
        <div @click="goLocation">
          <van-dropdown-menu>
            <van-dropdown-item v-model="value1" :options="option1" disabled />
          </van-dropdown-menu>
        </div>
      </div>
    </div>
    <div class="positionHeight" />
    <!-- 选择地址弹出层 -->
    <van-popup v-model="addressShow" position="bottom" round closeable :style="{ width: '100%', height: '319px' }">
      <div class="address-wrap">
        <div class="title">
          选择收货地址
        </div>
        <van-radio-group v-model="radiovalue" @change="radioChange">
          <ul class="address">
            <li v-for="(item,index) in addrList" :key="index" class="address-item">
              <van-radio :name="index" icon-size="18px" checked-color="#5dcb4f">
                <div class="item-detail">{{ item.address| ellipsis(18) }} <van-tag v-if="item.isDefault == true" plain type="danger">默认</van-tag></div>
                <div class="item-userinfo"><span>{{ item.username }}</span> <span>{{ item.mobile }}</span></div>
              </van-radio>
            </li>
          </ul>
          <div class="fixedBottom">
            <div class="addBtn" @click="goAddress">
              <van-icon name="add-o" color="#39CF3F" size="17" style="margin-right:5px" />
              新增收货地址
            </div>
          </div>
        </van-radio-group>
      </div>
    </van-popup>

    <div v-if="locationMsgStatus" class="location" @click="setLoctions">
      <div class="close" @click.stop="closeLocation" />
    </div>
  </div>
</template>

<script>
import {	AddressList } from '@/api/address'
export default {
  data() {
    return {
      value1: 0,
      option1: [
        { text: '遂昌县', value: 0 }
      ],
      addressShow: false,
      radiovalue: '',
      addrList: [],
      poiName: '',
      locationMsgStatus: false
    }
  },
  created() {
    // 第一次进入页面获取地址以及定位
    this.getFirstAddrList()

    this.getLocation()
  },
  methods: {
    // 选择地址
    goLocation() {
      this.getAddrList()
    },
    getFirstAddrList() {
      let marketSn = 'SH800430'
      let self = this
      AddressList(marketSn).then(res => {
        if (res.status == 200) {
          self.addrList = res.data.withinDistance.concat(
            res.data.beyondDistance
          )
          let data = res.data.withinDistance.concat(
            res.data.beyondDistance
          )
          if (res.data.withinDistance.length == 0) {
            this.$emit('restSearch')
            return
          }
          for (let i = 0; i < data.length; i++) {
            if (data[i].isDefault == true) {
              this.option1[0].text = data[i].address.length > 10 ? data[i].address.slice(0, 10) + '...' : data[i].address
              this.$store.getters.getLocation.latitude = data[i].latitude
              this.$store.getters.getLocation.longitude = data[i].longitude
              this.$emit('restSearch')
            }
          }
        }
        self.poiName = self.$store.getters.getLocation.address.poiName
      })
    },
    // 打开地址列表
    getAddrList() {
      this.$toast.loading({
        message: '',
        duration: 0,
        forbidClick: true
      })
      let marketSn = 'SH800430'
      AddressList(marketSn).then(res => {
        this.$toast.clear()
        if (res.status == 200) {
          this.addressShow = true
          this.addrList = res.data.withinDistance.concat(
            res.data.beyondDistance
          )
        }
      })
    },
    radioChange(e) {
      let location = {
        latitude: this.addrList[e].latitude,
        longitude: this.addrList[e].longitude
      }
      this.$store.commit('updateLocation', location)

      this.$emit('restSearch')

      this.poiName = this.addrList[e].address
      this.option1[0].text = this.addrList[e].address.length > 10 ? this.addrList[e].address.slice(0, 10) + '...' : this.addrList[e].address
      this.addressShow = false
    },
    goBack() {
      this.$router.go(-1)
    },
    goAddress() {
      this.$router.push({ name: 'Address' })
    },
    // 检查定位信息
    getLocation() {
      let self = this
      AlipayJSBridge.call('LocationMsg', {}, function(result) {
        console.log(result)
        let locationdata = JSON.parse(result.locationMsg)
        if (locationdata.latitude == 0.0 || locationdata == 0) {
          self.locationMsgStatus = true
        }
      })
    },
    // 跳转定位
    setLoctions() {
      AlipayJSBridge.call('SetLoctions', {}, function(result) {})
    },
    // 关闭定位提示
    closeLocation() {
      this.locationMsgStatus = false
    }
  }
}
</script>

<style scoped lang="scss">
    .home {
      font-family: PingFangSC;
        .nav {
          position: fixed;
          left: 0;
          // top: 0;
          z-index: 2;
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 100%;
            height: 92px;
            background: linear-gradient(270deg,#69d4ff 0%, #69d4ff 0%, #29bcff 0%, #0bb0ff);
            padding: 0 33px;
            .nav-left {
              height: 92px;
              display: flex;
              .back{
                width: 40px;
                height: 40px;

                img{
                  float: left;
                  margin-top: 25px;
                }
              }
              .navLogo{
                width: 174px;
                height: 42px;
                img{
                  float: left;
                  margin-top: 25px;
                }
              }
              ::v-deep .van-icon:nth-child(2){
                  top: 4px;
              }
            }
            .nav-right {
                display: flex;
                align-items: center;
                height: 92px;
                .locinion{
                  position: relative;
                  left: 10px;
                }
                ::v-deep .van-dropdown-menu__bar {
                    height: inherit;
                    box-shadow: none;
                    background-color: transparent;
                    height: 92px;
                    .van-ellipsis{
                      height: 92px;
                      line-height: 92px;
                    }
                }
                ::v-deep .van-dropdown-menu__title {
                    color: #f7fbff;
                    font-size: 32px;
                    font-family: PingFangSC;
                }
                ::v-deep .van-dropdown-menu__title::after {
                    border-color:transparent transparent #fff #fff;
                    opacity: 1;
                }
            }
        }
        .positionHeight {
          width: 100%;
          height: 92px;
          background: linear-gradient(270deg,#69d4ff 0%, #69d4ff 0%, #29bcff 0%, #0bb0ff);
        }
        .address-wrap {
          .title {
            position: absolute;
            top: 0;
            left: 50%;
            transform: translateX(-50%);
            z-index: 10;
            height: 149px;
            padding-top: 40px;
            font-size: 32px;
            color: #333;
            background: #fff;
          }
          .address {
            height: 382px;
            overflow-y: auto;
            margin-top: 149px;
            .address-item {
            width: 710px;
            height: 110px;
            border-bottom: 1px solid #f4f4f4;
            margin: 0 auto 20px;
            .item-detail {
              font-size: 32px;
              color: #222;
              margin-bottom: 8px;
            }
            .item-userinfo {
              font-size: 26px;
              color: #999;
              >span {
                margin-right: 30px;
              }
            }
          }
          }
          .fixedBottom {
            position: fixed;
            width: 100%;
            height: 107px;
            background-color: #fff;
            bottom: 20px;
            .addBtn {
              display: flex;
              align-items: center;
              justify-content: center;
              width: 690px;
              height: 88px;
              background: #f5f6f7;
              border-radius: 7px;
              margin: 10px auto 0;
              font-size: 28px;
              color: #222222;
            }
          }
        }
        .location{
          width: 680px;
          height: 84px;
          background-image: url(https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/region/dwbj.png);
          background-size: 100% 100%;
          margin-left: 30px;
          position: fixed;
          left: 0;
          z-index: 2;
          .close{
            float: right;
            width: 50px;
            height: 84px;

          }
        }
    }
</style>
