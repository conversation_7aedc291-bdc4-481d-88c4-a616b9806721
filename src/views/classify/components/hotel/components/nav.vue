<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <PERSON>haoyu<PERSON>
 * @Date: 2021-05-22 15:18:49
 * @LastEditors: 孙立政
 * @LastEditTime: 2021-06-25 09:56:00
-->
<template>
  <!-- 导航栏 -->
  <div class="home">
    <div class="nav">
      <div class="nav-left">
        <van-icon name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/user/classify/arrow-left-white.png" size="20px" @click="goBack" />
        <van-icon name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/user/classify/nav-title-hotel-white.png" size="42px" />
      </div>
    </div>
    <div class="positionHeight" />
  </div>
</template>

<script>
export default {
  props: {
    type: {
      type: [Number, String],
      default: 1
    }
  },
  data() {
    return {
    }
  },
  created() {
  },
  mounted() {

  },
  methods: {
    goBack() {
      this.$router.push('/')
    }
  }
}
</script>

<style scoped lang="scss">
    .home {
        .nav {
          position: fixed;
          left: 0;
          // top: 0;
          z-index: 2;
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 100%;
            height: 92px;
            background: linear-gradient(270deg,#737aff 0%, #3b5bff 50%, #3b5bff);
            padding: 0 33px;
            .nav-left {
                height: 92px;
                ::v-deep .van-icon:first-child{
                    top: -16px;
                }
                ::v-deep .van-icon:nth-child(2){
                    top: 4px;
                }
            }
        }
        .positionHeight {
          width: 100%;
          height: 92px;
          background: linear-gradient(270deg,#737aff 0%, #3b5bff 50%, #3b5bff);
        }
        .address-wrap {
          .title {
            position: absolute;
            top: 0;
            left: 50%;
            transform: translateX(-50%);
            z-index: 10;
            height: 149px;
            padding-top: 40px;
            font-size: 32px;
            color: #333;
            background: #fff;
          }
          .address {
            height: 382px;
            overflow-y: auto;
            margin-top: 149px;
            .address-item {
            width: 710px;
            height: 110px;
            border-bottom: 1px solid #f4f4f4;
            margin: 0 auto 20px;
            .item-detail {
              font-size: 32px;
              color: #222;
              margin-bottom: 8px;
            }
            .item-userinfo {
              font-size: 26px;
              color: #999;
              >span {
                margin-right: 30px;
              }
            }
          }
          }
          .fixedBottom {
            position: fixed;
            width: 100%;
            height: 107px;
            background-color: #fff;
            .addBtn {
              display: flex;
              align-items: center;
              justify-content: center;
              width: 690px;
              height: 88px;
              background: #f5f6f7;
              border-radius: 7px;
              margin: 10px auto 0;
              font-size: 28px;
              color: #222222;
            }
          }
        }
    }
</style>
