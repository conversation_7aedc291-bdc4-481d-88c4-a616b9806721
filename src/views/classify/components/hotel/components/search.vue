<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-05-22 15:18:58
 * @LastEditors: 孙立政
 * @LastEditTime: 2021-07-26 20:31:45
-->
<template>
  <div class="home">
    <div class="container">
      <div class="search">
        <van-search v-model="keyword" placeholder="请输入商家或商品名称" show-action action-text="搜索"	@search="goSearch">
          <template #action>
            <div @click="goSearch">搜索</div>
          </template>
        </van-search>
      </div>
    </div>
  </div>
</template>

<script>
import { addSearchData } from '@/utils/upLog.js'
export default {
  data() {
    return {
      keyword: ''
    }
  },
  created() {},
  mounted() {},
  methods: {
    goSearch() {
      if (this.keyword.trim() == '') {
        this.$toast('请输入关键字')
        return false
      }
      let histor = this.$store.state.home.historyKey
      let flag = false
      histor.some((item) => {
        if (item == this.keyword) {
          flag = true
        }
      })
      if (!flag) {
        histor.unshift(this.keyword)
      }
      this.$store.state.searchKey = this.keyword
      // 记录搜索轨迹
      addSearchData(this.keyword)
      this.keyword = ''
      this.$router.push({
        name: 'Results',
        query: {
          istakeaway: false,
          categoryId: this.$route.query.cateId
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
.home {
  .container {
    height: 128px;
    background: linear-gradient(270deg,#737aff 0%, #3b5bff 50%, #3b5bff);
    .search {
      height: 129px;
      background-color: #fff;
      border-radius: 44px 44px 0 0;
      padding: 32px 24px 0 24px;
      ::v-deep .van-search {
          width: 702px;
          border-radius:34px;
          overflow: hidden;
          padding: 0;
          border: 1px solid #737AFF;
      }
      ::v-deep .van-search__content {
          background-color: #fff;
      }
      ::v-deep .van-search__action {
          font-size: 28px;
          color: #fff;
          background-color: #737AFF;
          padding: 0 24px;
          border-radius: 30px;
          overflow: hidden;
          font-family:PingFangSC-Medium;
          font-weight: 500;
      }
    }
  }
}
</style>
