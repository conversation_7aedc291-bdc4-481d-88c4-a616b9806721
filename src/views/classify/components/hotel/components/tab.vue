<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: zhaoyu<PERSON>
 * @Date: 2021-05-22 16:44:42
 * @LastEditors: zhaoyuxin
 * @LastEditTime: 2021-06-04 17:57:43
-->
<template>
  <div class="home">
    <ul class="tab">
      <li v-for="(item,index) in list" :key="item.id" class="tab-item" :class="currentIndex==index?'active':''">
        <span :data-current="index" @click="clickIndex($event,item.id)">{{ item.title }}</span>
        <div v-show="currentIndex==index" class="tab-item-icon" />
      </li>
    </ul>
  </div>
</template>

<script>
export default {
  data() {
    return {
      list: [
        {
          id: 1,
          title: '综合'
        },
        {
          id: 3,
          title: '距离'
        },
        {
          id: 4,
          title: '优惠最高'
        }
      ],
      currentIndex: 0
    }
  },
  created() {

  },
  mounted() {

  },
  methods: {
    clickIndex(e) {
      // console.log(e.target.dataset.current)
      let current = e.target.dataset.current
      if (this.currentIndex == current) {
        return false
      } else {
        this.currentIndex = current
      }
      let data = {
        orderBy: this.list[current].id,
        type: 'tab'
      }
      this.$emit('condition', data)
    }
  }
}
</script>

<style scoped lang="scss">
    .home {
        .tab {
            display: flex;
            height: 85px;
            margin-left: 20px;
            color: #333;
            .tab-item{
                font-size: 28px;
                margin-right: 64px;
                font-family: PingFangSC;
            }
            .active {
                font-weight: 600;
                font-size: 32px;
                font-family: PingFangSC-Semibold;
            }
            .tab-item-icon {
                width: 45px;
                height: 15px;
                background-image: url(https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/user/classify/tab-active.png);
                background-size: 100%;
                margin: 3px auto 0;
            }
        }

    }
</style>
