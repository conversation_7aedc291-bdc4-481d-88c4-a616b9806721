<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-05-22 15:17:21
 * @LastEditors: 孙立政
 * @LastEditTime: 2021-07-19 17:08:28
-->
<template>
  <!-- 美食 -->
  <div class="home">
    <!-- 导航栏 -->
    <Nav type="2" />
    <!-- 搜索框 -->
    <Search />
    <!-- 分类 -->
    <Category @condition="changeRest" />
    <!-- tab切换 -->
    <Tab @condition="changeRest" />
    <!-- 店铺列表 -->
    <van-list v-model="loading" :finished="finished" finished-text="" @load="onLoad">
      <Empty v-if="list.length==0" msg="暂时没有相关酒店哦~" msg1="去下看看其他的吧" type="去逛逛" />
      <GoodsCard v-for="(item,index) in list" :key="index" type="5" :list="item" />
    </van-list>

    <Loading :show="loadingShow" />

    <div style="height:50px" />
  </div>
</template>

<script>
import Nav from './components/nav'
import Search from './components/search'
import Category from './components/category'
import Tab from './components/tab'
import GoodsCard from '../../../../components/GoodsCard'
import { MarketList } from '@/api/classify'
import Loading from '@/components/Loading/index'
export default {
  components: {
    Nav,
    Search,
    Category,
    Tab,
    GoodsCard,
    Loading
  },
  data() {
    return {
      query: {
        marketRequestVO: {
          page: 0,
          size: 10,
          latitude: this.$store.getters.getLocation.latitude,
          longitude: this.$store.getters.getLocation.longitude,
          regionId: this.$store.getters.getRegionId,
          categoryId: this.$route.query.id,
          orderBy: '',
          isTakeaway: this.$route.query.isTakeaway
        }
      },
      list: [],
      finished: false,
      loading: false,
      loadingShow: true
    }
  },
  created() {
  },
  mounted() {

  },
  methods: {
    onLoad() {
      this.query.marketRequestVO.page++
      this.getList()
    },
    // 获取列表
    getList() {
      MarketList(this.query).then((res) => {
        this.loadingShow = false
        if (res.status == 200) {
          this.loading = false
          this.list.push(...res.marketVOList)
          if (res.marketVOList.length == 0) {
            this.finished = true
          }
        } else {
          this.finished = true
          this.$toast(res.message)
        }
      })
    },
    // 变化查询条件
    changeRest(data) {
      this.list = []
      this.query.marketRequestVO.page = 1
      if (data.type == 'tab') { // tab-综合/距离切换
        this.query.marketRequestVO.orderBy = data.orderBy
      } else if (data.type == 'cate') { // 二级分类切换
        this.query.marketRequestVO.categoryId = data.categoryId
      }
      this.getList()
    }
  }
}
</script>

<style scoped lang="scss">
</style>
