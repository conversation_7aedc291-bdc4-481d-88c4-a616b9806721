<!--
 * @Descripttion: 美食
 * @version: 1.0.0
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-05-22 15:17:21
 * @LastEditors: 孙立政
 * @LastEditTime: 2023-06-15 10:15:33
-->
<template>
  <!-- 美食 -->
  <div class="home">
    <!-- 导航栏 -->
    <Nav />
    <!-- 搜索框 -->
    <Search />
    <!-- 分类 -->
    <Category @condition="changeRest" />
    <!-- tab切换 -->
    <Tab @condition="changeRest" />
    <!-- 店铺列表 -->

    <van-list v-model="loading" :finished="finished" finished-text="" @load="onLoad">
      <Empty v-if="list.length==0" msg="暂时没有相关美食哦~" msg1="去下看看其他的吧" type="去逛逛" />

      <GoodsCard v-for="(item,index) in list" v-show="item.id!=1439&&item.id!=1445" :key="index" type="2" :list="item" />
    </van-list>
    <div v-if="list.length!=0" style="height:100px" />
  </div>
</template>

<script>
import Nav from './components/nav'
import Search from './components/search'
import Category from './components/category'
import Tab from './components/tab'
import GoodsCard from '../../../../components/GoodsCard'
import { MarketList } from '@/api/classify'
export default {
  components: {
    Nav,
    Search,
    Category,
    Tab,
    GoodsCard
  },
  data() {
    return {
      query: {
        marketRequestVO: {
          page: 0,
          size: 2000,
          latitude: this.$store.getters.getLocation.latitude,
          longitude: this.$store.getters.getLocation.longitude,
          regionId: this.$store.getters.getRegionId,
          categoryId: this.$route.query.cateId ? this.$route.query.cateId : this.$route.query.id,
          orderBy: '5',
          isTakeaway: this.$route.query.isTakeaway
        }
      },
      list: [],
      finished: false,
      loading: false
    }
  },
  created() {
    // this.getList()
  },
  mounted() {

  },
  methods: {
    onLoad() {
      this.query.marketRequestVO.page++
      this.getList()
    },
    // 获取列表
    getList() {
      MarketList(this.query).then((res) => {
        if (res.status == 200) {
          this.loading = false
          this.finished = true
          this.list = res.marketVOList
          // if (this.query.marketRequestVO.page == 1) {
          //   this.list = []
          // }
          // this.list.push(...res.marketVOList)
          // if (res.marketVOList.length == 0) {
          //   this.finished = true
          // }
        } else {
          this.finished = true
          this.$toast(res.message)
        }
      })
    },
    // 变化查询条件
    changeRest(data) {
      // this.list = []
      this.query.marketRequestVO.page = 1
      this.loading = true
      this.finished = false
      if (data.type == 'tab') { // tab-综合/距离切换
        this.query.marketRequestVO.orderBy = data.orderBy
      } else if (data.type == 'cate') { // 二级分类切换
        this.query.marketRequestVO.categoryId = data.categoryId
      }
      this.getList()
    }
  }
}
</script>

<style scoped lang="scss">

</style>
