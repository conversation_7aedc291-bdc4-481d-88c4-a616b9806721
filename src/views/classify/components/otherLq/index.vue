<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-05-22 15:17:21
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-05-26 14:47:02
-->
<template>
  <!-- 美食 -->
  <div class="home">
    <!-- 导航栏 -->
    <Nav />
    <!-- 搜索框 -->
    <Search />
    <!-- tab切换 -->
    <Tab @condition="changeRest" />
    <!-- 店铺列表 -->
    <div v-if="$store.getters.getRegionId == 3">
      <GoodsCard v-for="(item,index) in lqList" :key="index" type="2" :list="item" />
    </div>

    <van-list v-model="loading" :finished="finished" finished-text="" @load="onLoad">
      <Empty v-if="list.length==0" msg="暂时没有相关产品哦~" msg1="去下看看其他的吧" type="去逛逛" />
      <GoodsCard v-for="(item,index) in list" v-show="item.id!=1439&&item.id!=1445" :key="index" type="2" :list="item" />
    </van-list>

    <Loading :show="loadingShow" />
  </div>
</template>

<script>
import Nav from './components/nav'
import Tab from './components/tab'
import Search from './components/search'
import GoodsCard from '../../../../components/GoodsCard'
import { MarketList } from '@/api/classify'
import Loading from '@/components/Loading/index'
export default {
  components: {
    Nav,
    Tab,
    Search,
    Loading,
    GoodsCard
  },
  data() {
    return {
      query: {
        marketRequestVO: {
          page: 0,
          size: 10,
          latitude: this.$store.getters.getLocation.latitude,
          longitude: this.$store.getters.getLocation.longitude,
          regionId: this.$store.getters.getRegionId,
          unionPayCode: 'https',
          categoryIds: [46, 56, 67]
        }
      },
      list: [],
      finished: false,
      loading: false,
      loadingShow: true,
      lqList: [
        {
          'id': 1445,
          'address': '万达广场2号门',
          'cover': 'https://diandi-video.oss-cn-hangzhou.aliyuncs.com/face/16657373290001665737329421.png',
          'description': '万达广场2号门',
          'province': '浙江省',
          'city': '丽水市',
          'district': '龙泉市',
          'isOpen': 1,
          'latitude': '28.069259',
          'longitude': '119.149726',
          'marketName': '肯德基龙泉万达广场店',
          'openingHours': null,
          'openingTime': null,
          'closingHours': null,
          'closingTime': null,
          'poolId': 3,
          'regionId': 3,
          'regionSn': '331181',
          'type': 3,
          'distance': 59.5316328,
          'status': true,
          'isBusinessTime': null,
          'isTakeaway': false,
          'mobilePhone': '***********',
          'fixedPhone': '***********',
          'phone': '***********',
          'deliverLimitPrice': 0,
          'marketTags': [
            {
              'id': 6705,
              'marketId': 1445,
              'tagTitle': '西餐'
            },
            {
              'id': 6706,
              'marketId': 1445,
              'tagTitle': '快餐'
            }
          ],
          'marketDiscounts': [
            {
              'id': 1096,
              'marketId': 1445,
              'marketSn': 'SH725956',
              'discount': 0.95,
              'mode': 1,
              'isValid': 1
            }
          ],
          'goodsDTOList': [
            {
              'id': 13318,
              'goodsName': '秘汁全鸡',
              'cover': 'https://shenghuofw.oss-cn-beijing.aliyuncs.com/78f0da6b12464b5dbc08dd99993ad96b.jpg',
              'description': '秘汁全鸡',
              'price': 39,
              'sales': 0,
              'inventory': null,
              'isDiscount': false,
              'isHot': null,
              'isOnSell': true,
              'marketId': 1445,
              'marketSn': 'SH725956',
              'isMandatoryClose': 1,
              'isDelete': false,
              'isShowIndex': null,
              'oriPrice': 39
            },
            {
              'id': 13319,
              'goodsName': '香辣鸡腿堡',
              'cover': 'https://shenghuofw.oss-cn-beijing.aliyuncs.com/54e4191a304e4447834dcbaa60e90a1e.jpg',
              'description': '香辣鸡腿堡',
              'price': 18.5,
              'sales': 0,
              'inventory': null,
              'isDiscount': false,
              'isHot': null,
              'isOnSell': true,
              'marketId': 1445,
              'marketSn': 'SH725956',
              'isMandatoryClose': 1,
              'isDelete': false,
              'isShowIndex': null,
              'oriPrice': 18.5
            },
            {
              'id': 13320,
              'goodsName': '新奥尔良鸡腿堡',
              'cover': 'https://shenghuofw.oss-cn-beijing.aliyuncs.com/ea2d7861b1c2492394922f2e01b4e841.jpg',
              'description': '新奥尔良烤鸡腿堡',
              'price': 19,
              'sales': 0,
              'inventory': null,
              'isDiscount': false,
              'isHot': null,
              'isOnSell': true,
              'marketId': 1445,
              'marketSn': 'SH725956',
              'isMandatoryClose': 1,
              'isDelete': false,
              'isShowIndex': null,
              'oriPrice': 19
            },
            {
              'id': 13321,
              'goodsName': '薯条',
              'cover': 'https://shenghuofw.oss-cn-beijing.aliyuncs.com/8e0df2d2448b4b46b9d5d2e9c0bb784c.jpg',
              'description': '薯条',
              'price': 11.5,
              'sales': 0,
              'inventory': null,
              'isDiscount': false,
              'isHot': null,
              'isOnSell': true,
              'marketId': 1445,
              'marketSn': 'SH725956',
              'isMandatoryClose': 1,
              'isDelete': false,
              'isShowIndex': null,
              'oriPrice': 11.5
            },
            {
              'id': 13322,
              'goodsName': '小食拼盘',
              'cover': 'https://shenghuofw.oss-cn-beijing.aliyuncs.com/af6f55fa79a14739a4bb2ac1acb19c2e.jpg',
              'description': '小食拼盘',
              'price': 37,
              'sales': 0,
              'inventory': null,
              'isDiscount': false,
              'isHot': null,
              'isOnSell': true,
              'marketId': 1445,
              'marketSn': 'SH725956',
              'isMandatoryClose': 1,
              'isDelete': false,
              'isShowIndex': null,
              'oriPrice': 37
            },
            {
              'id': 13323,
              'goodsName': '原味圣代（北美蓝莓酱）',
              'cover': 'https://shenghuofw.oss-cn-beijing.aliyuncs.com/deb75594282049ecb2d09a628c1a1348.jpg',
              'description': '原味圣代（北美蓝莓酱）',
              'price': 12,
              'sales': 0,
              'inventory': null,
              'isDiscount': false,
              'isHot': null,
              'isOnSell': true,
              'marketId': 1445,
              'marketSn': 'SH725956',
              'isMandatoryClose': 1,
              'isDelete': false,
              'isShowIndex': null,
              'oriPrice': 12
            }
          ],
          'marketCategorys': [
            {
              'id': 5827,
              'categoryId': 50,
              'icon': 'https://shenghuofw.oss-cn-beijing.aliyuncs.com/3721467649494afe93c6681000653301.png',
              'isOpen': true,
              'level': 2,
              'name': '快餐&小吃',
              'sort': 1,
              'regionSn': '331181',
              'capitalPoolId': 3,
              'marketId': '1445',
              'pid': 46
            },
            {
              'id': 5827,
              'categoryId': 46,
              'icon': 'https://shenghuofw.oss-cn-beijing.aliyuncs.com/fd898c65094947d5861a4668a7a4d496.png',
              'isOpen': true,
              'level': 1,
              'name': '美食',
              'sort': 1,
              'regionSn': '331181',
              'capitalPoolId': 3,
              'marketId': '1445',
              'pid': 0
            }
          ],
          'goodsCount': 8,
          'consumType': 0,
          'busTime': '[{"closingHours":"22:00", "openingHours":"10:00"}]',
          'userPostFee': 0,
          'agentPostFee': 0,
          'isDistanceExceeded': true,
          'sales': 0,
          'sort': null,
          'oriPostFee': 0,
          'unionPayCode': null,
          'foodSetMeals': [],
          'mealSetSales': null
        },
        {
          'id': 1439,
          'address': '中山西路一号龙渊新天地肯德基',
          'cover': 'https://diandi-video.oss-cn-hangzhou.aliyuncs.com/face/16657356670001665735667365.png',
          'description': '龙渊新天地肯德基',
          'province': '浙江省',
          'city': '丽水市',
          'district': '龙泉市',
          'isOpen': 1,
          'latitude': '28.074036',
          'longitude': '119.131855',
          'marketName': '肯德基龙渊新天地店',
          'openingHours': null,
          'openingTime': null,
          'closingHours': null,
          'closingTime': null,
          'poolId': 3,
          'regionId': 3,
          'regionSn': '331181',
          'type': 3,
          'distance': 59.4029181,
          'status': true,
          'isBusinessTime': null,
          'isTakeaway': false,
          'mobilePhone': '***********',
          'fixedPhone': '***********',
          'phone': '***********',
          'deliverLimitPrice': 0,
          'marketTags': [
            {
              'id': 6996,
              'marketId': 1439,
              'tagTitle': '西餐'
            },
            {
              'id': 6997,
              'marketId': 1439,
              'tagTitle': '快餐'
            }
          ],
          'marketDiscounts': [
            {
              'id': 1090,
              'marketId': 1439,
              'marketSn': 'SH497787',
              'discount': 0.95,
              'mode': 1,
              'isValid': 1
            }
          ],
          'goodsDTOList': [
            {
              'id': 13304,
              'goodsName': '薯条',
              'cover': 'https://shenghuofw.oss-cn-beijing.aliyuncs.com/c7c0a60c9bd144b7a958a66e6757d903.jpg',
              'description': '薯条',
              'price': 11.5,
              'sales': 0,
              'inventory': null,
              'isDiscount': false,
              'isHot': null,
              'isOnSell': true,
              'marketId': 1439,
              'marketSn': 'SH497787',
              'isMandatoryClose': 1,
              'isDelete': false,
              'isShowIndex': null,
              'oriPrice': 11.5
            },
            {
              'id': 13306,
              'goodsName': '小食拼盘',
              'cover': 'https://shenghuofw.oss-cn-beijing.aliyuncs.com/cb757a2b57fd460080699e9ae8d7f489.jpg',
              'description': '小食拼盘',
              'price': 37,
              'sales': 0,
              'inventory': null,
              'isDiscount': false,
              'isHot': null,
              'isOnSell': true,
              'marketId': 1439,
              'marketSn': 'SH497787',
              'isMandatoryClose': 1,
              'isDelete': false,
              'isShowIndex': null,
              'oriPrice': 37
            },
            {
              'id': 13308,
              'goodsName': '劲脆鸡腿堡',
              'cover': 'https://shenghuofw.oss-cn-beijing.aliyuncs.com/bd7a16c97238441d8890a77f251e51b8.jpg',
              'description': '劲脆鸡腿堡',
              'price': 18.5,
              'sales': 0,
              'inventory': null,
              'isDiscount': false,
              'isHot': null,
              'isOnSell': true,
              'marketId': 1439,
              'marketSn': 'SH497787',
              'isMandatoryClose': 1,
              'isDelete': false,
              'isShowIndex': null,
              'oriPrice': 18.5
            },
            {
              'id': 13309,
              'goodsName': '新奥尔良烤鸡腿堡',
              'cover': 'https://shenghuofw.oss-cn-beijing.aliyuncs.com/aaff6ac7061e40ba8d35aea0ddb50cfe.jpg',
              'description': '新奥尔良烤鸡腿堡',
              'price': 19,
              'sales': 0,
              'inventory': null,
              'isDiscount': false,
              'isHot': null,
              'isOnSell': true,
              'marketId': 1439,
              'marketSn': 'SH497787',
              'isMandatoryClose': 1,
              'isDelete': false,
              'isShowIndex': null,
              'oriPrice': 19
            },
            {
              'id': 13311,
              'goodsName': '黄金鸡块',
              'cover': 'https://shenghuofw.oss-cn-beijing.aliyuncs.com/3bd47a6692ed42fc88c8259800da0bfb.jpg',
              'description': '黄金鸡块',
              'price': 11.5,
              'sales': 0,
              'inventory': null,
              'isDiscount': false,
              'isHot': null,
              'isOnSell': true,
              'marketId': 1439,
              'marketSn': 'SH497787',
              'isMandatoryClose': 1,
              'isDelete': false,
              'isShowIndex': null,
              'oriPrice': 11.5
            },
            {
              'id': 13316,
              'goodsName': '原味圣代（黑糖珍珠酱）',
              'cover': 'https://shenghuofw.oss-cn-beijing.aliyuncs.com/388b523fa06143148655fe1217e54ae3.jpg',
              'description': '原味圣代（黑糖珍珠酱）',
              'price': 11.5,
              'sales': 0,
              'inventory': null,
              'isDiscount': false,
              'isHot': null,
              'isOnSell': true,
              'marketId': 1439,
              'marketSn': 'SH497787',
              'isMandatoryClose': 1,
              'isDelete': false,
              'isShowIndex': null,
              'oriPrice': 11.5
            }
          ],
          'marketCategorys': [
            {
              'id': 6105,
              'categoryId': 50,
              'icon': 'https://shenghuofw.oss-cn-beijing.aliyuncs.com/3721467649494afe93c6681000653301.png',
              'isOpen': true,
              'level': 2,
              'name': '快餐&小吃',
              'sort': 1,
              'regionSn': '331181',
              'capitalPoolId': 3,
              'marketId': '1439',
              'pid': 46
            },
            {
              'id': 6105,
              'categoryId': 46,
              'icon': 'https://shenghuofw.oss-cn-beijing.aliyuncs.com/fd898c65094947d5861a4668a7a4d496.png',
              'isOpen': true,
              'level': 1,
              'name': '美食',
              'sort': 1,
              'regionSn': '331181',
              'capitalPoolId': 3,
              'marketId': '1439',
              'pid': 0
            }
          ],
          'goodsCount': 10,
          'consumType': 0,
          'busTime': '[{"closingHours":"22:00:00", "openingHours":"10:00:00"}]',
          'userPostFee': 0,
          'agentPostFee': 0,
          'isDistanceExceeded': true,
          'sales': 0,
          'sort': null,
          'oriPostFee': 0,
          'unionPayCode': 'https://qr.95516.com/14293300/J018290b32086c1ea0e34e5b0c8d88900bb',
          'foodSetMeals': [],
          'mealSetSales': null
        }
      ]
    }
  },
  methods: {
    onLoad() {
      this.query.marketRequestVO.page++
      this.getList()
    },
    // 获取列表
    getList() {
      MarketList(this.query).then((res) => {
        this.loadingShow = false
        if (res.status == 200) {
          this.loading = false
          this.list.push(...res.marketVOList)
          if (res.marketVOList.length == 0) {
            this.finished = true
          }
        } else {
          this.finished = true
          this.$toast(res.message)
        }
      })
    },
    // 变化查询条件
    changeRest(data) {
      this.list = []
      this.query.marketRequestVO.page = 1
      if (data.type == 'tab') { // tab-综合/距离切换
        this.query.marketRequestVO.orderBy = data.orderBy
      } else if (data.type == 'cate') { // 二级分类切换
        this.query.marketRequestVO.categoryId = data.categoryId
      }
      this.getList()
    }
  }
}
</script>

<style scoped lang="scss">

</style>
