<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <PERSON><PERSON>yu<PERSON>
 * @Date: 2021-05-22 15:18:49
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-09-27 09:30:13
-->
<template>
  <!-- 导航栏 -->
  <div class="home">
    <div class="nav">
      <div class="nav-left">
        <van-icon name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/user/classify/arrow-left-white.png" size="20px" @click="goBack" />
        <!-- <van-icon name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/user/classify/nav-title-fineFood-white.png" size="42px" /> -->
        <span>江泰领悦广场</span>
      </div>
      <div v-if="this.$store.getters.getRegionId == 1" class="nav-right">
        <van-icon name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/order/foodorder.png" size="32px" @click="goOrder" />
      </div>
    </div>
    <div class="positionHeight" />
  </div>
</template>

<script>
export default {
  props: {
    type: {
      type: [Number, String],
      default: 1
    }
  },
  data() {
    return {
    }
  },
  created() {
  },
  mounted() {

  },
  methods: {
    goBack() {
      this.$router.push('/')
    },
    goOrder() {
      this.$router.push({
        name: 'SetMealorderList'
      })
    }
  }
}
</script>

<style scoped lang="scss">
    .home {
      margin-top: -1px;
        .nav {
          position: fixed;
          left: 0;
          // top: 0;
          z-index: 2;
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 100%;
            height: 92px;
            background: linear-gradient(270deg,#fe9428 0%, #ff6610 50%, #ff6610);
            padding: 0 33px;
            .nav-left {
                display: flex;
                align-items: center;
                height: 92px;
                ::v-deep .van-icon:first-child{
                    margin-right: 10px;
                }
                ::v-deep .van-icon:nth-child(2){
                    top: 4px;
                }
                font-size: 33px;
                color: #fff;
                font-weight: bold;
            }
            .nav-right{
                height: 92px;
                ::v-deep .van-icon{
                    top: 4px;
                }
            }
        }
        .positionHeight {
          width: 100%;
          height: 92px;
          background: linear-gradient(270deg,#fe9428 0%, #ff6610 50%, #ff6610);
        }
        .address-wrap {
          .title {
            position: absolute;
            top: 0;
            left: 50%;
            transform: translateX(-50%);
            z-index: 10;
            height: 149px;
            padding-top: 40px;
            font-size: 32px;
            color: #333;
            background: #fff;
          }
          .address {
            height: 382px;
            overflow-y: auto;
            margin-top: 149px;
            .address-item {
            width: 710px;
            height: 110px;
            border-bottom: 1px solid #f4f4f4;
            margin: 0 auto 20px;
            .item-detail {
              font-size: 32px;
              color: #222;
              margin-bottom: 8px;
            }
            .item-userinfo {
              font-size: 26px;
              color: #999;
              >span {
                margin-right: 30px;
              }
            }
          }
          }
          .fixedBottom {
            position: fixed;
            width: 100%;
            height: 107px;
            background-color: #fff;
            .addBtn {
              display: flex;
              align-items: center;
              justify-content: center;
              width: 690px;
              height: 88px;
              background: #f5f6f7;
              border-radius: 7px;
              margin: 10px auto 0;
              font-size: 28px;
              color: #222222;
            }
          }
        }
    }
</style>
