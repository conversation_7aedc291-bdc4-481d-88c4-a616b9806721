<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-05-22 16:44:42
 * @LastEditors: 孙立政
 * @LastEditTime: 2023-02-15 16:32:05
-->
<template>
  <div class="home" :style="styleVar">
    <ul class="tab" :class="ifFixTab?'fixTab':''">
      <li v-for="(item,index) in list" :key="item.id" class="tab-item" :class="currentIndex==index?'active':''">
        <span :data-current="index" @click="clickIndex($event,item.id)">{{ item.title }}</span>
        <div v-show="currentIndex==index" class="tab-item-icon" />
      </li>
    </ul>
  </div>
</template>

<script>
export default {
  data() {
    return {
      list: [
        {
          id: 5,
          title: '综合'
        },
        {
          id: 3,
          title: '距离'
        },
        {
          id: 4,
          title: '优惠最高'
        }
      ],
      currentIndex: 0,
      ifFixTab: false
    }
  },
  computed: {
    styleVar() {
      return {
        '---nav-height': this.$store.getters.getStatusHeight + 'px'
      }
    }
  },
  created() {

  },
  mounted() {
    window.addEventListener('scroll', this.handleScrollx, true)
  },
  destroyed() {
    window.removeEventListener('scroll', this.handleScrollx, true)
  },
  methods: {
    // 获取页面滚动距离
    handleScrollx() {
      let top = document.documentElement.scrollTop || document.body.scrollTop || window.pageYOffset
      if (top > 130) {
        this.ifFixTab = true
      } else {
        this.ifFixTab = false
      }
    },
    clickIndex(e) {
      let current = e.target.dataset.current
      if (this.currentIndex == current) {
        return false
      } else {
        this.currentIndex = current
      }
      let data = {
        orderBy: this.list[current].id,
        type: 'tab'
      }
      this.$emit('condition', data)
    }
  }
}
</script>

<style scoped lang="scss">
    .home {
        .fixTab{
          width: 100%;
          position: fixed;
          top: calc(92px + var(---nav-height));
          background-color: #f9f9f9;
          z-index: 2;
          li{
            margin-top: 20px;
          }
        }
        .tab {
            display: flex;
            height: 85px;
            padding-left: 20px;
            color: #333;
            .tab-item{
              font-size: 28px;
              margin-right: 64px;
              font-family: PingFangSC;
            }
            .active {
              font-weight: 600;
              font-size: 32px;
              font-family: PingFangSC-Semibold;
            }
            .tab-item-icon {
              width: 45px;
              height: 15px;
              background-image: url(https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/user/classify/tab-active.png);
              background-size: 100%;
              margin: 3px auto 0;
            }
        }

    }
</style>
