<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-05-22 15:19:57
 * @LastEditors: 孙立政
 * @LastEditTime: 2021-07-19 11:20:09
-->
<template>
  <div class="home">
    <ul class="category">
      <li v-for="(item,index) in cateList" :key="index" class="category-item">
        <img style="width:49px;height:49px;border-radius: 6px;" :src="item.icon" :class="currentIndex==index?'activebd':''" :data-current="index" alt="" @click="clickIndex($event,item.id)">
        <div class="category-name" :class="currentIndex==index?'active':''" :data-current="index" @click="clickIndex($event,item.id)">{{ item.name }}</div>
      </li>
    </ul>
    <Loading :show="loadingShow" />
  </div>
</template>

<script>
import {
  ClassList
} from '@/api/classify'
import Loading from '@/components/Loading/index'
import { addData } from '@/utils/upLog.js'
export default {
  components: {
    Loading
  },
  data() {
    return {
      loadingShow: true,
      currentIndex: -1,
      cateList: []
    }
  },
  created() {
    // 获取分类
    this.getCate()
  },
  methods: {
    getCate() {
      // 美食分类
      let data = {
        // id: this.$store.state.classify.categoryId
        id: this.$route.query.id
      }
      ClassList(data)
        .then((res) => {
          this.loadingShow = false
          if (res.status == 200) {
            this.cateList = res.data
          } else {
            this.$toast(res.message)
          }
        })
    },
    clickIndex(e, id) {
      // 记录二级目录轨迹
      addData(11)
      let current = e.target.dataset.current
      if (this.currentIndex == current) {
        return false
      } else {
        this.currentIndex = current
      }
      let data = {
        categoryId: id,
        type: 'cate'
      }
      this.$emit('condition', data)
    }
  }
}
</script>

<style scoped lang="scss">
    .home {
        .category {
            display: flex;
            width: 100%;
            overflow-x: auto;
            padding-bottom: 37px;
            background: linear-gradient(180deg,#ffffff, #ffffff 46%, rgba(255,255,255,0.00));
            .category-item {
              flex-shrink: 0;
                width: 126px;
                text-align: center;
                font-family:PingFangSC;
                .category-name {
                    font-size: 26px;
                    transform: scale(.9);
                    margin-top: -5px;
                }
                .active {
                  color: #ff6a32;
                }
            }

        }
    }
</style>
