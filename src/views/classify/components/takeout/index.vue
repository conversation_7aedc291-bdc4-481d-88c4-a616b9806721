<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-05-22 15:17:21
 * @LastEditors: 孙立政
 * @LastEditTime: 2023-04-03 13:58:29
-->
<template>
  <div class="home">
    <!-- 导航栏 -->
    <Nav v-if="addStatus" @restSearch="restSearch" />
    <!-- 搜索框 -->
    <Search />
    <!-- 分类 -->
    <Category @condition="changeRest" />
    <!-- tab切换 -->
    <Tab ref="restStatus" @condition="changeRest" />
    <!-- 店铺列表 -->

    <!-- 优惠券店铺列表 -->
    <!-- <GoodsCard v-for="(item,index) in couponShopList" :key="index+'coupon'" type="1" :list="item" /> -->

    <GoodsCard v-for="(item,index) in list" v-show="isShow(item,index)" :key="index" type="1" :list="item" />

    <!-- 搜索列表 -->
    <!-- <van-list v-model="loading" :finished="finished" finished-text="" @load="onLoad">
      <GoodsCard v-for="(item,index) in list" v-show="isShow(item,index)" :key="index" type="1" :list="item" />
    </van-list> -->

    <Loading :show="loadingShow" />
  </div>
</template>

<script>
import Nav from './components/nav'
import Search from './components/search'
import Category from './components/category'
import Tab from './components/tab'
import GoodsCard from '../../../../components/GoodsCard'
import { MarketList } from '@/api/classify'
import { marketCouponList } from '@/api/coupon'
import Loading from '@/components/Loading/index'
export default {
  components: {
    Nav,
    Search,
    Category,
    Tab,
    GoodsCard,
    Loading
  },
  data() {
    return {
      query: {
        marketRequestVO: {
          page: 1,
          size: 2000,
          latitude: this.$store.getters.getLocation.latitude,
          longitude: this.$store.getters.getLocation.longitude,
          regionId: this.$store.getters.getRegionId,
          categoryId: '',
          orderBy: 1,
          isTakeaway: this.$route.query.isTakeaway
        }
      },
      list: [],
      finished: false,
      loading: false,
      listNull: false,
      addStatus: false,
      couponShopList: [],
      isDistanceShop: [],
      loadingShow: true
    }
  },
  created() {
    // this.marketCouponList()
    this.getList()
  },
  methods: {
    onLoad() {
      this.query.marketRequestVO.page++
      this.getList()
    },
    // 获取列表
    getList1() {
      MarketList(this.query).then((res) => {
        if (res.status == 200) {
          this.addStatus = true
          this.loading = false
          this.list.push(...res.marketVOList)
          if (res.marketVOList.length == 0) {
            this.finished = true
            this.listNull = true
          }

          // this.marketCouponList()
        } else {
          this.finished = true
          this.$toast(res.message)
        }
      })
    },
    // 获取全部列表
    getList() {
      this.$toast.loading({
        message: '',
        duration: 0,
        forbidClick: true
      })
      MarketList(this.query).then((res) => {
        this.$toast.clear()
        this.loadingShow = false
        if (res.status == 200) {
          this.addStatus = true
          this.loading = false
          this.list = res.marketVOList

          // this.marketCouponList()
        } else {
          this.finished = true
          this.$toast(res.message)
        }
      })
    },
    // 获取优惠券外卖店铺
    marketCouponList() {
      let data = {
        'lat': this.$store.getters.getLocation.latitude,
        'lng': this.$store.getters.getLocation.longitude,
        'poolId': this.$store.getters.getRegionId,
        'regionId': this.$store.getters.getRegionId,
        'userId': this.$store.getters.getUserId,
        'filterBy': this.query.marketRequestVO.filter,
        'orderBy': this.query.marketRequestVO.orderBy == 1 ? 1 : this.query.marketRequestVO.orderBy,
        'categoryId': this.query.marketRequestVO.categoryId
      }
      marketCouponList(data).then((res) => {
        if (res.status == 200) {
          let data = res.data
          let isDistanceShop = []
          let distanceShop = []
          for (let i = 0; i < data.length; i++) {
            if (data[i].isDistanceExceeded) {
              isDistanceShop.push(data[i])
            } else {
              distanceShop.push(data[i])
            }
          }
          this.isDistanceShop = isDistanceShop
          this.couponShopList = distanceShop

          this.getList()
        }
      })
    },
    // 变化查询条件
    changeRest(data) {
      this.list = []
      this.query.marketRequestVO.page = 1
      if (data.type == 'tab') { // tab-综合/距离切换
        this.query.marketRequestVO.orderBy = data.orderBy
        this.query.marketRequestVO.filter = data.filter
      } else if (data.type == 'cate') { // 二级分类切换
        this.query.marketRequestVO.categoryId = data.categoryId
      }

      this.finished = false

      this.$toast.loading({
        message: '',
        duration: 1,
        forbidClick: true
      })

      // this.$toast.clear()
      // this.marketCouponList()
      this.getList()
    },
    // 重置查询条件
    restSearch() {
      // this.list = []
      this.query = {
        marketRequestVO: {
          page: 1,
          size: 2000,
          latitude: this.$store.getters.getLocation.latitude,
          longitude: this.$store.getters.getLocation.longitude,
          regionId: this.$store.getters.getRegionId,
          categoryId: '',
          orderBy: 1,
          isTakeaway: this.$route.query.isTakeaway
        }
      }

      this.getList()
      this.$refs.restStatus.restStatus()
      // this.marketCouponList()
    },
    // 判断店铺是否展示
    isShow(row, index) {
      let data = this.couponShopList
      let isDistancedata = this.isDistanceShop
      if (data.length > 0) {
        for (let i = 0; i < data.length; i++) {
          if (row.id === data[i].id) {
            this.list[index].isShow = true
          }
        }
      }
      // 不在配送距离 优惠券插入
      if (isDistancedata.length > 0) {
        for (let i = 0; i < isDistancedata.length; i++) {
          if (row.id === isDistancedata[i].id) {
            this.list[index].couponList = isDistancedata[i].couponList
          }
        }
      }

      return true
    }
  }
}
</script>

<style scoped lang="scss">
</style>
