<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-05-22 16:44:42
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-10-11 15:38:48
-->
<template>
  <div class="home">
    <van-dropdown-menu active-color="#169D1B">
      <van-dropdown-item v-model="orderBy" :options="option" @change="clickIndex" />
    </van-dropdown-menu>
    <div class="tab_item">
      <ul class="tab">
        <li v-for="(item,index) in list" :key="item.value" class="tab-item" :class="ifIndex(index)?'active':''">
          <span :data-current="index" @click="clickIndexList(item.value,index)">{{ item.text }}</span>
        </li>
      </ul>
      <div v-if="couponCount>0" class="coupon" @click="goMyCoupon">
        <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/coupon/couponlicon.png" alt="">
        {{ couponCount }}张优惠券
      </div>
    </div>

  </div>
</template>

<script>
import { myCouponCount } from '@/api/coupon'
export default {
  data() {
    return {
      orderBy: 1,
      currentIndex: [],
      filter: [],
      option: [
        { text: '综合排序', value: 1 },
        { text: '销量优先', value: 2 },
        { text: '距离优先', value: 3 }
      ],
      list: [
        { text: '减配送费', value: 1 },
        { text: '0元起送', value: 2 }
      ],
      couponCount: 0
    }
  },
  created() {
    this.myCouponCount()
  },
  methods: {
    myCouponCount() {
      myCouponCount({
        regionId: this.$store.getters.getRegionId
      }).then(res => {
        if (res.status == 200) {
          this.couponCount = res.data
        }
      })
    },
    goMyCoupon() {
      this.$router.push('/my/coupon')
    },
    // tab
    clickIndex(e) {
      let data = {
        orderBy: this.orderBy,
        type: 'tab',
        filter: this.filter
      }
      this.$emit('condition', data)
    },
    // 标签
    clickIndexList(e, index) {
      if (this.currentIndex.includes(index)) {
        this.currentIndex.splice(this.currentIndex.indexOf(index), 1)
        this.filter.splice(this.filter.indexOf(e), 1)
      } else {
        this.currentIndex.push(index)
        this.filter.push(e)
      }
      let data = {
        orderBy: this.orderBy,
        type: 'tab',
        filter: this.filter
      }
      this.$emit('condition', data)
    },
    ifIndex(index) {
      return this.currentIndex.includes(index)
    },
    restStatus() {
      this.orderBy = 1
    }
  }
}
</script>

<style scoped lang="scss">
    .home {
      ::v-deep .van-dropdown-menu{
        .van-dropdown-menu__bar{
          background-color: rgba(0,0,0,0);
          box-shadow: 0px 0px 0px rgba(0,0,0,0);
          .van-dropdown-menu__item{
            -webkit-justify-content: left;
            justify-content: left;
            font-size: 30px;
            font-family: PingFangSC-Medium;
          }
        }
        .van-popup{
          background-color: #f9f9f9;
        }
        .van-cell{
          font-size: 26px;
          background-color: rgba(0,0,0,0);
        }
        .van-cell::after{
          border-bottom: 0px solid #ebedf0;
          font-family: PingFangSC;
        }
        .van-cell__value{
          opacity: 0;
        }
        .van-dropdown-item__content {
          border-bottom-left-radius: 16px;
          border-bottom-right-radius: 16px;
        }
      }

        .tab {
            display: flex;
            height: 85px;
            margin-left: 20px;
            color: #333;
            .tab-item{
              width: 120px;
              height: 54px;
              font-size: 24px;
              margin-right: 16px;
              border-radius: 6px;
              background: #ededed;
              font-family: PingFangSC;
              text-align: center;
              line-height: 54px;
              color: #333333;
            }
            .active {
              background: #DEF6DF;
              color: #169d1b;
              font-family: PingFangSC;
            }
            .tab-item-icon {
                width: 45px;
                height: 15px;
                background-image: url(https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/user/classify/tab-active.png);
                background-size: 100%;
                margin: 3px auto 0;
            }
        }
        .tab_item{
          display: flex;
          justify-content: space-between;
        }
        .coupon{
          min-width: 208px;
          height: 48px;
          line-height: 48px;
          font-size: 28px;
          color: #333333;
          background-image: url(https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/coupon/couponbg.png);
          background-size: 100% 100%;
          background-repeat: no-repeat;
          img{
            width: 32px;
            height: 32px;
            position: relative;
            top: 5px;
            margin-left: 20px;
          }
        }
    }
</style>
