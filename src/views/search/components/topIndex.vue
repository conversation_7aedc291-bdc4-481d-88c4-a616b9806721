<template>
  <div class="home">
    <div class="content">
      <div class="top">
        <div class="left" @click="goBack()">
          <van-icon
            name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/user/icon/arrow-black.png"
            size="20px"
          />
        </div>
        <div class="center">
          <van-search
            v-model="keyword"
            placeholder="请输入商家或商品名称"
            autofocus
            @search="goSearch"
            @clear="goClear"
          />
        </div>
        <div class="right" @click="goSearch">搜索</div>
      </div>
    </div>
    <div style="height:55px" />
  </div>
</template>

<script>
import { addSearchData, addData } from '@/utils/upLog.js'
export default {
  data() {
    return {
      keyword: this.$store.state.searchKey
    }
  },
  mounted() {
  },
  methods: {
    goClear() {
      this.$store.state.searchKey = ''
      this.keyword = ''
    },
    goBack() {
      this.$store.state.searchKey = ''
      this.keyword = ''
      this.$router.go(-1)
      // this.$router.push('/index')
    },
    goSearch() {
      // 搜索
      if (this.keyword == '') {
        this.$toast('请输入关键字')
        return false
      }
      this.$store.state.searchKey = this.keyword
      this.$store.state.home.historyKey.push(this.keyword)
      this.$emit('function')

      // 记录搜索轨迹
      addData(4)
      addSearchData(this.keyword)

      this.keyword = ''
      this.$router.push({
        name: 'Results',
        query: {
          istakeaway: false
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.content {
  width: 100%;
  position: fixed;
  // top: 0;
  z-index: 2;
  background-color: #fff;
  .top {
    display: flex;
    align-items: center;
    width: 100%;
    height: 110px;
    background-color: #fff;

    .left {
      margin-left: 34px;
      margin-right: 18px;
      margin-top: -15px;
    }

    .center {
      display: flex;
      align-items: center;
      // margin-left: 32px;
      ::v-deep .van-search {
        background: #f1f2f2;
        padding: 0;
        width: 538px;
        border-radius: 34px;
        overflow: hidden;
      }

      ::v-deep .van-field__left-icon {
        color: #8e8e93;
      }

      ::v-deep .van-cell__value--alone {
        color: #c4c4c7;
      }
      input {
					border: none;
				}
    }

    .right {
      font-size: 28px;
      color: #333;
      margin-left: 32px;
    }
  }
}
</style>
