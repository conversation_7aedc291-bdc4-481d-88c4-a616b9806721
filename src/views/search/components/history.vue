<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-05-27 18:56:52
 * @LastEditors: 孙立政
 * @LastEditTime: 2021-07-05 11:51:17
-->
<template>
  <div class="home">
    <div class="history">
      <div class="history-title">
        <div class="name">历史搜索</div>
        <van-icon name="delete" size="20px" color="#acafb6" @click="delClick" />
      </div>
      <div class="history-tag">
        <div v-for="(item,index) in $store.state.home.historyKey" :key="index" class="tag-one" @click="goSearch(item)">{{ item }}</div>
      </div>
      <!-- <div class="history-title mt16">
        <div class="name">热门搜索</div>
      </div>
      <div class="history-tag">
        <div v-for="(item,index) in $store.state.home.historyKey" :key="index" class="tag-one" @click="goSearch(item)">{{ item }}</div>
      </div> -->
    </div>
  </div>
</template>

<script>
export default {
  components: {},
  data() {
    return {}
  },
  mounted() {},
  methods: {
    goSearch(item) {
      this.$store.state.searchKey = item
      // 搜索
      this.$router.push({
        path: '/results',
        query: {
          // goodsName: item,
          // istakeaway: "true",
        }
      })
    },
    delClick() {
      // 清空
      this.$store.state.searchKey = ''
      this.$store.state.home.historyKey = []
    }
  }
}
</script>

<style lang="scss" scoped>
  .home::before {
  // 利用伪元素设置整个页面的背景色
  content: " ";
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: -100;
  min-height: 100%;
  background-color: #fff;
}
	.home {
		width: 690px;
		margin: 0 auto;
		.history {
			color: #000010;
			margin-top:6px;
			.history-title {
				display: flex;
				align-items: center;
				justify-content: space-between;
				margin-bottom: 24px;
        margin-top: 30px;
        .name {
          color: #333;
          font-size: 26px;
        }
			}
			.history-tag {
				display: flex;
				flex-wrap: wrap;
				.tag-one {
					flex-shrink: 0;
					font-size: 26px;
					padding: 8px 27px;
					border-radius:8px;
					background-color: #f5f6f7;
					color: #333;
					margin-right: 16px;
					margin-bottom: 24px;
				}
			}
      .mt16 {
        margin-top: 16px;
      }
		}
	}
</style>
