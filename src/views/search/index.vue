<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-05-27 18:41:26
 * @LastEditors: 孙立政
 * @LastEditTime: 2021-12-30 22:00:59
-->
<template>
  <div class="home">
    <NavHeight bgc="#fff" />
    <Top />
    <History />
  </div>

</template>

<script>
import Top from './components/topIndex'
import History from './components/history'
export default {
  components: {
    Top,
    History
  },
  data() {
    return {

    }
  },
  created() {

  },
  mounted() {

  },
  methods: {

  }
}
</script>

<style scoped lang="scss">
</style>
