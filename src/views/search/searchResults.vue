<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-05-27 18:41:26
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-05-26 16:55:03
-->
<template>
  <div class="home">
    <NavHeight bgc="#fff" />
    <Top @function="goSearch" />
    <GoodsCard v-for="(item,index) in list" :key="index" type="3" :list="item" />
    <van-empty v-if="list.length == 0" description="暂时没有找到相关信息" />
    <Loading :show="loadingShow" />
  </div>

</template>

<script>
import Top from './components/top'
import GoodsCard from '../../components/GoodsCard'
import { searchList	} from '@/api/classify'
import Loading from '@/components/Loading/index'
// import { addSearchData, addData } from '@/utils/upLog.js'
export default {
  components: {
    Top,
    GoodsCard,
    Loading
  },
  data() {
    return {
      list: [],
      loadingShow: true,
      goodsName: ''
    }
  },
  created() {
    this.goodsName = this.$store.state.searchKey
    this.tagName = this.$route.query.tagName
    this.istakeaway = this.$route.query.istakeaway
    this.tagId = this.$route.query.tagId
    this.titleName = this.$route.query.titleName
    this.keyword = this.goodsName || this.tagName
    this.getList()
  },
  mounted() {

  },
  methods: {
    getList() {
      let data = {
        marketRequestVO: {
          tag: this.tagName,
          goodsName: this.goodsName,
          latitude: this.$store.getters.getLocation.latitude,
          longitude: this.$store.getters.getLocation.longitude,
          page: 1,
          size: 1000,
          regionId: this.$store.getters.getRegionId,
          orderBy: 1
        }
      }

      // 判断是否龙泉bannner跳入
      if (this.$route.query.isLqBanner == true) {
        data.marketRequestVO.unionPayCode = 'https'
        data.marketRequestVO.categoryIds = [46, 56, 67]
      }

      data.marketRequestVO.isTakeaway = this.istakeaway ? true : null
      console.log(this.$route.query.categoryId)
      data.marketRequestVO.categoryId = this.$route.query.categoryId
      let urls
      if (typeof this.goodsName == 'undefined') {
        urls = 'searchByMarketCategory'
      } else if (typeof this.tagName == 'undefined') {
        urls = 'searchByGoodsName'
      }
      searchList(data, urls)
        .then((res) => {
          this.loadingShow = false
          if (res.status == 200) {
            this.list.push(...res.marketVOList)
          } else {
            this.$toast(res.message)
          }
        })
    },
    goSearch() {
      this.loadingShow = true
      this.list = []
      this.goodsName = this.$store.state.searchKey
      this.getList()
    }
  }
}
</script>

<style scoped lang="scss">
</style>
