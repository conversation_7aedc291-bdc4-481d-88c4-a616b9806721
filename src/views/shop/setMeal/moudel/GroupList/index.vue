<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2022-09-19 11:46:40
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-09-27 15:17:21
-->
<template>
  <div class="home">
    <NavHeight bgc="#f2f2f2" />
    <div class="back" :style="styleVar" @click="goBack">
      <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/loginV2/left.png">
    </div>

    <!-- <div v-show="opacity>=70" class="toptab">
      <Tab :info="info" @handTab="handTab" />
    </div> -->
    <Tab :info="info" @handTab="handTab" />
    <Information :info="info" />

    <PackageContent :info="info" />

    <Tips :info="info" />
    <Agein :info="info" />
    <Bottom :info="info" />

    <div style="height: 120px;" />
  </div>
</template>

<script>
import { setMealInfo } from '@/api/shop'
import { Tab, Information, PackageContent, Tips, Agein, Bottom } from './components'
import { Toast } from 'vant'
export default {
  components: {
    Tab, Information, PackageContent, Tips, Agein, Bottom
  },
  data() {
    return {
      info: {
        setMealConfig: {
          notAvailableWeekly: ''
        },
        isHolidays: 0,
        tabIndex: 0

      },
      opacity: 0
    }
  },
  computed: {
    styleVar() {
      return {
        '---nav-height': this.$store.getters.getStatusHeight + 'px'
      }
    }
  },
  mounted() {
    // js动态获取高度
    // window.addEventListener('scroll', this.handleScrollx, true)
  },
  destroyed() {
    // window.removeEventListener('scroll', this.handleScrollx, true)
  },
  created() {
    this.tabIndex = this.$route.query.id
    this.getSetMealInfo()
    document.body.scrollTop = 0
    document.documentElement.scrollTop = 0
  },
  methods: {
    getSetMealInfo() {
      Toast.loading({
        duration: 10000,
        forbidClick: true
      })
      setMealInfo(this.tabIndex).then(res => {
        setTimeout(() => {
          Toast.clear()
        }, 800)
        if (res.status === 200) {
          this.info = res.data
        }
      })
    },
    // 切换tab
    handTab(val) {
      this.tabIndex = val
      this.getSetMealInfo()
    },
    goBack() {
      this.$router.go(-1)
    },
    // 监听滚动
    handleScrollx() {
      // 获取页面滚动距离
      let top = document.documentElement.scrollTop || document.body.scrollTop || window.pageYOffset
      console.log(top)
      this.opacity = top
    }
  }
}
</script>

<style scoped lang="scss">
.home {

  .back {
    width: 100%;
    height: 80px;
    position: fixed;
    top: calc(var(---nav-height));
    background-color: #f2f2f2;
    overflow: hidden;
    z-index: 3;
    img {
      width: 40px;
      height: 40px;
      margin-left: 34px;
      display: block;
      margin-top: 20px;
    }
  }
  .toptab{
    width: 100%;
    position: fixed;
    top: calc(var(---nav-height));
    z-index: 999999;
  }
}
</style>
