<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2022-09-19 15:20:59
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-10-22 10:14:10
-->
<template>
  <div class="tips">
    <div class="box">
      <div class="title">有效期</div>
      <div class="conents">
        <span style="font-size: 13px;">{{ info.reviewTime?info.reviewTime:'/' }} 至 {{ info.invalidTime }} </span>
        <!-- <span v-if="info.setMealConfig.notAvailableWeekly!==''">（{{ info.setMealConfig.notAvailableWeekly }}不可用、</span>
        <span v-if="info.setMealConfig.isHolidays === 1">法定节假日不可用、</span>
        <span v-if="info.setMealConfig.notAvailableOther!=''" style="margin-left: 8px;">{{ info.setMealConfig.notAvailableOther }}</span>
        <span>）</span> -->

        <span v-if="info.setMealConfig.notAvailableWeekly!==''">（{{ info.setMealConfig.notAvailableWeekly }}</span>
        <span v-if="info.setMealConfig.isHolidays === 1&&info.setMealConfig.notAvailableWeekly!==''">、</span>
        <span v-if="info.setMealConfig.isHolidays === 1">法定节假日</span>

        <span v-if="info.setMealConfig.isHolidays === 1||info.setMealConfig.notAvailableWeekly!==''">不可用</span>
        <span v-if="info.setMealConfig.notAvailableOther!=''" style="margin-left: 8px;color: red;">{{ info.setMealConfig.notAvailableOther }}</span>
        <span>）</span>
      </div>
      <div class="title">使用时间</div>
      <div class="conents">
        <span v-if="info.setMealConfig.dissipate === 0">
          <span style="font-size: 13px;margin-right: -3px;">24</span>
          小时可用</span>
        <span v-for="(item,index) in info.setMealConfig.dissipateTimes" v-else :key="index" style="margin-right: 6px;">{{ item.startTime.substring(0, item.startTime.length - 3) }}-{{ item.endTime.substring(0, item.endTime.length - 3) }} </span>
      </div>
      <div class="title">使用规则</div>
      <ul class="conents">
        <li>
          <span v-if="info.setMealConfig.supportWifi === 0">商家提供免费WiFi</span>
          <span v-else>商家不提供WiFi</span>
        </li>
        <li>
          <span v-if="info.setMealConfig.supportProvideInvoice === 0">本单发票由商家提供，请在消费时向商家索取</span>
          <span v-else>发票问题，请咨询商家</span>

        </li>
        <li>
          <span v-if="info.setMealConfig.supportTakeOut === 0">堂食外卖均可打包</span>
          <!-- <span v-else>仅限堂食，不提供餐前外带，餐毕未吃完可打包</span> -->
          <span v-else>仅限堂食</span>
        </li>
        <li>
          <span v-if="info.setMealConfig.supportUseBox === 0">可使用包厢</span>
          <span v-else>不可使用包厢，包厢问题咨询商家</span>
        </li>
        <li>
          <span v-if="info.setMealConfig.supportFreeParking === 0">免费提供车位</span>
          <span v-else>不提供免费车位</span>
        </li>
        <li>
          <span v-if="info.setMealConfig.meanwhileDiscount === 1">团购用户不可同时享受商家其他优惠</span>
          <span v-else>同享优惠咨询商家</span>
        </li>
        <li>酒水饮料等问题，请致电商家咨询，以商家反馈为准如部分菜品因时令或其他不可抗因素导致无法提供，商家会用等价菜品替换，具体事宜请与商家协商</li>
        <li>
          <span v-if="info.setMealConfig.makeAnAppointment === 0">无需预约，消费高峰期可能需要等位</span>
          <span v-else>预约: {{ info.setMealConfig.appointmentOther }}</span>
        </li>
        <li v-if="info.setMealConfig.restrictedUsePerson > 0">每人最多使用{{ info.setMealConfig.restrictedUsePerson }}张套餐团购券</li>
        <li v-if="info.setMealConfig.restrictedUseTable > 0">每桌最多使用{{ info.setMealConfig.restrictedUseTable }}张套餐团购券</li>
      </ul>
      <div class="title">备注</div>
      <div class="conents">
        {{ info.setMealConfig.supplementaryNote?info.setMealConfig.supplementaryNote:'无' }}
      </div>
      <div style="height: 10px;" />
    </div>
  </div>
</template>

<script>
export default {
  props: {
    info: {
      type: Object,
      default: function() {
        return {}
      }
    }
  },
  data() {
    return {}
  },
  created() {},
  mounted() {},
  methods: {
    // 格式化周几
    formWeekly(val) {
      let week = ['周一', '周二', '周三', '周四', '周五', '周六', '周天']
      let arr = val.split('、')
      let newarr = []
      for (let i = 0; i < week.length; i++) {
        for (let j = 0; j < arr.length; j++) {
          if (week[i] == arr[j]) {
            newarr.push(week[i])
          }
        }
      }
      let result = []
      for (let i = 0; i < week.length; i++) {
        let obj = week[i]
        let isExist = false
        for (let j = 0; j < newarr.length; j++) {
          let aj = newarr[j]
          if (aj == obj) {
            isExist = true
            break
          }
        }
        if (!isExist) {
          result.push(obj)
        }
      }
      const format1 = ['周一', '周二', '周三', '周四', '周五']
      const format2 = ['周一', '周二', '周三', '周四', '周五', '周六']
      if (result == format1) {
        return '周一至周五'
      } else if (result == format2) {
        return '周一至周六'
      } else {
        return result.join('、')
      }
    }
  }
}
</script>

<style scoped lang="scss">
.tips {
  width: 706px;
  background: #ffffff;
  border-radius: 24px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  margin-top: 20px;
  .box {
    width: 93%;
    height: 93%;
    margin: 0 auto;
  }
  .title {
    height: 80px;
    line-height: 80px;
    font-size: 30px;
    font-family: PingFangSC-Medium;
  }
  .conents{
    color: #333333;
    font-size: 28px;
    li{
      list-style-type:disc;
      margin-left: 30px;
    }
  }
}
</style>
