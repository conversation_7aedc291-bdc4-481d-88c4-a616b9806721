<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2022-09-19 15:28:46
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-09-27 14:43:34
-->
<template>
  <div class="agein">
    <div class="box">
      <div class="title">使用商户</div>
      <div class="conents">
        <div class="card">
          <img
            :src="shopData.pic"
            alt=""
            class="card-img"
          >
          <div class="card-content">
            <div class="card-content-name">{{ shopData.marketName }}</div>
            <div class="card-shop-desc">
              <div class="card-shop-evaScore">{{ shopData.evaScore }}</div>

              <van-rate
                v-model="shopData.evaScore"
                class="card-shop-rate"
                allow-half
                readonly
                :size="13"
                color="#ffd21e"
                void-icon="star"
                void-color="#eee"
              />
              <div v-for="(item,index) in shopData.tagTitleList.slice(0,4)" :key="index" class="card-shop-tag">
                {{ item }}
              </div>
            </div>
            <div class="card-content-desc">
              {{ shopData.address+'' }}
              <span class="km">
                <van-icon name="location-o" />
                {{ shopData.distance.toFixed(2) }}km
              </span>

            </div>

            <div class="card-shop-status">
              <span v-if="shopData.status == true">营业中</span>
              <span v-if="shopData.status == false">休息中</span>
              <span v-for="(item,index) in shopData.marketConfig.marketBusTimes" :key="index" style="margin-right: 6px;font-size: 13px;">{{ item.openingHours }}-{{ item.closingHours }}</span>
            </div>
          </div>
          <div class="card-right" @click="CallPhone(shopData.mobilePhone)">
            <div>
              <img
                src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/shop/fineFood/phone.png"
                alt=""
              >
            </div>
            <div>联系商家</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getShopData } from '@/api/shop'
export default {
  data() {
    return {
      shopData: {
        tagTitleList: [],
        distance: 0,
        marketConfig: {
          marketBusTimes: [{
            openingHours: '',
            closingHours: ''
          }]
        }

      }
    }
  },
  created() {
    this.getShopData()
  },
  methods: {
    // 获取店铺详情
    getShopData() {
      let data = {
        marketId: this.$route.query.marketId,
        longitude: this.$store.getters.getLocation.longitude,
        latitude: this.$store.getters.getLocation.latitude
      }
      getShopData(data).then((res) => {
        this.shopData = res.data
      })
    },
    // 拨打电话
    CallPhone(data) {
      AlipayJSBridge.call('CallPhone', {
        phoneNum: data
      }, function(result) {})
    }
  }
}
</script>

<style scoped lang="scss">
.agein {
  width: 706px;
  min-height: 300px;
  background: #ffffff;
  border-radius: 24px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  margin-top: 20px;
  .box {
    width: 93%;
    min-height: 93%;
    margin: 0 auto;
  }
  .title {
    min-height: 80px;
    line-height: 80px;
    font-size: 40px;
    font-family: PingFangSC-Medium;
  }
  .conents {
    .card {
      display: flex;
      min-height: 205px;
      .card-img {
        width: 180px;
        height: 180px;
        margin-right: 24px;
        border-radius: 8px;
        overflow: hidden;
      }
      .card-content {
        width: 350px;
        position: relative;
        min-height: 180px;
        .card-content-name {
          font-size: 33px;
          font-family: PingFangSC-Medium;
          font-weight: 500;
          color: #222222;
          margin-bottom: 8px;
        }
        .card-shop-desc {
          display: flex;
          align-items: center;
          min-height: 30px;
          line-height: 30px;
          .card-shop-evaScore {
            font-size: 26px;
            color: #ffc61a;
            margin-right: 10px;
          }
          .card-shop-rate {
            min-height: 30px;
            line-height: 30px;
            margin-top: 2px;
          }
          .card-shop-tag {
            font-size: 25px;
            font-family: PingFangSC;
            color: #333;
            margin-left: 8px;
          }
        }
        .card-content-desc {
          font-size: 26px;
          font-family: PingFangSC;
          color: #787878;
          margin-top: 10px;
          .km{
            font-size: 23px;
          }
        }
        .card-shop-status {
          margin-bottom: 16px;
          font-size: 27px;
          color: #222222;
          margin-top: 10px;
          > span {
            margin-right: 20px;
          }
        }
      }
      .card-right {
        text-align: center;
        img {
          width: 33px;
          height: 33px;
          margin-top: 60px;
        }
        font-size: 23px;
        color: #333;
      }
    }
  }
}
</style>
