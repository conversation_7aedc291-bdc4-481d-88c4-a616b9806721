<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2022-09-20 11:25:36
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-09-21 16:59:09
-->
<template>
  <div class="tab" :style="styleVar">
    <!-- <ul>
      <li
        v-for="item in list"
        :key="item.id"
        :class="item.id === tabIndex ? 'in_tab' : ''"
      >
        <div>{{ item.name }}</div>
        <div v-if="item.id === tabIndex" class="border_Bottom" />
      </li>
    </ul> -->

    <van-tabs v-model="tabIndex" :ellipsis="false" background="#f2f2f2" @change="changeTab">
      <van-tab v-for="item in list" :key="item.id" :title="item.setMealName " :name="item.id" />
    </van-tabs>
  </div>
</template>

<script>
import { setMealList } from '@/api/shop'
export default {
  props: {
    info: {
      type: Object,
      default: function() {
        return {}
      }
    }
  },
  data() {
    return {
      list: [],
      tabIndex: 0,
      query: {
        'pageNum': 1,
        'pageSize': 1000,
        'search': this.$route.query.marketId
      }
    }
  },
  computed: {
    styleVar() {
      return {
        '---nav-height': this.$store.getters.getStatusHeight + 'px'
      }
    }
  },
  created() {
    this.tabIndex = Number(this.$route.query.id)
    this.getList()
  },
  methods: {
    // 获取列表
    getList() {
      setMealList(this.query).then(res => {
        if (res.status == 200) {
          this.list = res.data.list
        } else {
          this.$toast(res.message)
        }
      })
    },
    changeTab(val) {
      // this.$router.push({
      //   name: 'GroupList',
      //   query: {
      //     id: val,
      //     marketId: this.$route.query.marketId
      //   }
      // })
      this.$emit('handTab', val)
    }
  }
}
</script>

<style scoped lang="scss">
.tab {
  margin-top: calc(50px + var(---nav-height));
  ::v-deep .van-tabs__line{
    min-width: 100px;
    top: 58px;
    background: linear-gradient(270deg, #ff5922 98%, #fe8125 1%);
  }
  ::v-deep .van-tab--active{
    font-size: 36px;
    font-family: PingFangSC-Medium;
    font-weight: 500;
  }
  ::v-deep .van-tab{
    font-size: 33px;
    color: #222222;
  }
  ul {
    width: 660px;
    height: 60px;
    margin: 0 auto;
    overflow-x: auto;
    display: flex;
    font-size: 33px;
    color: #222222;
    margin-top: 15px;
    background-color: #f2f2f2;

    li {
      margin-right: 50px;
      text-align: center;
      white-space: nowrap;
      .border_Bottom {
        height: 8px;
        background: linear-gradient(270deg, #ff5922 98%, #fe8125 1%);
        border-radius: 4px;
        margin-top: -15px;
      }
    }
    .in_tab {
      font-size: 36px;
      font-family: PingFangSC-Medium;
      font-weight: 500;
    }
  }
}
</style>
