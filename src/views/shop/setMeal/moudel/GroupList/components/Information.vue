<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2022-09-19 14:21:51
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-11-03 09:55:27
-->
<template>
  <div class="information">
    <div class="box">
      <div class="title">{{ info.setMealName }}</div>
      <div class="conents">
        <!-- <img
          :src="info.headPicture"
          alt=""
        > -->
        <van-swipe :show-indicators="false" class="my-swipe" :autoplay="3000" indicator-color="white">
          <van-swipe-item v-for="(item,index) in info.picture.split(',')" :key="index">
            <img
              :src="item"
              alt=""
            >
          </van-swipe-item>
        </van-swipe>
      </div>
      <div class="price">
        <div class="card-content-other">
          <div class="card-content-price">
            <span>¥</span>
            <span>{{ info.specialPrice }}</span>
          </div>
          <div v-if="info.setMealDiscount>0" class="card-content-discount">{{ info.setMealDiscount }}折</div>
          <div class="card-content-oriPrice">¥{{ info.originalPrice }}</div>
        </div>
        <div class="card-content-distance" style="margin-left: -2px;">已售<span style="font-size: 13px;">{{ info.salesQuantity }}</span> </div>
      </div>
      <div class="divider" />
      <div class="guarantee">
        <span>保障</span>
        <span>随时退·过期自动退</span>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    info: {
      type: Object,
      default: function() {
        return {}
      }
    }
  },
  data() {
    return {}
  },
  created() {},
  mounted() {},
  methods: {
    // 计算折扣
    getDiscount(val1, val2) {
      let num = val1 / val2
      var f_x = parseFloat(num)
      if (isNaN(f_x)) {
        // alert('function:changeTwoDecimal->parameter error')
        return false
      }
      f_x = Math.round(num * 100) / 100
      var s_x = f_x.toString()
      var pos_decimal = s_x.indexOf('.')
      if (pos_decimal < 0) {
        pos_decimal = s_x.length
        s_x += '.'
      }
      while (s_x.length == pos_decimal + 1) {
        s_x = s_x.slice(0, s_x.length - 1)
      }
      console.log(s_x)
      if (s_x > 0) {
        return s_x + '折'
      } else {
        return '低于0.1折'
      }
    }
  }
}
</script>

<style scoped lang="scss">
.information {
  width: 706px;
  height: 632px;
  background: #ffffff;
  border-radius: 24px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  margin-top: 20px;
  .box {
    width: 93%;
    height: 93%;
    margin: 0 auto;
    .title {
      height: 80px;
      line-height: 80px;
      font-size: 40px;
      font-family: PingFangSC-Medium;
    }
    .conents {
      img {
        width: 100%;
        height: 368px;
        border-radius: 12px;
        object-fit: cover;
      }
    }
    .price {
      display: flex;
      justify-content: space-between;
      .card-content-other {
        display: flex;
        align-items: center;
        .card-content-price {
          font-size: 42px;
          font-family: PingFangSC-Medium;
          font-weight: 500;
          color: #ff301e;
          margin-right: 8px;
          span:nth-child(1) {
            font-size: 26px;
            font-weight: 500;
            margin-right: 1px;
          }
        }
        .card-content-oriPrice {
          font-size: 24px;
          font-family: PingFangSC;
          color: #999999;
          text-decoration: line-through;
          margin-left: 10px;
        }
        .card-content-discount {
          min-width: 72px;
          height: 31px;
          line-height: 30px;
          text-align: center;
          font-size: 24px;
          font-family: PingFangSC;
          border: 1px solid #ff301e;
          font-family: PingFangSC-Medium;
          color: #ff301e;
          border-radius: 6px;
          margin-right: 5px;
        }
      }
      .card-content-distance {
        color: #333333;
        font-size: 26px;
        margin-top: 10px;
      }
    }
    .divider {
      width: 100%;
      height: 1px;
      margin-top: 8px;
      background-image: url(https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/order/divider.png);
      background-size: 100% 100%;
    }
    .guarantee {
      height: 70px;
      line-height: 70px;
      font-size: 28px;
      span:nth-child(1) {
        margin-right: 20px;
        font-family: PingFangSC-Medium;
      }
    }
  }
}
</style>
