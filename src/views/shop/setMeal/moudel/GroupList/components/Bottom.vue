<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2022-09-19 15:44:34
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-11-24 11:07:03
-->
<template>
  <div class="bottom">
    <div class="call" @click="CallPhone()">
      <img
        src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/shop/fineFood/phone.png"
      >
      <div>联系商家</div>
    </div>
    <div class="btn" @click="goCart">立即抢购 折后￥{{ info.specialPrice }}</div>
  </div>
</template>

<script>
export default {
  props: {
    info: {
      type: Object,
      default: function() {
        return {}
      }
    }
  },
  data() {
    return {}
  },
  created() {},
  mounted() {},
  methods: {
    goCart() {
      // if (this.$store.getters.getRegionId === 3) {
      //   this.$toast('敬请期待')
      //   return
      // }
      this.$store.state.setMail.info = this.info
      this.$router.push({
        name: 'SubmitSetMeal',
        query: {
          id: this.info.id,
          marketSn: this.$route.query.marketSn,
          marketId: this.$route.query.marketId
        }
      })
    },
    // 拨打电话
    CallPhone(data) {
      AlipayJSBridge.call('CallPhone', {
        phoneNum: this.$route.query.tel
      }, function(result) {})
    }
  }
}
</script>

<style scoped lang="scss">
.bottom {
  width: 750px;
  height: 126px;
  position: fixed;
  bottom: 0;
  background: #ffffff;
  box-shadow: -4px -4px 27px 0px rgba(220, 220, 220, 0.5);
  display: flex;
  justify-content: space-between;
  align-items: center;
  .call {
    width:120px;
    text-align: center;
    margin-left: 20px;
    img {
      width: 33px;
      height: 33px;
    }
    font-size: 23px;
  }
  .btn {
    width: 430px;
    height: 94px;
    background: linear-gradient(90deg, #ff1e29, #ff5a25);
    border-radius: 47px;
    font-size: 38px;
    text-align: center;
    line-height: 94px;
    color: #ffffff;
    margin-right: 20px;
  }
}
</style>
