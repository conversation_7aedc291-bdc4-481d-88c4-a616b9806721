<template>
  <!-- 评价内容 -->
  <div v-if="temp.length>0" class="cell">
    <div class="header">
      <div class="header-title">餐厅评价</div>
      <div class="header-allEvalute" @click="goListEv">
        <span>全部评价</span>
        <van-icon
          name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/review/arrow.png"
          size="12"
        />
      </div>
    </div>
    <div v-for="(item,index) in temp" :key="index" class="evalute">
      <div class="evalute-top">
        <img :src="item.userImage?item.userImage:defaultImg" alt="" class="evalute-avatar">
        <div class="evalute-right">
          <div class="content-line1">
            <div class="content-line1-user">{{ item.userName }}</div>
            <div class="content-line1-time">
              <span>{{ item.evaTime }}</span>
            </div>
          </div>
          <div class="content-line2">
            <div class="content-line2-rate">
              <van-rate
                v-model="item.score"
                allow-half
                readonly
                :size="12"
                color="#ffd21e"
                void-icon="star"
                void-color="#eee"
              />
            </div>
          </div>
        </div>
      </div>
      <div class="content-line3">
        <span class="content-line3-tag" />
        <span v-for="tag in item.tbMarketEvaluations" :key="tag.id">{{ tag.evaTypeName }}{{ tag.score | filterRate }}
        </span>
      </div>
      <div class="content-line4">
        {{ item.tbEvaluationContents[0].content }}
      </div>
      <div v-if="item.pictures!=''" class="content-line4">
        <img v-for="( imgList, indexs ) in item.pictures.split(',')" :key="indexs" :src="imgList" alt="" class="content-line4-img">
      </div>
      <div v-if="item.tbEvaluationContents.length" class="content-line7">
        <div v-for="(item1,idx) in item.tbEvaluationContents" :key="idx">
          <div v-if="idx!=0" class="content-line7-shop">
            <div v-if="item1.replierType==1" class="content-line7-reply">我的追评：</div>
            <div v-if="item1.replierType==2" class="content-line7-reply">商家回复：</div>
            <span class="content-line7-feedback">{{ item1.content }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { queryByTypeV2 } from '@/api/shop'
export default {
  components: {
  },
  filters: {
    filterRate(val) {
      if (val >= 0) {
        if (val <= 2) {
          return '差'
        } else if (val <= 3) {
          return '一般'
        } else if (val <= 4) {
          return '满意'
        } else {
          return '非常满意'
        }
      }
    }
  },
  data() {
    return {
      temp: [],
      defaultImg: 'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/shop/appraise/default.png',
      query: {
        'pageNum': 1,
        'pageSize': 3,
        'search': {
          'evaType': 1,
          'marketId': null,
          'orderType': 3
        }
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    // 获取列表
    getList() {
      this.query.search.marketId = this.$route.query.id
      queryByTypeV2(this.query).then(res => {
        if (res.status == 200) {
          this.temp = res.data.list
        } else {
          this.$toast(res.message)
        }
      })
    },
    goListEv() {
      this.$router.push({
        name: 'FineFoodShopEvalute',
        query: {
          id: this.$route.query.id,
          type: 2
        }
      })
    }
  }
}
</script>

  <style scoped lang="scss">
      .cell {
          width: 710px;
          margin: 0 auto 16px;
          border-radius: 15px;
          background-color: #fff;
          overflow-x: hidden;
          .header {
              display: flex;
              justify-content: space-between;
              align-items: center;
              font-size: 22px;
              font-family: PingFangSC;
              color: #666666;
              padding: 24px 16px 20px 18px;
              .header-title {
                font-size: 34px;
                font-family:PingFangSC-Medium;
                font-weight: 500;
                color: #222222;
              }
              .header-allEvalute {
                display: flex;
                align-items: center;
              }
          }
          .evalute {
              width: 100%;
              background-color: #fff;
              padding: 25px 16px  24px 18px;
              box-sizing: border-box;
              .evalute-top {
                display: flex;
                justify-content: space-between;
                .evalute-avatar {
                    flex-shrink: 0;
                    width: 68px;
                    height: 68px;
                    margin-right: 12px;
                    border-radius: 34px;
                    overflow: hidden;
                }
                .evalute-right {
                    .content-line1 {
                      width: 576px;
                      height: 40px;
                      display: flex;
                      justify-content: space-between;
                      align-items: center;

                      .content-line1-user {
                          font-size: 24px;
                          font-family: PingFangSC-Medium;
                          font-weight: 500;
                          color: #333;
                      }

                      .content-line1-time {
                          font-size: 22px;
                          color: #999;
                          font-family: PingFangSC;
                      }
                  }
                    .content-line2 {
                        display: flex;
                        align-items: center;
                        font-size: 22px;
                        // margin-bottom: 16px;
                        margin-top: 3px;
                        .content-line2-rate {
                            ::v-deep .van-rate__item {
                                top:4px;
                            }
                        }
                    }
                }
              }
                .content-line3 {
                    font-size:22px;
                    color: #666;
                    margin-top: 16px;
                    font-family: PingFangSC;
                    line-height: 30px;
                    .content-line3-tag {
                      display: inline-block;
                      background-image: url(https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/user/icon/evalute_tag.png);
                      background-size: 100%;
                      width: 30px;
                      height: 20px;
                      margin-right: 8px;
                    }
                }
                .content-line4 {
                    font-size: 26px;
                    line-height: 37px;
                    color: #333;
                    margin-top: 16px;
                    font-family: PingFangSC;
                    overflow: hidden;
                    .content-line4-img {
                        width: 220px;
                        height: 220px;
                        border-radius: 8px;
                        overflow: hidden;
                        vertical-align: middle;
                    }
                    .content-line4-img:not(:last-child){
                        margin-right: 8px;
                    }
                }
                .content-line7 {
                    font-size: 22px;
                    overflow: hidden;
                    margin-top: 16px;
                    .content-line7-shop {
                      background: #fafafa;
                      padding: 12px 4px 8px 12px;
                      border-radius: 8px;
                    }
                    .content-line7-shop:nth-of-type(2){
                      margin-top: 16px;
                    }
                    .content-line7-feedback {
                      font-family: PingFangSC;
                      color: #666666;
                      line-height: 30px;
                    }
                    .content-line7-reply{
                        color: #333;
                        font-family:PingFangSC-Medium;
                        font-weight: 500;
                        line-height: 30px;
                    }

                }
          }
          .evalute:not(:last-child){
            border-bottom: 1px solid #f4f4f4;
          }
      }
  </style>
