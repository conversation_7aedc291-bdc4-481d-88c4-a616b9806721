<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-07-10 17:49:38
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-09-19 11:23:49
-->
<template>
  <div class="home" :style="styleVar">
    <div class="detail">
      <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/shop/fineFood/bj.png" alt="" class="detail-bg">
      <div class="detail-nav">
        <van-icon name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/shop/fineFood/back.png" size="29" @click="goBack" />
        <van-icon
          v-if="false"
          class="backimg"
          name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/shop/share.png"
          size="23px"
          @click="goShear"
        />
      </div>
      <div class="detail-nav fixed">
        <van-icon name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/rest/arrow_black.png" size="25" @click="goBack" />
        <van-icon
          v-if="false"
          class="backimg"
          name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/shop/share.png"
          size="23px"
          @click="goShear"
        />
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    shopData: {
      type: Object,
      default: function() {
        return {}
      }
    }
  },
  data() {
    return {
      shareForm: {
        sharePlatform: '',
        title: '点滴',
        content: '点滴畅享生活',
        redirectUrl: '',
        contentType: 'url',
        url: '',
        imgUrl: ''
      }
    }
  },
  computed: {
    styleVar() {
      return {
        '---nav-height': this.$store.getters.getStatusHeight + 'px'
      }
    }
  },
  mounted() {
    // 获取nav
    const nav = document.querySelector('.fixed')
    addEventListener('scroll', () => {
      // 获取偏移值
      const top = document.documentElement.scrollTop || document.body.scrollTop || window.pageYOffset
      // console.log(top)
      // 设置一个合适的范围
      if (top <= 85 + this.$store.getters.getStatusHeight && top >= 0) {
        // 令header的渐变色位置变成计算后的渐变位置
        nav.style.setProperty('opacity', top / 100)
      } else {
        // 在移动一定范围后令其完全不透明
        nav.style.setProperty('opacity', 1)
      }
    })
  },
  methods: {
    goBack() {
      this.$router.go(-1)
    },
    // 分享
    goShear(val) {
      let data = {
        text: this.shopData.address,
        title: this.shopData.marketName,
        url: `https://share.zjntwl.com/#/fineFoodShopIndex?id=${this.$route.query.id}&type=2`,
        image: this.shopData.pic
      }
      AlipayJSBridge.call('WatchShare', data, function(result) {
        console.log(result)
      })
    }
  }
}
</script>

<style scoped lang="scss">
    .home {
      margin-bottom: 16px;
        .detail {
          margin-bottom: 16px;
          display: flex;
          justify-content: space-between;
          .backimg{
            margin-right: 30px;
            position: relative;
            top: -5px;
          }
            .detail-bg {
              position: relative;
              z-index: -1;
              width: 100%;
              height: 342px;
              background-image: url(https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/shop/fineFood/bj.png);
              background-size: 100% 100%;
              vertical-align: middle;
              filter: blur(2px);
            }
            .detail-nav {
                position: fixed;
                left: 0;
                top: 0;
                z-index: 1;
                padding-top:calc(20px + var(---nav-height));
                padding-left: 34px;
                padding-bottom: 10px;
                width: 100%;
                display: flex;
                justify-content: space-between;
            }
            .fixed {
              background-color: #fff;
              opacity: 0;
            }
        }
    }
</style>
