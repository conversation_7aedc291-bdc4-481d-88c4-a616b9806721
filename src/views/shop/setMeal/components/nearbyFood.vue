<!--
 * @Descripttion:到店套餐
 * @version: 1.0.0
 * @Author: <PERSON>haoyu<PERSON>
 * @Date: 2021-07-06 15:17:17
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-11-08 09:57:34
-->
<template>
  <!-- 到店套餐 -->
  <div v-if="list.length>0" class="home">
    <div class="header">到店套餐({{ list.length }})</div>
    <div v-for="(item,index) in list" :key="index" class="card">
      <img :src="item.headPicture" alt="" class="card-img" style="object-fit: cover;" @click="goGroup(item)">
      <div class="card-content" @click="goGroup(item)">
        <div class="card-content-name">{{ item.setMealName }}</div>
        <div class="card-content-desc">

          <span>{{ item.notAvailableWeekly===''?'周一至周天':formWeekly(item.notAvailableWeekly) }}</span>
          <span v-if="item.makeAnAppointment === 0">·免预约</span>
          <span v-else>·需预约</span>
        </div>
        <div class="card-content-bottom">
          <div class="card-content-other">
            <div class="card-content-price">
              <span class="small">¥</span>
              <span>{{ item.specialPrice }}</span>
            </div>

            <div v-show="item.setMealDiscount>0" class="card-content-discount">{{ item.setMealDiscount }}折</div>
            <div class="card-content-oriPrice">¥{{ item.originalPrice }}</div>
          </div>
          <!-- <div class="card-content-distance">距商户&lt;500m</div> -->
        </div>
      </div>
      <div class="card-right" @click="goGroup(item)">
        <div>
          <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/shop/fineFood/qgbtn.png" alt="">
        </div>
        <div>
          已售{{ item.salesQuantity }}
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { setMealList } from '@/api/shop'
export default {
  props: {
    shopData: {
      type: Object,
      default: function() {
        return {}
      }
    }
  },
  data() {
    return {
      query: {
        'pageNum': 1,
        'pageSize': 1000,
        'search': this.$route.query.id
      },
      list: []
    }
  },
  created() {
    this.getList()
  },

  methods: {
    // 获取列表
    getList() {
      this.$store.state.setMail.info.marketId = this.$route.query.id
      this.query.search = this.$route.query.id
      setMealList(this.query).then(res => {
        if (res.status == 200) {
          this.list = res.data.list
        } else {
          this.$toast(res.message)
        }
      })
    },
    goCart(item) {
      this.$store.state.setMail.info = item
      this.$router.push({
        name: 'SubmitSetMeal',
        query: {
          id: this.$route.query.id,
          marketSn: this.shopData.marketSn
        }
      })
    },
    // 格式化周几
    formWeekly(val) {
      let week = ['周一', '周二', '周三', '周四', '周五', '周六', '周天']
      let arr = val.split('、')
      let newarr = []
      for (let i = 0; i < week.length; i++) {
        for (let j = 0; j < arr.length; j++) {
          if (week[i] == arr[j]) {
            newarr.push(week[i])
          }
        }
      }
      let result = []
      for (let i = 0; i < week.length; i++) {
        let obj = week[i]
        let isExist = false
        for (let j = 0; j < newarr.length; j++) {
          let aj = newarr[j]
          if (aj == obj) {
            isExist = true
            break
          }
        }
        if (!isExist) {
          result.push(obj)
        }
      }
      const format1 = ['周一', '周二', '周三', '周四', '周五']
      const format2 = ['周一', '周二', '周三', '周四', '周五', '周六']
      if (result == format1) {
        return '周一至周五'
      } else if (result == format2) {
        return '周一至周六'
      } else {
        return result.join('、')
      }
    },
    goGroup(item) {
      console.log(item)
      this.$router.push({
        name: 'GroupList',
        query: {
          id: item.id,
          marketId: this.$route.query.id,
          marketSn: this.shopData.marketSn,
          tel: this.shopData.mobilePhone
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
    .home {
        width: 710px;
        margin: 0 auto 16px;
        border-radius: 15px;
        background-color: #fff;
        margin-top: 17px;
        .header {
            font-size: 36px;
            font-family:PingFangSC-Medium;
            font-weight: 500;
            color: #222222;
            padding: 24px 0 20px 18px;
        }
        .card {
            display: flex;
            height: 205px;
            margin-left: 18px;
            .card-img {
               width: 180px;
               height: 180px;
                margin-right: 24px;
                border-radius: 8px;
                overflow: hidden;
                object-fit: cover;
                // background-color: gray;
            }
            .card-content {
              width: 350px;
                position: relative;
                height: 180px;
                .card-content-name {
                    font-size: 33px;
                    font-family:PingFangSC-Medium;
                    font-weight: 500;
                    color: #222222;
                    margin-bottom: 8px;
                }
                .card-content-desc {
                    font-size: 25px;
                    font-family: PingFangSC;
                    color: #222222;
                }
                .card-content-bottom {
                    position: absolute;
                    left: 0;
                    bottom: 0;
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    width: 472px;
                    .card-content-other {
                        display: flex;
                        align-items: center;
                        .card-content-price {
                            font-size: 36px;
                            font-family:PingFangSC-Medium;
                            font-weight: 500;
                            color: #ff301e;
                            margin-right: 8px;
                        }
                        .card-content-oriPrice {
                            font-size: 24px;
                            font-family: PingFangSC;
                            color: #999999;
                            text-decoration: line-through;
                            margin-left: 10px;
                        }
                        .card-content-discount {
                            width: 70px;
                            height: 29px;
                            line-height: 28px;
                            text-align: center;
                            font-size: 22px;
                            font-family: PingFangSC;
                            border: 1px solid #ff301e;
                            font-family:PingFangSC-Medium;
                            color: #ff301e;
                            border-radius: 6px;
                            margin-right: 5px;
                        }
                        }
                        .card-content-distance {
                        // position: absolute;
                        // right: 0;
                        // bottom: 0;
                        // float: right;
                        text-align: right;
                        width: 200px;
                        font-size: 22px;
                        font-family: PingFangSC;
                        color: #666666;
                        }
                }

            }
            .card-right{
              text-align: center;
              img{
                width: 100px;
                height: 56px;
                margin-top: 30px;
              }
              font-size: 22px;
              color: #999999;
            }
        }
        .small {
            font-size: 22px;
            font-family:PingFangSC-Medium;
            font-weight: 500;
            color: #ff301e;
            transform: scale(.9);
            margin-right: 1px;
            position: relative;
            top: -1px;
        }
    }
</style>
