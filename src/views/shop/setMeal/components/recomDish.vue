<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-07-06 17:31:30
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-09-28 00:54:35
-->
<template>
  <!-- 推荐菜 -->
  <div v-if="goodList.length>0" class="home">
    <div class="header">
      <div class="header-title">店家推荐</div>
      <div v-if="$store.getters.getRegionId == 1" class="header-allEvalute" @click="goListGoods">
        <span>查看更多</span>
        <van-icon
          name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/review/arrow.png"
          size="12"
        />
      </div>
    </div>
    <div class="card">
      <div class="card-img">
        <div v-for="(item,index) in goodList" :key="index" class="card-img-item">
          <!-- <img :src="item.cover+'?x-oss-process=image/resize,w_700/format,jpg/quality,q_80'" alt="" class="item-img"> -->
          <van-image
            width="105"
            height="89"
            radius="5"
            fit="cover"
            :src="item.cover+'?x-oss-process=image/resize,w_700/format,jpg/quality,q_80'"
          />
          <!-- 限制7个字 -->
          <div class="item-img-name">{{ item.goodsName | ellipsis(7) }}</div>
        </div>
      </div>
    </div>
    <div style="height:2px" />
  </div>
</template>

<script>
import { getGoodsList } from '@/api/shop'
export default {
  data() {
    return {
      goodList: []
    }
  },
  watch: {
    $route(n, o) {
      if (n.fullPath !== o.fullPath) {
        // 回到顶部
        document.body.scrollTop = 0
        document.documentElement.scrollTop = 0
        this.getList()
      }
    }
  },
  created() {
    this.getList()
  },
  mounted() {

  },
  methods: {
    // 获取商品/分类
    getList() {
      let data = {
        'pageNum': 1,
        'pageSize': this.$store.getters.getRegionId == 1 ? 3 : 100,
        'search': {
          'marketId': this.$route.query.id
        }
      }
      getGoodsList(data)
        .then((res) => {
          if (res.status == 200) {
            this.goodList = res.data.list
          }
        })
    },
    goListGoods() {
      this.$router.push({
        name: 'FineFoodShopMenuDetail',
        query: {
          id: this.$route.query.id,
          type: 2
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
    .home {
        width: 710px;
        opacity: 1;
        background: #ffffff;
        border-radius: 15px;
        margin:16px  auto 16px;
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 22px;
            font-family: PingFangSC;
            color: #666666;
            padding: 24px 16px 20px 18px;
            .header-title {
                font-size: 36px;
                font-family:PingFangSC-Medium;
                font-weight: 500;
                color: #222222;
            }
            .header-allEvalute {
                display: flex;
                align-items: center;
            }
          }
        .card {
            margin-left: 18px;
            .card-img {
                display: flex;
                flex-direction: row;
                flex-wrap: wrap;
                margin-bottom: 16px;
                .card-img-item {
                    // flex-shrink: 0;
                    margin-bottom: 10px;
                    .item-img {
                        width: 220px;
                        height: 178px;
                        border-radius: 8px;
                        overflow: hidden;
                        vertical-align: middle;
                        margin-bottom: 10px;
                        object-fit: cover;
                    }
                    .item-img-name {
                        font-size: 24px;
                        font-family:PingFangSC-Medium;
                        font-weight: 500;
                        color: #222222;
                        text-align: center;
                    }
                }
                .card-img-item:not(:last-child){
                    margin-right: 8px;
                }
            }
        }
    }
</style>
