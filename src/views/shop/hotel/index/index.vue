<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-05-22 15:17:21
 * @LastEditors: 孙立政
 * @LastEditTime: 2021-07-26 20:57:43
-->
<template>
  <!-- 酒店 -->
  <div class="home">
    <Top :shop-data="shopData" />
    <ShopCard :shop-data="shopData" />
    <Evalute ref="childEvalute" />
    <RecomHotel ref="child" />
    <Protect />

    <!-- 加载状态 -->
    <Loading :show="loadingShow" />
  </div>
</template>

<script>
import Top from './components/top.vue'
import ShopCard from './components/shopCard.vue'
import RecomHotel from './components/recomHotel.vue'
import Evalute from './components/evalute.vue'
import Protect from './components/protect.vue'

import Loading from '@/components/Loading/index'

import { getShopDataV2 } from '@/api/takeout'
export default {
  components: {
    Top,
    RecomHotel,
    Evalute,
    ShopCard,
    Protect,
    Loading
  },
  data() {
    return {
      shopData: {
        marketName: '',
        tagTitleList: [],
        announcement: '',
        openingHours: '',
        closingHours: '',
        address: '',
        isTakeaway: true,
        marketPhotos: [],
        marketConfig: {
          marketBusTimes: []
        },
        pic: ''
      },
      loadingShow: true
    }
  },
  watch: {
    $route(n, o) {
      if (n.fullPath !== o.fullPath) {
        this.getShopData()
        this.$refs.child.getList()
        this.$refs.childEvalute.getList()
        // 回到顶部
        document.body.scrollTop = 0
        document.documentElement.scrollTop = 0
      }
    }
  },
  created() {
    this.getShopData()
  },
  methods: {
    // 获取店铺详情
    getShopData() {
      let data = {
        marketId: this.$route.query.id,
        longitude: this.$store.getters.getLocation.longitude,
        latitude: this.$store.getters.getLocation.latitude
      }
      getShopDataV2(data).then((res) => {
        this.loadingShow = false
        this.shopData = res.data
      })
    }
  }
}
</script>

<style scoped lang="scss">
  .home::before {
  // 利用伪元素设置整个页面的背景色
  content: " ";
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: -100;
  min-height: 100%;
  background-color: #f7f9fb;
}
</style>
