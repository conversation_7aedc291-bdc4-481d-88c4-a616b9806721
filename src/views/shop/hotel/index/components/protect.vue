<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-07-12 15:41:50
 * @LastEditors: zhaoyuxin
 * @LastEditTime: 2021-07-12 16:02:18
-->
<template>
  <div class="home">
    <div class="header"><div class="line" />双重服务保障<div class="line" /></div>
    <div class="subname">由点滴与酒店供应商共同提供</div>
    <ul>
      <li>
        <van-icon name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/user/classify/protect-icon1.png" size="36" />
        <div class="label1">入住保障</div>
        <div class="label2">到店有房放心入住</div>
      </li>
      <li>
        <van-icon name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/user/classify/protect-icon2.png" size="36" />
        <div class="label1">退订保障</div>
        <div class="label2">部分可以申请退订</div>
      </li>
      <li>
        <van-icon name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/user/classify/protect-icon3.png" size="36" />
        <div class="label1">专业客服</div>
        <div class="label2">优质的客服服务</div>
      </li>
    </ul>
  </div>
</template>

<script>
export default {
  data() {
    return {

    }
  },
  created() {

  },
  mounted() {

  },
  methods: {

  }
}
</script>

<style scoped lang="scss">
    .home {
        width: 750px;
        height: 337px;
        background: #ffffff;
        .header {
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 38px;
            font-family: PingFangSC;
            font-weight: 600;
            color: #333333;
            line-height: 53px;
            margin-bottom: 2px;
            padding-top: 24px;
            .line {
                width: 37px;
                height: 1px;
                border: 2px solid #333333;
                margin: 0 18px;
            }
        }
        .subname {
            font-size: 24px;
            font-family: PingFangSC;
            font-weight: 400;
            text-align: center;
            color: #666666;
            line-height: 33px;
            margin-bottom: 30px;
        }
        ul {
            display: flex;
            justify-content: space-between;
            li {
                width: 33.33%;
                flex-shrink: 0;
                font-family: PingFangSC;
                text-align: center;
                color: #999999;
                img {
                    vertical-align: middle;
                }
                .label1 {
                    font-size: 24px;
                    line-height: 33px;
                    margin-bottom: 2px;
                    margin-top: 10px;
                }
                .label2 {
                    font-size: 20px;
                    line-height: 28px;
                }
            }
        }
    }
</style>
