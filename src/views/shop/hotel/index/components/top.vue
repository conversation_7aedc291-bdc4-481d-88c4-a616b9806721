<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-07-10 17:49:38
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-02-21 14:52:47
-->
<template>
  <div class="home" :style="styleVar">
    <div class="detail">
      <van-swipe v-if="shopData.marketPhotos!=null&&shopData.marketPhotos.length>0" class="my-swipe" :autoplay="100000" indicator-color="white" :show-indicators="false">
        <van-swipe-item v-for="(item,index) in shopData.marketPhotos" :key="index" @click="goPhoto">
          <img :src="item.photo" alt="" class="detail-bg">
        </van-swipe-item>
      </van-swipe>
      <img v-else src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/rest/rest-bg.png" alt="" class="detail-bg">

      <div class="detail-nav">
        <van-icon name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/rest/arrow_white.png" size="26" @click="goBack" />
        <van-icon
          v-if="false"
          class="backimg"
          name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/shop/share.png"
          size="23px"
          @click="goShear"
        />
      </div>
      <div class="detail-nav fixed">
        <van-icon name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/rest/arrow_black.png" size="26" @click="goBack" />
        <van-icon
          v-if="false"
          class="backimg"
          name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/shop/share.png"
          size="23px"
          @click="goShear"
        />
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    shopData: {
      type: Object,
      default: function() {
        return {}
      }
    }
  },
  data() {
    return {
      shareForm: {
        sharePlatform: '',
        title: '点滴',
        content: '点滴畅享生活',
        redirectUrl: '',
        contentType: 'url',
        url: '',
        imgUrl: ''
      }
    }
  },
  computed: {
    styleVar() {
      return {
        '---nav-height': this.$store.getters.getStatusHeight + 'px'
      }
    }
  },
  created() {

  },
  mounted() {
    // 获取nav
    const nav = document.querySelector('.fixed')
    addEventListener('scroll', () => {
      // 获取偏移值
      const top = document.documentElement.scrollTop || document.body.scrollTop || window.pageYOffset
      // console.log(top)
      // 设置一个合适的范围
      if (top <= 85 + this.$store.getters.getStatusHeight && top >= 0) {
        // 令header的渐变色位置变成计算后的渐变位置
        nav.style.setProperty('opacity', top / 100)
      } else {
        // 在移动一定范围后令其完全不透明
        nav.style.setProperty('opacity', 1)
      }
    })
  },
  methods: {
    goBack() {
      this.$router.go(-1)
    },
    // 跳转相册
    goPhoto() {
      console.log(555)
      this.$router.push({
        name: 'FineFoodShopAlbum',
        query: {
          id: this.$route.query.id,
          type: 2
        }
      })
      this.$store.state.market.photoList = this.shopData.marketPhotos
    },
    // 分享
    goShear(val) {
      let data = {
        text: this.shopData.address,
        title: this.shopData.marketName,
        url: `https://share.zjntwl.com/#/hotelShopIndex?id=${this.$route.query.id}&type=5`,
        image: this.shopData.pic
      }
      AlipayJSBridge.call('WatchShare', data, function(result) {
        console.log(result)
      })
    }
  }
}
</script>

<style scoped lang="scss">
    .home {
        .detail {
          display: flex;
          justify-content: space-between;
          .backimg{
            margin-right: 30px;
            position: relative;
            top: -5px;
          }
            .detail-bg {
                position: relative;
                z-index: -1;
                width: 100%;
                height: 380px;
                background-image: url(https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/rest/rest-bg.png);
                background-size: 100% 100%;
                vertical-align: middle;
            }
            .detail-nav {
                position: fixed;
                left: 0;
                top: 0;
                z-index: 2;
                padding-top:calc(20px + var(---nav-height));
                padding-left: 34px;
                padding-bottom: 10px;
                width: 100%;
                display: flex;
                justify-content: space-between;
            }
            .fixed {
              background-color: #fff;
              opacity: 0;
            }
        }
    }
</style>
