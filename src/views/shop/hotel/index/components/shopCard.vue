<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-07-06 17:52:28
 * @LastEditors: 孙立政
 * @LastEditTime: 2021-07-26 20:55:10
-->
<template>
  <div class="home">
    <div class="card-top">
      <div class="card-shop-name">{{ shopData.marketName | ellipsis(15) }}</div>
      <div class="card-shop-desc">
        <div class="card-shop-mark">{{ shopData.evaScore }}<span class="fen">分</span> </div>
        <van-rate
          v-model="shopData.evaScore"
          allow-half
          readonly
          :size="13"
          color="#ffd21e"
          void-icon="star"
          void-color="#eee"
          class="card-shop-rate"
        />
      </div>
      <div class="card-shop-navigation">
        <div class="card-shop-address">
          <div class="card-shop-address-name">{{ shopData.address | ellipsis(13) }}</div>
          <div v-if="shopData.distance!=null" class="card-shop-distance">距离你约{{ shopData.distance.toFixed(2) }}km</div>
        </div>
        <div class="card-shop-icon">
          <div v-if="!isAmap" class="icon1" @click="goMarket(shopData)">
            <van-icon name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/user/classify/fineFood-navigation.png" size="20" />
            <div>导航</div>
          </div>
          <div v-else v-clipboard:copy="shopData.address" v-clipboard:success="onCopy" class="icon1">
            <van-icon name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/user/classify/fineFood-navigation.png" size="20" />
            <div>导航</div>
          </div>
          <div class="icon2" @click="CallPhone(shopData.mobilePhone)">
            <van-icon name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/user/classify/fineFood-call.png" size="20" />
            <div>电话</div>
          </div>
        </div>
      </div>
      <div style="height:8px" />
    </div>

    <div v-if="goodList.length>0" class="card-shop-rooms">
      <div class="header">选择房型</div>
      <ul class="rooms-type">
        <li v-for="(item,index) in goodList" :key="index" :class="{active:returncss(index)}" class="rooms-type-item" @click="goItme(index)">{{ item.tagTitle }}</li>
      </ul>
      <ul class="pdb24">
        <div class="rooms-card">
          <li v-for="(item,index) in goodList[itemindex].goodsList" :key="index" class="rooms-card-item ">
            <div class="left">
              <img class="card-item-img" :src="item.cover">
              <div class="card-item-desc">
                <div class="">
                  <div class="card-item-name">{{ item.goodsName | ellipsis(18) }}</div>
                  <div class="card-item-subname">{{ item.description | ellipsis(10) }}</div>
                </div>
                <div class="card-item-tag">
                  <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/shop/hotel/tag.png" alt="">
                </div>
              </div>
            </div>
            <div class="card-item-price">
              <span class="small">¥</span>{{ item.price }}
              <span class="wan">/晚</span>
            </div>
          </li>
        </div>
      </ul>
    </div>

    <!-- 导航 -->
    <van-popup v-model="showPop" position="bottom" round>
      <div class="mapList">
        <div @click="openMap(1)">百度导航</div>
        <div @click="openMap(2)">高德导航</div>
        <div @click="showPop=false">取消</div>
      </div>
    </van-popup>
  </div>
</template>

<script>
import {
  getList
} from '@/api/takeout'
export default {
  props: {
    shopData: {
      type: Object,
      default: function() {
        return {}
      }
    }
  },
  data() {
    return {
      showPop: false,
      mapData: '',
      isAmap: false,
      goodList: [{ goodsList: [] }],
      itemindex: 0
    }
  },
  watch: {
    $route(n, o) {
      if (n.fullPath !== o.fullPath) {
        // 回到顶部
        document.body.scrollTop = 0
        document.documentElement.scrollTop = 0
        this.getList()
      }
    }
  },
  created() {
    // 判断是系统环境
    var u = navigator.userAgent
    var isiOS = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/) // ios终端
    if (isiOS) {
      this.isAmap = true
    }
    this.getList()
  },
  methods: {
    // 获取商品/分类
    getList() {
      getList(this.$route.query.id)
        .then((res) => {
          // this.loadingShow = false
          if (res.status == 200) {
            this.goodList = res.data
          }
        })
    },
    // 选择
    returncss(index) {
      if (this.itemindex == index) {
        return true
      } else {
        return false
      }
    },
    goItme(index) {
      console.log(index)
      this.itemindex = index
    },
    // 拨打电话
    CallPhone(data) {
      AlipayJSBridge.call('CallPhone', {
        phoneNum: data
      }, function(result) {})
    },
    // 打开地图
    openMap(val) {
      let self = this
      if (val == 1) {
        var urlBaiduMap =
						`baidumap://map/marker?location=${this.mapData.bd_lat},${this.mapData.bd_lng}&title=${this.mapData.marketName}&content=${this.mapData.marketName}&src=Hello%20uni-app`
        AlipayJSBridge.call(
          'IsAvailable', {
            packageName: 'com.baidu.BaiduMap'
          },
          function(result) {
            if (result.available == true) {
              window.location.href = urlBaiduMap
            } else {
              self.$toast('未安装百度地图')
            }
          }
        )
      } else {
        var urlAmap =
						`androidamap://viewMap?sourceApplication=${this.mapData.marketName}&poiname=${this.mapData.marketName}&lat=${this.mapData.gd_lat}&lon=${this.mapData.gd_lng}&dev=0`

        var iosAmap =
						`iosamap://viewMap?sourceApplication=${this.mapData.marketName}&poiname=${this.mapData.marketName}&lat=${this.mapData.gd_lat}&lon=${this.mapData.gd_lng}&dev=0`
        var u = navigator.userAgent
        var isiOS = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/) // ios终端
        if (!isiOS) {
          AlipayJSBridge.call(
            'IsAvailable', {
              packageName: 'com.autonavi.minimap'
            },
            function(result) {
              if (result.available == true) {
                window.location.href = urlAmap
              } else {
                self.$toast('未安装高德地图')
              }
            }
          )
        } else {
          window.location.href = iosAmap
        }
      }
    },
    onCopy(e) {
      this.$toast({
        duration: 5000,
        forbidClick: false,
        message: '复制成功,请打开地图应用,粘贴店铺地址进行导航'
      })
    },
    // 导航店铺
    goMarket(data) {
      // 高德转百度坐标
      function bd_encrypt(gg_lng, gg_lat) {
        var X_PI = Math.PI * 3000.0 / 180.0
        var x = gg_lng
        var y = gg_lat
        var z = Math.sqrt(x * x + y * y) + 0.00002 * Math.sin(y * X_PI)
        var theta = Math.atan2(y, x) + 0.000003 * Math.cos(x * X_PI)
        var bd_lng = z * Math.cos(theta) + 0.0065
        var bd_lat = z * Math.sin(theta) + 0.006
        return {
          bd_lat: bd_lat,
          bd_lng: bd_lng
        }
      }
      let zuobiao = bd_encrypt(data.longitude, data.latitude)

      this.mapData = {
        bd_lng: zuobiao.bd_lng,
        bd_lat: zuobiao.bd_lat,
        gd_lng: data.longitude,
        gd_lat: data.latitude,
        marketName: data.marketName
      }
      this.showPop = true
    }
  }
}
</script>

<style scoped lang="scss">
    .home {
      .mapList {
        div {
          height: 150px;
          line-height: 150px;
          text-align: center;
          font-size: 28px;
        }

        div:not(:last-child) {
          border-bottom: 1px solid #EEEEEE;
          font-weight: bold;
        }
      }
      .header {
          font-size: 34px;
          font-family: PingFangSC-Medium;
          font-weight: 500;
          color: #222222;
          line-height: 48px;
          margin-left: 30px;
        }
      .small {
        font-size: 28px;
        font-family:PingFangSC-Medium;
        font-weight: 500;
        color: #fe5002;
        line-height: 40px;
        margin-right: 2px;
      }
      .wan {
        font-size: 22px;
        font-family: PingFangSC;
        font-weight: 400;
        color: #999999;
        line-height: 30px;
      }
      .fen {
        font-size: 28px;
        font-family: PingFangSC;
        font-weight: 600;
        color: #333333;
        line-height: 40px;
      }
      .only {
        font-size: 20px;
        font-family: PingFangSC;
        text-align: center;
        color: #ff3f48;
        line-height: 28px;
        margin-bottom: 6px;
      }
      .card-top {
          width: 710px;
          // height: 692px;
          opacity: 1;
          background: #ffffff;
          border-radius: 15px;
          margin: -110px  auto 0;
          padding-left: 18px;
          box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
          position: relative;
          z-index: 1;
          .card-shop-name {
              font-size: 40px;
              font-family:PingFangSC-Medium;
              font-weight: 500;
              color: #222222;
              margin-bottom: 6px;
              padding-top: 22px;
          }
          .card-shop-desc {
              display: flex;
              align-items: flex-end;
              height: 54px;
              margin-bottom: 26px;
              .card-shop-mark {
                font-size: 44px;
                font-family:PingFangSC;
                font-weight: 700;
                color: #333333;
                line-height: 54px;
                margin-right: 6px;
              }
              .card-shop-rate{
                position: relative;
                top: -5px;
              }
          }
          .card-shop-navigation {
              height: 110px;
              position: relative;
              .card-shop-address {
                  width: 448px;
                  font-size: 28px;
                  font-family:PingFangSC-Medium;
                  font-weight: 500;
                  color: #222222;
              }
              .card-shop-address-name{
                  height: 70px;
                  line-height: 35px;
                }
              .card-shop-distance {
                  font-size: 24px;
                  font-family: PingFangSC;
                  color: #999999;
              }
              .card-shop-icon {
                  position: absolute;
                  bottom: 0;
                  right: 34px;
                  width: 150px;
                  display: flex;
                  justify-content: space-between;
                  .icon1,.icon2 {
                      font-size: 20px;
                      font-family: PingFangSC;
                      color: #999999;
                      transform:scale(.83);
                      text-align: center;
                  }
              }

          }

      }
      .card-shop-rooms {
        margin-top: 26px;
        .rooms-type {
          display: flex;
          margin-top: 24px;
          margin-bottom: 36px;
          margin-left: 30px;
          .rooms-type-item {
            width: 114px;
            min-height: 58px;
            background: #F6F7FA;
            border-radius: 6px;
            font-size: 26px;
            font-family: PingFangSC;
            color: #454545;
            text-align: center;
            line-height: 58px;
            margin-right: 16px;
          }
          .active {
            position: relative;
            color: #3488ff;
            font-family:PingFangSC-Medium;
            font-weight: 500;
            background: #e9f2ff;
          }
          .active::after {
            content: '';
            position: absolute;
            right: 0;
            bottom: 0;
            width: 16px;
            height: 14px;
            background-image: url(https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/user/classify/rooms-checked.png);
            background-size: 100%;
          }
        }
        .rooms-recom {
          width: 690px;
          background: #ffffff;
          border-radius: 18px;
          box-shadow: 0px 2px 20px 0px rgba(219,227,241,0.51);
          margin: auto;
          padding-top: 16px;
          .rooms-recom-tips {
            display: flex;
            width: 300px;
            height: 44px;
            line-height: 44px;
            background: #fee6e5;
            border-radius: 4px 22px 22px 4px;
            margin-bottom: 16px;
            .rooms-recom-left {
              display: flex;
              align-items: center;
              width: 50%;
              font-size: 22px;
              font-family: PingFangSC;
              color: #ffffff;
              background: linear-gradient(90deg,#fe5d4c, #fe7c71 96%);
            }
            .rooms-recom-left::before {
              display: block;
              content: '';
              width: 26px;
              height: 26px;
              background-image: url(https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/user/classify/kudos.png);
              background-size: 100%;
              margin: 0 6px;
            }
            .rooms-recom-right {
              width: 50%;
              font-size: 22px;
              font-family: PingFangSC;
              color: #fe5d4c;
              margin-left: 8px;
            }
            .triangle {
              width: 0;
              height: 0;
              border-top:0px solid rgba(0,0,0,0);
              border-right:0px solid  rgba(0,0,0,0);
              border-bottom:44px solid #fee6e5;
              border-left:17px solid  rgba(0,0,0,0);
              margin-left: -17px;
            }
          }
        }
        .pdb24 {
          padding-bottom: 8px;
        }
        .rooms-card {
          width: 690px;
          margin:16px auto  0;
          border-radius: 18px;
          box-shadow: 0px 2px 20px 0px rgba(219,227,241,0.51);
          padding-top: 24px;
          background-color: #fff;
        }
        .rooms-card-item {
            position: relative;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            // width: 690px;
            padding-bottom: 24px;
            margin: auto;
            .left {
              display: flex;
            }
            .card-item-img {
              width: 146px;
              height: 146px;
              border-radius: 6px;
              // background-color: gray;
              overflow: hidden;
              margin-right: 16px;
            }
            .card-item-desc {
                height: 146px;
                display: flex;
                flex-direction:column;
                justify-content: space-between;
              .card-item-name {
                font-size: 30px;
                font-family:PingFangSC-Medium;
                font-weight: 500;
                // text-align: left;
                color: #333333;
                line-height: 42px;
                margin-bottom: 4px;
              }
              .card-item-subname {
                font-size: 22px;
                font-family: PingFangSC;
                // font-weight: 400;
                // text-align: left;
                color: #666666;
                line-height: 30px;
              }
              .card-item-tag {
                img{
                  width: 94px;
                  height: 30px;
                }
              }

            }
            .card-item-price {
                font-size: 42px;
                font-family: DIN, DIN-Bold;
                font-weight: 700;
                // text-align: left;
                color: #fe5002;
                line-height: 52px;
              }
          }
      }
    }
</style>
