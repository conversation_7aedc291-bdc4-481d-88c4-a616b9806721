<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: zhaoyu<PERSON>
 * @Date: 2021-06-02 17:31:55
 * @LastEditors: zhaoyuxin
 * @LastEditTime: 2021-07-10 16:53:22
-->
<template>
  <div class="content">
    <NavHeight bgc="#fff" />
    <div class="top">
      <div class="fixed">
        <van-nav-bar title="店铺相册" left-text="" left-arrow>
          <template #left>
            <van-icon name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/rest/arrow_black.png" size="20" @click="goBack" />
          </template>
        </van-nav-bar>
      </div>
      <div style="height:46px" />
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
    }
  },
  created() {
  },
  mounted() {

  },
  methods: {
    goBack() {
      this.$router.go(-1)
    }
  }
}
</script>

  <style lang="scss" scoped>
  .content {
    background-color: #fff;
    .top {
        .fixed {
            position: fixed;
            left: 0;
            // top: 0;
            z-index: 2;
            width: 100%;
            background-color: #fff;
        }
        ::v-deep .van-nav-bar__title {
            font-size: 36px;
            color: #222;
            font-family: PingFangSC;
        }
      }
      ::v-deep .van-nav-bar__title {
        font-size: 34px;
        color: #000010;
      }
  }
  </style>
