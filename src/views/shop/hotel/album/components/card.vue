<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-07-10 16:53:45
 * @LastEditors: zhaoyuxin
 * @LastEditTime: 2021-07-13 11:45:11
-->
<template>
  <div class="home">
    <div class="card">
      <img v-for="(item,index) in temp" :key="index" class="card-img" src="" @click="goPreview(item,index)">
    </div>

  </div>
</template>

<script>
import { ImagePreview } from 'vant'
export default {
  components: {
    [ImagePreview.Component.name]: ImagePreview.Component
  },
  data() {
    return {
      temp: [1, 2, 3, 4, 5]
    }
  },
  created() {

  },
  mounted() {

  },
  methods: {
    goPreview(item, index) {
      ImagePreview({
        images: [
          'https://img01.yzcdn.cn/vant/apple-1.jpg',
          'https://img01.yzcdn.cn/vant/apple-2.jpg'
        ],
        startPosition: index
      })
      // ImagePreview({
      //   images: item.images, // 预览图片的那个数组
      //   showIndex: true,
      //   loop: false,
      //   startPosition: index // 指明预览第几张图
      // })
    }
  }
}
</script>

<style scoped lang="scss">
    .home {
        padding-bottom: 100px;
        .card {
          display: flex;
          justify-content: space-between;
          flex-wrap: wrap;
          margin: 20px 24px 0;
            .card-img {
              flex-shrink: 0;
              width: 344px;
              height: 344px;
              // background-color: gray;
              border-radius: 16px;
              overflow: hidden;
              margin-bottom: 14px;

            }
        }
    }
</style>
