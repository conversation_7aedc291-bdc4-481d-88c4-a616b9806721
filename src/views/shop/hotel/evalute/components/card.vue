<template>
  <!-- 评价内容 -->
  <div class="cell">
    <div class="tag">
      <div v-for="(item,index) in tagList" :key="index" class="tag-item" :class="current==index?'active':''" @click="changeTag(item.id)">{{ item.title }}</div>
    </div>
    <div v-for="(item,index) in temp" :key="index" class="evalute">
      <div class="evalute-top">
        <img :src="item.userImage?item.userImage:defaultImg" alt="" class="evalute-avatar">
        <div class="evalute-right">
          <div class="content-line1">
            <div class="content-line1-user">{{ item.userName }}</div>
            <div class="content-line1-time">
              <span>{{ item.evaTime }}</span>
            </div>
          </div>
          <div class="content-line2">
            <div class="content-line2-rate">
              <van-rate
                v-model="item.score"
                allow-half
                readonly
                :size="12"
                color="#ffd21e"
                void-icon="star"
                void-color="#eee"
              />
            </div>
          </div>
        </div>
      </div>
      <div class="content-line3">
        <span class="content-line3-tag" />
        <span v-for="tag in item.tbMarketEvaluations" :key="tag.id">{{ tag.evaTypeName }}{{ tag.score | filterRate }}
        </span>
      </div>
      <div class="content-line4">
        {{ item.tbEvaluationContents[0].content }}
      </div>
      <div v-if="item.pictures!=''" class="content-line4">
        <template v-if="item.pictures.split(',').length==3">
          <img v-for="( imgList, indexs ) in item.pictures.split(',')" :key="indexs" :src="imgList" alt="" class="content-line4-img img3">
        </template>
        <template v-if="item.pictures.split(',').length==2">
          <img v-for="( imgList, indexs ) in item.pictures.split(',')" :key="indexs" :src="imgList" alt="" class="content-line4-img img2">
          <img v-for="( imgList, indexs ) in item.pictures.split(',')" :key="indexs" :src="imgList" alt="" class="content-line4-img img2">
        </template>
        <template v-if="item.pictures.split(',').length==1">
          <img v-for="( imgList, indexs ) in item.pictures.split(',')" :key="indexs" :src="imgList" alt="" class="content-line4-img img1">
        </template>
      </div>

      <div v-if="item.tbEvaluationContents.length>1" class="content-line7">
        <div v-for="(item1,idx) in item.tbEvaluationContents" :key="idx">
          <div v-if="idx!=0" class="content-line7-shop">
            <div v-if="item1.replierType==1" class="content-line7-reply">我的追评：</div>
            <div v-if="item1.replierType==2" class="content-line7-reply">商家回复：</div>
            <div class="content-line7-feedback">{{ item1.content }}</div>
          </div>
        </div>
      </div>
    </div>

    <Loading :show="loadingShow" />
  </div>
</template>

<script>
import { queryByTypeV2 } from '@/api/shop'
import Loading from '@/components/Loading/index'
export default {
  components: {
    Loading
  },
  filters: {
    filterRate(val) {
      if (val >= 0) {
        if (val <= 2) {
          return '差'
        } else if (val <= 3) {
          return '一般'
        } else if (val <= 4) {
          return '满意'
        } else {
          return '非常满意'
        }
      }
    }
  },
  data() {
    return {
      temp: [],
      defaultImg: 'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/shop/appraise/default.png',
      query: {
        'pageNum': 1,
        'pageSize': 1000,
        'search': {
          'evaType': 1,
          'marketId': this.$route.query.id,
          'queryType': 0
        }
      },
      current: 0,
      tagList: [
        {
          id: 0,
          title: '全部'
        },
        {
          id: 1,
          title: '晒图'
        },
        {
          id: 2,
          title: '低分'
        },
        {
          id: 3,
          title: '最新的'
        }
      ],
      flag: true,
      loadingShow: true
    }
  },
  created() {
    this.getList()
  },
  methods: {
    // 获取列表
    getList() {
      queryByTypeV2(this.query).then(res => {
        this.loadingShow = false
        if (res.status == 200) {
          let newData = []
          res.data.list.forEach(item => {
            item.flag = true
            newData.push(item)
          })
          this.temp.push(...newData)
        } else {
          this.$toast(res.message)
        }
      })
    },
    changeTag(index) {
      this.current = index
      this.query.search.queryType = index
      this.temp = []
      this.getList()
    }
  }
}
</script>

  <style scoped lang="scss">
    .cell::before {
      // 利用伪元素设置整个页面的背景色
      content: " ";
      position: absolute;
      left: 0;
      right: 0;
      top: 0;
      bottom: 0;
      z-index: -100;
      min-height: 100%;
      background-color: #fff;
    }
    .tag {
      display: flex;
      margin: 24px 0 7px 0;
      .tag-item {
        padding: 9px 32px;
        font-size: 24px;
        font-family: PingFangSC;
        line-height: 33px;
        background: #f5f6f7;
        margin-right: 16px;
        border-radius: 26px;
      }
      .active {
        font-family:PingFangSC-Medium;
        font-weight: 500;
        color: #169d1b;
        background: #dfffe0;

      }
    }
    .cell {
        padding: 0 37px;
        background-color: #fff;
        .evalute {
            width: 100%;
            padding-bottom: 24px;
            box-sizing: border-box;
            padding-top: 25px;
            border-bottom: 1px solid #f4f4f4;
            .evalute-top {
              display: flex;
              justify-content: space-between;
              .evalute-avatar {
                  flex-shrink: 0;
                  width: 68px;
                  height: 68px;
                  margin-right: 12px;
                  border-radius: 34px;
                  overflow: hidden;
              }
              .evalute-right {
                  .content-line1 {
                    width: 576px;
                    height: 40px;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;

                    .content-line1-user {
                        font-size: 24px;
                        font-family:PingFangSC-Medium;
                        font-weight: 500;
                        color: #333;
                        line-height: 33px;
                    }

                    .content-line1-time {
                        font-size: 22px;
                        color: #999;
                        font-family: PingFangSC;
                    }
                }
                  .content-line2 {
                      display: flex;
                      align-items: center;
                      font-size: 22px;
                      // margin-bottom: 16px;
                      margin-top: 3px;
                      .content-line2-rate {
                          ::v-deep .van-rate__item {
                              top:4px;
                          }
                      }
                  }
              }
            }
              .content-line3 {
                  font-size:22px;
                  color: #999;
                  margin-top: 16px;
                  font-family: PingFangSC;
                  line-height: 30px;
                  .content-line3-tag {
                    display: inline-block;
                    background-image: url(https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/user/icon/evalute_tag.png);
                    background-size: 100%;
                    width: 30px;
                    height: 20px;
                    margin-right: 8px;
                    font-family: PingFangSC;
                  }
              }
              .content-line4 {
                  font-size: 26px;
                  line-height: 37px;
                  color: #333;
                  margin-top: 16px;
                  font-family: PingFangSC;
                  overflow: hidden;
                  .content-line4-img {
                      width: 220px;
                      height: 220px;
                      border-radius: 8px;
                      overflow: hidden;
                      vertical-align: middle;
                  }
                  .img3:not(:last-child){
                    margin-right: 8px;
                  }
                  .img2 {
                    width: 332px;
                    height: 332px;

                  }
                  .img2:not(:last-child){
                    margin-right: 12px;
                  }
                  .img1 {
                    width: 432px;
                    height: 432px;
                  }
                  .content-line4-img:not(:last-child){
                      // margin-right: 8px;
                  }
              }
              .content-line7 {
                  font-size: 22px;
                  overflow: hidden;
                  margin-top: 16px;
                  .content-line7-shop {
                    background: #fafafa;
                    padding: 12px 4px 8px 12px;
                    border-radius: 8px;
                  }
                  .content-line7-shop:nth-of-type(2){
                    margin-top: 16px;
                  }
                  .content-line7-feedback {
                    font-family: PingFangSC;
                    color: #666666;
                    line-height: 30px;
                  }
                  .content-line7-reply{
                      color: #333;
                      font-family:PingFangSC-Medium;
                      font-weight: 500;
                      line-height: 30px;
                  }

              }

        }
    }
  </style>
