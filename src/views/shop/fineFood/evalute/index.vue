<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-07-10 16:12:49
 * @LastEditors: 孙立政
 * @LastEditTime: 2021-07-14 10:44:13
-->
<template>
  <div class="home">
    <Nav />
    <Card />
  </div>
</template>

<script>
import Nav from './components/nav.vue'
import Card from './components/card.vue'
export default {
  components: {
    Nav,
    Card
  },
  data() {
    return {

    }
  },
  created() {

  },
  mounted() {

  },
  methods: {

  }
}
</script>

<style scoped lang="scss">
</style>
