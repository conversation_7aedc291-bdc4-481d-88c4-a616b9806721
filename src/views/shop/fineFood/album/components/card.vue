<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-07-10 16:53:45
 * @LastEditors: 孙立政
 * @LastEditTime: 2021-07-26 20:00:51
-->
<template>
  <div class="home">
    <div class="card">
      <img v-for="(item,index) in $store.state.market.photoList" :key="index" class="card-img" :src="item.photo" @click="goPreview(item,index)">
    </div>

  </div>
</template>

<script>
import { ImagePreview } from 'vant'
export default {
  components: {
    [ImagePreview.Component.name]: ImagePreview.Component
  },
  data() {
    return {
      temp: []
    }
  },
  created() {

  },
  mounted() {

  },
  methods: {
    goPreview(item, index) {
      let data = this.$store.state.market.photoList
      let newdata = []

      for (let i = 0; i < data.length; i++) {
        newdata.push(data[i].photo)
      }

      ImagePreview({
        images: newdata,
        startPosition: index
      })
    }
  }
}
</script>

<style scoped lang="scss">
    .home {
        padding-bottom: 100px;
        .card {
          display: flex;
          justify-content: space-between;
          flex-wrap: wrap;
          margin: 20px 24px 0;
            .card-img {
              flex-shrink: 0;
              width: 344px;
              height: 344px;
              background-color: gray;
              border-radius: 16px;
              overflow: hidden;
              margin-bottom: 14px;
              object-fit: cover;
            }
        }
    }
</style>
