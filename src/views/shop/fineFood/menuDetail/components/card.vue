<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-07-10 16:53:45
 * @LastEditors: 孙立政
 * @LastEditTime: 2021-07-26 20:01:22
-->
<template>
  <div class="home">
    <div v-for="(item,index) in goodList" :key="index" class="card">
      <img class="card-img" :src="item.cover">
      <div class="card-bottom">
        <div class="card-name">{{ item.goodsName | ellipsis(15) }}</div>
        <div class="card-price">
          <span class="small">¥</span>{{ item.showPrice }}
        </div>
      </div>
    </div>

    <!-- 加载状态 -->
    <Loading :show="loadingShow" />
  </div>
</template>

<script>
import { getGoodsList } from '@/api/shop'
import Loading from '@/components/Loading/index'
export default {
  components: {
    Loading
  },
  data() {
    return {
      goodList: [],
      loadingShow: true
    }
  },
  created() {
    this.getList()
  },
  mounted() {

  },
  methods: {
    // 获取商品/分类
    getList() {
      let data = {
        'pageNum': 1,
        'pageSize': 1000,
        'search': {
          'marketId': this.$route.query.id
        }
      }
      getGoodsList(data)
        .then((res) => {
          if (res.status == 200) {
            this.loadingShow = false
            this.goodList = res.data.list
          }
        })
    }
  }
}
</script>

<style scoped lang="scss">
    .home {
        padding-bottom: 100px;
        .card {
            width: 676px;
            height: 460px;
            background: #ffffff;
            border-radius: 16px;
            margin:25px  auto;
            .card-img {
              width: 676px;
              height: 368px;
              // background-color: gray;
              border-radius: 16px;
              object-fit: cover;
            }
            .card-bottom {
                display: flex;
                align-items: center;
                justify-content: space-between;
                padding: 0 48px 0 20px;
                .card-name {
                    font-size: 28px;
                    font-family:PingFangSC-Medium;
                    font-weight: 500;
                    text-align: justify;
                    color: #333333;
                    line-height: 40px;
                }
                .card-price {
                    font-size: 42px;
                    font-family:PingFangSC-Medium;
                    font-weight: 500;
                    color: #ff301e;
                    line-height: 59px;
                }
                .small {
                    font-size: 22px;
                    font-family:PingFangSC-Medium;
                    font-weight: 500;
                    color: #ff301e;
                    line-height: 30px;
                }
            }
        }
    }
</style>
