<!--
 * @Descripttion: 更多推荐
 * @version: 1.0.0
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-07-10 16:12:49
 * @LastEditors: 孙立政
 * @LastEditTime: 2021-07-15 10:13:40
-->
<template>
  <div class="home">
    <Nav />
    <Card />
  </div>
</template>

<script>
import Nav from './components/nav.vue'
import Card from './components/card.vue'
export default {
  components: {
    Nav,
    Card
  },
  data() {
    return {

    }
  },
  created() {
    // 回到顶部
    document.body.scrollTop = 0
    document.documentElement.scrollTop = 0
  },
  mounted() {

  },
  methods: {

  }
}
</script>

<style scoped lang="scss">
</style>
