<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-07-06 17:52:28
 * @LastEditors: 孙立政
 * @LastEditTime: 2021-08-16 09:59:07
-->
<template>
  <div class="home">
    <div class="card">
      <div class="card-shop-name">{{ shopData.marketName | ellipsis(15) }}</div>
      <div class="card-shop-desc">
        <van-rate
          v-model="shopData.evaScore"
          class="card-shop-rate"
          allow-half
          readonly
          :size="13"
          color="#ffd21e"
          void-icon="star"
          void-color="#eee"
        />
        <div v-for="(item,index) in shopData.tagTitleList.slice(0,4)" :key="index" class="card-shop-tag">
          {{ item }}
        </div>
      </div>
      <div v-if="shopData.marketPhotos!=null" class="card-shop-img">
        <img v-for="(item,index) in shopData.marketPhotos" :key="index" :src="item.photo" alt="" class="item-img" @click="goPhoto">
      </div>
      <div class="card-shop-status">
        <span v-if="shopData.status == true">营业中</span>
        <span v-if="shopData.status == false">休息中</span>
        <span v-for="(item,index) in shopData.marketConfig.marketBusTimes" :key="index" style="margin-right: 6px;">{{ item.openingHours }}-{{ item.closingHours }}</span>
      </div>
      <div v-if="shopData.servingTitles.length>0" class="card-shop-serve">
        <div v-for="(item,index) in shopData.servingTitles" :key="index" class="serve-tag">{{ item }}</div>
      </div>
      <div class="card-shop-navigation">
        <div class="card-shop-address">
          <div class="card-shop-address-name">{{ shopData.address | ellipsis(13) }}</div>
          <div v-if="shopData.distance!=null" class="card-shop-distance">距离你约{{ shopData.distance.toFixed(2) }}km</div>
        </div>
        <div class="card-shop-icon">
          <div v-if="!isAmap" class="icon1" @click="goMarket(shopData)">
            <van-icon name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/user/classify/fineFood-navigation.png" size="20" />
            <div>导航</div>
          </div>
          <div v-else v-clipboard:copy="shopData.address" v-clipboard:success="onCopy" class="icon1">
            <van-icon name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/user/classify/fineFood-navigation.png" size="20" />
            <div>导航</div>
          </div>
          <div class="icon2" @click="CallPhone(shopData.mobilePhone)">
            <van-icon name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/user/classify/fineFood-call.png" size="20" />
            <div>电话</div>
          </div>
        </div>
      </div>
      <div v-if="shopData.marketConfig.isTakeaway" class="card-shop-takeout" @click="goTakeaway">
        <div class="takeout-label">
          <div class="takeout-label-point" />
          <span>外卖</span>
        </div>
        <div class="takeout-enter">
          <span>进入</span>
          <van-icon name="arrow" size="11" />
        </div>
      </div>
      <div style="height:15px" />
    </div>

    <!-- 导航 -->
    <van-popup v-model="showPop" position="bottom" round>
      <div class="mapList">
        <div @click="openMap(1)">百度导航</div>
        <div @click="openMap(2)">高德导航</div>
        <div @click="showPop=false">取消</div>
      </div>
    </van-popup>
  </div>
</template>

<script>
export default {
  props: {
    shopData: {
      type: Object,
      default: function() {
        return {}
      }
    }
  },
  data() {
    return {
      showPop: false,
      mapData: '',
      isAmap: false,
      tempscore: 4.6
    }
  },
  created() {
    // 判断是系统环境
    var u = navigator.userAgent
    var isiOS = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/) // ios终端
    if (isiOS) {
      this.isAmap = true
    }
  },
  methods: {
    // 跳转外卖
    goTakeaway() {
      this.$router.push({
        name: 'Shop',
        query: {
          id: this.$route.query.id
        }
      })
    },
    // 跳转相册
    goPhoto() {
      this.$router.push({
        name: 'FineFoodShopAlbum',
        query: {
          id: this.$route.query.id,
          type: 2
        }
      })
      this.$store.state.market.photoList = this.shopData.marketPhotos
    },
    // 拨打电话
    CallPhone(data) {
      AlipayJSBridge.call('CallPhone', {
        phoneNum: data
      }, function(result) {})
    },
    // 打开地图
    openMap(val) {
      let self = this
      if (val == 1) {
        var urlBaiduMap =
						`baidumap://map/marker?location=${this.mapData.bd_lat},${this.mapData.bd_lng}&title=${this.mapData.marketName}&content=${this.mapData.marketName}&src=Hello%20uni-app`
        AlipayJSBridge.call(
          'IsAvailable', {
            packageName: 'com.baidu.BaiduMap'
          },
          function(result) {
            if (result.available == true) {
              window.location.href = urlBaiduMap
            } else {
              self.$toast('未安装百度地图')
            }
          }
        )
      } else {
        var urlAmap =
						`androidamap://viewMap?sourceApplication=${this.mapData.marketName}&poiname=${this.mapData.marketName}&lat=${this.mapData.gd_lat}&lon=${this.mapData.gd_lng}&dev=0`

        var iosAmap =
						`iosamap://viewMap?sourceApplication=${this.mapData.marketName}&poiname=${this.mapData.marketName}&lat=${this.mapData.gd_lat}&lon=${this.mapData.gd_lng}&dev=0`
        var u = navigator.userAgent
        var isiOS = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/) // ios终端
        if (!isiOS) {
          AlipayJSBridge.call(
            'IsAvailable', {
              packageName: 'com.autonavi.minimap'
            },
            function(result) {
              if (result.available == true) {
                window.location.href = urlAmap
              } else {
                self.$toast('未安装高德地图')
              }
            }
          )
        } else {
          window.location.href = iosAmap
        }
      }
    },
    onCopy(e) {
      this.$toast({
        duration: 5000,
        forbidClick: false,
        message: '复制成功,请打开地图应用,粘贴店铺地址进行导航'
      })
    },
    // 导航店铺
    goMarket(data) {
      // 高德转百度坐标
      function bd_encrypt(gg_lng, gg_lat) {
        var X_PI = Math.PI * 3000.0 / 180.0
        var x = gg_lng
        var y = gg_lat
        var z = Math.sqrt(x * x + y * y) + 0.00002 * Math.sin(y * X_PI)
        var theta = Math.atan2(y, x) + 0.000003 * Math.cos(x * X_PI)
        var bd_lng = z * Math.cos(theta) + 0.0065
        var bd_lat = z * Math.sin(theta) + 0.006
        return {
          bd_lat: bd_lat,
          bd_lng: bd_lng
        }
      }
      let zuobiao = bd_encrypt(data.longitude, data.latitude)

      this.mapData = {
        bd_lng: zuobiao.bd_lng,
        bd_lat: zuobiao.bd_lat,
        gd_lng: data.longitude,
        gd_lat: data.latitude,
        marketName: data.marketName
      }
      this.showPop = true
    }

  }
}
</script>

<style scoped lang="scss">
    .home {
      .mapList {
        div {
          height: 150px;
          line-height: 150px;
          text-align: center;
          font-size: 28px;
        }

        div:not(:last-child) {
          border-bottom: 1px solid #EEEEEE;
          font-weight: bold;
        }
      }
        .card {
            width: 710px;
            opacity: 1;
            background: #ffffff;
            border-radius: 15px;
            margin:-190px  auto 0;
            padding-left: 18px;
            .card-shop-name {
              font-size: 40px;
              font-family:PingFangSC-Medium;
              font-weight: 500;
              color: #222222;
              margin-bottom: 8px;
              padding-top: 22px;
            }
            .card-shop-desc {
                display: flex;
                align-items: center;
                height: 30px;
                line-height: 30px;
                margin-bottom: 24px;
                .card-shop-rate {
                  height: 30px;
                  line-height: 30px;
                }
                .card-shop-tag {
                  font-size: 22px;
                  font-family: PingFangSC;
                  color: #222222;
                  margin-left: 8px;
                }
            }
            .card-shop-img {
                display: flex;
                width: 100%;
                overflow-x: auto;
                margin-bottom: 16px;
                .item-img {
                  flex-shrink: 0;
                  width: 252px;
                  height: 188px;
                  margin-right: 8px;
                  border-radius: 8px;
                  overflow: hidden;
                  object-fit: cover;
                }
            }
            .card-shop-status {
                font-size: 24px;
                font-family: PingFangSC;
                color: #222222;
                margin-bottom: 16px;
                >span {
                  margin-right: 20px;
                }
            }
            .card-shop-serve {
                display: flex;
                color: #666;
                margin-bottom: 32px;
                .serve-tag {
                  font-size: 22px;
                  border: 1px solid #dedede;
                  border-radius: 5px;
                  // transform: scale(.9);
                  padding: 2px 6px;
                  margin-right: 8px;
                }
            }
            .card-shop-navigation {
                height: 110px;
                position: relative;
                margin-bottom: 24px;
                .card-shop-address {
                  width: 448px;
                  font-size: 28px;
                  font-family:PingFangSC-Medium;
                  font-weight: 500;
                  color: #222222;
                }
                .card-shop-address-name{
                  height: 70px;
                  line-height: 35px;
                }
                .card-shop-distance {
                  font-size: 24px;
                  font-family: PingFangSC;
                  color: #999999;
                }
                .card-shop-icon {
                    position: absolute;
                    bottom: 0;
                    right: 34px;
                    width: 150px;
                    display: flex;
                    justify-content: space-between;
                    .icon1,.icon2 {
                        font-size: 20px;
                        font-family: PingFangSC;
                        color: #999999;
                        transform:scale(.83);
                        text-align: center;
                    }
                }

            }
            .card-shop-takeout {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    width: 674px;
                    height: 84px;
                    opacity: 1;
                    background: #ffffff;
                    border-radius: 8px;
                    box-shadow: 0px 0px 9px 0px rgba(0,0,0,0.06);
                    .takeout-label {
                        display: flex;
                        align-items: center;
                        font-size: 28px;
                        font-family:PingFangSC-Medium;
                        font-weight: 500;
                        color: #222222;
                        margin-left: 16px;
                        .takeout-label-point {
                            width: 10px;
                            height: 10px;
                            background: #39cf3f;
                            border-radius: 5px;
                            margin-right: 10px;
                        }
                    }
                    .takeout-enter {
                        display: flex;
                        align-items: center;
                        font-size: 24px;
                        font-family: PingFangSC;
                        color: #999999;
                        transform: scale(.83);
                    }
                }
        }
    }
</style>
