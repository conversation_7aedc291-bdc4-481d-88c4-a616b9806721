<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-07-06 11:52:58
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-09-27 22:35:39
-->
<template>
  <!-- 更多商家-->
  <div class="home">
    <div class="header">更多商家</div>
    <GoodsCard v-for="(item,index) in list" :key="index" type="2" :list="item" />
    <div style="height:15px" />
  </div>
</template>

<script>
import GoodsCard from '@/components/GoodsCard'
import { findRandomMarketByType } from '@/api/shop'
export default {
  components: {
    GoodsCard
  },
  data() {
    return {
      list: []
    }
  },
  watch: {

  },
  created() {
    this.getList()
  },
  mounted() {

  },
  methods: {
    // 获取商品/分类
    getList() {
      let data = {
        'marketType': 3,
        'regionId': this.$store.getters.getRegionId
      }
      findRandomMarketByType(data).then((res) => {
        if (res.status == 200) {
          this.list = res.data
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
    .home {
        width: 710px;
        margin: auto;
        background-color: #fff;
        border-radius: 15px;
        margin-top: 15px;
        .header {
            font-size: 34px;
            font-family:PingFangSC-Medium;
            font-weight: 500;
            color: #222222;
            padding: 24px 0 20px 18px;
          }
        .card {
            width: 676px;
            height: 290px;
            margin: auto;
            .card-top {
                display: flex;
                justify-content: space-between;
                height: 42px;
                margin-bottom: 16px;
                .card-top-title {
                    font-size: 30px;
                    font-family:PingFangSC-Medium;
                    color: #222222;
                }
                .card-top-distance {
                    font-size: 22px;
                    font-family:PingFangSC-Regular;
                    color: #666666;
                }
            }
            .card-img {
                display: flex;
                margin-bottom: 16px;
                .card-img1 {
                    flex-shrink: 0;
                    width: 220px;
                    height: 162px;
                    border-radius: 10px;
                    // background-color: gray;
                    overflow: hidden;
                }
                .card-img1:not(:last-child){
                    margin-right: 8px;
                }
            }
            .card-bottom {
                display: flex;
                font-size: 22px;
                font-family: PingFangSC;
                color: #888888;
                .card-bottom-icon {
                    width: 32px;
                    height: 32px;
                    opacity: 1;
                    background: #ff6a32;
                    border-radius: 6px;
                    text-align: center;
                    line-height: 32px;
                    color: #fff;
                    font-size: 20px;
                    transform: scale(.9);
                    margin-right: 11px;
                }
            }
        }
    }
</style>
