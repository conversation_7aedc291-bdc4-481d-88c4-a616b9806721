<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-07-06 17:31:30
 * @LastEditors: 孙立政
 * @LastEditTime: 2023-06-15 10:24:36
-->
<template>
  <div>

    <!-- 推荐菜 -->
    <div v-if="goodList.length > 0" class="home">
      <div class="header">
        <div class="header-title">店家推荐</div>
        <div v-if="$store.getters.getRegionId == 1" class="header-allEvalute" @click="goListGoods">
          <span>查看更多</span>
          <van-icon name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/review/arrow.png" size="12" />
        </div>
      </div>
      <div class="card">
        <div class="card-img">
          <div v-for="(item, index) in goodList" :key="index" class="card-img-item">
            <img class="item-img" :src="item.cover" alt="" @click="goPreview(item, index)">
            <!-- 限制7个字 -->
            <div class="item_info">
              <div class="item-img-name">{{ item.goodsName | ellipsis(6) }}</div>
              <div class="item_price">
                <div class="icon">￥</div>
                <div class="show_price">{{ item.showPrice }}</div>
                <div v-if="item.oriPrice>0" class="ori_price">￥{{ item.oriPrice }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div style="height:2px" />
    </div>
    <van-empty v-if="this.$store.getters.getRegionId == 3&&goodList.length == 0" description="欢迎光临店铺" />
  </div>
</template>

<script>
import { getGoodsList } from '@/api/shop'
import { ImagePreview } from 'vant'
export default {
  components: {
    [ImagePreview.Component.name]: ImagePreview.Component
  },
  data() {
    return {
      goodList: []
    }
  },
  watch: {
    $route(n, o) {
      if (n.fullPath !== o.fullPath) {
        // 回到顶部
        document.body.scrollTop = 0
        document.documentElement.scrollTop = 0
        this.getList()
      }
    }
  },
  created() {
    this.getList()
  },
  mounted() {

  },
  methods: {
    goPreview(item, index) {
      let data = this.goodList
      let newdata = []

      for (let i = 0; i < data.length; i++) {
        newdata.push(data[i].cover)
      }

      ImagePreview({
        images: newdata,
        startPosition: index
      })
    },
    // 获取商品/分类
    getList() {
      let data = {
        'pageNum': 1,
        'pageSize': this.$store.getters.getRegionId == 1 ? 3 : 100,
        'search': {
          'marketId': this.$route.query.id
        }
      }
      getGoodsList(data)
        .then((res) => {
          if (res.status == 200) {
            this.goodList = res.data.list
          }
        })
    },
    goListGoods() {
      this.$router.push({
        name: 'FineFoodShopMenuDetail',
        query: {
          id: this.$route.query.id,
          type: 2
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
.home {
  width: 710px;
  opacity: 1;
  background: #ffffff;
  border-radius: 15px;
  margin: 16px auto 16px;

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 22px;
    font-family: PingFangSC;
    color: #666666;
    padding: 24px 16px 20px 18px;

    .header-title {
      font-size: 34px;
      font-family: PingFangSC-Medium;
      font-weight: 500;
      color: #222222;
    }

    .header-allEvalute {
      display: flex;
      align-items: center;
    }
  }

  .card {
    margin-left: 18px;

    .card-img {
      display: flex;
      justify-content: space-between;
      flex-wrap: wrap;
      margin-bottom: 16px;

      .card-img-item {
        width: 220px;
        height: 316px;
        opacity: 1;
        border-radius: 13px;
        background: rgba(247, 248, 249, 1);
        flex-shrink: 0;
        margin-bottom: 13px;
        position: relative;

        .item_info {
          padding: 13px;
        }

        .item-img {
          width: 220px;
          height: 178px;
          border-top-left-radius: 13px;
          border-top-right-radius: 13px;
          overflow: hidden;
          vertical-align: middle;
          object-fit: cover;
        }

        .item-img-name {
          font-size: 30px;
          font-family: PingFangSC-Medium;
          font-weight: 500;
          color: #222222;
        }

        .item_price{
          height: 50px;
          line-height: 50px;
          display: flex;
          position: absolute;
          bottom: 10px;
          .icon{
            font-size: 23px;
            font-family: PingFangSC-Medium;
            font-weight: 500;
            color: rgba(255, 48, 30, 1);
            margin-top: 3px;
          }
          .show_price{
            font-size: 32px;
            font-family: PingFangSC-Medium;
            font-weight: 500;
            color: rgba(255, 48, 30, 1);
          }
          .ori_price{
            font-size: 25px;
            color: #999999;
            text-decoration: line-through;
            margin-left: 10px;
          }
        }
      }

      .card-img-item:not(:last-child) {
        margin-right: 8px;
      }
    }
  }
}</style>
