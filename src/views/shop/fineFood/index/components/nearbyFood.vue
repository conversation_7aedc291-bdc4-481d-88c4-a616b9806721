<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-07-06 15:17:17
 * @LastEditors: 孙立政
 * @LastEditTime: 2021-07-14 16:40:10
-->
<template>
  <!-- 附近美食 -->
  <div class="home">
    <div class="header">附近美食</div>
    <div v-for="(item,index) in list" :key="index" class="card">
      <img :src="item.cover" alt="" class="card-img">
      <div class="card-content">
        <div class="card-content-name">{{ item.goodsName }}</div>
        <div class="card-content-desc">{{ item.description }}</div>
        <div class="card-content-bottom">
          <div class="card-content-other">
            <div class="card-content-price">
              <span class="small">¥</span>
              <span>{{ item.showPrice }}</span>
            </div>
            <div class="card-content-oriPrice">¥{{ item.oriPrice }}</div>
            <!-- <div class="card-content-discount">4.8折</div> -->
          </div>
          <div class="card-content-distance">距商户&lt;500m</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getRecommand } from '@/api/shop'
export default {
  data() {
    return {
      list: []
    }
  },
  created() {
    this.getList()
  },
  mounted() {

  },
  methods: {
    // 获取列表
    getList() {
      getRecommand(this.$route.query.id).then(res => {
        if (res.status == 200) {
          this.list = res.data
        } else {
          this.$toast(res.message)
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
    .home {
        width: 710px;
        margin: 0 auto 16px;
        border-radius: 15px;
        background-color: #fff;
        .header {
            font-size: 34px;
            font-family:PingFangSC-Medium;
            font-weight: 500;
            color: #222222;
            padding: 24px 0 20px 18px;
        }
        .card {
            display: flex;
            height: 205px;
            margin-left: 18px;
            .card-img {
               width: 180px;
               height: 180px;
                margin-right: 24px;
                border-radius: 8px;
                overflow: hidden;
                // background-color: gray;
            }
            .card-content {
                position: relative;
                height: 180px;
                .card-content-name {
                    font-size: 30px;
                    font-family:PingFangSC-Medium;
                    font-weight: 500;
                    color: #222222;
                    margin-bottom: 8px;
                }
                .card-content-desc {
                    font-size: 22px;
                    font-family: PingFangSC;
                    color: #222222;
                }
                .card-content-bottom {
                    position: absolute;
                    left: 0;
                    bottom: 0;
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    width: 472px;
                    .card-content-other {
                        display: flex;
                        align-items: center;
                        .card-content-price {
                            font-size: 36px;
                            font-family:PingFangSC-Medium;
                            font-weight: 500;
                            color: #ff301e;
                            margin-right: 8px;
                        }
                        .card-content-oriPrice {
                            font-size: 22px;
                            font-family: PingFangSC;
                            color: #999999;
                            text-decoration: line-through;
                            margin-right: 10px;
                        }
                        .card-content-discount {
                            width: 70px;
                            height: 29px;
                            line-height: 28px;
                            text-align: center;
                            font-size: 22px;
                            font-family: PingFangSC;
                            border: 1px solid #ff301e;
                            font-family:PingFangSC-Medium;
                            color: #ff301e;
                            border-radius: 6px;
                        }
                        }
                        .card-content-distance {
                        // position: absolute;
                        // right: 0;
                        // bottom: 0;
                        // float: right;
                        text-align: right;
                        width: 200px;
                        font-size: 22px;
                        font-family: PingFangSC;
                        color: #666666;
                        }
                }

            }
        }
        .small {
            font-size: 22px;
            font-family:PingFangSC-Medium;
            font-weight: 500;
            color: #ff301e;
            transform: scale(.9);
        }
    }
</style>
