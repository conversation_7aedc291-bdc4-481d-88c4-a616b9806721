<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-05-22 15:17:21
 * @LastEditors: 孙立政
 * @LastEditTime: 2023-06-14 16:05:31
-->
<template>
  <!-- 美食 -->
  <div class="home">
    <Top :shop-data="shopData" />
    <ShopCard :shop-data="shopData" />
    <RecomDish />
    <Evalute ref="childEvalute" />
    <!-- <NearbyFood /> -->
    <MoreShop v-show="this.$store.getters.getRegionId == 3?false:true" ref="child" />
    <!-- 加载状态 -->
    <Loading :show="loadingShow" />
  </div>
</template>

<script>
import Top from './components/top.vue'
import ShopCard from './components/shopCard.vue'
import RecomDish from './components/recomDish.vue'
import Evalute from './components/evalute.vue'
// import NearbyFood from './components/nearbyFood.vue'
import MoreShop from './components/moreShop.vue'
import Loading from '@/components/Loading/index'

import { getShopData } from '@/api/shop'
export default {
  components: {
    Top,
    RecomDish,
    Evalute,
    MoreShop,
    // NearbyFood,
    ShopCard,
    Loading
  },
  data() {
    return {
      shopData: {
        marketName: '',
        tagTitleList: [],
        announcement: '',
        openingHours: '',
        closingHours: '',
        address: '',
        isTakeaway: true,
        servingTitles: [],
        marketConfig: {
          marketBusTimes: []
        },
        pic: ''
      },
      loadingShow: true
    }
  },
  watch: {
    $route(n, o) {
      if (n.fullPath !== o.fullPath) {
        this.getShopData()
        this.$refs.child.getList()
        this.$refs.childEvalute.getList()
        // 回到顶部
        document.body.scrollTop = 0
        document.documentElement.scrollTop = 0
      }
    }
  },
  created() {
    // 回到顶部
    document.body.scrollTop = 0
    document.documentElement.scrollTop = 0
    this.getShopData()
  },
  mounted() {

  },
  methods: {
    // 获取店铺详情
    getShopData() {
      let data = {
        marketId: this.$route.query.id,
        longitude: this.$store.getters.getLocation.longitude,
        latitude: this.$store.getters.getLocation.latitude
      }
      getShopData(data).then((res) => {
        this.loadingShow = false
        this.shopData = res.data
      })
    }
  }
}
</script>

<style scoped lang="scss">

</style>
