<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2021-08-26 09:59:46
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-11-03 14:19:26
-->
<template>
  <div class="home">
    <div class="xkjh" />
    <div class="box">
      <div class="address">
        <div v-if="!isAmap" @click="goMarket(shopData)">{{ shopData.address | ellipsis(23) }}</div>
        <div v-else v-clipboard:copy="shopData.address" v-clipboard:success="onCopy">{{ shopData.address | ellipsis(23) }}</div>
        <div @click="CallPhone(shopData.mobilePhone)">
          <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/shop/data/phone.png" alt="">
        </div>
      </div>
      <div v-if="marketPhotos!=null" class="shopImg">
        <img v-for="(item,index) in marketPhotos" :key="index" :src="item.photo" alt="" @click="goPhoto">
      </div>
      <div class="shopTime">
        <div class="shopTimeTitle">商家信息</div>
        <div class="shopTimeBody">
          <div class="shopTimeBodyLeft">营业时间：</div>
          <div class="shopTimeBodyRight">
            <span v-for="(item,index) in shopData.marketConfig.marketBusTimes" :key="index" style="margin-right: 10px;">{{ item.openingHours }}-{{ item.closingHours }}</span>
          </div>
        </div>
        <div class="shopTimeTitle">店铺简介</div>
        <div v-if="shopData.marketConfig.description!=null&&shopData.marketConfig.description!=''" class="shopTimeBody">{{ shopData.marketConfig.description }}</div>
        <div v-else class="shopTimeBody">欢迎光临，很高兴为您服务~</div>
      </div>
      <div class="shopBtn" @click="lookCertificate">
        查看营业资质
      </div>
    </div>

    <!-- 导航 -->
    <van-popup v-model="showPop" position="bottom" round>
      <div class="mapList">
        <div @click="openMap(1)">百度导航</div>
        <div @click="openMap(2)">高德导航</div>
        <div @click="showPop=false">取消</div>
      </div>
    </van-popup>

  </div>
</template>

<script>
import { getShopData } from '@/api/shop'
import { ImagePreview } from 'vant'
export default {
  components: {
    [ImagePreview.Component.name]: ImagePreview.Component
  },
  props: {
    shopData: {
      type: Object,
      default: function() {
        return {}
      }
    }
  },
  data() {
    return {
      showPop: false,
      mapData: '',
      isAmap: false,
      marketPhotos: [],
      certificate: '',
      foodSafetyPermit: ''
    }
  },
  created() {
    // 回到顶部
    document.body.scrollTop = 0
    document.documentElement.scrollTop = 0
    this.getShopData()
  },
  mounted() {
    // 判断是系统环境
    var u = navigator.userAgent
    var isiOS = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/) // ios终端
    if (isiOS) {
      this.isAmap = true
    }
  },
  methods: {
    // 获取店铺详情
    getShopData() {
      let data = {
        marketId: this.$route.query.id,
        longitude: this.$store.getters.getLocation.longitude,
        latitude: this.$store.getters.getLocation.latitude
      }
      getShopData(data).then((res) => {
        if (res.data.marketPhotos != null) {
          this.marketPhotos = res.data.marketPhotos
        }
        this.certificate = res.data.certificate
        this.foodSafetyPermit = res.data.foodSafetyPermit
      })
    },
    // 跳转相册
    goPhoto() {
      this.$router.push({
        name: 'FineFoodShopAlbum',
        query: {
          id: this.$route.query.id,
          type: 2
        }
      })
      this.$store.state.market.photoList = this.marketPhotos
    },
    // 查看营业执照
    lookCertificate() {
      var marketCertificate = this.certificate.split(',')
      let arrdata = [...marketCertificate]
      if (this.foodSafetyPermit != '' && this.foodSafetyPermit != null) {
        arrdata.push(this.foodSafetyPermit)
      }
      console.log(arrdata)
      ImagePreview({
        images: arrdata,
        closeable: true
      })
    },
    // 拨打电话
    CallPhone(data) {
      AlipayJSBridge.call('CallPhone', {
        phoneNum: data
      }, function(result) {})
    },
    // 打开地图
    openMap(val) {
      let self = this
      if (val == 1) {
        var urlBaiduMap =
						`baidumap://map/marker?location=${this.mapData.bd_lat},${this.mapData.bd_lng}&title=${this.mapData.marketName}&content=${this.mapData.marketName}&src=Hello%20uni-app`
        AlipayJSBridge.call(
          'IsAvailable', {
            packageName: 'com.baidu.BaiduMap'
          },
          function(result) {
            if (result.available == true) {
              window.location.href = urlBaiduMap
            } else {
              self.$toast('未安装百度地图')
            }
          }
        )
      } else {
        var urlAmap =
						`androidamap://viewMap?sourceApplication=${this.mapData.marketName}&poiname=${this.mapData.marketName}&lat=${this.mapData.gd_lat}&lon=${this.mapData.gd_lng}&dev=0`

        var iosAmap =
						`iosamap://viewMap?sourceApplication=${this.mapData.marketName}&poiname=${this.mapData.marketName}&lat=${this.mapData.gd_lat}&lon=${this.mapData.gd_lng}&dev=0`
        var u = navigator.userAgent
        var isiOS = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/) // ios终端
        if (!isiOS) {
          AlipayJSBridge.call(
            'IsAvailable', {
              packageName: 'com.autonavi.minimap'
            },
            function(result) {
              if (result.available == true) {
                window.location.href = urlAmap
              } else {
                self.$toast('未安装高德地图')
              }
            }
          )
        } else {
          window.location.href = iosAmap
        }
      }
    },
    onCopy(e) {
      this.$toast({
        duration: 5000, // 持续展示 toast
        forbidClick: false,
        message: '复制成功,请打开地图应用,粘贴店铺地址进行导航'
      })
    },
    // 导航店铺
    goMarket(data) {
      // 高德转百度坐标
      function bd_encrypt(gg_lng, gg_lat) {
        var X_PI = Math.PI * 3000.0 / 180.0
        var x = gg_lng
        var y = gg_lat
        var z = Math.sqrt(x * x + y * y) + 0.00002 * Math.sin(y * X_PI)
        var theta = Math.atan2(y, x) + 0.000003 * Math.cos(x * X_PI)
        var bd_lng = z * Math.cos(theta) + 0.0065
        var bd_lat = z * Math.sin(theta) + 0.006
        return {
          bd_lat: bd_lat,
          bd_lng: bd_lng
        }
      }
      let zuobiao = bd_encrypt(data.longitude, data.latitude)

      this.mapData = {
        bd_lng: zuobiao.bd_lng,
        bd_lat: zuobiao.bd_lat,
        gd_lng: data.longitude,
        gd_lat: data.latitude,
        marketName: data.marketName
      }
      this.showPop = true
    }
  }
}
</script>

<style scoped lang="scss">
    .home::before {
		// 利用伪元素设置整个页面的背景色
		content: " ";
		position: absolute;
		left: 0;
		right: 0;
		top: 0;
		bottom: 0;
		z-index: -100;
		min-height: 100%;
		background-color: #fff;
	}
    .home {
        width: 100%;
        .mapList {
          div {
            height: 150px;
            line-height: 150px;
            text-align: center;
            font-size: 28px;
          }

          div:not(:last-child) {
            border-bottom: 1px solid #EEEEEE;
            font-weight: bold;
          }
        }
        .xkjh{
            width: 100%;
            height: 20px;
            background-color: #F5F5F5;
        }
        .box{
            width: 680px;
            margin: 0 auto;
        }
        .address{
            height: 88px;
            line-height: 88px;
            display: flex;
            justify-content: space-between;
            font-size: 28px;
            font-family: PingFangSC;
            margin-top: 10px;
            img{
                width: 44px;
                height: 44px;
                position: relative;
                top: 12px;
            }
            color: #666666;
        }
        .shopImg{
          width: 100%;
          overflow-x: auto;
          // height: 156px;
          white-space: nowrap;
          img{
              width: 186px;
              height: 136px;
              margin-right: 10px;
              border-radius: 5px;
          }
        }
        .shopTime{
            border-top: 2px solid #f4f4f4;
            margin-top: 15px;
            font-size: 26px;
            color: #666666;
            font-family: PingFangSC;
            .shopTimeTitle{
                font-family: PingFangSC-Semibold;
                font-size: 27px;
                font-weight: 600;
                color: #333333;
                margin-bottom: 10px;
                margin-top: 25px;
            }
            .shopTimeBody{
              margin-top: 10px;
              display: flex;
              .shopTimeBodyLeft{
                width: 130px;
              }

            }
            span{
              margin-right: 10px;
            }
        }
        .shopBtn{
            width: 191px;
            height: 60px;
            font-size: 24px;
            color: #666666;
            border: 1px solid #cccccc;
            border-radius: 30px;
            margin: 0 auto;
            text-align: center;
            line-height: 60px;
            margin-top: 40px;
        }
    }
</style>
