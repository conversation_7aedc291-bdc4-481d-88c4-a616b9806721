<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-06-18 10:57:13
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-08-17 23:06:41
-->
<template>
  <!-- 优惠券 -->
  <div v-if="list!=null&&list.length>0" class="coupons_shop">
    <div class="coupons-card">
      <ul class="card-first">
        <li v-for="(item,index) in list.slice(0, 8)" :key="index" class="card-first-coupon coupon">
          <div class="medium">满{{ item.useThreshold }}减{{ item.preferentialAmount }}</div>
        </li>
      </ul>
    </div>
  </div>
</template>

<script>
import { marketCouList, receiveCou } from '@/api/shoppingMall'
import { marketCoupon } from '@/api/coupon'
export default {
  data() {
    return {
      show: false,
      list: []
    }
  },
  created() {
    this.marketCoupon()
  },
  mounted() {

  },
  methods: {
    goCoupon() {
      this.show = true
    },
    getMarketCouList() {
      let data = {
        userId: this.$store.getters.getUserId,
        marketId: this.$route.query.id
      }
      marketCouList(data).then((res) => {
        if (res.status == 200) {
          this.list = res.data
        }
      })
    },
    // 领取操作
    getReceiveCop(item) {
      if (item.receiveStatus == 0) {
        let data = {
          userId: this.$store.getters.getUserId,
          regId: item.regId,
          couponSn: item.couponSn
        }
        receiveCou(data).then((res) => {
          console.log(res)
          if (res.status == 200) {
            this.$toast('领取成功')
            this.getMarketCouList()
          } else {
            this.$toast(res.message)
          }
        })
      } else {
        this.$toast('请勿重复领取')
      }
    },
    marketCoupon() {
      marketCoupon(this.$route.query.id, 7).then((res) => {
        if (res.status == 200) {
          this.list = res.data
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
    .coupons_shop {
      max-height: 100px;
      overflow: hidden;
        .coupons-card {
          margin-top: 20px;
          margin-left: 33px;
            .card-first {
                display: flex;
                flex-wrap: wrap;
                .card-first-coupon {
                    flex-shrink: 0;
                    min-width: 118px;
                    max-width: 338px;
                    height: 36px;
                    font-size: 22px;
                    line-height: 36px;
                    text-align: center;
                    margin-right: 15px;
                    color: #fff;
                    background-image: url(https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/shoppingMall/index/coupon.png);
                    background-size: 100% 100%;
                    background-repeat: no-repeat;
                    font-family:PingFangSC-Medium;
                    font-weight: 500;
                    margin-bottom: 8px;
                    .medium{
                      margin-left: 15px;
                      margin-right: 15px;
                      height: 36px;
                      max-width: 300px;
                      letter-spacing: 1px;
                    }
                }
            }
        }
    }
</style>
