<template>
  <div class="home">
    <div class="body">
      <div class="sku_title">
        {{ skuData.goodsName | ellipsis(16) }}
      </div>
      <div class="sku_list">
        <div v-for="(ProductItem,n) in tadlist" :key="ProductItem.id" class="sku_sku">
          <div class="sku_prop">
            {{ ProductItem.name | ellipsis(7) }}
          </div>
          <div class="sku_tag">
            <ul>
              <li
                v-for="(oItem,index) in ProductItem.item"
                :key="oItem.id"
                :class="[oItem.isShow?'':'die',subIndex[n] == oItem.id?'in':'out']"
                @click="specificationBtn(oItem.name,n,index,oItem,ProductItem.item)"
              >
                {{ oItem.name | ellipsis(4) }}
              </li>
            </ul>
          </div>
        </div>
        <div style="height: 2.2rem;" />
      </div>

    </div>

    <div class="bottom">
      <div class="okselect">
        <span class="okselecttitle">已选规格：</span>
        <span v-for="(item) in selectArrFormdata()" :key="item.id" style="margin-right: .1rem;">{{ item.name }}</span>
      </div>

      <div class="price_btn">
        <div class="price">
          <span>总计</span>
          <span>￥</span>
          <span>{{ price }}</span>
          <span v-if="price!=oriPrice" class="ori_price">￥{{ oriPrice }}</span>
        </div>
        <div v-if="badgeNum(skuData)==0" class="btn" @click="addCart">
          <van-icon name="plus" />
          加入购物车
        </div>
        <!-- 加减 -->
        <div v-else class="btn2">
          <div @click="reduce(skuData)">
            <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/shop/jian.png">
          </div>
          <div class="inputNum" style="color: #000000;">{{ badgeNum(skuData) }}</div>
          <div @click="addCart">
            <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/shop/jia.png">
          </div>
          <div v-if="badgeNum(skuData) != 0&&selectItem.skuList[0].leastCopies>1" class="prop_leastCopies_box">
            <div class="prop_leastCopies">
              {{ selectItem.skuList[0].leastCopies }}份起购
            </div>
          </div>

        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { guid } from '@/utils/guid'
export default {
  props: {
    skuData: {
      type: Object,
      default: function() {
        return {}
      }
    }
  },
  data() {
    return {
      tadlist: '',
      simulatedDATA: { // 模拟后台返回的数据 多规格
        'difference': [],
        'specifications': []
      },
      subIndex: [], // 是否选中 因为不确定是多规格还是单规格，所以这里定义数组来判断
      selectItem: '', // 当前选择则的商品
      marketId: '',
      selectArr: [], // 存放被选中的值
      shopItemInfo: {}, // 存放要和选中的值进行匹配的数据
      price: '0', // 选中规格的价钱
      oriPrice: '0',
      skuGoods: '', // 选中sku商品
      loaclCart: []
    }
  },
  computed: {
    cartPrice() {
      return this.$store.getters['cart/sumPrice']
    }
  },
  mounted() {
    this.selecttag()
  },
  methods: {
    // 添加购物车
    addCart(event) {
      let cart = this.$store.state.cart.cartData[0].goodsList
      if (this.price == 0) {
        this.$toast('请选择商品规格!')
        return
      }
      let news = {}
      news.skuId = this.skuGoods.id
      news.goodsId = this.skuGoods.goodsId

      news.cover = this.skuGoods.cover
      news.difference = this.skuGoods.difference
      news.attrs = this.skuGoods.attrs
      news.tagId = this.selectItem.tagId
      news.stock = this.skuGoods.stock
      news.goodsName = this.skuGoods.goodsName
      news.numberOfPackages = this.skuGoods.numberOfPackages
      // 记录价格
      news.price = {}
      news.price.price = this.skuGoods.price
      news.price.oriPrice = this.skuGoods.oriPrice
      news.price.packPrice = this.skuGoods.packPrice
      news.leastCopies = this.skuGoods.leastCopies // 最少购买份数
      news.distributionMode = this.selectItem.tbGoodsConfigure.distributionMode
      // 起购限制
      if (this.skuGoods.leastCopies > 0 && this.badgeSkuNum(this.skuGoods) < this.skuGoods.leastCopies) {
        for (let i = 0; i < this.skuGoods.leastCopies; i++) {
          cart.push(news)
          this.loaclCart.push(news)
        }
      } else {
        cart.push(news)
        this.loaclCart.push(news)
      }
    },
    // 减购物车
    reduce(item) {
      let skuid = this.skuGoods.id
      let leastCopies = item.skuList[0].leastCopies
      let cart = this.$store.state.cart.cartData[0].goodsList
      let bcart = this.loaclCart
      for (let i = 0; i < cart.length; i++) {
        if (skuid == cart[i].skuId) {
          if (leastCopies == this.badgeSkuNum({ id: skuid })) {
            cart.splice(i, this.badgeSkuNum({ id: skuid }))
            break
          } else {
            cart.splice(i, 1)
            break
          }
        }
      }
      for (let i = 0; i < bcart.length; i++) {
        if (skuid == bcart[i].skuId) {
          if (leastCopies == this.badgeSkuNum({ id: skuid })) {
            bcart.splice(i, this.badgeSkuNum({ id: skuid }))
            break
          } else {
            bcart.splice(i, 1)
            break
          }
        }
      }
    },
    // 检查商品选中数量
    badgeNum(item) {
      let cart = this.loaclCart
      let n = 0
      if (cart) {
        for (let i in cart) {
          if (cart[i].goodsId == item.goodsId) {
            n++
          }
        }
      }
      this.$emit('cartNum', item.goodsId, n)
      return n
    },
    // 检查商品选中数量-sku维度
    badgeSkuNum(item) {
      let cart = this.loaclCart
      let n = 0
      if (cart) {
        for (let i in cart) {
          if (cart[i].skuId == item.id) {
            n++
          }
        }
      }
      return n
    },
    selecttag() { // 多规格
      let self = this
      let data = this.skuData

      data.skuList.map(nameitem => {
        nameitem.goodsName = data.goodsName
        nameitem.cover = data.cover
      })
      data.skuList.map(newitem => {
        let rence = {}
        let proptt = JSON.parse(newitem.properties)
        var str = ''
        proptt.map(propttitem => {
          str += propttitem.propValue + ','
        })
        if (str.length > 0) {
          str = str.substr(0, str.length - 1)
        }
        rence.id = String(newitem.skuId)
        rence.goodsId = String(newitem.goodsId)
        rence.price = String(newitem.price)
        rence.oriPrice = String(newitem.oriPrice)
        rence.cover = newitem.pic
        rence.leastCopies = newitem.leastCopies // 设置最少购买份数
        rence.numberOfPackages = newitem.numberOfPackages // 设置最少购买份数
        if (newitem.status == true) {
          rence.stock = 999
        } else {
          rence.stock = 0
        }
        rence.difference = str
        rence.goodsName = newitem.goodsName
        rence.oriPrice = newitem.oriPrice
        rence.packPrice = newitem.packPrice
        this.simulatedDATA.difference.push(rence)
      })

      this.specishow = true
      this.selectItem = data
      let propName = []
      let propvaluelist = []

      data.skuList.map(item => {
        item.props.map(itempropName => {
          propName.push(itempropName.propName)
        })
        propvaluelist.push(...item.props)
      })
      let newx = new Set(propName)
      let newxlist = [...newx]
      let tadlist = [] // 重组后的数据
      newxlist.map(item5 => {
        let list = {}
        list.name = item5
        list.item = []
        list.id = guid()
        tadlist.push(list)
      })

      tadlist.map(items => {
        data.skuList.map(skuitem => {
          skuitem.props.map(itempropName => {
            if (items.name == itempropName.propName) {
              let newvalueprop = {
                name: itempropName.propValue,
                id: guid()
              }
              items.item.push(newvalueprop)
            }
          })
        })
      })
      tadlist.map(item1 => {
        let newvalse = item1.item
        // 去重
        var obj = {}
        let arr = newvalse.reduce(function(item2, next) {
          obj[next.name] ? '' : obj[next.name] = true && item2.push(next)
          return item2
        }, [])
        item1.item = arr
      })

      this.tadlist = tadlist
      // 可能性
      for (var i in self.simulatedDATA.difference) {
        self.shopItemInfo[self.simulatedDATA.difference[i].difference] = self.simulatedDATA.difference[i] // 修改数据结构格式，改成键值对的方式，以方便和选中之后的值进行匹配
      }
      self.checkItem()

      // this.moren(data)
      if (this.tadlist.length > 0) {
        let defaultIdx = 0// 默认第一个未被禁用
        this.tadlist.map((item, index) => {
          const findIndex = item.item.findIndex(item1 => item1.isShow == true)
          defaultIdx = findIndex
          self.specificationBtn(item.item[defaultIdx].name, index, defaultIdx, item.item[defaultIdx])
        })
      }
    },
    moren(data) { // 默认选择
      var self = this
      let item = data.skuList[0].props[0].propValue
      if (data.skuList[0].status == true) {
        if (self.selectArr[0] != item) {
          self.selectArr[0] = item
          self.subIndex[0] = 0
        } else {
          self.selectArr[0] = ''
          self.subIndex[0] = -1 // 去掉选中的颜色
        }
        self.checkItem()
      }
    },
    specificationBtn(item, n, index, oItem) { // n规格索引，index规格值索引----切换sku
      // var self = this
      // let cart = this.$store.state.cart.cartData[0].goodsList
      let skuData = this.selectArr[n]

      console.log(skuData)
      console.log(this.selectArr)
      // self.loaclCart = []
      // for (let i = 0; i < cart.length; i++) {
      //   if (cart[i].skuId == skuData.skuId) {
      //     self.loaclCart.push(cart[i])
      //   }
      // }

      // if (oItem.isShow == false) {
      //   return false
      // }
      // if (self.selectArr[n] != item) {
      //   self.selectArr[n] = item
      //   self.subIndex[n] = index
      // } else {
      //   self.selectArr[n] = ''
      //   self.subIndex[n] = -1 // 去掉选中的颜色
      // }
      // self.checkItem()

      var self = this
      self.loaclCart = []
      if (oItem.isShow == false) {
        return false
      }
      if (self.selectArr[n] != item) {
        self.selectArr[n] = item
        // self.subIndex[n] = index
        self.subIndex[n] = oItem.id
      } else {
        self.selectArr[n] = ''
        self.subIndex[n] = -1 // 去掉选中的颜色
      }
      self.checkItem()
    },
    checkItem() {
      var self = this
      var option = self.tadlist
      var result = [] // 定义数组储存被选中的值
      for (var i in option) {
        result[i] = self.selectArr[i] ? self.selectArr[i] : ''
      }
      for (var m in option) {
        var last = result[m] // 把选中的值存放到字符串last去
        for (var k in option[m].item) {
          result[m] = option[m].item[k].name // 赋值，存在直接覆盖，不存在往里面添加name值
          option[m].item[k].isShow = self.isMay(result) // 在数据里面添加字段isShow来判断是否可以选择
        }
        result[m] = last // 还原，目的是记录点下去那个值，避免下一次执行循环时被覆盖
      }
      if (this.shopItemInfo[result]) {
        this.price = this.shopItemInfo[result].price || ''
        this.oriPrice = this.shopItemInfo[result].oriPrice || ''
      }

      // 判断添加失败
      if (this.shopItemInfo[result] === undefined) {
        this.price = '0'
        this.oriPrice = '0'
      }
      // 选中sku商品
      this.skuGoods = this.shopItemInfo[result]
      self.$forceUpdate() // 重绘
    },
    isMay(result) {
      for (var i in result) {
        if (result[i] == '') {
          return true // 如果数组里有为空的值，那直接返回true
        }
      }
      return this.shopItemInfo[result].stock == 0 ? false : true // 匹配选中的数据的库存，若不为空返回true反之返回false
    },
    selectArrFormdata() {
      let data = this.selectArr
      let newArr = []
      for (let i = 0; i < data.length; i++) {
        let obj = {}
        obj.name = data[i]
        obj.id = guid()
        newArr.push(obj)
      }
      return newArr
    }
  }
}
</script>

<style lang="scss" scoped>

	.home{
		width: 682px;
		margin: 0 auto;
		border-radius: 16px;
		position: relative;
		background-color: #FFFFFF;
		.body{
			margin-left: 34px;
		}
		.sku_title{
			width: 100%;
			height: 80px;
			line-height:80px;
			font-size:38px;
      font-family: PingFangSC-Medium;
		}
		.sku_list{
			max-height:800px;
			min-height: 100px;
			overflow-y: auto;
      padding-bottom: 80px;
		}
		.sku_sku{
			margin-top: 24px;
			.sku_prop{
				color: #999999;
				font-size: 26px;
			}
			.sku_tag{
				margin-top: 12px;
				ul{
					display: flex;
					flex-wrap: wrap;
				}

				li{
					width: 178px;
					height: 62px;
					line-height: 62px;
					text-align: center;
					border-radius: 10px;
					margin-right: 26px;
					margin-bottom: 12px;
				}
				.in{
					background-color: #F9FFF8;
					border: 1px solid #5ECC52;
					color:#65b05c;
					font-size: 26px;
					font-weight: bold;
				}
				.out{
					border: 1px solid rgba(0,0,0,.3);
					color:#373737;
					font-size:26px;
				}
				.die{
					background-color: rgba(0,0,0,.1);
				}
			}
		}
		.bottom{
			width: 100%;
			position: absolute;
			bottom: 0;
		}
		.okselect {
			width: 100%;
			height:70px;
			background-color: #F8F8F8;
			line-height: 50px;
			.okselecttitle {
				font-size:26px;
				color: #666666;
				margin-left: 34px;
			}
			span{
				font-size: 26px;
			}
		}
		.price_btn{
			width: 93%;
			height: 140px;
			margin: 0 auto;
			display: flex;
			background-color: #FFFFFF;
			justify-content: space-between;
			.price{
				line-height: 140px;
        font-family: PingFangSC-Medium;
				span:nth-child(1){
					font-size: 32px;
				}
        span:nth-child(2){
					font-size: 32px;
					color: #FB4C58;
				}
				span:nth-child(3){
					font-size: 40px;
					color: #FB4C58;
				}
			}
      .ori_price{
        font-size: 26px;
        color: #b6b4b4;
				text-decoration: line-through;
        margin-left: 13px;
      }
			.btn{
				width: 202px;
				height:69px;
				line-height:69px;
				text-align: center;
				font-size: 26px;
				background-image: url(https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/takeout/btnbj.png);
				background-size: 100% 100%;
				color: #fff;
				margin-top: 40px;
				::v-deep .van-icon {
					top:.3px
				}
			}
			.btn2{
        position: relative;
				display: flex;
        align-items: center;
				margin-top: 22px;
				.inputNum{
					margin-left: 12px;
					margin-right:12px;
          font-size: 30px;
          padding-top: 7px;
				}
				img{
					width: 40px;
					height: 40px;
				}
        .prop_leastCopies_box{
          width: 100%;
          position: absolute;
          top: 0;
          .prop_leastCopies{
            margin: 0 auto;
            font-size: 20px;
            width: 110px;
            height: 30px;
            text-align: center;
            line-height: 30px;
            border: 1px dashed #169d1b;
            border-radius: 15px;
            color: #169d1b;
          }
        }
			}
		}
	}
</style>
