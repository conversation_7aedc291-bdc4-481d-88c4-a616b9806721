<template>
  <div :id="goodList.length==0&&tabActive==0 ? 'goods':''" class="goods_list">
    <div class="swiper-container">
      <div v-if="opacity>=1" class="topNav" :style="styleVar" />
      <div ref="element" class="opacity_back" :class="{fixedBack:opacity>=1}" :style="styleVar">
        <div class="opacity_top">
          <van-icon
            style="margin-left:19px;"
            class="icons"
            name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/review/back.png"
            size="19px"
            @click="goBack()"
          />
          <span class="marketName">{{ shopData.marketName | ellipsis(8) }}</span>
          <!-- <span></span> -->
          <div style="margin-top: -15px;">
            <van-icon
              v-if="true"
              class="icons iconsShare"
              name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/share/share2.png"
              size="22px"
              @click="goShear"
            />
            <van-icon
              v-if="shopData.isCollection === 1"
              class="icons iconsShare"
              name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/shop/favorite-i.png"
              size="24px"
              @click="handlerCollect"
            />
            <van-icon
              v-if="shopData.isCollection === 0"
              class="icons iconsShare"
              name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/shop/favorite-b.png"
              size="24px"
              @click="handlerCollect"
            />
          </div>

        </div>
      </div>
      <!-- 切换tab -->
      <div
        ref="swiperContainerUl"
        class="swiper-container-ul"
        :style="'top: '+($store.getters.getStatusHeight+heightTop)+'px;'"
        :class="{isFixed:isscroll}"
      >
        <van-tabs v-model="tabActive" color="#39CF3F" :line-width="25" @click="clickTabs">
          <van-tab>
            <template #title>
              <span :class="tabActive==0?'tabNames':'tabName'">点菜</span>
            </template>
          </van-tab>
          <van-tab>
            <template #title>
              <span :class="tabActive==1?'tabNames':'tabName'">评论</span>
            </template>
          </van-tab>
          <van-tab>
            <template #title>
              <span :class="tabActive==2?'tabNames':'tabName'">商家</span>
            </template>
          </van-tab>
          <van-tab disabled />
          <van-tab disabled />
        </van-tabs>
      </div>

      <!-- 评价 -->
      <evaluate v-show="tabActive == 1" ref="getChildList" />
      <!-- 商家信息 -->
      <shopInfo v-show="tabActive == 2" :shop-data="shopData" />

      <!-- 商品列表 -->
      <div v-show="tabActive == 0" class="swiper-wrapper">
        <div class="content" :style="styleVar">
          <!--左侧-->
          <div id="lefts" ref="left" class="left" :class="{isFixed:isscroll}">
            <ul>
              <li
                v-for="(item,index) in goodList"
                ref="lItem"
                :key="index"
                :class="{active:returncss(index)}"
                @click="goScll(index)"
              >
                <div v-if="returncss(index)" class="line" />
                <div v-if="item.tagTitle.length>4" class="title">
                  {{ item.tagTitle }}
                </div>
                <div v-else class="title1">
                  <van-icon v-if="item.tagTitle === '热销'" class="tagIcon" name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/shop/listicon/icon1.png" size="16" />
                  <van-icon v-if="item.tagTitle === '招牌'" class="tagIcon" name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/shop/listicon/icon3.png" size="16" />
                  <van-icon v-if="item.tagTitle === '新品'" class="tagIcon" name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/shop/listicon/icon2.png" size="16" />
                  {{ item.tagTitle }}
                </div>
                <div v-if="item.tagTitle.length>=4" class="badge">
                  <div v-if="badgeMenuNum(item)!==0" class="badge_box">{{ badgeMenuNum(item) }}</div>
                </div>
                <div v-else class="badge_two">
                  <div v-if="badgeMenuNum(item)!==0" class="badge_box">{{ badgeMenuNum(item) }}</div>
                </div>
              </li>
            </ul>
          </div>
          <!--右侧 -->
          <div ref="right" class="right" :class="{isMarginLeft:isscroll}">
            <ul>
              <li v-for="(items, index) in goodList" :key="index" ref="rItem" :class="items.required?'li_required_css_bj':''">
                <div class="goods_list_tagTitle">
                  <span :class="items.required?'required_css':''">{{ items.tagTitle }}</span>
                  <img v-if="items.required" class="required_img" src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/shop/tagTitleTag.png" alt="">
                </div>
                <div
                  v-for="(item, key) in items.goodsList"
                  :key="key"
                  class="goods_list"
                  style="-webkit-transform:translate3d(0,0,0);"
                  :class="items.required?'required_css_bj':''"
                >
                  <div class="goods_list_left">
                    <img class="goods_img" :src="item.cover + '?x-oss-process=image/resize,w_700/format,jpg/quality,q_35' " @click="imagePreviews(item.cover)">
                    <img v-if="items.tagTitle == '热销'&&key<3" class="goods_img_tag" src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/shop/goods/rx.png" alt="">
                    <img v-if="items.tagTitle == '招牌'" class="goods_img_tag" src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/shop/goods/zp.png" alt="">
                    <img v-if="items.tagTitle == '新品'" class="goods_img_tag" src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/shop/goods/xp.png" alt="">
                    <img v-if="item.tbGoodsConfigure!=null&&item.tbGoodsConfigure.sellStatus == 2" class="goods_img_not" src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/shop/goods/no.png" alt="">
                  </div>
                  <div class="goods_list_Right">
                    <div class="goods_title">
                      {{ item.goodsName+'' | ellipsis(17) }}
                    </div>
                    <div class="goods_detile" @click="getDesc(item.description)">
                      <van-tag>{{ item.description | ellipsis(10) }}</van-tag>
                    </div>
                    <div class="goods_separate">
                      <div v-if="item.sales>0" class="sales">
                        已售 {{ item.sales }}
                      </div>
                      <div v-if="item.tbGoodsConfigure!=null&&item.tbGoodsConfigure.distributionMode == 2" class="distributionMode">
                        单点不送
                      </div>
                    </div>
                    <div v-if="item.price != item.oriPrice&&item.skuList.length == 1&&getDiscount(item.price,item.oriPrice)>0" class="price_Discount">
                      {{ getDiscount(item.price,item.oriPrice) + '折' }}
                    </div>
                    <div v-if="item.price > 0" class="goods_price">
                      <div class="price">
                        <div class="price_icon">￥</div>
                        <div class="price_price">{{ item.price }}</div>

                        <div v-if="item.skuList.length>1" class="price_qi">起 </div>
                        <div v-if="item.price != item.oriPrice" class="price_oriPrice"> ￥{{ item.oriPrice }}</div>
                      </div>
                      <div v-if="item.tbGoodsConfigure!=null&&item.tbGoodsConfigure.sellStatus == 1" class="sl_prop">
                        <div v-if="item.skuList.length > 1" class="tag">
                          <van-badge v-if="badgeNum(item)>0" class="sl_prop_badge" :content="badgeNum(item)">
                            <div class="button" @click="selecttag(item)">
                              {{ item.skuList[0].leastCopies>1?'+'+item.skuList[0].leastCopies+'份起购':'选规格' }}
                            </div>
                          </van-badge>
                          <div v-else class="button" @click="selecttag(item)">
                            {{ item.skuList[0].leastCopies>1?'+'+item.skuList[0].leastCopies+'份起购':'选规格' }}
                          </div>
                        </div>
                        <div v-else class="tag2" :class="item.price != item.oriPrice&&item.skuList.length == 1&&String(item.oriPrice).length>4&&badgeNum(item)>0?'':''">
                          <div v-if="badgeNum(item) != 0" @click="reduce(item)">
                            <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/shop/jian.png">
                          </div>
                          <div v-if="badgeNum(item) != 0" class="inputNum" style="color: #000000;">
                            {{ badgeNum(item) }}
                          </div>
                          <div v-if="item.skuList[0].leastCopies>1&&badgeNum(item)!=0" @click="selectOnetag(item)">
                            <img
                              src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/shop/jia.png"
                              @click="additem(item,$event)"
                            >
                          </div>
                          <div v-else-if="item.skuList[0].leastCopies==1" @click="selectOnetag(item)">
                            <img
                              src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/shop/jia.png"
                              @click="additem(item,$event)"
                            >
                          </div>
                          <div v-else class="button" @click="selectOnetag(item)">
                            {{ item.skuList[0].leastCopies>1?'+'+item.skuList[0].leastCopies+'份起购':'选规格' }}
                          </div>
                        </div>

                      </div>
                    </div>
                    <!-- <div :class="item.price != item.oriPrice&&item.skuList.length == 1&&String(item.oriPrice).length>4?'tag5':''"> -->
                    <div v-if="item.skuList.length == 1&&badgeNum(item) != 0&&item.skuList[0].leastCopies>1" :class="badgeNum(item)>99?'sl_prop_leastCopies_one':'sl_prop_leastCopies_two'" class="sl_prop_leastCopies">
                      {{ item.skuList[0].leastCopies }}份起购
                    </div>
                    <!-- </div> -->
                    <!-- <div class="or_price_down">￥123</div> -->
                  </div>
                </div>
              </li>
            </ul>
          </div>
        </div>
      </div>
      <!-- 空状态 -->
      <div v-if="goodList.length==0&&tabActive==0" class="empty">
        <div class="emptyImg">
          <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/empty/empty.png" alt="">
          <div class="msg">暂时没有上架商品哦</div>
        </div>
      </div>

    </div>
    <!-- sku弹出 -->
    <van-overlay :show="skuShow" z-index="9999" :lock-scroll="false" @click="skuShow = false">
      <div class="skuLine">
        <div @click.stop>
          <Sku v-if="skuShow" :sku-data="skuData" />
        </div>
        <div v-if="skuShow" class="close" style="text-align: center;margin-top: 40px;">
          <van-icon name="close" size="40" color="#fff" />
        </div>
      </div>
    </van-overlay>

    <!--小球-->
    <div class="ball-container">
      <div v-for="(ball,index) in balls" :key="index">
        <transition name="drop" @before-enter="beforeDrop" @enter="dropping" @after-enter="afterDrop">
          <div v-show="ball.show" class="ball">
            <div class="inner inner-hook" />
          </div>
        </transition>
      </div>
    </div>
    <!-- 加载状态 -->
    <Loading :show="loadingShow" />

    <van-overlay :show="describeStatus" z-index="9999" :lock-scroll="false" @click="describeStatus = false">
      <div class="describe">
        <div class="describe_box">
          <img class="describe_box_img" src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/shop/goods/close.png" alt="">
          <div class="describe_box_title">
            商品描述
          </div>
          <div class="describe_box_desc">
            {{ descMsg }}
          </div>
        </div>
      </div>
    </van-overlay>

  </div>
</template>

<script>
import {
  getList
} from '@/api/takeout'
import {
  marketCollect, marketUnCollect
} from '@/api/shop'
import Sku from './sku.vue'
import evaluate from './evaluate/index'
import shopInfo from './shopInfo'
import Loading from '@/components/Loading/index'
import { ImagePreview } from 'vant'
export default {
  components: {
    Sku,
    evaluate,
    Loading,
    shopInfo
  },
  props: {
    shopData: {
      type: Object,
      default: function() {
        return {}
      }
    }
  },
  data() {
    return {
      goodList: [],
      skuStatuis: false,
      skuShow: false,
      describeStatus: false,
      skuData: {},
      itemNum: '', // 多规格商品数量
      balls: [ // 小球 设为3个
        { show: false },
        { show: false },
        { show: false }
      ],
      dropBalls: [],
      itemindex: 0,
      tabActive: 0,
      opacity: 0,
      isscroll: false,
      loadingShow: true,
      heightTop: 0,
      shareForm: {
        sharePlatform: '',
        title: '点滴',
        content: '点滴畅享生活',
        redirectUrl: '',
        contentType: 'url',
        url: '',
        imgUrl: ''
      },
      descMsg: ''
    }
  },
  computed: {
    styleVar() {
      return {
        '--nav-height': this.$store.getters.getStatusHeight + 'px'
      }
    }
  },
  watch: {
    itemindex(n, o) {
      let lis = this.$refs.lItem
      if (this.isElementNotInViewport(lis[n])) {
        document.getElementById('lefts').scrollTop = 200
      }
      if (this.isElementNotInViewportbottom(lis[n])) {
        document.getElementById('lefts').scrollTop = -100
      }
    }
  },
  created() {
    this.getList()
  },
  mounted() {
    // js动态获取高度
    this.heightTop = this.$refs.element.offsetHeight
    window.addEventListener('scroll', this.handleScrollx, true)
  },
  destroyed() {
    window.removeEventListener('scroll', this.handleScrollx, true)
  },
  methods: {
    // 收藏
    handlerCollect() {
      if (this.shopData.isCollection === 1) {
        marketUnCollect({
          id: this.shopData.collectionId
        }).then((res) => {
          if (res.status == 200) {
            this.$toast('取消收藏')
            this.shopData.isCollection = 0
            this.$emit('resGetShopDatat')
          }
        })
      } else {
        marketCollect({
          targetId: this.$route.query.id,
          regionId: this.$store.getters.getRegionId,
          type: 1
        }).then((res) => {
          if (res.status == 200) {
            this.$toast('收藏成功')
            this.shopData.isCollection = 1
            this.$emit('resGetShopDatat')
          }
        })
      }
    },
    // 切换tab
    clickTabs(name) {
      let status = name == 0 ? true : false
      this.$store.commit('market/updateCartShow', status)
    },
    // 判断顶部区域是否在可视区域
    isElementNotInViewport(el) {
      let rect = el.getBoundingClientRect()
      return (rect.top >= (window.innerHeight - 200 || document.documentElement.clientHeight - 200))
    },
    // 判断顶部区域是否在可视区域
    isElementNotInViewportbottom(el) {
      let rect = el.getBoundingClientRect()
      return (rect.bottom <= 300)
    },
    // 获取商品/分类
    getList() {
      getList(this.$route.query.id).then((res) => {
        this.loadingShow = false
        if (res.status == 200) {
          // 重置必选品
          this.$store.state.market.marketData.required.isShow = false
          this.$store.state.market.marketData.required.tagId = ''

          let data = []
          // 存在热销/招牌/新品
          let appendList = res.data.marketAppendGoodsListVOList
          if (appendList.length > 0) {
            for (let i = 0; i < appendList.length; i++) {
              appendList[i].tagId = appendList[i].appendId
              appendList[i].tagTitle = appendList[i].appendTitle
            }
            data = [...appendList, ...res.data.marketTagGoodsListVOList]
          } else {
            // 不存在
            data = [...res.data.marketTagGoodsListVOList]
          }

          for (let i = 0; i < data.length; i++) {
            // 记录tagId
            for (let b = 0; b < data[i].goodsList.length; b++) {
              data[i].goodsList[b].tagId = data[i].tagId
            }

            // 存在必选品，记录
            if (data[i].required == true) {
              this.$store.state.market.marketData.required.isShow = data[i].required
              this.$store.state.market.marketData.required.tagId = data[i].tagId
              this.$store.state.market.marketData.required.tagIndex = i
            }
          }
          this.goodList = data
        }
      })
    },
    // 左侧分类展示状态
    returncss(index) {
      if (this.itemindex == index) {
        return true
      } else {
        return false
      }
    },
    scrollSmoothTo(position) { // 平滑滚动
      if (!window.requestAnimationFrame) {
        window.requestAnimationFrame = function(callback, element) {
          return setTimeout(callback, 17)
        }
      }
      // 当前滚动高度
      var scrollTop = document.documentElement.scrollTop || document.body.scrollTop
      // 滚动step方法
      var step = function() {
        // 距离目标滚动距离
        var distance = position - scrollTop
        // 目标滚动位置
        scrollTop = scrollTop + distance / 5
        if (Math.abs(distance) < 1) {
          window.scrollTo(0, position)
        } else {
          window.scrollTo(0, scrollTop)
          requestAnimationFrame(step)
        }
      }
      step()
    },
    // 左侧分类
    goScll(index) {
      let lis = this.$refs.rItem
      this.itemindex = index
      // 点击第一个tagtitle时不让其滚动
      if (index == 0) {
        this.scrollSmoothTo(lis[index].offsetTop - 80)
      } else {
        this.scrollSmoothTo(lis[index].offsetTop - 140)
      }
    },
    // 监听滚动
    handleScrollx() {
      // 获取页面滚动距离
      let top = document.documentElement.scrollTop || document.body.scrollTop || window.pageYOffset

      // 一定要减去状态栏的高度（状态栏的高度是不会滑动的）
      // if (top >= 195 - this.$store.getters.getStatusHeight) {
      if (top >= 195) {
        this.isscroll = true
      } else {
        this.isscroll = false
      }
      let lis = this.$refs.rItem
      if (typeof (lis) == 'undefined') {
        return false
      }
      for (let i = 0; i < lis.length; i++) {
        // var target = parseInt(lis[i].offsetTop - top - 90 - this.$store.getters.getStatusHeight)
        var target = parseInt(lis[i].offsetTop - top - 140)
        if (target <= 0) {
          this.itemindex = i
        }
      }
      const opacityBack = document.querySelector('.opacity_back')
      if (top <= 120 && top >= 0) {
        opacityBack.style.setProperty('opacity', 0)
        this.opacity = 0
      } else {
        opacityBack.style.setProperty('opacity', (top - 80) / 100)
        this.opacity = (top - 50) / 100
      }
    },
    // 选择sku
    selecttag(item) {
      this.skuShow = true
      this.skuData = item
    },
    // 检查商品选中数量
    badgeNum(item) {
      let cart = this.$store.state.cart.cartData[0].goodsList
      let n = 0
      if (cart) {
        for (let i in cart) {
          if (cart[i].goodsId == item.goodsId) {
            n++
          }
        }
      }
      return n
    },
    // 左侧分类数量
    badgeMenuNum(item) {
      let cart = this.$store.state.cart.cartData[0].goodsList
      let n = 0
      if (cart) {
        for (let i in cart) {
          if (cart[i].tagId == item.tagId) {
            n++
          }
        }
      }
      return n
    },
    // 减购物车
    reduce(item) {
      let skuid = item.skuList[0].skuId
      let cart = this.$store.state.cart.cartData[0].goodsList
      for (let i = 0; i < cart.length; i++) {
        if (skuid == cart[i].skuId) {
          if (cart[i].leastCopies == this.badgeNum(item)) {
            cart.splice(i, this.badgeNum(item))
            break
          } else {
            cart.splice(i, 1)
            break
          }
        }
      }
    },
    // 加购物车
    selectOnetag(data) {
      let cart = this.$store.state.cart.cartData[0].goodsList
      // let newCart = this.$store.state.cart.cartData[0].goodsList
      let cartList = {}
      cartList.skuId = data.skuList[0].skuId
      cartList.goodsId = data.goodsId
      cartList.tagId = data.tagId
      cartList.leastCopies = data.skuList[0].leastCopies // 最少购买份数
      cartList.attrs = data.attrs
      cartList.cover = data.skuList[0].pic
      cartList.difference = data.skuList[0].skuName
      cartList.numberOfPackages = data.skuList[0].numberOfPackages
      cartList.stock = 999
      cartList.goodsName = data.goodsName
      // 记录价格
      cartList.price = {}
      cartList.price.price = data.price
      cartList.price.oriPrice = data.oriPrice
      cartList.price.packPrice = data.skuList[0].packPrice

      if (data.tbGoodsConfigure == null) {
        cartList.distributionMode = 1
      } else {
        cartList.distributionMode = data.tbGoodsConfigure.distributionMode // 配送方式1：单点可送2单点不送
      }

      // 起购限制
      if (cartList.leastCopies > 0 && this.badgeNum(data) < cartList.leastCopies) {
        for (let i = 0; i < cartList.leastCopies; i++) {
          cart.push(cartList)
        }
      } else {
        cart.push(cartList)
      }
    },
    // 添加动画
    additem(img, event) {
      this.drop(event.target)
    },
    drop(el) { // 抛物
      for (let i = 0; i < this.balls.length; i++) {
        let ball = this.balls[i]
        if (!ball.show) {
          ball.show = true
          ball.el = el
          this.dropBalls.push(ball)
          return
        }
      }
    },
    beforeDrop(el) {
      /* 购物车小球动画实现 */
      let count = this.balls.length
      while (count--) {
        let ball = this.balls[count]
        if (ball.show) {
          let rect = ball.el.getBoundingClientRect() // 元素相对于视口的位置
          let x = rect.left - 60 // 小球起点位置？
          let y = -(window.innerHeight - rect.top - 42) // 小球终点位置？
          el.style.display = ''
          el.style.webkitTransform = 'translateY(' + y + 'px)' // translateY
          el.style.transform = 'translateY(' + y + 'px)' // 整个小球y轴偏移的坐标（每个item高度不一样）
          let inner = el.getElementsByClassName('inner-hook')[0]
          inner.style.webkitTransform = 'translateX(' + x + 'px)'
          inner.style.transform = 'translateX(' + x + 'px)' // 整个小球x轴偏移的坐标（固定）
        }
      }
    },
    dropping(el, done) {
      /* 重置小球数量  样式重置*/
      el.style.webkitTransform = 'translate3d(0,0,0)'
      el.style.transform = 'translate3d(0,0,0)'
      let inner = el.getElementsByClassName('inner-hook')[0]
      inner.style.webkitTransform = 'translate3d(0,0,0)'
      inner.style.transform = 'translate3d(0,0,0)'
      el.addEventListener('transitionend', done)
    },
    afterDrop(el) {
      /* 初始化小球*/
      let ball = this.dropBalls.pop()
      if (ball) {
        ball.show = false
        el.style.display = 'none'
      }
    },
    // 添加动画--end
    // 返回
    goBack() {
      let data = this.$route.query.st
      if (data == 'index') {
        this.$router.push('/index')
      } else {
        this.$router.go(-1)
      }
    },
    // 计算折扣
    getDiscount(val1, val2) {
      let num = val1 / val2 * 10
      var f_x = parseFloat(num)
      if (isNaN(f_x)) {
        alert('function:changeTwoDecimal->parameter error')
        return false
      }
      f_x = Math.round(num * 100) / 100
      var s_x = f_x.toString()
      var pos_decimal = s_x.indexOf('.')
      if (pos_decimal < 0) {
        pos_decimal = s_x.length
        s_x += '.'
      }
      while (s_x.length == pos_decimal + 1) {
        s_x = s_x.slice(0, s_x.length - 1)
      }
      if (s_x > 0) {
        return s_x
      }
    },
    // 分享
    goShear() {
      let data = {
        text: this.shopData.address,
        title: this.shopData.marketName,
        url: 'https://share.zjntwl.com/#/shop?id=' + this.$route.query.id,
        image: this.shopData.pic
      }
      AlipayJSBridge.call('WatchShare', data, function(result) {
        console.log(result)
      })
    },
    goNext() {},
    getDesc(val) {
      this.descMsg = val
      this.describeStatus = true
    },
    // 商品图片放大
    imagePreviews(val) {
      ImagePreview({ showIndex: false, images: [val], closeable: true })
    }
  }
}
</script>

<style lang="scss" scoped>
  #goods::before {
    // 利用伪元素设置整个页面的背景色
    content: " ";
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    z-index: -100;
    min-height: 100%;
    background-color: #fff;
  }
	.isFixed {
		position: fixed;
	}

	.isMarginLeft {
		margin-top: 80px;
		margin-left: 21%;
	}

	.opacity_back {
		width: 100%;
		position: fixed;
		background-color: #fff;
		// top: 0;
    top: var(--nav-height);
		left: 0;
		opacity: 0;
    z-index: 100;
	}

	.fixedBack {
		z-index: 20;
	}

	.opacity_top {
		width: 100%;
		height: 80px;
		line-height: 80px;
		background-color: #fff;
    display: flex;
    justify-content: space-between;
    .marketName{
      font-size: 36px;
    }
    .icons{
      position: relative;
      top: 20px;
    }
    .iconsShare{
      margin-right: 30px;
    }
	}

	#wrap {
		display: flex;
		width: 100%;
		position: absolute;
		top:275px;
		bottom: 0px;
		left: 0px;
		background-color: #ffffff;
		overflow: hidden;
		padding-bottom: 108px;
	}

	.swiper-container-ul {
		list-style: none;
		overflow: hidden;
		width: 100%;
		background: #fff;
		z-index: 99;

		.tabName {
			font-size: 33px;
		}
    .tabNames {
      font-size: 33px;
      font-family: PingFangSC-Medium;
		}
    ::v-deep .van-tabs__line{
      height: 6px;
    }
	}
  .topNav{
    width: 100%;
    height: var(--nav-height);
    position: fixed;
    top: 0;
    z-index: 2;
    background-color: #fff;
  }
	.right {
		float: left;
		width: 79%;
		height: 100%;
		background-color: #fff;
		padding-top: 45px;
		padding-bottom: 150px;
		min-height: 70vh;

    .goods_list_tagTitle{
      font-size: 33px;
      font-family: PingFangSC-Medium;
      margin-left: 15px;
      margin-bottom: 15px;
      .required_css{
        color: #ff3817;
      }
      .required_img{
        width: 164px;
        height: 37px;
        margin-left: 13px;
        position: relative;
        top: 3px;
      }
    }
		.goods_list {
			min-height: 212px;
			margin: 0 auto;
			display: flex;
      margin-bottom: 80px;

      .goods_list_left{
        position: relative;
        .goods_img {
          width: 186px;
          height: 186px;
          border-radius: 10px;
          overflow: hidden;
          margin: 0 18px;
        }
        .goods_img_tag{
          width: 48px;
          height: 30px;
          position: absolute;
          top: 0;
          right: 10px;
        }
        .goods_img_not{
          width: 186px;
          height: 42px;
          position: absolute;
          bottom: 27px;
          right: 17px;
        }
      }

      .goods_list_Right{
        margin-left: 12px;
        .tag5{
          padding-top: 40px;
        }
      }
			.goods_title {
        min-height: 38px;
				line-height: 38px;
				font-size: 33px;
        font-family: PingFangSC-Medium;
        margin-top: 5px;
			}

			.goods_detile {
        max-width: 250px;
				height: 37px;
				line-height: 37px;
				font-size: 23px;
        overflow: hidden;
        text-overflow:ellipsis;
        white-space: nowrap;
        margin-top: 12px;
        color: #454545;
        ::v-deep .van-tag--default {
          background-color: #F5F5F6;
          color:#454545
        }
        ::v-deep .van-tag {
          padding:0 10px;
        }
			}
      .goods_separate{
        height: 30px;
				line-height: 30px;
        margin-top: 10px;
        font-size: 23px;
        color: #666666;
        display: flex;
        .sales{
          margin-right: 10px;
        }
        .distributionMode{
          width: 100px;
          height: 30px;
          line-height: 28px;
          border: 2px solid #e4e4e4;
          border-radius: 6px;
          font-size: 23px;
          text-align: center;
        }

        // justify-content: space-between;
      }
      .price_Discount{
        width: 84px;
        height: 30px;
        border: 2px solid #ff9a92;
        border-radius: 6px;
        font-size: 23px;
        color: #ff301e;
        text-align: center;
        line-height: 28px;
        margin-top: 8px;
      }

      .sl_prop_leastCopies {
        font-size: 23px;
        width: 120px;
        min-height: 30px;
        text-align: center;
        line-height: 30px;
        border: .5px dashed #169d1b;
        border-radius: 15px;
        color: #169d1b;
        float: right;
        margin-top: -80px;

      }
      .or_price_down{
        font-size: 24px;
				color: #b6b4b4;
        text-decoration: line-through;
      }
      .sl_prop_leastCopies_one{
        margin-right: 25px;
      }
      .sl_prop_leastCopies_two{
        margin-right: 15px;
      }

			.goods_price {
				width: 330px;
        height: 38px;
				display: flex;
				justify-content: space-between;
        align-items: center;
        margin-top: 11px;

				.sl_prop {
          height: 38px;
          display: flex;
          align-items: center;
					.tag {
						.button {
							font-size: 23px;
              min-width: 89px;
              height: 43px;
              text-align: center;
              line-height: 43px;
              color: #ffffff;
              background: linear-gradient(90deg,#40d243, #1fc432);
              border-radius: 10px;
						}
            .sl_prop_badge{
              position: relative;
              top: -15px;
            }
					}

					.tag2 {
            height: 38px;
						display: flex;
            align-items: center;
            >div {
              display: flex;
              align-items: center;
              height: 38px;
            }
						.inputNum {
              font-size: 32px;
							margin-left: 18px;
							margin-right: 18px;
							position: relative;
							top: 2px;
						}

						img {
							width: 42px;
							height: 42px;
						}

            .button {
							font-size: 23px;
              min-width: 89px;
              height: 43px;
              text-align: center;
              line-height: 43px;
              color: #ffffff;
              background: linear-gradient(90deg,#40d243, #1fc432);
              border-radius: 10px;
						}
					}
          .tag3{
            padding-top: 80px;
          }
				}

				.price {
          display: flex;
          align-items: center;
          width: 140px;

					.price_icon {
						font-size: 18px;
						color: #FF301E;
            position: relative;
            top: 1px;
					}

					.price_price {
						font-size: 30px;
						color: #FF301E;
            font-family: PingFangSC-Medium;
					}

					.price_oriPrice {
						font-size: 24px;
						color: #b6b4b4;
						// margin-left:8px;
						text-decoration: line-through;
            position: relative;
            top: 2px;
					}

          .price_qi{
            font-size: 24px;
						color: #b6b4b4;
						margin-left:5px;
            position: relative;
            top: 2px;
          }
				}
			}

			.close {
				width: 100%;
				text-align: center;
			}
		}
    .required_css_bj{
      background: #fffae5;
      padding-top: 20px;
      // margin-bottom: 0;
    }
    .li_required_css_bj{
      padding-top: 20px;
      margin-top: -15px;
      background: #fffae5;
    }
	}
   .left {
		float: left;
		width: 21%;
		height: calc(100% - 303px - var(--nav-height));
		z-index: 15;
		font-size: 24px;
		color: #454545;
		overflow-y: auto;
    overflow-x: hidden;
		top:calc(168px +  var(--nav-height));
    background-color: #f5f5f5;
    margin-top: 29px;
    border-top-right-radius: 12px;
		.title {
			margin: 27.1px 20px;
			display: -webkit-box;
			-webkit-box-orient: vertical;
			-webkit-line-clamp: 2;
			overflow: hidden;

		}
		.title1 {
			margin: 0 auto;
			min-height: 90px;
			line-height: 90px;
      word-wrap:break-word;
      // overflow:hidden;
		}
    ul{
      margin-bottom: 25px;
    }

		li {
			position: relative;
			text-align: center;
			display: flex;
			background-color: #f5f5f5;
			font-size: 27px;
			.line {
				position: absolute;
				width: 8.3px;
				height: 50px;
				top: 50%;
				left: 0;
				transform: translateY(-25px);
				background-color: #39CF3F;
				border-top-right-radius: 10px;
				border-bottom-right-radius: 10px;
			}
      .badge{
        width: 0;
        position: relative;
        top: 15px;
        right: 35px;
        font-family: PingFangSC;
        .badge_box{
          width: 32px;
          height: 32px;
          background: #ff301e;
          border-radius: 50%;
          color: #fff;
          font-size: 20px;
          text-align: center;
          line-height: 33px;
        }
      }
      .badge_two{
        width: 0;
        position: relative;
        top: 15px;
        right: 43px;
        font-family: PingFangSC;
        .badge_box{
          width: 32px;
          height: 32px;
          background: #ff301e;
          border-radius: 50%;
          color: #fff;
          font-size: 20px;
          text-align: center;
          line-height: 33px;
        }
      }

		}

    .tagIcon{
      position: relative;
      top: 5px;
      margin-left: -15px;
    }

		.active {
			background: #fff;
			// font-weight: 600;
			color: #333;
      font-family: PingFangSC-Medium;
      .badge,.badge_two{
        font-family: PingFangSC;
      }
		}

	}

	.ball {
		position: fixed;
		left: 100px;
		bottom: 52px;
		z-index: 200;
		transition: all 0.4s cubic-bezier(0.49, -0.29, 0.75, 0.41);
		/*贝塞尔曲线*/
	}

	.inner {
		width: 16px;
		height: 16px;
		border-radius: 50%;
		background: red;
		transition: all .4s linear;
	}

	.skuLine {
		position: relative;
		top: 50%;
		/*偏移*/
		transform: translateY(-50%);
	}
  .describe {
		position: relative;
		top: 50%;
		/*偏移*/
		transform: translateY(-50%);
    .describe_box{
      width: 516px;
      min-height: 204px;
      background: #f5f5f6;
      border-radius: 22px;
      margin: 0 auto;
      .describe_box_img{
        width: 40px;
        height: 40px;
        float: right;
        position: relative;
        top: 20px;
        right: 20px;
      }
      .describe_box_title{
        width: 100%;
        height: 100px;
        line-height: 100px;
        font-size: 35px;
        font-family: PingFangSC-Medium;
        color: #333333;
        text-align: center;
      }
      .describe_box_desc{
        width: 446px;
        word-wrap: break-word;
        margin: 0 auto;
        color: #454545;
        font-size: 30px;
      }
    }
	}
  .empty {
      position: fixed;
        width: 100%;
        height: 500px;
        top:45%;
        left: 0;
        right: 0;
        bottom: 0;
        background-color:#fff;
        .emptyImg {
            position: absolute;
            left: 50%;
            top: 5%;
            transform: translateX(-50%);
            width: 420px;
            height: 300px;
            margin: 0 auto;
            img {
                width: 100%;
                height: 100%;
            }
        }
        .msg {
            font-size: 30px;
            font-family:PingFangSC-Medium;
            font-weight: 500;
            color: #333333;
            text-align: center;
            margin-top: 24px;
        }
        .msg1 {
            font-size: 26px;
            font-family: PingFangSC;
            text-align: center;
            color: #999999;
            margin-top: 16px;
        }
        .btn {
            width: 230px;
            height: 80px;
            line-height: 80px;
            text-align: center;
            font-size: 32px;
            border: 2px solid #169d1b;
            border-radius: 14px;
            color: #169d1b;
            font-family: PingFangSC-Medium;
            font-weight: 500;
            margin: 36px auto 0;
        }

  }
  .shareList{
      width: 90%;
      height: 92%;
      background-color: #fff;
      margin: 0 auto;
      border-radius: 15px;
      position: relative;
      overflow: hidden;
      .shareListIcon{
        display: flex;
        font-size: 28px;
        text-align: center;
        width: 90%;
        margin: 0 auto;
        margin-top: 10%;
        img{
          width: 100px;
          height: 100px;
          margin-bottom: 10px;
        }
        .shareListIconLetf{
          width: 50%;
        }
        .shareListIconRight{
          width: 50%;
        }
      }
      .close{
        width: 100%;
        height: 150px;
        text-align: center;
        line-height: 150px;
        font-size: 35px;
        font-weight: PingFangSC-Medium;
        position: absolute;
        bottom: 0;
      }
    }
</style>
