<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: zhaoyuxin
 * @Date: 2021-05-08 17:37:12
 * @LastEditors: zhaoyuxin
 * @LastEditTime: 2021-07-27 14:30:52
-->
<template>
  <div class="home">
    <div v-if="totalEvas.length" class="ev-top">
      <div class="left">
        <div v-if="totalEvas[0].score" class="fraction">4.8</div>
        <div class="ev-score">
          <div class="ev-allfraction">整体评分</div>
          <div class="ev-start">
            <van-rate v-model="totalEvas[0].score" readonly :size="18" color="#ffd21e" void-icon="star" void-color="#eee" />
          </div>
        </div>
      </div>
      <ul class="right">
        <li v-for="(item,index) in totalEvas" :key="index" class="ev-fast">
          <div v-if="index>0">
            <div class="ev-title">{{ item.evaTypeName }}</div>
            <div class="ev-num">{{ item.score }}</div>
          </div>
        </li>
        <!-- <div class="ev-fast">
					<div class="ev-title">味道</div>
					<div class="ev-num">3</div>
				</div>
				<div class="ev-last">
					<div class="ev-title">包装</div>
					<div class="ev-num">2</div>
				</div> -->
      </ul>
    </div>
    <div class="line" />
  </div>
</template>

<script>
import { marketRate } from '@/api/evaluate'
import { getShopData } from '@/api/takeout'
export default {
  data() {
    return {
      value: 3.5,
      marketId: '',
      orderType: '',
      totalEvas: []
    }
  },
  created() {
    // this.getMarketRate()
    this.getShopData()
  },
  methods: {
    getMarketRate() {
      let data = {
        marketId: this.marketId,
        orderType: this.orderType
      }
      marketRate(data).then((res) => {
        if (res.status == 200) {
          this.totalEvas = res.data.totalEvas
        }
      })
    },
    // 获取店铺详情
    getShopData() {
      getShopData(this.$route.query.id).then((res) => {
        if (res.status == 200) {
          this.marketId = res.data.marketConfig.marketId
          this.orderType = res.data.marketConfig.isTakeaway ? '7' : '3'
          this.getMarketRate()
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
	.home{
		width: 100%;
		background-color: #FFFFFF;
		border-top:1px solid #f4f4f4;
		.ev-top{
			width: 93%;
			margin: 0 auto;
			display: flex;
			padding-top: 20px;
			.left{
				display: flex;
				width: 60%;
				.fraction{
					font-size:78px;
					color: #FF5A30;
				}
				.ev-score{
					margin-left: 16px;
					margin-top: 10px;
					.ev-allfraction{
						font-size: 26px;
						color: #999999;
					}
					.ev-start{
						margin-top: 10px;
					}
				}

			}
			.right{
				width: 40%;
				display: flex;
				.ev-fast,.ev-last{
					width: 50%;
					text-align: center;
					margin-top:10px;
				}
				.ev-title{
					font-size: 26px;
					color: #999999;
				}
				.ev-num{
					font-size: 29px;
					color: #454545;
					margin-top: 5px;
				}
			}
		}
		.line {
			width: 100%;
			height: 16px;
			background-color: #F5F5F5;
		}
	}
</style>
