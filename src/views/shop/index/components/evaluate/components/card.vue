<template>
  <div class="home">
    <div class="tab">
      <div :class="{'tabIn':tabIndex}" @click="goTab(1)">外卖点评</div>
      <div :class="{'tabIn':!tabIndex}" @click="goTab(2)">到店点评</div>
    </div>

    <!-- 评价卡片 -->
    <van-list v-if="evData.length!=0" v-model="loading" :finished="finished" finished-text="没有更多评价了~" @load="onLoad">
      <div v-for="item in evData" :key="item.id" class="card">
        <div class="card-top">
          <div class="card-top-left">
            <van-image round width="34px" height="34px" :src="item.userImage ? item.userImage:defaultImg" />
            <div class="card-top-info">
              <div class="card-top-name">{{ item.isAnonymous==true?'匿名用户':item.userName }}</div>
              <div class="card-top-start">
                <van-rate
                  v-model="item.score"
                  readonly
                  :size="16"
                  color="#ffd21e"
                  void-icon="star"
                  void-color="#eee"
                />
              </div>
            </div>
          </div>
          <div class="card-top-right">
            {{ item.evaTime }}
          </div>
          <div v-if="item.isRecommend" class="card-top-isRecommend" />
        </div>
        <div class="card-msg">
          {{ item.tbEvaluationContents[0].content }}
        </div>
        <div v-if="item.pictures!=''" class="card-img">
          <van-image v-for="(imgList,index) in item.pictures.split(',')" :key="index" :width="imgHeight(item.pictures)" :height="imgHeight(item.pictures)" radius="8" :src="imgList" />
        </div>
        <div v-if="item.tbEvaluationContents.length>1">
          <div v-if="item.isReply == true" class="card-reply">
            <div class="card-reply-title">商家回复：</div>
            <div class="card-reply-body">
              {{ item.tbEvaluationContents[1].content }}
            </div>
          </div>
          <div v-if="item.isAppendComment == true&&item.isReply == true" class="card-reply">
            <div class="card-reply-title">追评：</div>
            <div class="card-reply-body">
              {{ item.tbEvaluationContents[2].content }}
            </div>
          </div>
          <div v-if="item.isAppendComment == true&&item.isReply == false" class="card-reply">
            <div class="card-reply-title">追评：</div>
            <div class="card-reply-body">
              {{ item.tbEvaluationContents[1].content }}
            </div>
          </div>
        </div>

      </div>
    </van-list>
    <div v-if="!$store.state.market.cartShow" class="card-evaluate">
      <div @click="evaluate" />
    </div>
    <!-- 无评价 -->
    <div v-if="evData==null||evData.length == 0||evData.length == ''" class="nullEv">
      <div>
        <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/user/shop/null.png" alt="">
      </div>
      <div style="font-size:20px;font-family:PingFangSC-Medium;">
        暂无评论，期待您的餐后评价~
      </div>
    </div>

    <div style="height: 100px;" />
  </div>
</template>

<script>
import {
  queryByType
} from '@/api/evaluate'
export default {
  data() {
    return {
      height: 180,
      tabIndex: true,
      query: {
        'pageNum': 1,
        'pageSize': 10,
        'search': {
          'evaType': 1,
          'marketId': this.$route.query.id,
          orderType: 7
        }
      },
      evData: [],
      loading: false,
      finished: false,
      ifnull: false,
      defaultImg: 'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/shop/appraise/default.png'
    }
  },
  created() {
    this.getList()
  },
  methods: {
    // 获取列表
    getList() {
      queryByType(this.query).then(res => {
        if (res.status == 200 && res.data.list != null) {
          this.evData.push(...res.data.list)
          this.loading = false
          // 数据全部加载完成
          if (this.query.pageNum == res.data.pages) {
            this.finished = true
          }
        } else {
          this.loading = false
          this.finished = true
          this.ifnull = true
        }
      })
    },
    // 分页加载
    onLoad() {
      this.query.pageNum = this.query.pageNum + 1
      this.getList()
    },
    goTab(value) {
      this.query.pageNum = 1
      this.evData = []
      this.tabIndex = this.tabIndex == true ? false : true
      if (value == 1) {
        this.query.search.orderType = 7
      } else {
        this.query.search.orderType = 3
      }
      this.getList()
    },
    imgHeight(row) {
      let data = row.split(',')
      if (data.length == 2) {
        return 190
      } else if (data.length == 3) {
        return 125
      } else if (data.length == 1) {
        return 190
      }
    },
    evaluate() {
      this.$router.push({
        name: 'PublishEvalute',
        query: {
          marketId: this.$route.query.id
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
	.home::before {
		// 利用伪元素设置整个页面的背景色
		content: " ";
		position: absolute;
		left: 0;
		right: 0;
		top: 0;
		bottom: 0;
		z-index: -100;
		min-height: 100%;
		background-color: #fff;
	}
	.home {
		width: 100%;
		height: auto;
		background-color: #fff;
		overflow: hidden;
		.tab {
			width: 690px;
			height: 66px;
			display: flex;
			line-height: 66px;
			margin: .4rem auto 0;
			font-family:PingFangSC-Medium;
			div {
				width: 50%;
				text-align: center;
				font-size: 30px;
				font-weight: bold;
				background-color: #E3E6E9;
				border: 1px solid #e3e6e9;
				border-radius: 8px;
			}

			.tabIn {
				background-color: #169D1B;
				color: #fff;
			}
		}

		.card {
			width: 93%;
			height: auto;
			margin: 40px auto 0;
			padding-bottom: 30px;
			border-bottom: 1px solid #f8f8f8;
			.card-top {
				position: relative;
				width: 100%;
				// height: 1rem;
				display: flex;

				.card-top-left {
					width: 60%;
					display: flex;
					.card-top-info {
						margin-left: 12px;
						.card-top-name {
							font-size: 26px;
							font-weight: bold;
							color: #333333;
							margin-top: -.5px;
						}

						.card-top-start {
							margin-top: 4px;
						}
					}
				}

				.card-top-right {
					width: 40%;
					font-size: 22px;
					text-align: right;
					color: #999999;
				}
				.card-top-isRecommend {
					position: absolute;
					right:25px;
					top:-15px;
					width: 66px;
					height: 66px;
					background: url(https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/user/goods/isRecommend.png);
					background-size: 100%;
				}
			}

			.card-msg {
				margin-top:8px;
				font-size: 26px;
				font-family: PingFangSC;
				font-weight: 400;
				text-align: justify;
				color: #333333;
				line-height: 37px;
			}

			.card-img {
				margin-top:16px;
				display: flex;
				justify-content: space-between;
				.van-image:not(:last-child) {
					margin-right:12px;
				}
			}

			.card-reply {
				width: 100%;
				background-color: #FAFAFA;
				margin-top: 18px;
				overflow: hidden;
				border-radius: 12px;
				// margin-bottom: .3rem;

				div {
					width: 93%;
					margin: 0 auto;
				}

				.card-reply-title {
					font-size: 26px;
					font-weight: bold;
					margin-top: 12px;
				}

				.card-reply-body {
					color: #666666;
					font-size: 25px;
					margin-top: 8px;
					margin-bottom: 8px;
				}
			}

		}
		.card-evaluate {
				position: fixed;
				left:0;
				right: 0;
				bottom: 0;
				width: 100%;
				height: 108px;
				background-color: #fff;
				>div {
					width: 690px;
					height:88px;
					margin-left: 30px;
					margin-top: 10px;
					background-image: url(https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/review/evalute.png);
					background-size: 100% 100%;
				}
			}
		.nullEv{
			width: 100%;
			height: 200px;
			text-align: center;
			margin-top: 150px;
			img{
				width: 266px;
				height: 158px;
			}
			color: #999999;
			div{
				margin-bottom:50px;
			}
		}
	}
</style>
