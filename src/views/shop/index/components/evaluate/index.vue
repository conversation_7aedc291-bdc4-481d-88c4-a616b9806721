<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-05-27 15:41:38
 * @LastEditors: 孙立政
 * @LastEditTime: 2021-06-18 10:00:06
-->
<template>
  <div class="home">
    <Top />
    <Card />
  </div>
</template>

<script>
import Top from './components/top.vue'
import Card from './components/card.vue'
export default {
  components: {
    Top,
    Card
  },
  data() {
    return {
      tabIndex: true
    }
  },
  created() {
  }
}
</script>

<style lang="scss" scoped>
</style>
