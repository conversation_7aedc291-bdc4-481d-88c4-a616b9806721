<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-04-30 14:58:18
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-03-23 14:07:54
-->
<template>
  <div class="home" :style="styleVar">
    <div class="back">
      <van-icon
        style="margin-left: 16px;"
        name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/shop/left.png"
        size="27px"
        class="left"
        @click="goBack"
      />
      <div style="margin-top: -5px;">
        <van-icon
          v-if="true"
          class="backimg"
          name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/shop/share.png"
          size="27px"
          @click="goShear"
        />
        <van-icon
          v-if="shopData.isCollection === 1"
          class="backimg"
          name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/shop/favorite-j-i.png"
          size="27px"
          @click="goShear"
        />
        <van-icon
          v-if="shopData.isCollection === 0"
          class="backimg"
          name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/shop/favorite-j.png"
          size="27px"
          @click="goShear"
        />
      </div>

    </div>
    <img class="banner" src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/shop/shopbj.png">
    <div class="img" />

    <!-- 分享弹出 -->
    <van-popup v-model="onShare" position="bottom" :style="{ height: '30%' }">
      <div class="shareList">
        <div class="shareListIcon">
          <div class="shareListIconLetf" @click="getShare('wechat')">
            <div>
              <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/share/weixin.png" alt="">
            </div>
            <div>微信</div>
          </div>
          <div class="shareListIconRight" @click="getShare('wechattimeline')">
            <div>
              <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/share/pyq.png" alt="">
            </div>
            <div>朋友圈</div>
          </div>
        </div>
        <div class="close" @click="onShare = false">取消</div>
      </div>
    </van-popup>
  </div>
</template>

<script>
export default {
  name: 'Home',
  components: {},
  props: {
    shopData: {
      type: Object,
      default: function() {
        return {}
      }
    }
  },
  data() {
    return {
      onShare: false,
      shareForm: {
        sharePlatform: '',
        title: '点滴',
        content: '点滴畅享生活',
        redirectUrl: '',
        contentType: 'url',
        url: '',
        imgUrl: ''
      }
    }
  },
  computed: {
    styleVar() {
      return {
        '---nav-height': this.$store.getters.getStatusHeight + 'px'
      }
    }
  },
  mounted() {

  },
  methods: {
    goShear() {
      this.onShare = true
    },
    // 分享
    getShare(val) {
      this.shareForm.sharePlatform = val
      let url = `https://share.zjntwl.com/#/shop?id=${this.$route.query.id}&from=share`
      this.shareForm.url = url
      this.shareForm.title = this.shopData.marketName
      this.shareForm.imgUrl = this.shopData.pic

      // AlipayJSBridge.call('Share', this.shareForm, function(result) {
      //   console.log(result)
      // })

      this.$throttle(() => {
        AlipayJSBridge.call('Share', this.shareForm, function(result) {
          console.log(result)
        })
      }, 3000)
    },
    goBack() {
      let data = this.$route.query.str
      if (data == 'index') {
        this.$router.push('/')
      } else {
        this.$router.go(-1)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
	.home{
		width: 100%;
		height: calc(192px + var(---nav-height));
		.back{
			width: 100%;
			position: absolute;
			z-index: 9;
      padding-top: calc(var(---nav-height));
      display: flex;
      justify-content: space-between;
      align-items: center;
      overflow: hidden;
			.backimg{
				margin-right: 30px;
			}
		}
		.banner{
			width: 100%;
      height: 346px;
		}
		.img{
			width: 100%;
			height: 346px;
			position: absolute;
			top: 0;
		}
    .shareList{
      width: 90%;
      height: 92%;
      background-color: #fff;
      margin: 0 auto;
      border-radius: 15px;
      position: relative;
      overflow: hidden;
      .shareListIcon{
        display: flex;
        font-size: 28px;
        text-align: center;
        width: 90%;
        margin: 0 auto;
        margin-top: 10%;
        img{
          width: 100px;
          height: 100px;
          margin-bottom: 10px;
        }
        .shareListIconLetf{
          width: 50%;
        }
        .shareListIconRight{
          width: 50%;
        }
      }
      .close{
        width: 100%;
        height: 150px;
        text-align: center;
        line-height: 150px;
        font-size: 35px;
        font-weight: PingFangSC-Medium;
        position: absolute;
        bottom: 0;
      }
    }
	}
</style>
