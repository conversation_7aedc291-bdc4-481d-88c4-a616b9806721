<template>
  <div class="home">
    <div class="shop_data">
      <div class="left">
        <div class="shop_title">
          {{ shopData.marketName | ellipsis(20) }}
        </div>
        <div v-if="shopData.tagTitleList.length>0" class="shop_tag">
          <div v-for="(item,index) in shopData.tagTitleList.slice(0,4)" :key="index">
            {{ item }}
          </div>
        </div>
        <div v-if="false" class="shop_time">
          <span>营业时间：</span>
          <span v-for="(item,index) in shopData.marketConfig.marketBusTimes" :key="index">{{ item.openingHours }}-{{ item.closingHours }}</span>
        </div>

        <!-- 优惠券 -->
        <Coupons />
        <div v-if="false" class="shop_address">
          <span v-if="!isAmap" @click="goMarket(shopData)">{{ shopData.address }}</span>
          <span v-else v-clipboard:copy="shopData.address" v-clipboard:success="onCopy">{{ shopData.address }}</span>
          <van-icon v-if="!isAmap" class="rightIcon" name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/shop/data/address.png" @click="goMarket(shopData)" />
          <van-icon v-else v-clipboard:copy="shopData.address" v-clipboard:success="onCopy" class="rightIcon" name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/shop/data/address.png" />
        </div>
        <div class="shop_detile">
          <div class="tag">
            公告
          </div>
          <div v-if="shopData.announcement!=null&&shopData.announcement!=''" class="msg" @click="lookAnnouncement">{{ shopData.announcement | ellipsis(30) }}</div>
          <div v-else class="msg">欢迎光临，很高兴为您服务~</div>
        </div>
      </div>
      <div class="right">
        <div class="shop_img">
          <van-image radius="6" class="img" :src="shopData.pic+'?x-oss-process=image/resize,w_700/format,jpg/quality,q_85'" />
        </div>
        <div v-if="false" class="division" />
        <div v-if="false" class="telPhone" @click="CallPhone(shopData.mobilePhone)">
          <div>
            <van-icon size="20" class="phone" name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/shop/data/phone.png" />
          </div>
          <div>
            电话
          </div>
        </div>
      </div>
    </div>
    <!-- 导航 -->
    <van-popup v-model="showPop" position="bottom" round>
      <div class="mapList">
        <div @click="openMap(1)">百度导航</div>
        <div @click="openMap(2)">高德导航</div>
        <div @click="showPop=false">取消</div>
      </div>
    </van-popup>
  </div>
</template>

<script>
import Coupons from './coupons.vue'
export default {
  components: { Coupons },
  props: {
    shopData: {
      type: Object,
      default: function() {
        return {}
      }
    }
  },
  data() {
    return {
      showPop: false,
      mapData: '',
      isAmap: false
    }
  },
  created() {
    // 判断是系统环境
    var u = navigator.userAgent
    var isiOS = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/) // ios终端
    if (isiOS) {
      this.isAmap = true
    }
  },
  methods: {
    // 拨打电话
    CallPhone(data) {
      AlipayJSBridge.call('CallPhone', {
        phoneNum: data
      }, function(result) {})
    },
    // 打开地图
    openMap(val) {
      let self = this
      if (val == 1) {
        var urlBaiduMap =
						`baidumap://map/marker?location=${this.mapData.bd_lat},${this.mapData.bd_lng}&title=${this.mapData.marketName}&content=${this.mapData.marketName}&src=Hello%20uni-app`
        AlipayJSBridge.call(
          'IsAvailable', {
            packageName: 'com.baidu.BaiduMap'
          },
          function(result) {
            if (result.available == true) {
              window.location.href = urlBaiduMap
            } else {
              self.$toast('未安装百度地图')
            }
          }
        )
      } else {
        var urlAmap =
						`androidamap://viewMap?sourceApplication=${this.mapData.marketName}&poiname=${this.mapData.marketName}&lat=${this.mapData.gd_lat}&lon=${this.mapData.gd_lng}&dev=0`

        var iosAmap =
						`iosamap://viewMap?sourceApplication=${this.mapData.marketName}&poiname=${this.mapData.marketName}&lat=${this.mapData.gd_lat}&lon=${this.mapData.gd_lng}&dev=0`
        var u = navigator.userAgent
        var isiOS = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/) // ios终端
        if (!isiOS) {
          AlipayJSBridge.call(
            'IsAvailable', {
              packageName: 'com.autonavi.minimap'
            },
            function(result) {
              if (result.available == true) {
                window.location.href = urlAmap
              } else {
                self.$toast('未安装高德地图')
              }
            }
          )
        } else {
          window.location.href = iosAmap
        }
      }
    },
    onCopy(e) {
      this.$toast({
        duration: 5000, // 持续展示 toast
        forbidClick: false,
        message: '复制成功,请打开地图应用,粘贴店铺地址进行导航'
      })
    },
    // 导航店铺
    goMarket(data) {
      // 高德转百度坐标
      function bd_encrypt(gg_lng, gg_lat) {
        var X_PI = Math.PI * 3000.0 / 180.0
        var x = gg_lng
        var y = gg_lat
        var z = Math.sqrt(x * x + y * y) + 0.00002 * Math.sin(y * X_PI)
        var theta = Math.atan2(y, x) + 0.000003 * Math.cos(x * X_PI)
        var bd_lng = z * Math.cos(theta) + 0.0065
        var bd_lat = z * Math.sin(theta) + 0.006
        return {
          bd_lat: bd_lat,
          bd_lng: bd_lng
        }
      }
      let zuobiao = bd_encrypt(data.longitude, data.latitude)

      this.mapData = {
        bd_lng: zuobiao.bd_lng,
        bd_lat: zuobiao.bd_lat,
        gd_lng: data.longitude,
        gd_lat: data.latitude,
        marketName: data.marketName
      }
      this.showPop = true
    },
    lookAnnouncement() {
      this.$dialog.alert({
        message: this.shopData.announcement,
        confirmButtonColor: '#39cf3f'
      }).then(() => {
        // on close
      })
    }
  }
}
</script>

<style lang="scss" scoped>
	.home {
		.mapList {
			div {
				height: 150px;
				line-height: 150px;
				text-align: center;
        font-size: 28px;
			}

			div:not(:last-child) {
				border-bottom: 1px solid #EEEEEE;
				font-weight: bold;
			}
		}

		.shop_data {
			width: 100%;
			height: auto;
			margin: 0 auto;
			background-color: #FFFFFF;
			border-top-left-radius: 23px;
			border-top-right-radius: 23px;
			margin-top: -55px;
			position: relative;
			z-index: 2;
			display: flex;
			.left{
				width: 650px;
			}
			.right{
				width: 100px;
			}
		}

		.shop_img {
			width: 128px;
			height: 126px;
			float: right;
			margin-top: -20px;
			margin-right: 30px;
			position: relative;
			z-index: 3;
			border-radius: 30px;
			.img {
				width: 128px;
				height: 126px;
				border-radius: 30px;
			}
		}
		.division{
			width: 50%;
			height: 55px;
			margin-top: 165px;
			border-left: 2px solid #f4f4f4;
		}
		.telPhone{
			width: 50px;
			text-align: center;
			font-size: 22px;
			color: #999999;
			margin: 0 auto;
			margin-top: -30px;
      font-family: PingFangSC;
		}

		.shop_title {
			font-size: 41px;
			margin-top: 20px;
			margin-left: 30px;
			font-weight: 600;
			color: #222;
      font-family: PingFangSC-Medium;
      line-height: 50px;
		}

		.shop_tag {
			margin-top: 13px;
			margin-left: 32px;
			display: flex;
			div{
				min-width: 75px;
				height: 38px;
				text-align: center;
				line-height: 38px;
				color: #FF4534;
				background-color: #FFF0EF;
				border-radius: 8px;
				font-size: 25px;
				font-weight: 400;
				margin-right: 10px;
			}
		}

		.shop_time {
			font-size: 25px;
			margin-top: 13px;
			margin-left: 32px;
			font-weight: 400;
			color: #333333;
		}

		.shop_detile {
      max-height: 50px;
			margin-top: 20px;
			margin-left: 33px;
			display: flex;
      margin-bottom: 20px;
			.tag{
				width: 66px;
				height: 34px;
				text-align: center;
				line-height: 34px;
				font-size: 25px;
				color: #FFFFFF;
				background: linear-gradient(270deg,#ff7452, #ff2a2a);
				border-radius: 8px;
			}
			.msg{
				width: 100%;
				height: 33px;
				line-height: 33px;
				font-size: 25px;
				color: #999999;
				margin-left: 10px;
			}
		}

		.shop_address {
      max-width: 350px;
			margin-top: 13px;
			margin-bottom: 13px;
			margin-left: 31px;
			color: #b49f61;
      min-height: 35px;
      font-size: 24px;
      border: 1px solid #baa66e;
      border-radius: 9px;
      padding-left: 5px;
			span{
				margin-left:12px;
			}
			.rightIcon {
				font-size: 0.16;
				margin-left: 8px;
				position: relative;
				top: 4px;
				margin-right: 10px;
			}
		}
	}
</style>
