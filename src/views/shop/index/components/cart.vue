<template>
  <div class="home">
    <div ref="circleBox" class="cart-box" :class="shopData.isTakeaway == true && shopData.status == true&&show == false&&couponList.length>0?'cart-box_h':''" :style="styleVar">
      <div v-if="shopData.isTakeaway == true && shopData.status == true&&show == false&&couponList.length>0" class="couponTipsShow">
        <span style="margin-right:8px">用券可享</span> <span v-for="(item,index) in couponList" :key="index" class="red">满{{ item.useThreshold }}减{{ item.preferentialAmount }}</span></div>

      <div
        v-if="shopData.isTakeaway == true && shopData.status == true"
        class="cart"
      >
        <div class="cart-left">
          <div class="cart_img" @click="showTrue">
            <van-icon
              v-if="this.$store.state.cart.cartData[0].goodsList.length == 0"
              class="cartIcon"
              size="28"
              name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/user/takeout/graycart.png"
            />
            <van-icon
              v-else
              class="cartIcon"
              size="28"
              :badge="this.$store.state.cart.cartData[0].goodsList.length"
              name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/takeout/carticon.png"
            />
          </div>
          <div class="cart_price" @click="showTrue">
            <div class="top">
              <div>￥</div>
              <div>{{ cartPrice }}</div>
            </div>
            <div class="bottom">
              <div>另需配送费</div>
              <div>￥</div>
              <div>{{ shopData.userPostFee }}</div>
            </div>
          </div>
        </div>

        <div
          v-if="
            cartPrice < shopData.deliverLimitPrice &&
              this.$store.getters['cart/cartStatus'] == 1
          "
          class="cart_price_btn_dubale"
          @click="mustMenu"
        >
          <div class="deliverLimitPrice">
            {{ shopData.deliverLimitPrice }}元起送
          </div>
          <div>未点必需品</div>
        </div>
        <div
          v-else-if="cartPrice < shopData.deliverLimitPrice"
          class="cart_price_btn"
          @click="test"
        >
          <span>￥ {{ shopData.deliverLimitPrice }} 起送</span>
        </div>
        <div
          v-else-if="this.$store.getters['cart/cartStatus'] == 1"
          class="cart_price_btn"
          @click="mustMenu"
        >
          <span>未点必需品</span>
        </div>
        <div
          v-else-if="this.$store.getters['cart/cartStatus'] == 0"
          class="cart_price_btn"
        >
          <span>单点不送</span>
        </div>
        <div v-else class="cart_btn" @click="goTakeOrder">去结算</div>
      </div>

      <div v-else class="isTakeaways">
        <span
          v-if="shopData.isTakeaway == false && shopData.status != true"
        >店铺已打烊</span>
        <span
          v-if="shopData.isTakeaway == true && shopData.status != true"
        >店铺休息中</span>
        <span
          v-if="shopData.isTakeaway == false && shopData.status == true"
        >请到线下店铺下单</span>
        <span
          v-if="shopData.isTakeaway == true && shopData.status == true"
        >营业中</span>
      </div>
    </div>

    <!-- 购物车弹出 -->
    <van-popup v-model="show" position="bottom" round z-index="999">
      <div v-if="couponList.length>0" class="couponTips"> <span style="margin-right:8px">用券可享</span><span v-for="(item,index) in couponList" :key="index" class="red">满{{ item.useThreshold }}减{{ item.preferentialAmount }}</span></div>

      <div class="popup">
        <div class="popup_top">
          <div>已选商品</div>
          <div style="color:#666" @click="clearNull">
            <van-icon
              class="del"
              name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/takeout/del.png"
            />
            清空购物车
          </div>
        </div>
        <div class="popup_body">
          <ul class="goodList">
            <li v-for="(item, index) in cartlist" :key="index" ref="rItem">
              <div class="goods_list">
                <!-- <div> -->
                <img class="goods_img" :src="item.cover">
                <!-- </div> -->
                <div>
                  <div class="goods_title">
                    {{ item.goodsName | ellipsis(20) }}
                  </div>
                  <div class="goods_detile">
                    {{ item.difference }}
                  </div>
                  <div class="goods_price">
                    <div class="price">
                      <div>￥</div>
                      <div>{{ item.price.price }}</div>
                      <div v-if="item.price != item.price.oriPrice">
                        ￥{{ item.price.oriPrice }}
                      </div>
                    </div>
                    <!-- 加减 -->
                    <div class="tag2">
                      <div v-if="badgeNum(item) != 0" @click="reduce(item)">
                        <img
                          src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/shop/jian.png"
                        >
                      </div>
                      <div
                        v-if="badgeNum(item) != 0"
                        class="inputNum"
                        style="color: #000000"
                      >
                        {{ badgeNum(item) }}
                      </div>
                      <div @click="selectOnetag(item)">
                        <img
                          src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/shop/jia.png"
                        >
                      </div>
                      <div
                        v-if="badgeNum(item) != 0 && item.leastCopies > 1"
                        class="prop_leastCopies_box"
                      >
                        <div class="prop_leastCopies">
                          {{ item.leastCopies }}份起购
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </li>
          </ul>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script>
import NP from 'number-precision'
export default {
  name: 'Home',
  props: {
    shopData: {
      type: Object,
      default: function() {
        return {}
      }
    },
    couponList: {
      type: Array,
      default: function() {
        return []
      }
    }
  },
  data() {
    return {
      show: false
    }
  },
  computed: {
    styleVar() {
      return {
        '---nav-height': this.$store.getters.getNavigationBarHeight + 'px'
      }
    },
    cartlist() {
      let cart = this.$store.state.cart.cartData[0].goodsList
      let newcart = []
      const res = new Map()
      newcart = cart.filter((a) => !res.has(a.skuId) && res.set(a.skuId, 1))
      return newcart
    },
    cartPrice() {
      return NP.strip(this.$store.getters['cart/sumPrice'])
    }
  },
  mounted() {
    const $circle = this.$refs.circleBox
    this.$store.state.market.cartLat = this.getParentTop($circle)
  },
  methods: {
    test() {
      console.log(this.$store.state.cart.cartData[0].goodsList)
    },
    // 去结算
    goTakeOrder() {
      console.log(this.$store.getters)

      if (this.$store.state.cart.cartData[0].goodsList.length == 0) {
        this.$toast('请选择商品')
        return
      }
      this.$store.state.market.marketData.remark = ''
      this.$router.push({
        path: '/submitOrder'
      })
    },
    showTrue() {
      if (this.$store.state.cart.cartData[0].goodsList.length == 0) {
        this.$toast('请选择商品')
        return
      }
      this.show = true
    },
    // 检查商品选中数量
    badgeNum(item) {
      let cart = this.$store.state.cart.cartData[0].goodsList
      let n = 0
      if (cart) {
        for (let i in cart) {
          if (cart[i].skuId == item.skuId) {
            n++
          }
        }
      }
      return n
    },
    // 减购物车
    reduce(item) {
      let skuid = item.skuId
      let leastCopies = item.leastCopies
      let cart = this.$store.state.cart.cartData[0].goodsList // 获取加入购物车
      let bcart = this.cartlist
      for (let i = 0; i < cart.length; i++) {
        if (skuid == cart[i].skuId) {
          if (leastCopies == this.badgeNum({ skuId: skuid })) {
            cart.splice(i, this.badgeNum({ skuId: skuid }))
            break
          } else {
            cart.splice(i, 1)
            break
          }
        }
      }
      for (let i = 0; i < bcart.length; i++) {
        if (skuid == bcart[i].skuId) {
          if (leastCopies == this.badgeNum({ skuId: skuid })) {
            bcart.splice(i, this.badgeNum({ skuId: skuid }))
            break
          } else {
            bcart.splice(i, 1)
            break
          }
        }
      }

      if (cart.length == 0) {
        this.show = false
      }
    },
    // 加购物车
    selectOnetag(item) {
      let cart = this.$store.state.cart.cartData[0].goodsList // 获取加入购物车
      cart.push(item)
    },
    // 清空购物车
    clearNull() {
      this.$store.state.cart.cartData[0].goodsList = []
      this.show = false
    },
    /**
     * 获取顶部div的距离
     */
    getParentTop(e) {
      var offset = e.offsetTop
      if (e.offsetParent != null) {
        offset += this.getParentTop(e.offsetParent)
      }
      return offset
    },
    /**
     * 获取左侧div的距离
     */
    getParentLeft(e) {
      var offset = e.offsetLeft
      if (e.offsetParent != null) {
        offset += this.getParentLeft(e.offsetParent)
      }
      return offset
    },
    mustMenu() {
      this.$emit('setScll')
    }
  }
}
</script>

<style lang="scss" scoped>
.home {
  width: 100%;
  height: 108px;
  margin: 0 auto;
  .couponTips {
      width: 750px;
      height: 60px;
      line-height: 60px;
      background: #fff3f1;
      border-radius: 24px 24px 0px 0px;
      font-family: PingFangSC;
      font-size: 24px;
      padding-left: 40px;
      padding-right: 40px;
      overflow: hidden;
      text-overflow:ellipsis;
      white-space: nowrap;
      margin-right: 5px;

    }
    .couponTipsShow{
      width: 750px;
      height: 60px;
      line-height: 60px;
      background: #fff3f1;
      border-radius: 10px 10px 0px 0px;
      font-family: PingFangSC;
      font-size: 24px;
      padding-left: 40px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;

    }
  .isTakeaways {
    width: 100%;
    height: 100px;
    position: fixed;
    bottom: 0;
    z-index: 2004;
    background-color: #000000;
    color: #dbdbdb;
    line-height: 100px;
    text-align: center;
    font-size: 28px;
  }

  .cart-box {
    width: 100%;
    height: calc(150px + var(---nav-height));
    background-color: rgba(255, 255, 255, 0.8);
    position: fixed;
    bottom: 0;
    z-index: 2003;
    .cart {
      width: 676px;
      border-radius: 54px;
      position: fixed;
      left: 0;
      right: 0;
      margin: 0 auto;
      bottom: calc(26px + var(---nav-height));
      display: flex;
      .cart-left {
        display: flex;
        width: 476px;
        background-color: #000000;
        border-radius: 54px 0 0 54px;
        .cart_img {
          width: 118px;
          height: 108px;
          line-height: 108px;
          .cartIcon {
            margin-top: 28px;
            margin-left: 42px;
          }
        }
        .cart_price {
          width: 406px;
          color: #ffffff;
          display: flex;
          flex-direction: column;
          justify-content: center;
          margin-left: 26px;
          .top {
            display: flex;
            align-items: center;
            div:nth-child(1) {
              font-size: 38px;
            }
            div:nth-child(2) {
              font-size: 38px;
              margin-left: 2px;
            }
          }
          .bottom {
            display: flex;
            align-items: center;
            color: #a7a3a1;

            div:nth-child(1) {
              font-size: 20px;
            }

            div:nth-child(2) {
              font-size: 16px;
              margin-left: 12px;
            }

            div:nth-child(3) {
              font-size: 20px;
              margin-left: 5px;
              position: relative;
              top: 1px;
            }
            div:nth-child(4) {
              font-size: 20px;
              margin-left: 10px;
              text-decoration: line-through;
            }
          }
        }
      }
      .cart_btn {
        width: 192px;
        height: 108px;
        line-height: 108px;
        text-align: center;
        color: #ffffff;
        background: linear-gradient(90deg, #ff1e29, #ff5a25);
        border-top-right-radius: 54px;
        border-bottom-right-radius: 54px;
        font-size: 32px;
      }
      .cart_price_btn {
        width: 192px;
        height: 108px;
        line-height: 108px;
        text-align: center;
        color: #ffffff;
        background-color: #000000;
        border-top-right-radius: 54px;
        border-bottom-right-radius: 54px;
        margin-left: -1px;
        font-size: 30px;
        span {
          position: relative;
          left: -0.3px;
        }
      }
      .cart_price_btn_dubale {
        width: 202px;
        height: 108px;
        text-align: center;
        color: #ffffff;
        background-color: #000000;
        border-top-right-radius: 54px;
        border-bottom-right-radius: 54px;
        font-size: 30px;
        .deliverLimitPrice {
          margin-top: 10px;
        }
        div {
          margin-right: 10px;
        }
      }
    }
  }
  .cart-box_h{
    height: calc(200px + var(---nav-height));
  }
  .red {
      color: #ff7063;
      margin-right: 6px;
      margin-left: 5px;
  }
  .popup {
    width: 670px;
    max-height: 822px;
    min-height: 522px;
    margin: 0 auto;
    position: relative;
    overflow: hidden;

    .popup_top {
      position: absolute;
      top: 0;
      z-index: 9;
      width: 100%;
      height: 95px;
      line-height: 95px;
      display: flex;
      justify-content: space-between;
      border-bottom: 1px solid #f4f4f4;
      box-sizing: border-box;
      background-color: #fff;
      div:nth-child(1) {
        font-size: 36px;
      }

      div:nth-child(2) {
        font-size: 27px;
      }

      .del {
        position: relative;
        top: 3px;
        left: -6px;
      }
    }

    .popup_body {
      width: 100%;
      max-height: 600px;

      overflow-y: auto;
      margin-top: 88px;
      margin-bottom: 200px;
    }

    .goods_list {
      width: 100%;
      min-height: 135px;
      margin: 40px auto 0;
      display: flex;
      position: relative;
      border-bottom: 1px solid #f4f4f4;
      .goods_img {
        width: 110px;
        height: 110px;
        border-radius: 8px;
        overflow: hidden;
        margin-right: 16px;
      }

      .goods_title {
        width: 366px;
        font-size: 30px;
        margin-top: -3px;
        font-family: PingFangSC-Medium;
        font-weight: 500;
        color: #333333;
      }

      .goods_detile {
        width: 366px;
        font-size: 20px;
        word-wrap: break-word;
        word-break: normal;
        color: #454545;
        font-family: PingFangSC;
      }

      .goods_price {
        width: 540px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 9px;
        .price {
          display: flex;
          align-items: center;
          div:nth-child(1) {
            font-size: 22px;
            color: #fb4c58;
          }

          div:nth-child(2) {
            font-size: 28px;
            color: #fb4c58;
          }

          div:nth-child(3) {
            font-size: 24px;
            color: #b6b4b4;
            margin-left: 14px;
            text-decoration: line-through;
          }
        }
        .tag2 {
          display: flex;
          align-items: center;
          position: relative;
          img {
            width: 38px;
            height: 38px;
          }
          > div {
            display: flex;
            align-items: center;
            height: 38px;
          }
          .inputNum {
            margin-left: 18px;
            margin-right: 18px;
            font-size: 30px;
          }
          .prop_leastCopies_box {
            width: 100%;
            position: absolute;
            top: -50px;
            .prop_leastCopies {
              margin: 0 auto;
              font-size: 20px;
              width: 110px;
              height: 30px;
              text-align: center;
              line-height: 30px;
              border: 1px dashed #169d1b;
              border-radius: 15px;
              color: #169d1b;
            }
          }
        }
      }
    }
    .goods_list:last-child {
      margin-bottom: 40px;
    }
  }
}
</style>
