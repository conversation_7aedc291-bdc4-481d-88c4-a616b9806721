<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-04-30 14:58:18
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-08-10 16:17:28
-->
<template>
  <div class="home">
    <!-- 头部 -->
    <Top :shop-data="shopData" />
    <!-- 店铺信息 -->
    <ShopData :shop-data="shopData" />
    <!-- 商品列表 -->
    <GoodsList ref="child" :shop-data="shopData" @resGetShopDatat="getShopData" />

    <!-- 购物车 -->
    <div v-show="$store.state.market.cartShow">
      <Cart v-if="titleName!='菜市场'&&titleName!='酒店'" :shop-data="shopData" :coupon-list="couponList" @setScll="setScll" />
      <div v-else class="isTakeaways">
        请到线下店铺下单
      </div>
    </div>
  </div>
</template>

<script>
import Top from './components/top.vue'
import ShopData from './components/shopdata.vue'
import GoodsList from './components/goodslist.vue'
import Cart from './components/cart.vue'
import { getShopDataV2 } from '@/api/takeout'
import {
  addShopData
} from '@/utils/upLog.js'

import { marketCoupon } from '@/api/coupon'

export default {
  components: {
    Top,
    ShopData,
    GoodsList,
    Cart
  },
  data() {
    return {
      shopData: {
        marketName: '',
        tagTitleList: [],
        announcement: '',
        openingHours: '',
        closingHours: '',
        address: '',
        isTakeaway: true,
        marketConfig: {
          marketBusTimes: []
        },
        pic: ''
      },
      titleName: '',
      couponList: []
    }
  },
  beforeRouteLeave(to, from, next) {
    this.$store.state.market.cartShow = true
    next()
  },
  created() {
    this.getShopData()
    if ('scrollRestoration' in history) {
      history.scrollRestoration = 'manual'
    }
  },
  mounted() {
    let self = this
    this.titleName = this.$route.query.titleName
    document.documentElement.scrollTop = 0
    document.body.scrollTop = 0
    window.addEventListener('scroll', this.handleScrollx, true)

    AlipayJSBridge.call('LocationMsg', {}, function(result) {
      console.log('------定位--------')
      console.log(result)
      console.log('------定位end--------')

      let locationdata = JSON.parse(result.locationMsg)
      self.$store.state.location.latitude = locationdata.latitude
      self.$store.state.location.longitude = locationdata.longitude
      self.$store.state.location.address = locationdata.address
    })

    AlipayJSBridge.call('ClearGetUrl', {}, function(result) {
      console.log(result)
    })
  },
  methods: {
    // 获取店铺详情
    getShopData() {
      let data = {
        marketId: this.$route.query.id,
        longitude: this.$store.getters.getLocation.longitude,
        latitude: this.$store.getters.getLocation.latitude,
        userId: this.$store.getters.getUserId
      }
      getShopDataV2(data).then((res) => {
        this.shopData = res.data
        this.$store.state.market.marketData.marketId = this.$route.query.id
        this.$store.state.market.marketData.type = res.data.type
        this.$store.state.market.marketData.marketSn = res.data.marketSn
        this.$store.state.market.marketData.marketName = res.data.marketName
        this.$store.state.market.marketData.agentPostFee = res.data.marketConfig.agentPostFee
        this.$store.state.market.marketData.pic = res.data.pic
        this.$store.state.market.marketData.address = res.data.address
        this.$store.state.market.marketData.distance = res.data.distance
        this.$store.state.market.marketData.isSelfMention = res.data.marketConfig.isSelfMention

        this.$store.state.market.marketData.map.latitude = res.data.latitude
        this.$store.state.market.marketData.map.longitude = res.data.longitude
        this.$store.state.market.marketData.map.marketName = res.data.marketName

        this.marketCoupon()
        // 记录进入店铺轨迹
        addShopData(this.$route.query.id, res.data.marketName, res.data.tagTitleList, Date.parse(new Date()))
      })
    },
    marketCoupon() {
      marketCoupon(this.$route.query.id, 7).then((res) => {
        if (res.status == 200) {
          this.couponList = res.data
        }
      })
    },
    setScll() {
      this.$refs.child.goScll(this.$store.state.market.marketData.required.tagIndex)
    }
  }
}
</script>

<style lang="scss" scoped>
	.home {
		// background-color: #fff;
		.isTakeaways {
			width: 100%;
			height: 100px;
			position: fixed;
			bottom: 0;
			background-color: #000000;
			color: #dbdbdb;
			line-height: 100px;
			text-align: center;
			font-size: 28px;
		}
	}
</style>
