<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2021-06-17 10:28:17
 * @LastEditors: 孙立政
 * @LastEditTime: 2021-07-07 15:11:45
-->
<template>
  <div class="home">
    <div class="back">
      <van-icon
        name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/common/arrowlbft.png"
        size="19"
        @click="goBack"
      />
    </div>
    <div class="top">
      <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/shoppingMall/actieve/top.png" alt="">
    </div>

    <div class="body">
      <div class="coupon">
        <div
          class="couponOne"
          :style="'background-image: url('+ifImg1(couponList[6])+')'"
          @click="getCoupon(couponList[6])"
        />
        <div class="couponTwo">
          <div
            class="couponTwoBox"
            :style="'background-image: url('+ifImg2(couponList[2])+')'"
            @click="getCoupon(couponList[2])"
          />
          <div
            class="couponTwoBox"
            :style="'background-image: url('+ifImg3(couponList[5])+')'"
            @click="getCoupon(couponList[5])"
          />
        </div>
      </div>
      <div class="center">
        <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/shoppingMall/actieve/center.png" alt="">
      </div>

      <div v-for="(item,index) in list" :key="index" class="goodsCard">
        <div class="left" @click="goCard(item)">
          <img :src="item.cover + '?x-oss-process=image/resize,w_700/format,jpg/quality,q_80'" alt="">
        </div>
        <div class="right" @click="goCard(item)">
          <div class="title">{{ item.goodsName }}</div>
          <div class="brief">{{ item.description }}</div>
          <div class="price">
            <span class="price_1">￥</span>
            <span class="price_2">{{ item.showPrice }}</span>
            <span class="price_3">原价：￥{{ item.oriPrice }}</span>
          </div>
          <div>
            <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/shoppingMall/actieve/btn.png" alt="">
          </div>
        </div>
      </div>
    </div>
    <Loading :show="loadingShow" />
  </div>
</template>

<script>
import {
  getList
} from '@/api/takeout'
import Loading from '@/components/Loading/index'
import { marketCouList, receiveCou } from '@/api/shoppingMall'
export default {
  components: {
    Loading
  },
  data() {
    return {
      list: [],
      couponList: [
        { receiveStatus: 0 },
        { receiveStatus: 0 },
        { receiveStatus: 0 },
        { receiveStatus: 0 },
        { receiveStatus: 0 },
        { receiveStatus: 0 },
        { receiveStatus: 0 }
      ],
      loadingShow: true
    }
  },
  created() {
    this.getMarketCouList()

    this.getList()
  },
  mounted() {

  },
  methods: {
    // 获取优惠券
    getMarketCouList() {
      let data = {
        userId: this.$store.getters.getUserId,
        marketId: this.$route.query.id
      }
      marketCouList(data).then((res) => {
        if (res.status == 200) {
          if (res.data != null) {
            this.couponList = res.data
          }
        }
      })
    },
    ifImg1(val) {
      if (val.receiveStatus != 1) {
        return 'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/index/actieve/coupon1.png'
      } else {
        return 'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/index/actieve/coupon1-1.png'
      }
    },
    ifImg2(val) {
      if (val.receiveStatus != 1) {
        return 'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/index/actieve/coupon2.png'
      } else {
        return 'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/index/actieve/coupon2-2.png'
      }
    },
    ifImg3(val) {
      if (val.receiveStatus != 1) {
        return 'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/index/actieve/coupon3.png'
      } else {
        return 'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/index/actieve/coupon3-3.png'
      }
    },
    // 获取商品
    getList() {
      getList(this.$route.query.id).then((res) => {
        this.loadingShow = false
        if (res.status == 200) {
          let data = []
          res.data.map(item => {
            data.push(...item.goodsList)
          })
          this.list = data
        }
      })
    },
    // 领取优惠券
    getCoupon(item) {
      if (item.receiveStatus == 0) {
        let data = {
          userId: this.$store.getters.getUserId,
          regId: item.regId,
          couponSn: item.couponSn
        }
        receiveCou(data).then((res) => {
          console.log(res)
          if (res.status == 200) {
            this.$toast('领取成功')
            this.getMarketCouList()
          } else {
            this.$toast(res.message)
          }
        })
      } else {
        this.$toast('请勿重复领取')
      }
    },
    goBack() {
      this.$router.push('/index')
    },
    goCard(data) {
      let cart = this.$store.state.cart.cartData[0].goodsList
      let cartList = {}
      cartList.skuId = data.skuList[0].skuId
      cartList.goodsId = data.goodsId
      cartList.attrs = data.attrs
      cartList.price = data.price
      cartList.agentId = data.agentId
      cartList.cover = data.skuList[0].pic
      cartList.difference = data.skuList[0].skuName
      cartList.stock = data.skuList[0].actualStocks
      cartList.goodsName = data.goodsName
      cartList.oriPrice = data.oriPrice
      cartList.packPrice = data.skuList[0].packPrice
      cart.push(cartList)
      this.$router.push(`/shoppingMall/index?id=${this.$route.query.id}&goodsId=` + data.goodsId)
    }
  }
}
</script>

<style scoped lang="scss">
    .home {
        .back{
          position: fixed;
          top: 50px;
          left: 25px;
        }
        .top{
            width: 100%;
            height: 854px;
            img{
                width: 100%;
                height: 854px;
            }
        }
        .body {
            width: 100%;
            background-color: #DC0D39;
            overflow: hidden;
            .coupon {
              width: 710px;
              margin: 0 auto;
              margin-top: 14px;

              img {
                width: 100%;
              }
              .couponOne {
                width: 710px;
                height: 124px;
                // background-image: url(https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/index/actieve/coupon1.png);
                background-size: 100% 100%;
                display: flex;
                .couponOneLeft {
                  width: 40%;
                  line-height: 124px;
                  font-family: PingFangSC-Medium;
                  color: #DC0D39;
                  text-align: center;
                  display: flex;
                  .couponOneIcon {
                    font-size: 48px;
                    margin-left: 51px;
                    margin-top: 10px;
                  }
                  .couponOneNum {
                    font-size: 80px;
                  }
                }
                .couponOneRight {
                  width: 60%;
                }
                .couponOnerightTitle {
                  font-family: PingFangSC-Medium;
                  font-size: 30px;
                  margin-top: 26px;
                  color: #222222;
                  text-align: center;
                }
                .couponOnerightTime {
                  font-family: PingFangSC;
                  font-size: 18px;
                  color: #222222;
                }
              }
              .couponTwo {
                display: flex;
                justify-content: space-between;
                margin-top: 10px;
                .couponTwoBox {
                  width: 350px;
                  height: 134px;
                  // background-image: url(https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/index/actieve/coupon2.png);
                  background-size: 100% 100%;
                  display: flex;
                }
                .couponOneLeft {
                  width: 47%;
                  line-height: 134px;
                  font-family: PingFangSC-Medium;
                  color: #DC0D39;
                  text-align: center;
                  display: flex;
                  .couponOneIcon {
                    font-size: 33px;
                    margin-left: 23px;
                    margin-top: 15px;
                  }
                  .couponOneNum{
                    font-size: 60px;
                    margin-top: 8px;
                  }
                }
                .couponOneRight{
                  width: 53%;
                }
                .couponOnerightTitle{
                  font-family: PingFangSC-Medium;
                  font-size: 30px;
                  margin: 0 auto;
                  margin-top: 13px;
                  color: #222222;
                  text-align: center;

                }
                .couponOnerightTime{
                  font-family: PingFangSC;
                  font-size: 22px;
                  color: #222222;
                  margin: 0 auto;
                  text-align: center;
                }
                .couponOnerightBtn{
                  width: 110px;
                  height: 34px;
                  text-align: center;
                  line-height: 34px;
                  font-size: 20px;
                  font-weight: 400;
                  color: #fff;
                  margin: 0 auto;
                  font-family: PingFangSC;
                  background-color: #F72F57;
                  border-radius: 21px;
                }
              }
            }
            .center{
                width: 540px;
                height: 88px;
                text-align: center;
                margin: 0 auto;
                img{
                    width: 100%;
                    height: 100%;
                }
                margin-bottom: 25px;
                margin-top: 32px;
            }
            .goodsCard{
                width: 710px;
                min-height: 332px;
                background-image: url(https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/shoppingMall/actieve/bi.png);
                background-size: 100% 100%;
                margin: 0 auto;
                display: flex;
                justify-content: space-between;
                margin-bottom: 10px;
                .left{
                    width: 50%;
                    img{
                        width: 334px;
                        height: 289px;
                        border-radius: 21px;
                        margin-top: 22px;
                        margin-left: 18px;
                    }
                }
                .right{
                    width: 50%;
                    div{
                        margin-left: 24px;
                    }
                    img{
                        width: 198px;
                        height: 52px;
                        margin-top: 15px;
                    }
                    .title{
                        font-size: 34px;
                        font-weight: 600;
                        color: #222222;
                        margin-top: 42px;
                    }
                    .brief{
                        font-size: 26px;
                        font-weight: 400;
                        color: #333333;
                        margin-top: 5px;
                    }
                    .price{
                        margin-top: 18px;
                        .price_1{
                            font-size: 24px;
                            color: #ff0000;
                            font-weight: 400;
                        }
                        .price_2{
                            font-size: 56px;
                            color: #ff0000;
                            font-weight: 700;
                        }
                        .price_3{
                            font-size: 24px;
                            color: #333333;
                            font-weight: 400;
                            text-decoration: line-through;
                            margin-left: 5px;
                        }
                    }
                }
            }
        }
    }
</style>
