<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-06-18 10:42:59
 * @LastEditors: 孙立政
 * @LastEditTime: 2021-12-30 17:10:45
-->
<template>
  <div class="home" :style="styleVar">
    <div class="back">
      <van-icon
        name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/common/arrowlbft.png"
        size="19"
        @click="goBack"
      />
    </div>
    <div class="topFixed" @click="goBack">
      <van-icon
        name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/common/arrowlwft.png"
        size="19"
      />
      <span style="margin-left:8px">助农集市</span>
    </div>
    <div class="shadow" />
  </div>
</template>

<script>
export default {
  name: 'Home',
  components: {},
  props: {
    shopData: {
      type: Object,
      default: function() {
        return {}
      }
    }
  },
  data() {
    return {
    }
  },
  computed: {
    styleVar() {
      return {
        '---nav-height': this.$store.getters.getStatusHeight + 'px'
      }
    }
  },
  mounted() {
    window.addEventListener('scroll', this.handleScrollx, true)
  },
  destroyed() {
    window.removeEventListener('scroll', this.handleScrollx, true)
  },
  methods: {
    handleScrollx() {
      let top = document.documentElement.scrollTop || document.body.scrollTop || window.pageYOffset
      const topFixed = document.querySelector('.topFixed')
      if (top <= 20 && top >= 0) {
        topFixed.style.setProperty('opacity', 0)
      } else {
        topFixed.style.setProperty('opacity', top / 100)
      }
    },
    goBack() {
      this.$router.go(-1)
      // this.$store.state.tabbar.index = 0
      // this.$router.push('/index')
    },
    remove() {
      window.removeEventListener('scroll', this.handleScrollx, true)
    }
  }
}
</script>

  <style lang="scss" scoped>
.home{
    width: 100%;
    height: calc(140px + var(---nav-height));
    .back{
        position: fixed;
        width: 100%;
        height: calc(76px + var(---nav-height));
        padding-top:calc(13px + var(---nav-height));
        padding-left: 33px;
        z-index: 9;
        .backimg{
            width: 31px;
            height: 30px;
        }
    }
    .topFixed {
        display: flex;
        align-items: center;
        position: fixed;
        height: calc(84px + var(---nav-height));
        width: 100%;
        z-index: 9;
        opacity: 0;
        padding-left: 33px;
        background-color: #fff;
        font-size: 34px;
    }
    .banner{
        width: 100%;
        height: 346px;
    }
    .shadow{
        width: 100%;
        height: 346px;
        background-image: url(https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/shoppingMall/index/shopmallbg.png);
        background-size: 100% 100%;
        position: absolute;
        top: 0;
    }
}
  </style>
