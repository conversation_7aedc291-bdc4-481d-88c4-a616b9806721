<template>
  <div class="home">
    <div class="shop-card">
      <div class="shop-card-header">
        <img :src="shopData.pic" alt="" class="shop-card-img">
        <div class="shop-card-header-right">
          <div>
            <div class="shop-card-title">{{ shopData.marketName | ellipsis(13) }}</div>
            <div class="shop-card-subtitle">每一样商品都是精心挑选，安全配送</div>
          </div>
          <div class="shop-card-phone" @click="CallPhone(shopData.mobilePhone)">
            <van-icon size="18" class="phone" name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/shop/data/phone.png" />
            <div>电话</div>
          </div>
        </div>
      </div>
      <div class="shop-card-desc">
        <div class="shop-card-rate">
          <van-icon size="14" name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/shoppingMall/index/rate.png" />
          <span style="margin-left:2px">5</span>
        </div>
        <div>最快30分钟配送 · 可自取</div>
      </div>
      <Coupons />
      <div class="shop-card-announce">{{ shopData.announcement }}</div>
    </div>
  </div>
</template>

<script>
import Coupons from './coupons.vue'
export default {
  components: { Coupons },
  props: {
    shopData: {
      type: Object,
      default: function() {
        return {

        }
      }
    }
  },
  data() {
    return {
    }
  },
  created() {
  },
  methods: {
    // 拨打电话
    CallPhone(data) {
      AlipayJSBridge.call('CallPhone', {
        phoneNum: data
      }, function(result) {})
    }
  }
}
</script>

<style lang="scss" scoped>
	.home {
    .shop-card {
		width: 100%;
		// height: 300px;
		margin: 0 auto;
		background-color: #FFFFFF;
		border-top-left-radius: 24px;
		border-top-right-radius: 24px;
		position: relative;
		z-index: 2;
		padding: 0 26px;
		.shop-card-header {
			width: 100%;
			display: flex;
			margin-bottom: 13px;
			.shop-card-img {
				width: 124px;
				height: 124px;
				margin-right: 16px;
				margin-top: -25px;
				border-radius: 17px;
				overflow: hidden;
			}
			.shop-card-header-right {
				width: 545px;
				display: flex;
				justify-content: space-between;
				// align-items: center;
				.shop-card-title {
					font-family:PingFangSC-Medium;
					font-weight: 500;
					color: #222222;
					font-size: 38px;
					margin-top: 16px;
				}
				.shop-card-subtitle {
					font-size: 22px;
					font-family: PingFangSC;
					color: #333333;
				}
				.shop-card-phone {
					font-size: 18px;
					font-family: PingFangSC;
					color: #999999;
					text-align: center;
					margin-top: 22px;
					transform: scale(.85);
				}
			}

		}
		.shop-card-desc {
			display: flex;
			align-items: center;
			font-size: 22px;
			font-family: PingFangSC;
			color: #666666;
			line-height: 30px;
			margin-bottom: 13px;
			.shop-card-rate {
				display: flex;
				align-items: center;
				color: #ff7807;
				font-size: 24px;
				font-family:PingFangSC-Medium;
				font-weight: 500;
				margin-right: 10px;
				margin-top: -1px;
			}
		}
		.shop-card-announce {
			height: 30px;
			font-size: 22px;
			font-family: PingFangSC;
			color: #999999;
			line-height: 30px;
		}
	}
	}
</style>
