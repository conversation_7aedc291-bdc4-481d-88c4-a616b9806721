<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-06-18 10:57:13
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-10-09 10:44:48
-->
<template>
  <!-- 优惠券 -->
  <div class="home">
    <div class="coupons-card">
      <ul class="card-first" @click="goCoupon">
        <li v-for="(item,index) in list" :key="index" class="card-first-coupon" :class="item.receiveStatus==1?'coupon-o':'coupon'">
          <span class="medium">{{ item.usedAmount }}元</span><span v-if="item.receiveStatus==1"> 已领取</span><span v-else> 领取</span>
        </li>
      </ul>
      <div class="card-second-wrap">
        <ul class="card-second" style="list.length>0?'border: 1px solid #ffd0d3;':''">
          <li v-for="(item,index) in list" :key="index" class="card-second-coupon">
            <span>满{{ item.withAmount }}减{{ item.usedAmount }}</span>
          </li>
          <!-- <div class="card-coupon-postfree">
            免配送费
          </div> -->
          <div class="card-coupon-postfree">
            不支持退款
          </div>
        </ul>
      </div>
    </div>
    <!-- 优惠券弹出 -->
    <van-popup v-model="show" round closeable close-icon-position="top-right" position="bottom" :style="{ height: '70%' }">
      <div class="pop-title">
        <span>优惠券</span>
        <!-- <van-icon
          name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/shoppingMall/index/icon4.png"
          size="10"
          style="top:-4px"
        /> -->
      </div>
      <div class="coupon-pop">
        <ul style="margin-top:59px">
          <div class="pop-packet">红包</div>
          <li v-for="(item,index) in list" :key="index" class="pop-card">
            <div class="pop-card-price">
              <span class="small">¥</span>
              <span>{{ item.usedAmount }}</span>
            </div>
            <div class="pop-card-shop">
              <span>{{ item.title }}</span>
              <div class="pop-card-deadline">满{{ item.withAmount }}减{{ item.usedAmount }} {{ item.validEndTime }}到期</div>
            </div>
            <div class="pop-card-btn " :class="item.receiveStatus==1?'already':'will'" @click="getReceiveCop(item)">
              <span v-if="item.receiveStatus==1">已领取</span>
              <span v-else>领取</span>
            </div>
            <div class="pop-card-tip">全品类券</div>
          </li>
        </ul>

        <ul class="pop-desc1">
          <div class="pop-packet">优惠</div>
          <li><span class="pop-desc1-tag">特价</span><span>特价商品9.9元起</span></li>
          <li><span class="pop-desc1-tag">配送</span><span>全场免配送费</span></li>
          <li><span class="pop-desc1-tag">满减</span><div>领券<span v-for="(item,index) in list" :key="index" style="margin-right:3px">
            满{{ item.withAmount }}减{{ item.usedAmount }}
          </span></div></li>
        </ul>

        <div class="pop-packet">商家服务</div>
        <ul class="pop-desc2">
          <li><span class="pop-desc2-tag tag1" /><span>限时特惠</span></li>
          <li><span class="pop-desc2-tag tag2" /><span>品质保障</span></li>
          <li><span class="pop-desc2-tag tag3" /><span>配送及时</span></li>
        </ul>
        <div class="pop-packet">公告</div>
        <div class="pop-desc3">点滴严选店铺，以严格把控每件有品质有价值的商品质量，经过层层筛 选精心包装，将优质的商品送达让您吃的放心用的安心
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script>
import { marketCouList, receiveCou } from '@/api/shoppingMall'
export default {
  data() {
    return {
      show: false,
      temp: [
        {
          flag: true,
          index: 0
        },
        {
          flag: false,
          index: 1
        },
        {
          flag: false,
          index: 2
        },
        {
          flag: false,
          index: 3
        }
      ],
      list: []
    }
  },
  created() {
    this.getMarketCouList()
  },
  mounted() {

  },
  methods: {
    goCoupon() {
      this.show = true
    },
    getMarketCouList() {
      let data = {
        userId: this.$store.getters.getUserId,
        marketId: this.$route.query.id
      }
      marketCouList(data).then((res) => {
        if (res.status == 200) {
          this.list = res.data
        }
      })
    },
    // 领取操作
    getReceiveCop(item) {
      if (item.receiveStatus == 0) {
        let data = {
          userId: this.$store.getters.getUserId,
          regId: item.regId,
          couponSn: item.couponSn
        }
        receiveCou(data).then((res) => {
          console.log(res)
          if (res.status == 200) {
            this.$toast('领取成功')
            this.getMarketCouList()
          } else {
            this.$toast(res.message)
          }
        })
      } else {
        this.$toast('请勿重复领取')
      }
    }
  }
}
</script>

<style scoped lang="scss">
    .home {
        .coupons-card {
            .card-first {
                display: flex;
                flex-wrap: wrap;
                .card-first-coupon {
                    flex-shrink: 0;
                    min-width: 136px;
                    max-width: 166px;
                    height: 36px;
                    font-size: 22px;
                    font-family: PingFangSC;
                    line-height: 36px;
                    text-align: center;
                    margin-right: 8px;
                    margin-bottom: 12px;
                }
                .coupon-o {
                    color: #ff4850;
                    background-image: url(https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/shoppingMall/index/coupon-o.png);
                    background-size: 100%;
                }
                .coupon {
                    color: #fff;
                    background-image: url(https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/shoppingMall/index/coupon.png);
                    background-size: 100%;
                }
            }
            .card-second-wrap {
                display: flex;
                margin-bottom: 12px;
                overflow: auto;
            }
            .card-second {
                display: flex;
                flex-wrap: wrap;
                font-size: 22px;
                font-family: PingFangSC;
                color: #ff6248;
                border-radius: 9px;
                margin-right: 8px;
                .card-second-coupon {
                    flex-shrink: 0;
                    padding: 0 8px;
                    margin: 3px 0;
                }
                .card-second-coupon:not(:first-child) {
                    border-left: 1px solid #ffd0d3;
                }
            }
            .card-coupon-postfree {
                padding: 0 11px;
                font-size: 22px;
                border: 1px solid #ecd099;
                border-radius: 9px;
                height: 40px;
                font-family: PingFangSC;
                text-align: center;
                color: #c08e29;
                line-height: 40px;
                margin-right: 20px;
                }
        }
        .medium {
            font-family:PingFangSC-Medium;
            font-weight: 500;
        }
        .small {
            font-size: 26px;
            font-family:PingFangSC-Medium;
        }
        .pop-title {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 117px;
            background-color: #f5f5f5;
            font-size: 34px;
            font-family:PingFangSC-Medium;
            font-weight: 500;
            color: #333333;
            line-height: 48px;
            text-align: center;
            padding-top: 40px;
            z-index: 1;
        }
        .coupon-pop {
            background-color: #f5f5f5;
            padding: 0 22px;
            height: 100%;
            overflow: scroll;
            .pop-packet {
                font-size: 34px;
                font-family:PingFangSC-Medium;
                font-weight: 500;
                color: #333333;
                margin-left: 4px;
                padding-bottom: 8px;
            }
            .pop-card {
                position: relative;
                display: flex;
                align-items: center;
                width: 706px;
                height: 154px;
                background-image: url(https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/shoppingMall/index/coupon-bg.png);
                background-size: 100%;
                padding-left: 24px;
                padding-right: 30px;
                padding-top: 14px;
                box-sizing: border-box;
                margin-bottom: 16px;
                .pop-card-price {
                    width: 135px;
                    font-size: 64px;
                    font-family:DINPro-Medium;
                    font-weight: 500;
                    text-align: justify;
                    color: #ff301e;
                }
                .pop-card-shop {
                    width: 360px;
                    font-size: 30px;
                    font-family:PingFangSC-Medium;
                    font-weight: 500;
                    color: #222222;
                    .pop-card-deadline{
                        font-size: 22px;
                        font-family: PingFangSC;
                        color: #999999;
                        margin-top: 8px;
                    }
                }
                .pop-card-btn {
                    width: 160px;
                    height: 64px;
                    line-height: 64px;
                    text-align: center;
                    color: #fff;
                    border-radius: 32px;
                    font-size: 28px;
                    font-family: PingFangSC-Medium;
                    font-weight: 500;
                }
                .pop-card-tip {
                    position: absolute;
                    left: 0;
                    top: 0;
                    width: 124px;
                    height: 36px;
                    background-image: url(https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/shoppingMall/index/tip3.png);
                    background-size: 100%;
                    font-size: 22px;
                    font-family:PingFangSC-Medium;
                    font-weight: 500;
                    color: #ffffff;
                    text-align: center;
                }
                .will {
                    background: linear-gradient(90deg,#ff1e29, #ff5a25);
                }
                .already {
                    background: #d8d8d8;
                }
            }
            .pop-card:last-child {
                margin-bottom: 34px;
            }
        }
        .pop-desc1 {
            font-size: 22px;
            font-family: PingFangSC;
            color: #333333;
            margin-bottom: 34px;
            li {
              display: flex;
                // margin-bottom: 20px;
            }
            .pop-desc1-tag {
                height: 34px;
                border: 1px solid #ffd0d3;
                border-radius: 9px;
                font-size: 22px;
                font-family: PingFangSC;
                text-align: center;
                color: #ff6248;
                padding: 2px 10px;
                margin-right: 6px;
            }
        }
        .pop-desc2 {
            display: flex;
            font-size: 22px;
            font-family: PingFangSC-Medium;
            color: #222;
            margin-bottom: 32px;
            li {
                display: flex;
                align-items: center;
                margin-right: 48px;
            }
            .pop-desc2-tag {
                display: inline-block;
                width: 32px;
                height: 32px;
                margin-right: 8px;
                background-size: 100%;
            }
            .tag1{
                background-image: url(https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/shoppingMall/index/icon1.png);
            }
            .tag2{
                background-image: url(https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/shoppingMall/index/icon2.png);
            }
            .tag3{
                background-image: url(https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/shoppingMall/index/icon3.png);
            }
        }
        .pop-desc3 {
            font-size: 22px;
            font-family: PingFangSC;
            color: #666666;
            padding-bottom: 200px;
        }
        ::v-deep .van-popup__close-icon--top-right {
            top: 42px;
        }
        ::v-deep .van-popup__close-icon {
            color: #333;
        }
    }
</style>
