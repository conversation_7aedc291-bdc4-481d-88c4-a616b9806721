<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-06-19 10:08:21
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-10-09 10:43:11
-->
<template>
  <div class="home">
    <div v-if="false" v-show="!show" class="couponTipsShow fixed">用券可享<span v-for="(item,index) in list" :key="index" class="red">满{{ item.withAmount }}减{{ item.usedAmount }}</span></div>
    <div ref="circleBox" class="cart-box">
      <div class="cart">
        <div class="cart-left">
          <div class="cart_img" @click="showTrue">
            <van-icon v-if="this.$store.state.cart.cartData[0].goodsList.length == 0" class="cartIcon" size="27" name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/user/takeout/graycart.png" />
            <van-icon v-else class="cartIcon" size="27" :badge="this.$store.state.cart.cartData[0].goodsList.length" name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/takeout/carticon.png" />
          </div>
          <div class="cart_center" @click="showTrue">
            <div>
              <span class="cart_price">￥{{ cartPrice }}</span>
              <span class="cart_oriPrice">¥{{ cartOriPrice }}</span>
            </div>
            <!-- <div class="cart_postfree">
              免配送费
            </div> -->
          </div>
        </div>
        <div v-if="cartPrice >= shopData.marketConfig.deliverLimitPrice" class="cart_btn" @click="goTakeOrder">去结算</div>
        <div v-else class="cart_btn  disabled">¥{{ shopData.marketConfig.deliverLimitPrice }}起送</div>
      </div>
    </div>

    <!-- 购物车弹出 -->
    <van-popup v-model="show" position="bottom" round :style="{ height: '60%' }">
      <div class="popup">
        <div class="popup_top">
          <div v-if="list!=null&&list.length>0" class="couponTips">用券可享<span v-for="(item,index) in list" :key="index" class="red">满{{ item.withAmount }}减{{ item.usedAmount }}</span></div>
          <div v-else class="couponTips">欢迎下单</div>
          <div class="selected">
            <span>已选商品</span>
            <div class="clear">
              <van-icon class="del" name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/takeout/del.png" @click="clearNull" />
              <span style="margin-left:3px" @click="clearNull">清空购物车</span>
            </div>
          </div>
        </div>
        <div class="popup_body">
          <ul>
            <li v-for="(item,index) in cartlist" ref="rItem" :key="index" class="goods_list">
              <div class="goods_list_left">
                <img :src="item.cover" alt="" class="goods_img">
                <div class="goods_list_info">
                  <div class="goods_title">
                    {{ item.goodsName | ellipsis(20) }}
                  </div>
                  <div class="goods_detile">
                    {{ item.difference }}
                  </div>
                  <div class="goods_price">
                    <span>￥</span>
                    <span>{{ item.price.price }}</span>
                  </div>
                </div>
              </div>
              <div>
                <div class="sl_prop">
                  <div class="tag2">
                    <div v-if="badgeNum(item) != 0" @click="reduce(item)">
                      <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/takeout/jian.png" class="numIcon"></div>
                    <div v-if="badgeNum(item) != 0" class="inputNum">
                      {{ badgeNum(item) }}
                    </div>
                    <div @click="selectOnetag(item)">
                      <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/takeout/jia.png" class="numIcon">
                    </div>
                  </div>
                </div>
              </div>
            </li>
          </ul>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script>
import NP from 'number-precision'
import { marketCouList } from '@/api/shoppingMall'
export default {
  name: 'Home',
  props: {
    shopData: {
      type: Object,
      default: function() {
        return {}
      }
    }
  },
  data() {
    return {
      show: false,
      list: []
    }
  },
  computed: {
    cartlist() {
      let cart = this.$store.state.cart.cartData[0].goodsList
      let newcart = []
      const res = new Map()
      newcart = cart.filter((a) => !res.has(a.skuId) && res.set(a.skuId, 1))
      return newcart
    },
    cartPrice() {
      return NP.strip(this.$store.getters['cart/sumPrice'])
    },
    cartOriPrice() {
      return NP.strip(this.$store.getters['cart/cartSumOriPrice'])
    }
  },
  mounted() {
    // const $circle = this.$refs.circleBox
    // this.$store.state.market.cartLat = this.getParentTop($circle)
  },
  created() {
    this.getMarketCouList()
  },
  methods: {
    // 去结算
    goTakeOrder() {
      if (this.$store.state.cart.cartData[0].goodsList.length == 0) {
        this.$toast('请选择商品')
        return
      }
      this.$store.state.market.marketData.remark = ''
      this.$router.push({
        path: '/submitOrder'
      })
      this.$emit('goTakeOrder')
    },
    showTrue() {
      if (this.$store.state.cart.cartData[0].goodsList.length == 0) {
        this.$toast('请选择商品')
        return
      }
      this.show = true
    },
    // 检查商品选中数量
    badgeNum(item) {
      let cart = this.$store.state.cart.cartData[0].goodsList
      let n = 0
      if (cart) {
        for (let i in cart) {
          if (cart[i].skuId == item.skuId) {
            n++
          }
        }
      }
      return n
    },
    // 减购物车
    reduce(item) {
      let cart = this.$store.state.cart.cartData[0].goodsList // 获取加入购物车
      let shoppCart = this.cartlist
      for (let i in shoppCart) {
        console.log(i)
        let index = cart.indexOf(item)
        if (index > -1) {
          cart.splice(index, 1)
        }
        shoppCart = cart
        break
      }
      if (cart.length == 0) {
        this.show = false
      }
    },
    // 加购物车
    selectOnetag(item) {
      let cart = this.$store.state.cart.cartData[0].goodsList // 获取加入购物车
      cart.push(item)
    },
    // 清空购物车
    clearNull() {
      this.$store.state.cart.cartData[0].goodsList = []
      this.show = false
    },
    /**
     * 获取顶部div的距离
     */
    // getParentTop(e) {
    //   var offset = e.offsetTop
    //   if (e.offsetParent != null) {
    //     offset += this.getParentTop(e.offsetParent)
    //   }
    //   return offset
    // },
    /**
     * 获取左侧div的距离
     */
    // getParentLeft(e) {
    //   var offset = e.offsetLeft
    //   if (e.offsetParent != null) {
    //     offset += this.getParentLeft(e.offsetParent)
    //   }
    //   return offset
    // }
    getMarketCouList() {
      let data = {
        userId: this.$store.getters.getUserId,
        marketId: this.$route.query.id
      }
      marketCouList(data).then((res) => {
        if (res.status == 200) {
          this.list = res.data
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.home {
    position: relative;
    width: 100%;
    height: 196px;
    margin: 0 auto;
    background-color: #fff;
    .couponTips {
        width: 100%;
        height: 60px;
        line-height: 60px;
        background: #fff3f1;
        border-radius: 24px 24px 0px 0px;
        font-family: PingFangSC;
        font-size: 22px;
        padding-left: 40px;
        padding-right: 40px;
        overflow: hidden;
        text-overflow:ellipsis;
        white-space: nowrap;
    }
    .couponTipsShow{
        width: 100%;
        height: 60px;
        line-height: 60px;
        background: #fff3f1;
        border-radius: 24px 24px 0px 0px;
        font-family: PingFangSC;
        font-size: 22px;
        padding-left: 40px;
    }
    .numIcon {
        width: 38px;
        height: 38px;
        // vertical-align: middle;
      }
    .fixed {
        position: fixed;
        left: 36px;
        bottom: 126px;
        z-index: 888;
        width: 678px;
    }
    .cart-box {
        width: 100%;
        height: 126px;
        background-color: rgba(255,255,255,.8);
        position: fixed;
        bottom: 0;
        z-index: 9999;
        .cart {
            width: 676px;
            height: 94px;
            border-radius: 54px;
            position: fixed;
            left: 0;
            right: 0;
            margin: 0 auto;
            bottom: 20px;
            display: flex;
            .cart-left {
                display: flex;
                align-items: center;
                width: 486px;
                background-color: #000000;
                border-radius: 54px 0 0 54px;
                .cart_img {
                    margin-left: 48px;
                    margin-top: 15px;
                }
                .cart_center {
                    display: flex;
                    flex-direction: column;
                    justify-content: center;
                    width: 406px;
                    color: #ffffff;
                    margin-left: 26px;
                    font-family: PingFangSC;
                    letter-spacing: 0px;
                    font-size: 38px;
                    .cart_price {
                        margin-right: 8px;
                    }
                    .cart_oriPrice {
                        opacity: 0.6;
                        font-size: 24px;
                        text-decoration: line-through;
                    }
                    .cart_postfree {
                        font-size: 20px;
                    }
                }
            }
            .cart_btn {
                width: 192px;
                height: 94px;
                line-height: 94px;
                text-align: center;
                color: #ffffff;
                background: linear-gradient(90deg,#ff1e29, #ff5a25);
                border-top-right-radius: 54px;
                border-bottom-right-radius: 54px;
                font-size: 32px;
                font-family:PingFangSC-Medium;
                font-weight: 500;
            }
          .disabled {
            background: #000000;
          }
        }
    }
    .red {
        color: #ff7063;
        margin-right: 3px;
    }
    .popup {
        .popup_top {
            position: absolute;
            top: 0;
            width: 100%;
            height: 156px;
            .selected {
                width: 100%;
                display: flex;
                justify-content: space-between;
                align-items: center;
                height: 96px;
                line-height: 96px;
                font-size: 34px;
                font-family: PingFangSC;
                color: #333333;
                padding: 0 40px;
                border-bottom: 1px solid #f4f4f4;
            }
            .clear {
                font-size: 22px;
                color: #666666;
            }
        }
        .popup_body {
            position: absolute;
            top: 156px;
            bottom: 126px;
            overflow-y: auto;
            margin: 0 40px;
            .goods_list {
              position: relative;
                display: flex;
                justify-content: space-between;
                width: 670px;
                height: 135px;
                margin-top: 40px;
                border-bottom: 1px solid #f4f4f4;
                .goods_list_left {
                    display: flex;
                    .goods_img {
                     width: 110px;
                    height: 110px;
                    border-radius: 8px;
                    overflow: hidden;
                    margin-right: 16px;
                    }
                    .goods_list_info{
                        .goods_title {
                            font-size: 30px;
                            font-family: PingFangSC-Medium;
                            font-weight: 500;
                            color: #333333;
                        }
                        .goods_detile {
                            font-size: 20px;
                            color: #454545;
                            font-family: PingFangSC;
                        }
                        .goods_price {
                            font-size: 28px;
                            font-family:PingFangSC-Medium;
                            font-weight: 500;
                            color: #ff301e;
                        }
                    }
                }
                .sl_prop {
                  position: absolute;
                  right: 0;
                  bottom: 20px;
                  height: 38px;
                    // margin-top: 70px;
                    .tag2 {
                        display: flex;
                        align-items: center;
                        height: 38px;
                        // border: 1px solid #ccc;
                        .inputNum {
                            margin-left: 18px;
                            margin-right:18px;
                            font-size: 30px;
                            position: relative;
                            top: 3px;
                        }
                    }
                }
            }
        }
    }
}
</style>
