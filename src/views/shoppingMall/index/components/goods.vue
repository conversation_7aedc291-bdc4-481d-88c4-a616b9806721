<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-06-18 15:05:12
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-10-09 10:43:55
-->
<template>
  <div class="home">
    <div class="goods-recom">
      <div class="goods-title">店家力荐</div>
      <ul class="recom-card">
        <li v-for="(item,index) in goodlist" :key="index" @click="goPopShow(item)">
          <img :src="item.cover + '?x-oss-process=image/resize,w_700/format,jpg/quality,q_80' " alt="" class="recom-card-img">
          <div class="recom-card-name">{{ item.goodsName | ellipsis(8) }}</div>
          <div class="recom-card-price">
            <div class="recom-card-tag">特卖价</div>
            <div class="card-price">
              <div class="card-price-left">¥</div>
              <div class="card-price-right">{{ item.showPrice }}</div>
            </div>
          </div>
          <div class="recom-card-tip">人气推荐</div>
        </li>
      </ul>
    </div>
    <div class="line" />
    <div class="goods-feature">
      <div class="goods-title">精选好货</div>
      <ul class="feature-card">
        <li v-for="(item1,index1) in goodlisttwo" :key="index1">
          <div class="feature-card-img">
            <img :src="item1.cover + '?x-oss-process=image/resize,w_700/format,jpg/quality,q_80' " alt="" class="feature-card-img" @click="goPopShow(item1)">
          </div>
          <div class="feature-card-box">
            <div class="feature-card-subname" @click="goPopShow(item1)">{{ item1.description| ellipsis(10) }}</div>
            <div class="feature-card-name" @click="goPopShow(item1)">{{ item1.goodsName| ellipsis(10) }}</div>
            <div class="feature-card-tag">优惠价</div>
            <div class="feature-card-bottom">
              <div class="feature-card-bottom-price">
                <div class="card-price">
                  <div class="card-price-left">¥</div>
                  <div class="card-price-right">{{ item1.showPrice }}</div>
                </div>
                <div class="feature-card-oriPrice">¥{{ item1.oriPrice }}</div>
              </div>
              <div class="feature-card-btn">
                <div v-if="badgeNum(item1) != 0" @click="reduce(item1)">
                  <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/takeout/jian.png" class="numIcon"></div>
                <div v-if="badgeNum(item1) != 0" class="inputNum" style="color: #000000">
                  {{ badgeNum(item1) }}
                </div>
                <div v-if="badgeNum(item1)<item1.skuList[0].actualStocks" @click="selectOnetag(item1)">
                  <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/takeout/jia.png" class="numIcon">
                </div>
              </div>

            </div>
          </div>
          <!-- <div class="feature-card-tip">新鲜</div> -->
        </li>
      </ul>
    </div>
    <van-popup v-model="show" position="bottom" :style="{ height: '85%' }" round>
      <div class="detail-pop">
        <div class="pop-top" @click="show = false">
          <van-icon name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/user/icon/arrow-black.png" size="19" />
        </div>
        <div class="pop-content">
          <div class="pop-detail-header">
            <img :src="detail.cover" alt="" class="pop-detail-cover">
            <div class="pop-detail-label">
              <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/shoppingMall/index/quality-icon.png" alt="" class="pop-detail-labelIcon">
              价格实惠·好吃再来
            </div>
          </div>
          <div class="pop-detail-name">
            <span>{{ detail.goodsName| ellipsis(20) }}</span>
            <span v-if="detail.skuList[0].actualStocks" class="pop-detail-stock">库存 {{ detail.skuList[0].actualStocks }}</span>
          </div>
          <div v-if="detail.skuList[0].skuName" class="pop-detail-tag">
            {{ detail.skuList[0].skuName }}
          </div>
          <div class="pop-detail-bottom">
            <div>
              <span class="small">¥</span><span class="pop-detail-price">{{ detail.showPrice }}</span>
              <span class="pop-detail-oriPrice">¥{{ detail.oriPrice }}</span>
            </div>
            <!-- <div class="pop-detail-add">
              <van-button icon="plus" type="primary" color="linear-gradient(90deg,#40d243, #1fc432)">加入购物车</van-button>
            </div> -->
            <div class="pop-detail-btn">
              <div v-if="badgeNum(detail) != 0">
                <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/takeout/jian.png" class="numIcon" @click="reduce(detail)"></div>
              <div v-if="badgeNum(detail) != 0" class="inputNum">
                {{ badgeNum(detail) }}
              </div>
              <div v-if="badgeNum(detail)<detail.skuList[0].actualStocks">
                <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/app/newuser/takeout/jia.png" class="numIcon" @click="selectOnetag(detail)">
              </div>
            </div>
          </div>
          <div class="line16" />
          <ul class="pop-detail-desc">
            <li>
              <span class="desc-label">服务</span>
              <div class="desc-tag">
                <van-icon name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/shoppingMall/index/icon6.png" size="13" />
                质量保证
              </div>
              <div class="desc-tag">
                <van-icon name="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/shoppingMall/index/icon5.png" size="13" />
                配送及时
              </div>
            </li>
            <!-- <li>
              <span class="desc-label">运费</span>
              <div>全场包邮</div>
            </li> -->
            <li>
              <span class="desc-label">规格</span>
              <div v-if="detail.skuList[0].skuName">{{ detail.skuList[0].skuName }}</div>
            </li>
          </ul>
          <div class="line16" />
          <div class="detail">
            <div class="title">
              商品详情
            </div>
            <div class="body" v-html="detail.goodsDetail.goodsDetail" />
          </div>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script>
export default {
  props: {
    goodlist: {
      type: Array,
      default: function() {
        return []
      }
    },
    goodlisttwo: {
      type: Array,
      default: function() {
        return []
      }
    }
  },
  data() {
    return {

      show: false,
      detail: {
        goodsDetail: {
          goodsDetail: ''
        },
        skuList: [
          {
            skuName: '',
            actualStocks: ''
          }
        ]
      }
    }
  },
  created() {
  },
  mounted() {

  },
  methods: {
    // 检查商品选中数量
    badgeNum(item) {
      let cart = this.$store.state.market.cartList[0].goods
      let n = 0
      if (cart) {
        for (let i in cart) {
          if (cart[i].goodsId == item.goodsId) {
            n++
          }
        }
      }
      return n
    },
    // 减购物车
    reduce(item) {
      let skuid = item.skuList[0].skuId
      let cart = this.$store.state.cart.cartData[0].goodsList
      for (let i = 0; i < cart.length; i++) {
        if (skuid == cart[i].skuId) {
          if (cart[i].leastCopies == this.badgeNum(item)) {
            cart.splice(i, this.badgeNum(item))
            break
          } else {
            cart.splice(i, 1)
            break
          }
        }
      }
    },
    // 加购物车
    selectOnetag(data) {
      let cart = this.$store.state.cart.cartData[0].goodsList
      let cartList = {}
      cartList.skuId = data.skuList[0].skuId
      cartList.goodsId = data.goodsId
      cartList.tagId = data.tagId
      cartList.leastCopies = data.skuList[0].leastCopies // 最少购买份数
      cartList.attrs = data.attrs
      cartList.cover = data.skuList[0].pic
      cartList.difference = data.skuList[0].skuName
      cartList.numberOfPackages = data.skuList[0].numberOfPackages
      cartList.stock = data.skuList[0].actualStocks
      cartList.goodsName = data.goodsName
      // 记录价格
      cartList.price = {}
      cartList.price.price = data.price
      cartList.price.oriPrice = data.oriPrice
      cartList.price.packPrice = data.skuList[0].packPrice

      cartList.agentId = data.agentId

      if (data.tbGoodsConfigure == null) {
        cartList.distributionMode = 1
      } else {
        cartList.distributionMode = data.tbGoodsConfigure.distributionMode // 配送方式1：单点可送2单点不送
      }

      // 起购限制
      if (cartList.leastCopies > 0 && this.badgeNum(data) < cartList.leastCopies) {
        for (let i = 0; i < cartList.leastCopies; i++) {
          cart.push(cartList)
        }
      } else {
        cart.push(cartList)
      }
    },
    goBack() {
      this.$router.go(-1)
    },
    goPopShow(item) {
      this.show = true
      this.detail = item
    }
  }
}
</script>

<style scoped lang="scss">
    .home {
      .numIcon {
        width: 38px;
        height: 38px;
        // vertical-align: middle;
      }
        .goods-title {
            font-size: 30px;
            font-family:PingFangSC-Medium;
            font-weight: 500;
            color: #333333;
            line-height: 42px;
            padding: 16px 0;
        }
        .card-price {
            // width: 60px;
            color: #ff301e;
            display: flex;
            margin-top: 3px;
            .card-price-left{
              width: 20px;
              height: 28px;
              font-size: 20px;
              font-family: PingFangSC;
              margin-top: 8px;
            }
            .card-price-right{
              font-size: 30px;
              font-weight: 500;
              font-family:PingFangSC-Medium;
            }
        }
        .goods-recom,.goods-feature {
            background-color: #fff;
            padding: 0 26px ;
            box-sizing: border-box;
        }
        .recom-card {
            width: 100%;
            display: flex;
            overflow: auto;
            padding-bottom: 24px;
            overflow: hidden;
            li:not(:last-child){
                margin-right: 18px;
            }
            li {
                position: relative;
            }
            .recom-card-img {
                width: 220px;
                height: 220px;
                border-radius: 12px;
                overflow: hidden;
            }
            .recom-card-name {
                font-size: 26px;
                font-family: PingFangSC-Medium;
                color: #333333;
                font-weight: 400;
            }
            .recom-card-price{
              display: flex;
              .recom-card-tag {
                min-width: 78px;
                max-width: 100px;
                height: 30px;
                margin-right: 10px;
                border: 1px solid #ff1929;
                text-align: center;
                line-height: 30px;
                font-size: 16px;
                font-family: PingFangSC;
                border-radius: 7px;
                color: #ff1929;
                margin-top: 8px;
              }
            }

            .recom-card-tip {
                position: absolute;
                left: 0;
                top: 0;
                width: 116px;
                height: 36px;
                line-height: 36px;
                background-image: url(https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/shoppingMall/index/tip1.png);
                background-size: 100%;
                font-size: 20px;
                font-family: PingFangSC;
                font-weight: 400;
                text-align: center;
                color: #ffffff;
            }
        }
        .feature-card {
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
            li{
              width: 339px;
              border-top-left-radius: 17px;
              border-top-right-radius: 17px;
              border: 1px solid #eeeeee;
              border-bottom-left-radius: 17px;
              border-bottom-right-radius: 17px;

              .feature-card-box{
                border-bottom-left-radius: 17px;
                border-bottom-right-radius: 17px;
              }
            }
            li {
                position: relative;
                padding-bottom: 10px;
            }
            li:nth-of-type(n+3) {
                margin-top: 14px;
            }
            .feature-card-img {
                width: 339px;
                height: 314px;
                border-top-left-radius: 12px;
                border-top-right-radius: 12px;
                overflow: hidden;
            }
            .feature-card-subname {
                font-size: 24px;
                font-family: PingFangSC;
                color: #169d1b;
                line-height: 33px;
                margin-top: 8px;
                margin-left: 10px;
            }
            .feature-card-name {
                font-size: 27px;
                font-family:PingFangSC-Medium;
                font-weight: 500;
                color: #333333;
                margin-top: 8px;
                margin-left: 10px;
            }
            .feature-card-tag {
                min-width: 78px;
                max-width: 100px;
                height: 30px;
                margin-right: 10px;
                border: 1px solid #ff1929;
                text-align: center;
                line-height: 30px;
                font-size: 16px;
                font-family: PingFangSC;
                border-radius: 7px;
                color: #ff1929;
                margin-top: 8px;
                margin-left: 10px;
            }
            .feature-card-bottom {
                display: flex;
                align-items: center;
                justify-content: space-between;
                height: 44px;
                margin-top: 6px;
                margin-left: 10px;
                margin-bottom: 16px;
                .feature-card-bottom-price{
                  display: flex;
                }
            }
            .feature-card-oriPrice {
                font-size: 24px;
                font-family: PingFangSC;
                color: #999999;
                margin-left: 20px;
                margin-top: 9px;
                text-decoration: line-through;
            }
            .feature-card-btn {
              display: flex;
              align-items: center;
              margin-right: 16px;
              height: 38px;
              margin-top: -5px;
              .inputNum {
                  margin-left: 12px;
                  margin-right:12px;
                  font-size: 30px;
                  position: relative;
                  top: 3px;
              }
            }
            .feature-card-add {
                width: 38px;
                height: 38px;
                background-image: url(https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/shoppingMall/index/add.png);
                background-size: 100%;
                margin-right: 16px;
                margin-top: 8px;
            }
            .feature-card-tip {
                position: absolute;
                left: 10px;
                top: 270px;
                width: 66px;
                height: 40px;
                background-image: url(https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/shoppingMall/index/tip2.png);
                background-size: 100%;
                background-repeat: no-repeat;
                font-size: 20px;
                font-family: PingFangSC;
                font-weight: 400;
                text-align: center;
                color: #ffffff;
            }
        }
        .line {
            height: 20px;
            width: 100%;
        }
        .line16 {
            height: 16px;
            width: 100%;
            background-color: #f5f5f5;
        }
        .detail{
          width: 90%;
          margin: 0 auto;
          font-size: 26px;
          margin: 25px;
          .title{
            font-size: 36px;
            font-family: PingFangSC-Medium;
          }
          .body{
            margin-top: 30px;
            width: 100%;
            ::v-deep img{
              width: 100%;
            }
          }
        }
        .detail-pop {
          padding-bottom: 186px;
          .small {
            font-size: 26px;
            font-family:PingFangSC-Medium;
            color: #ff301e;
        }
          .pop-top {
              position: fixed;
              width: 100%;
              height: 76px;
              padding-top: 19px;
              padding-left: 33px;
              z-index: 9;
              .backimg{
                  width: 31px;
                  height: 30px;
              }
          }
          .pop-content {
            .pop-detail-header {
              position: relative;
              .pop-detail-cover {
                width: 100%;
                height: 664px;
                background-color: rgb(226, 216, 216);
              }
              .pop-detail-label {
                position: absolute;
                left: 0;
                bottom: 0;
                width: 100%;
                height: 64px;
                line-height: 64px;
                padding-left: 29px;
                background: #e0f5ec;
                font-size: 22px;
                font-family: PingFangSC;
                color: #169d1b;
                .pop-detail-labelIcon {
                  width: 132px;
                  height: 36px;
                  margin-right: 9px;
                  vertical-align: middle;
                }
              }
            }
            .pop-detail-name {
              display: flex;
              justify-content: space-between;
              align-items: center;
              font-size: 38px;
              font-family:PingFangSC-Medium;
              font-weight: 500;
              color: #222222;
              margin:16px 30px 8px 30px ;
              .pop-detail-stock {
                font-size: 22px;
                font-family: PingFangSC;
                color: #999999;
              }
            }
            .pop-detail-tag {
              font-size: 28px;
              font-family: PingFangSC;
              color: #454545;
              margin-left: 30px;
              margin-bottom: 8px;
            }
            .pop-detail-bottom {
              display: flex;
              justify-content: space-between;
              align-items: center;
              margin:16px 30px 8px 30px ;
              .pop-detail-price {
                font-size: 48px;
                font-family:PingFangSC-Medium;
                font-weight: 500;
                color: #ff301e;
                margin-right: 8px;
              }
              .pop-detail-oriPrice {
                font-size: 26px;
                font-family: PingFangSC;
                color: #999999;
                text-decoration: line-through;
              }
              .pop-detail-add {
                // height: 83px;
                ::v-deep .van-button--normal {
                  padding: 0;
                  font-size: 24px;
                  border-radius: 10px;
                  width: 190px;
                  height: 54px;
                }
              }
              .pop-detail-btn {
                display: flex;
                align-items: center;
                height:38px;
                margin-right: 16px;
                margin-top: 10px;
                // border: 1px solid #ccc;
                .inputNum {
                    margin-left: 12px;
                    margin-right:12px;
                    position: relative;
                    top: 3px;
                    font-size: 30px;
                }
              }
            }
            .pop-detail-desc {
              font-size: 26px;
              font-family: PingFangSC;
              color: #333333;
              margin-left: 30px;
              li {
                display: flex;
                align-items: center;
                padding: 22px 0;
                .desc-label {
                  width: 110px;
                  font-family:PingFangSC-Medium;
                  font-weight: 500;
                  color: #333333;
                }
                .desc-tag {
                  border: 1px solid #e6e6e6;
                  padding: 2px 10px;
                  margin-right: 10px;
                  border-radius: 6px;
                }
              }
              li:first-child {
                ::v-deep .van-icon {
                  top: 3px;
                }
                div {
                  font-size: 24px;
                }
              }
            }
          }
        }
    }
</style>
