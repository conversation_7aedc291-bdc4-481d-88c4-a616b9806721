<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-06-18 10:42:45
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-04-09 01:04:10
-->
<template>
  <div class="home">
    <Top ref="child1Container" />
    <ShopCard :shop-data="shopData" />
    <Goods :goodlist="goodList" :goodlisttwo="goodListTwo" />
    <Cart :shop-data="shopData" @goTakeOrder="goTakeOrder" />

    <!-- 加载状态 -->
    <Loading :show="loadingShow" />
  </div>
</template>

<script>
import Top from './components/top.vue'
import ShopCard from './components/shopCard.vue'
import Goods from './components/goods.vue'
import Cart from './components/cart.vue'
import { getShopDataV2, getList } from '@/api/takeout'
import Loading from '@/components/Loading/index'
export default {
  components: {
    Top,
    ShopCard,
    Goods,
    Cart,
    Loading
  },
  data() {
    return {
      shopData: {
        marketConfig: {}
      },
      loadingShow: true,
      goodList: [],
      goodListTwo: []
    }
  },
  created() {
    // 回到顶部
    document.body.scrollTop = 0
    document.documentElement.scrollTop = 0
    this.getShopData()
    this.getList()
  },
  beforeRouteLeave(to, from, next) {
    if (to.path == '/submitOrder') {
      from.meta.keepAlive = true
    } else {
      this.removeKeepAliveCache()
    }
    next()
  },
  methods: {
    goTakeOrder() {
      this.$refs.child1Container.remove()
    },
    removeKeepAliveCache() {
      if (this.$vnode && this.$vnode.data.keepAlive && this.$vnode.parent) {
        const tag = this.$vnode.tag
        let caches = this.$vnode.parent.componentInstance.cache
        let keys = this.$vnode.parent.componentInstance.keys
        for (let [key, cache] of Object.entries(caches)) {
          if (cache.tag === tag) {
            if (keys.length > 0 && keys.includes(key)) {
              keys.splice(keys.indexOf(key), 1)
            }
            delete caches[key]
          }
        }
      }
      this.$destroy()
    },
    // 店铺信息
    getShopData() {
      let data = {
        marketId: this.$route.query.id,
        longitude: this.$store.getters.getLocation.longitude,
        latitude: this.$store.getters.getLocation.latitude
      }
      getShopDataV2(data).then((res) => {
        this.loadingShow = false
        this.shopData = res.data
        this.$store.state.market.marketData.type = res.data.type
        this.$store.state.market.marketData.marketSn = res.data.marketSn
        this.$store.state.market.marketData.marketId = this.$route.query.id
        this.$store.state.market.marketData.marketName = res.data.marketName
        this.$store.state.market.marketData.agentPostFee = res.data.marketConfig.agentPostFee
      })
    },
    // 获取商品
    getList() {
      getList(this.$route.query.id)
        .then((res) => {
          if (res.status == 200) {
            if (res.data.marketTagGoodsListVOList.length > 0) {
              this.goodList = res.data.marketTagGoodsListVOList[0].goodsList
              this.goodListTwo = res.data.marketTagGoodsListVOList[1].goodsList
            }
          } else {
            this.$toast(res.message)
          }
        })
    }
  }
}
</script>

<style scoped lang="scss">
</style>
