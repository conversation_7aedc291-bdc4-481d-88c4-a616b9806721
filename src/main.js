/*
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2021-05-22 15:21:14
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-02-15 16:04:55
 */
import Vue from 'vue'
import App from './App.vue'
import router from './router'
import store from './store'

// css
import 'amfe-flexible/index.min.js'
import '@/styles/index.scss'
import animated from 'animate.css'
Vue.use(animated)

// vant
import Vant from 'vant'
import 'vant/lib/index.css'
Vue.use(Vant)
// 懒加载
import { Lazyload } from 'vant'
Vue.use(Lazyload)

// 全局Top
import NavHeight from './components/NavHeight'
Vue.component('NavHeight', NavHeight)

// 全局缺省页Empty
import Empty from './components/Empty'
Vue.component('Empty', Empty)

// 二维码
import VueQrcode from '@chenfengyuan/vue-qrcode'
Vue.component(VueQrcode.name, VueQrcode)

// 剪切板
import VueClipboard from 'vue-clipboard2'
Vue.use(VueClipboard)

// 防抖
import throttle from '@/utils/throttle.js'
Vue.prototype.$throttle = throttle

// 过滤器
import filter from './utils/filter'

// 阿里云监控
import logger from './utils/aliLog'

Vue.config.productionTip = false
// 错误捕获
Vue.config.errorHandler = (err, vm, info) => {
  if (String(err) != 'ReferenceError: AlipayJSBridge is not defined') {
    console.error(err)
  }
}

new Vue({
  router,
  store,
  filter,
  logger,
  render: h => h(App)
}).$mount('#app')
