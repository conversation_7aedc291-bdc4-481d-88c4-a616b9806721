// eslint-disable-next-line no-unused-vars
import Vue from 'vue'
import axios from 'axios'
import {
  Toast
} from 'vant'

import {
  invalidCode,
  noPermissionCode,
  requestTimeout,
  successCode,
  baseURL,
  // eslint-disable-next-line no-unused-vars
  baseUrlSearch,
  version,
  rel
} from '@/config/settings'
import store from '@/store/index.js'
import router from '@/router'
import {
  isArray
} from '@/utils/validate'

let loadingInstance

/**
 * @description 处理code异常
 * @param {*} code
 * @param {*} msg
 */
const handleCode = (code, msg) => {
  switch (code) {
    case invalidCode:
      Toast('系统异常，请稍后重试')
      break
    case noPermissionCode:
      Toast('登录失效，请重新登录')
      break
    default:
      Toast('系统异常，请稍后重试')
      break
  }
}

let u = navigator.userAgent
let isiOS = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/) // ios终端

let datainfo = {
  userId: localStorage.getItem('userId'), // 用户ID
  regionId: localStorage.getItem('regionId'), // 大区id
  deviceType: isiOS ? 'IOS' : 'Android', // 手机型号
  networkEnvironment: '', // 网络状态
  version: version, // 版本号
  deviceId: localStorage.getItem('deviceId') ? localStorage.getItem('deviceId') : 'test', // 手机唯一标识
  infVersion: rel//
}

const instance = axios.create({
  baseURL,
  timeout: requestTimeout,
  headers: {
    'content-type': 'application/json'
  }
})

instance.interceptors.request.use(
  config => {
    // if (localStorage.getItem('token')) {
    //   config.headers['Token'] = localStorage.getItem('token')
    // }
    config.headers['Token'] = localStorage.getItem('token')

    config.headers['data'] = JSON.stringify(datainfo)
    return config
  },
  error => {
    console.log(error) // for debug
    return Promise.reject(error)
  }
)

instance.interceptors.response.use(
  (response) => {
    if (loadingInstance) loadingInstance.close()
    const {
      data
    } = response

    const {
      status
    } = data
    // 操作正常Code数组
    const codeVerificationArray = isArray(successCode) ? [...successCode] : [...[successCode]]
    // 是否操作正常
    if (codeVerificationArray.includes(status)) {
      data.headers = response.headers
      return data
    } else {
      if (data.status == 401) {
        let path = router.history.current.path
        localStorage.removeItem('token')
        localStorage.removeItem('userId')
        localStorage.removeItem('phone')
        localStorage.removeItem('headImg')
        localStorage.removeItem('isWxBind')
        localStorage.removeItem('phone')
        store.state.My.headImg = ''
        store.state.My.blance = 0
        store.state.userId = ''
        store.state.token = ''
        store.state.phone = ''
        store.state.order.orderList = []
        store.state.market.marketData.balance = ''
        store.state.market.marketData.payradio = ''
        store.state.market.marketData.postFee = 0
        if (path != '/order') {
          store.state.tabbar.index = 0
        }
        if (path != '/' && path != '/menu' && path != '/order' && path != '/my' && path != '/index' && path != '/region/index' && path != '/shop' && path != '/Classify' && path != '/market/home') {
          localStorage.removeItem('token')
          localStorage.removeItem('userId')
          localStorage.removeItem('phone')
          router.push({
            path: '/wxLogin2'
          })
          return data
        } else {
          return data
        }
      } else {
        if (data.message && data.message != 'success') { Toast(data.message) }
        const contentType = response.headers['content-type'] || ''

        if (contentType.includes('image/jpeg')) {
          return {
            status: 200,
            data: response.data,
            headers: response.headers

          }
        }
        return data
      }
    }
  },
  (error) => {
    console.log(error)
    if (loadingInstance) loadingInstance.close()
    const {
      response,
      message
    } = error
    if (error.response && error.response.data) {
      const {
        status,
        data
      } = response
      handleCode(status, data.msg || message)
      return Promise.reject(error)
    } else {
      let {
        message
      } = error
      if (message === 'Network Error') {
        Toast('系统异常，请稍后重试')
      }
      if (message.includes('timeout')) {
        Toast('后端接口请求超时')
      }
      if (message.includes('Request failed with status code')) {
        const code = message.substr(message.length - 3)
        message = '后端接口' + code + '异常'
        Toast('后端接口连接异常')
      }
      return Promise.reject(error)
    }
  }
)

export default instance
