/*
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2022-03-17 18:03:50
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-03-18 14:14:05
 */
import BigNumber from 'bignumber.js'
class Maths {
  constructor(num1, num2) {
    this.num1 = num1
    this.num2 = num2
  }
  sum() {
    let newNum = new BigNumber(this.num1)
    return newNum.plus(this.num2).toNumber()
  }
  minus() {
    let newNum = new BigNumber(this.num1)
    return newNum.minus(this.num2).toNumber()
  }
  multipliedBy() {
    let newNum = new BigNumber(this.num1)
    return newNum.times(this.num2).toNumber()
  }
  dividedBy() {
    let newNum = new BigNumber(this.num1)
    return newNum.div(this.num2).toNumber()
  }
}
export default Maths
