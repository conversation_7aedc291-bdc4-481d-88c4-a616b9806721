/*
 * @Descripttion: 过滤器
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2021-05-24 09:54:46
 * @LastEditors: 孙立政
 * @LastEditTime: 2021-07-05 17:27:20
 */
import Vue from 'vue'

// 字数限制过滤器
Vue.filter('ellipsis', (value, num) => {
  const nums = num
  if (!value) return ''
  if (value.length > nums) {
    return value.slice(0, nums) + '...'
  }
  return value
})
// 脱敏
Vue.filter('price', (value, num) => {
  var pat = /(\d{3})\d*(\d{4})/

  return value.replace(pat, '$1****$2')
})

// 金额格式化
Vue.filter('Fmoney', function(val) {
  val = val.toString().replace(/\$|\,/g, '')
  if (isNaN(val)) {
    val = '0'
  }
  let sign = (val == (val = Math.abs(val)))
  val = Math.floor(val * 100 + 0.50000000001)
  let cents = val % 100
  val = Math.floor(val / 100).toString()
  if (cents < 10) {
    cents = '0' + cents
  }
  for (var i = 0; i < Math.floor((val.length - (1 + i)) / 3); i++) {
    val = val.substring(0, val.length - (4 * i + 3)) + ',' + val.substring(val.length - (4 * i + 3))
  }

  return (((sign) ? '' : '-') + val + '.' + cents)
})

// 精度
Vue.filter('time', (s, num) => {
  var h
  h = Math.floor(s / 60)
  s = s % 60
  h += ''
  s += ''
  h = (h.length == 1) ? '0' + h : h
  s = (s.length == 1) ? '0' + s : s
  return h
})
