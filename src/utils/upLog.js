/*
 * @Descripttion: 埋点
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2021-05-24 09:54:46
 * @LastEditors: 孙立政
 * @LastEditTime: 2021-10-12 15:46:02
 */
import store from '@/store/index.js'
import uplog from './uplog.json'
// import {
//   baseUrlUpLog
// } from '@/config/settings.js'
let uplogData = JSON.parse(JSON.stringify(uplog))

// var sensors = window['sensorsDataAnalytic201505']

// 记录类型
export function addData(type) {
  if (store.state.userMap.creatTime != '') {
    uplogData = JSON.parse(JSON.stringify(uplog))
  }

  store.state.userMap = uplogData
  store.state.userMap.type = type
  let trackArr = {}
  trackArr.type = type
  trackArr.times = Date.parse(new Date())
  store.state.userMap.track.push(trackArr)

  if (type != 4) {
    store.state.userMap.keyword = ''
  }
  // 记录点击次数
  // let clickData = {
  //   type: type,
  //   creatTime: Date.parse(new Date()),
  //   userId: localStorage.getItem('userId')
  // }
  // let img = new Image()
  // img.src = baseUrlUpLog + 'setSumData?data=' + JSON.stringify(clickData)
}

// 记录关键字店铺
export function addSearchData(kets) {
  store.state.userMap.keyword = kets
  // 记录搜索关键字
  // sensors.track('BuyProduct', {
  //   keywords: kets
  // })
}

// 记录店铺
export function addShopData(shopId, shopName, shopTag, shopInTime) {
  store.state.userMap.shopData.shopId = shopId
  store.state.userMap.shopData.shopName = shopName
  store.state.userMap.shopData.shopTag = shopTag
  store.state.userMap.shopData.shopInTime = shopInTime
}

// 记录商品
export function addGoodsData(goodsData) {
  store.state.userMap.goodsData = goodsData
}

// 记录订单-地址
export function addOrderAddressData(address) {
  store.state.userMap.orderData.address = address
}

// 记录订单-订单数据
export function addOrderData(order) {
  store.state.userMap.orderData.orderMoney = order.orderMoney
  store.state.userMap.orderData.orderTime = order.orderTime
  store.state.userMap.orderData.orderPayType = order.orderPayType
  store.state.userMap.orderData.orderNo = order.orderNo
}

// 订单上报
export function logDataUp() {
  store.state.userMap.userData.userId = localStorage.getItem('userId')
  store.state.userMap.userData.phone = localStorage.getItem('phone')
  store.state.userMap.creatTime = Date.parse(new Date())
  console.log(JSON.stringify(store.state.userMap))

  // sensors.track('BuyProduct', {
  //   takeOutData: JSON.stringify(store.state.userMap),
  //   isOrder: true
  // })
  // let img = new Image()
  // img.src = baseUrlUpLog + 'setdata?data=' + JSON.stringify(store.state.userMap)
}
