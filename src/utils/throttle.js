/*
 * @Descripttion: 节流
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2021-06-29 18:34:56
 * @LastEditors: 孙立政
 * @LastEditTime: 2021-07-05 17:26:43
 */
let timer, flag // eslint-disable-line no-unused-vars
/**
 *
 * @param {Function} func 要执行的回调函数
 * @param {Number} wait 延时的时间
 * @param {Boolean} immediate 是否立即执行
 * @return null
 */
function throttle(func, wait = 500, immediate = true) {
  if (immediate) {
    if (!flag) {
      flag = true
      // 如果是立即执行，则在wait毫秒内开始时执行
      typeof func === 'function' && func()
      timer = setTimeout(() => {
        flag = false
      }, wait)
    }
  } else {
    if (!flag) {
      flag = true
      // 如果是非立即执行，则在wait毫秒内的结束处执行
      timer = setTimeout(() => {
        flag = false
        typeof func === 'function' && func()
      }, wait)
    }
  }
}
export default throttle
