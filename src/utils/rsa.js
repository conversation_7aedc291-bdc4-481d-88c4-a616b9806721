/**
 * @Descripttion: RSA加密工具
 * @version: 1.0.0
 * @Author: Cline
 * @Date: 2025-02-28
 */

import { KJUR, KEYUTIL } from 'jsrsasign'

/**
 * RSA加密方法
 * @param {string} publicKey - RSA公钥
 * @param {object|string} data - 需要加密的数据对象或字符串
 * @param {string} mode - 加密模式，默认为'RSAOAEP'
 * @returns {string|null} - 返回加密后的字符串，失败返回null
 */
export function rsaEncrypt(publicKey, data, mode = 'RSAOAEP') {
  try {
    // 检查公钥是否有效
    if (!publicKey || typeof publicKey !== 'string') {
      console.error('RSA加密失败: 无效的公钥')
      return null
    }

    // 将公钥转换为密钥对象
    const keyObj = KEYUTIL.getKey(publicKey)

    // 如果数据是对象，则转换为JSON字符串
    const dataStr = typeof data === 'object' ? JSON.stringify(data) : String(data)

    // 使用指定模式进行加密
    const encrypted = KJUR.crypto.Cipher.encrypt(dataStr, keyObj, mode)
    return encrypted
  } catch (error) {
    console.error('RSA加密失败:', error)
    return null
  }
}

/**
 * 示例用法:
 *
 * import { rsaEncrypt } from '@/utils/rsa'
 *
 * const publicKey = 'MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAmYoXuSfeT88OgZ...'
 * const data = { id: 123, name: 'test' }
 *
 * const encrypted = rsaEncrypt(publicKey, data)
 * console.log('加密结果:', encrypted)
 */
