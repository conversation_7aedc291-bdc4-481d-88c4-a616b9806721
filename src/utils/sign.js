/*
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2022-01-11 10:23:44
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-01-11 17:38:05
 */

import { KJUR, hex2b64 } from 'jsrsasign'

const publicKey = 'MIICdQIBADANBgkqhkiG9w0BAQEFAASCAl8wggJbAgEAAoGBALFMQ9FMd0u+OJKgeS1gw0ZS/5wis87gnXm1zoVtCRal/f8POwhVKBSlkPvWJGyI9DOZ+Nxv5rZqCNUC74JkU+V/Yg+a4ZKiCyyWdcJ//yQc8h3I/qPEDGmPhA4rIwHvxcftbtjNNHJwfbeL0s3wpnVXRPaHBxgW7049xdwUsrqrAgMBAAECgYBYOFyebnAFzlcUDql9POKtwWrS5CmYTQfOofprRb/59wqVuZxkHwtciq3DweBJ6TOp4XmFJmEkb0qJw3Yt6YIcoX9INss5DvR4rTlj5heB3HGY0jqui9VkwBJmpvLvEPw9X7K3rLd5C21rwpzYVvZN+bO/cEADx5EJJzEWTghRsQJBAOLos+YvkQnC1fZeJ2ZTLIL8MZwZq2CRaUocduhhmpUQu5/r3fi1tqh7rBMrewIhKB0VoDHfVXMeauBOb1edbNUCQQDIB0t8hctQ7TLcjhsgveHPKKxKYl2faRkBANr/q2ww1sEw64EbX+48fOS8xjMjqZwrneiJoaEDJO2puRLHzUl/AkB36BBo62nxhowajNa1M+6pqStuGgJ+HVlOWIxdcHaHnzgG4lWCQEQt9GySFMAD+BGCjRMCR8qX8tL3hXtBCWehAkB7VYwkznZLgsW+mlx9tra5vdFl1/r0JKBHLxjZVgBSqMfb+wnTMDY706JSBz9W+OiTOD0+R4t43dcwtC2xRpHPAkByKSVCTZZes8wpwhGvdrLwx/mhLeHZJXU/RjR/sbBT/y2rtdyu3YGkykg04KKX3y0bOL4WJ9MwGt9rurFbrlhF'

export function query(obj) {
  let newObj = objKeySort(obj)

  let signPrivateKey = '-----BEGIN PRIVATE KEY-----' + publicKey + '-----END PRIVATE KEY-----'

  let sig = new KJUR.crypto.Signature({ 'alg': 'SHA256withRSA', 'prov': 'cryptojs/jsrsa', 'prvkeypem': signPrivateKey })
  var hashAlg = 'sha256'
  var sign = sig.signString(newObj, hashAlg) // 加签
  sign = hex2b64(sign)

  return sign
}

function objKeySort(obj) { // 排序的函数
  // 排序
  var newkey = Object.keys(obj).sort()
  var newObj = {}
  for (var i = 0; i < newkey.length; i++) {
    newObj[newkey[i]] = obj[newkey[i]]
  }

  // 拼接
  let paramsStr = ''
  for (var k in newObj) {
    paramsStr += k + '=' + newObj[k] + '&'
  }
  paramsStr = paramsStr.substr(0, paramsStr.length - 1)

  return paramsStr
}
