/*
 * @Descripttion: 文字超出省略 v-ellipsis
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2021-07-07 10:01:05
 * @LastEditors: 孙立政
 * @LastEditTime: 2021-07-07 10:01:51
 */

// <div v-ellipsis:100> 需要省略的文字是阿萨的副本阿萨的副本阿萨的副本阿萨的副本</div>

export default function(el, binding) {
  el.style.width = binding.arg || 100 + 'px'
  el.style.whiteSpace = 'nowrap'
  el.style.overflow = 'hidden'
  el.style.textOverflow = 'ellipsis'
}
