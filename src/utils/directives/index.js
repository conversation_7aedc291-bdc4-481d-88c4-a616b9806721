/*
 * @Descripttion: 指令
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2021-07-07 09:59:58
 * @LastEditors: 孙立政
 * @LastEditTime: 2021-07-07 10:08:13
 */
import Vue from 'vue'
import expandClick from './expandClick' // 元素点击范围扩展指令
import ellipsis from './ellipsis' // 文字超出省略
import copy from './copy' // 文本内容复制指令
import badge from './badge' // 徽标指令

const directives = {
  expandClick,
  ellipsis,
  copy,
  badge
}

Object.keys(directives).forEach(name => Vue.directive(name, directives[name]))
