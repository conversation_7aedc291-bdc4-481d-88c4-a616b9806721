/*
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2021-11-08 18:03:02
 * @LastEditors: 孙立政
 * @LastEditTime: 2023-05-26 19:41:23
 */
// 测试环境
export function test() {
  let u = navigator.userAgent
  let isiOS = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/) // ios终端
  let data = {
    'initParam': {
      'appKey': isiOS ? '6ca1b3b9_9406_40bf_92e7_07ec5e3a612a' : 'b02c2242_6ff2_46a8_b9ea_38188b73c276', // 平台下发
      'bPublicUrl': isiOS ? 'http://*************:16620/getMerchantSign' : 'http://*************:16619/getMerchantSign', // 商户url
      'bPublicKey': 'MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAkeiNH7VaUYHKiR3SxSN0LQc6c7Rpj5Ko/Yhv/7zHv97osOcyEO5DF7eUl5QQc+EmUIQ3IZA0O4EI/Q5/FXcq3FO02MDhK0uwtp5AHMYLidOwMB+cP536K9//AtSyFOc7BwFQ9lWcDYl5tfty3qTiAWZ3ANKodsEeXSDReiuNwPlic1EfI0AjCLtRlW52vCpn0p2uOjqAuOYNxrNvBfhgciUk3Jrq1jSX/UsSlB8CmoC7r/11xjDfvhBu+xMtDGpHo9C0yz4zg2uEPSwTuyERwJzkSGzJtTTwRdCOLV5yDI04C6WiIjynDYNbbVaQBqcKI0RultGUyL4aJAP/BAwNSwIDAQAB', // 商户公钥
      'sPublicUrl': isiOS ? 'https://sandbox.open.ccb.com/api/ios/' : 'https://sandbox.open.ccb.com/api/android/', // 平台url
      'sPublicKey': 'MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAjVdGe8P2JumYU4sB1Zywioxw1+HaZB/KmzrzlClj2m2e4ZPqS5cYe2FCiCwBmyizZrVYGfECugszj7e+OjbrFeqOpCgOYnXrbX0me0YdqLAowELbI4cqRxGciFlbgkjJXLoTHX/ZPPKAaF8VCNfQJrfpAqYZBeGPHWiODu4m3P8lGG5U+kHsjemdFf+5iwBabtm8IKvvE3fIfboXjnsdLSW3xxWjFK2A7iubUA8U0jA9UCCJp61qDnjmMC4hfW5QEWQMchW2D6vRYyZZStKGYxKto60jj5qnqm0+xyX9X5pBz9hTWTIqCHpzbyeLTOeG3l2jQ4ut9ZgVXP9IRj1IzwIDAQAB' // 平台公钥
    },
    'faceParam': {
      'appSecret': isiOS ? 'JjVnDQYjjJZHH86HI6O6oqTnJoHGqBaFJm45Ij2KyyxvweSvPTDxtnZAWCqg4+YXqLUYGLnG1abEVy1wZWw21aYhJIWRFSDlVGSeoFfQC6X7QwDMm9xbi7D/4ONvjhye6w/mtPcD5OMI7mcIRbG8OhTHjMGIY19JG5NfD1yBBTclcvQ7RS+PlnLAWf5w1f0Bo15q4h9kJKfTxu0dwp7XW7GtzQkO5zd8oF5Em4iUSmbwkvbMu1h+O6AMJDlcNorQC0fiXBjf49uvLCIRho+sFhd4ZPfOs5HX/J1xwW+pZu9qfWXuFeoUvZedTdBbgrP81Ma6/tlu8gF3+Cw3/W9/a+mDv/98BtgVQ+XPJtXjhvlv/xTX093hJPMlixAkGp6ABkiTaqqU0Hcn7/znAgmINW1+pS7UwRHn9gaC+DbuS1lQOwUQhVlHmU0oiscY+WEMC2KdUOXeh+/BAENxz30l+ZqJFdDMLpLtAfm6m70DfkdoccgUwrVgls+3u7k5ilONx5hGUwKrXA2TdM41UvNgDiVH2mRgrUvj7CN/odMUvYX8RNIckxYm9t1r/4Uv47Dz06Gsx923zFl7YTroJjVs/eV3eJTHYGXnpuA5VLgvUoTpOjbNq97qmzyndKVQ3Mr5TJ2KaVhQ/fTLrbMgKM227iFz/Ivntv10+Go8lc2c3ON+fEfZAjNraaTYRP/7RO04sEgRimCCmVYzRnBS0xPeWNavnLJ9yqxdK77ACR7OesPxuGMdAwnoziWiSrEfCNneIxVK2tCiTDdlQfND8WOtFyHsLgYLp+llY3A0050eIpUmzSSHR7txzsCDlC1FhbZ1XZWKtMpE6ZYxUInrCu4LB4BI1x4WKTRUMEDbb3JYNnJ5U3WVBzgbNtrQVJR53/nSOVaaa9dT4bNlHlBbuVBD5WlzM4lvHPqKR2ISBoj75hlvv4R+hVBe+uzDg8aD5UctkNeymBeKoANCrzqbEhxj8uMhxXf0Sxoq6Fp15PXBnAhTcDXnZ+7bs4hzdd/h3FFor6joucrhTGxdOjVhc2N6jWiNpFyBzOd8U/w5DtSnV2rwnysRU7CH0SYiI/nDsfN89piI0o+w7GKGn2JEDbxlUvW4AwTh+B9hV4sN45LaSS4xhfmTfgaOJKa5e8uZsu08eoy1XXD3HY3BSQheUYUh+xXfFWMoe2ktOAf1U2iAaUCdBhnl/gM/rGYe+jpCrcMjxKBx4toXanWt7Q0fvGZ66sEtegVzyt2jOrWOJxgVNuiCVIjc7W+8ygSR+tRv26xcqaQVHm/MTpFiFLdWPFF5lye7tygSjdmPMBnOpM6DAogaHQK6HPT+5dnoKT7At6BaCuVkmHPlHsNN7bwb8aIPPAt0qeXGc5G0VyEI0SZN4+weKFGMTnH8GSeDGmMR4UWQtumTUsP1cN5wrEqIzMMgCQ==' : 'JjVnDQYjjJZHH86HI6O6ovc71e0db4vR0KN6rHAiQHT48qfmTQPEdKFfKuOaSLL3UWNkNuM15a550/iOc92zqFfLHWtR3VBN7WsRJj+RmGDCuqK95WIqL6jVt8/aYW79PDg2+blX9XYA+soTo10+hS+bDBWHnlVHAI/4AydTLOWmb5xuAApT1WAihgifeRU54kYIxPi8XX/N+jgfnj2Al52TF/aqeTwX8v9+kzZFvqAus4pJD+u70MAaAXLRFOcloR33trWrUUNyWsfgS1DwS/nh0wTdinvHAnPOdshnxSigl5XvVgBjiJHUPkvJ1ZvNbMC3aT0Tn4jzOXR1gZzK52/UCgdk/4+Rs4sx8/XJxStgwAYXW/zBI347WmZna/zBGtCaoIiB+80EFWY5HUX2R1YPe8+kxRiNVspLJ0GOfjWmOBKpdzrjcRgSfGuNq23rn+sYkKHNeouCT3cbMa8FU4wkRF9D/eS/de6tqpE6ajX2RTsvQ4m03B2tz1r72FDGo15q4h9kJKfTxu0dwp7XW7GtzQkO5zd8oF5Em4iUSmbwkvbMu1h+O6AMJDlcNorQC0fiXBjf49uvLCIRho+sFhd4ZPfOs5HX/J1xwW+pZu9qfWXuFeoUvZedTdBbgrP81Ma6/tlu8gF3+Cw3/W9/a+mDv/98BtgVQ+XPJtXjhvlv/xTX093hJPMlixAkGp6ABkiTaqqU0Hcn7/znAgmINW1+pS7UwRHn9gaC+DbuS1lQOwUQhVlHmU0oiscY+WEMC2KdUOXeh+/BAENxz30l+ZqJFdDMLpLtAfm6m70DfkdoccgUwrVgls+3u7k5ilONx5hGUwKrXA2TdM41UvNgDiVH2mRgrUvj7CN/odMUvYX8RNIckxYm9t1r/4Uv47Dz06Gsx923zFl7YTroJjVs/eV3eJTHYGXnpuA5VLgvUoTpOjbNq97qmzyndKVQ3Mr5TJ2KaVhQ/fTLrbMgKM227hCg6ZkgViZvl3L+kSH9VJKQYe6rxGl9jJzsYTzG2nt/Zt/HNxQStJavTWZZPFQ5P6K0G+ebTxSK7HUE+ri/KP5J8qwhbwbH9nyNzGL9MBTE5Ko/Aklp/NKlU/VVnTjgWaHX8djNg1/LDLo3iZHmmNI89WvOS202JVGM5VCvjk0e7Pg9QZHLDg8u57hXxMEj1zRdYOmBJf5qHsei+BNoJjc+CDDYbZ9l9sX+Z1ZgypzNQ7bO8DjdSZUdpUz1acEbQXNwW8JQIj35srcuLPUemWgiC1W+H5966F7RIc3Ps8dxl7hsc0gfs9qOMWlkZFrxArnJJB05XZz/qbnNFepo7Mx7PUFdZAaCHqRToyh43+ZN37/ruid5udfjOT5GumXtHiH6djBJJSXvxEs6nsv9T3A=', // 人脸识别权限秘钥
      'safeConsoleUrl': 'http://124.127.94.35:8801/NCCBPL4/CCBCommonTXRoute', // 人脸识别网络环境Url
      'channelId': '12009001', // 渠道号
      'channelTxnCode': 'FACE-GKZHCH', // 渠道交易号
      // 设置活体检测异常上送 ,默认不上送。请与对接人确认本场景是否支持活检异常上送
      'isDetectExportInfo': false,
      'isDetectFailReg': false
    },
    'bizParam': {
      'productId': 'ShrWlt', // 产品id ，必填
      'sceneId': 'Main_Standard', // 业务场景id，只有一个场景入口传“Main”，必填
      'thirdParams': {
        'TrdPt_Cst_OrCd': localStorage.getItem('userNo')
      }, // 第三方填写传给H5的数据（Map对象）
      'closeBtnColor': '' // 产品页面“关闭”按钮,传””或”Default”即可;隐藏此按钮需调用
    }
  }
  return data
}

// 生产环境
export function prod() {
  let u = navigator.userAgent
  let isiOS = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/) // ios终端
  let data = {
    'initParam': {
      'appKey': isiOS ? 'bc47acad_317e_48e3_b73b_15958d72d6ef' : 'a812d004_7b8e_4931_bc99_97222b8dbe55', // 平台下发
      'bPublicUrl': isiOS ? 'http://*************:16620/getMerchantSign' : 'http://*************:16619/getMerchantSign', // 商户url
      'bPublicKey': 'MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAlXptvd7TwKP/lOAOpXBnUWgRGZXRyUFbvk7R6OKcRc7iPXwumvKotxyN2IAKQYkIrU4D43cRCK7Mg3KI316P3LOZOcoWW2MFAx+Dnebv1vhPNhXAwnYFcX3O3yruRyHVbwXbH/u5IV3lsFoRwnQI/SSG+boKyM2gL2nHiRrsvBDw4nvFSVMrAsATbCXL//1IJQBkDrs2vzKN5DrNNjQGIsjlERj620cW+RcliSIYBTN4WjFl4R/FnLBchRJQlMJYJpusUo3djXiA0lAtYhkE4OJTB2g6ZJMGAJKLToTr/ArD1O8AkXKE8T0PWJ4FHZyRCu8IgOCleSC9Ii61piBfYQIDAQAB', // 商户公钥
      'sPublicUrl': isiOS ? 'https://open.ccb.cn/api/ios' : 'https://open.ccb.cn/api/android', // 平台url
      'sPublicKey': 'MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAgQ6fYsQ+KBx4wVGbsGV1uaCLpZaLMUZRoawliWrUsW59DbIRhq600LOsV5/ogW7M4ZM9wEu2mXyPVP+QOgu1a0liCxfyvpue4F10TDBmCRO3OAE8rq3TkHS+cl1Q1XUSUkzr8jXLesEcaUZY91EUCI6PfAi9B9IRmPN6zB4kNw+6oLoJXAcr/vMOkWlFBchWRKa1PCYVTVEGEKiiHURfPan7VJ1KvlLmp5IPquF58OfspZWlAxrcZWDCqYnywuPhyF3s3kF3pKV4vF6LReVwA1VmZjP86PwvKn3BuEnie0PD79rws1IkIGclXR6MSFgIhLaLQFLNS/unSYADm/jQ2QIDAQAB' // 平台公钥
    },
    'faceParam': {
      'appSecret': isiOS ? '8PPrlOYWm4b/JAx2cGVhkgL0hGdtWunoQDT1FFQsTMgKgbeiW+mFmzYKPE6IyhQb5sKRC9dfvRDM+63781UUUqYhJIWRFSDlVGSeoFfQC6X1r4AdFQ8HSWSowZhE7IGXoPJLOa/x1IEKz/93tfik7393pI4IYCxhlRW2fy/9xIglcvQ7RS+PlnLAWf5w1f0Bo15q4h9kJKfTxu0dwp7XW7GtzQkO5zd8oF5Em4iUSmbwkvbMu1h+O6AMJDlcNorQC0fiXBjf49uvLCIRho+sFhd4ZPfOs5HX/J1xwW+pZu9qfWXuFeoUvZedTdBbgrP81Ma6/tlu8gF3+Cw3/W9/a+mDv/98BtgVQ+XPJtXjhvlv/xTX093hJPMlixAkGp6ABkiTaqqU0Hcn7/znAgmINW1+pS7UwRHn9gaC+DbuS1lQOwUQhVlHmU0oiscY+WEMC2KdUOXeh+/BAENxz30l+ZqJFdDMLpLtAfm6m70DfkdoccgUwrVgls+3u7k5ilONx5hGUwKrXA2TdM41UvNgDiVH2mRgrUvj7CN/odMUvYX8RNIckxYm9t1r/4Uv47Dz06Gsx923zFl7YTroJjVs/eV3eJTHYGXnpuA5VLgvUoTpOjbNq97qmzyndKVQ3Mr5TJ2KaVhQ/fTLrbMgKM227iFz/Ivntv10+Go8lc2c3ON0CzNxgrE5/QiYawsYMB2F5/q9Fjan9/TzxnzjfJ4OCDFE2kQqXk14hvS088yvQAWsy7bhPuJqu76ggkiDFfH1IxVK2tCiTDdlQfND8WOtFyHsLgYLp+llY3A0050eIpWkk0JwrKTAr7rvv39eyhuDk/bkWP+x6HcYkcejnjcFldFNinTTw5oKAe1s5384V/95U3WVBzgbNtrQVJR53/nSOVaaa9dT4bNlHlBbuVBD5WlzM4lvHPqKR2ISBoj75hlvv4R+hVBe+uzDg8aD5UctkNeymBeKoANCrzqbEhxj8uMhxXf0Sxoq6Fp15PXBnAgkUTCwSJAlgC/vUvMmIO5BkooyBJd1uUALjF6DciVId87HCdPj1Ek+qTBeUll2Bzzr2PyS4zkTygV+gdRPpyfy9piI0o+w7GKGn2JEDbxlUvA2kXxXdokYwJo99M/fEldMlJJkamFz58SbB3SKB8s8eoy1XXD3HY3BSQheUYUh+xW9rvUNjqV5EdYpKFKAtRZiKZ+m8zQVCerK45ietuanAYic50drSCU2ceW1JDwlTF7hdGa85rm+wl7oHkjA8PS0alJ7zwvV3dLNBU0hJeHYGQ99TAo+NqSQfQHK6QKx25JXeTFPdiiYpPAIua5YeHrUFSiXYtr/9LjGHDsyIgty4Vhp/3dqhDjmGaj3FeFNGNGnnJA4c7C/QKf0JoyMg5C7utvRyeLr93IDYea2J9GeYfOtnExiR9xs91gsXpHDDw==' : '8PPrlOYWm4b/JAx2cGVhkvVlrokBKiAV5Xv1s1vUJ/kgRUBxdi0DVy0gmfF77VEsIWX3MCGdabRHnWCEkvGheCVvVCN7/9+wn5ARYZDKJtbw9o926AfoZ0/7Guglxyrhrvop/ziNu5Yvaf6edVFkx8NRq7FmHoLC4ghnQaqBgTa0q9uH87JK7jSusLG+c09wROvUfLiroqbVPnhS7DvySb6Ehq/DR/q/rncJQdroDdQmwPH6faQRKZHp8+ekcKg/SX/5k5DwqJYPUK7J75J/4xngsj3OGpb+lWgE4IJEmepyaDACA2SOb+q5Vs41nnRqbkbQ9rAiuVilDtFRCO5Pw2/UCgdk/4+Rs4sx8/XJxStmJx+fbOfgqCcpDjwse5yWr+KKJWHhaGtSoK8ce9f30/p9XgDj4YZ/CClLjjKIDZymOBKpdzrjcRgSfGuNq23rn+sYkKHNeouCT3cbMa8FU4wkRF9D/eS/de6tqpE6ajX2RTsvQ4m03B2tz1r72FDGo15q4h9kJKfTxu0dwp7XW7GtzQkO5zd8oF5Em4iUSmbwkvbMu1h+O6AMJDlcNorQC0fiXBjf49uvLCIRho+sFhd4ZPfOs5HX/J1xwW+pZu9qfWXuFeoUvZedTdBbgrP81Ma6/tlu8gF3+Cw3/W9/a+mDv/98BtgVQ+XPJtXjhvlv/xTX093hJPMlixAkGp6ABkiTaqqU0Hcn7/znAgmINW1+pS7UwRHn9gaC+DbuS1lQOwUQhVlHmU0oiscY+WEMC2KdUOXeh+/BAENxz30l+ZqJFdDMLpLtAfm6m70DfkdoccgUwrVgls+3u7k5ilONx5hGUwKrXA2TdM41UvNgDiVH2mRgrUvj7CN/odMUvYX8RNIckxYm9t1r/4Uv47Dz06Gsx923zFl7YTroJjVs/eV3eJTHYGXnpuA5VLgvUoTpOjbNq97qmzyndKVQ3Mr5TJ2KaVhQ/fTLrbMgKM227hCg6ZkgViZvl3L+kSH9VJKQYe6rxGl9jJzsYTzG2nt/Zt/HNxQStJavTWZZPFQ5PxZzHwSJaO1sh4YRn7URF3c2IkLEND3BLMlJx8stIQiYK1O+aLVOlxxmAB0X/43I0wAgQwq+AHo73kIA30k4z4s45piiQ724ciEUr+F+6XqEIzRN4QLIQJewoJHDq3GXhRWa1RPQ6qsysXzr5M/V8QtGutyrJIhsuQUfMT8pFGQ9B/TnKYDummrBa1zc6bS5zD5539YHRw9Vx36ZHgfIxspvgblVEYy6jn3DuCJEwuUf59aY+URZAzofR5FUnO9HgOLWfv5wCrBZ+ixQgVOkjhVOFhKPQ7ruHdi6R9Z0WpjM', // 人脸识别权限秘钥
      'safeConsoleUrl': '', // 人脸识别网络环境Url
      'channelId': '12009001', // 渠道号
      'channelTxnCode': 'FACE-GKZHCH', // 渠道交易号
      // 设置活体检测异常上送 ,默认不上送。请与对接人确认本场景是否支持活检异常上送
      'isDetectExportInfo': false,
      'isDetectFailReg': false
    },
    'bizParam': {
      'productId': 'ShrWlt', // 产品id ，必填
      'sceneId': 'Main_Standard', // 业务场景id，只有一个场景入口传“Main”，必填
      'thirdParams': {
        'TrdPt_Cst_OrCd': localStorage.getItem('userNo')
      }, // 第三方填写传给H5的数据（Map对象）
      'closeBtnColor': '' // 产品页面“关闭”按钮,传””或”Default”即可;隐藏此按钮需调用
    }
  }
  return data
}
