/*
 * @Descripttion: oss图片
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2021-07-28 10:59:36
 * @LastEditors: 孙立政
 * @LastEditTime: 2021-07-28 11:19:31
 */

import OSS from 'ali-oss'

export function upImg(file) {
  let client = new OSS({
    region: 'oss-cn-hangzhou',
    accessKeyId: 'LTAI5tJrypdaZZMSWvSEzDAG',
    accessKeySecret: '******************************',
    bucket: 'diandi-video'
  })
  let now = Date.parse(new Date())
  let names = now + file.name
  return client.put('face/' + names, file).then(function(res) {
    return res.url
  }).catch(function(err) {
    console.log(err)
  })
}

