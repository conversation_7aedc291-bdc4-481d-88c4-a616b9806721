/*
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2021-07-17 10:20:42
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-06-22 18:12:10
 */
var dayjs = require('dayjs')
function getDateArray(startDate, endDate, space) {
  if (!endDate) {
    endDate = new Date()
  }
  if (!startDate) {
    startDate = new Date(dayjs().valueOf() - 1 * 60 * 60 * 1000)
  }
  if (!space) {
    space = 30 * 60 * 1000
  } else {
    space = space * 60 * 1000
  }
  var endTime = dayjs(endDate).valueOf()
  var startTime = dayjs(startDate).valueOf()
  var mod = endTime - startTime
  //   if (mod <= space) {
  //     alert('时间太短')
  //     return
  //   }
  var dateArray = []
  while (mod >= space) {
    var d = new Date()
    d.setTime(startTime + space)
    dateArray.push(d)
    mod = mod - space
    startTime = startTime + space
  }
  // eslint-disable-next-line no-unused-vars
  var end = endDate.getTime()
  var start = dayjs(startDate).valueOf()
  dateArray.unshift(new Date(start)) // 插入开头时间
  // 正序
  return dateArray.sort(function(a, b) {
    return Date.parse(b) - Date.parse(a)
  })
}

// 时间格式化
function timeFormat(dt) {
  return (
    dayjs(dt).format('HH:mm')

  )
}

const formatInt = (num, prec = 1, ceil = true) => {
  const len = String(num).length
  if (len <= prec) { return num }

  const mult = Math.pow(10, prec)
  return ceil
    ? Math.ceil(Number(num) / mult) * mult
    : Math.floor(Number(num) / mult) * mult
}

export function formatDate(a, b, c) {
  if (b == '24:00') {
    b = '23:59:59'
  }

  let today = dayjs().format('YYYY/MM/DD')

  // 开始时间
  let startTime = today + ' ' + a
  startTime = dayjs(startTime).add(c, 'minute').format('HH:mm:ss').split(':')
  // 结束时间
  let endTime = b.split(':')
  if (Number(startTime[1]) < 10) {
    startTime[1] = '10'
  } else {
    startTime[1] = formatInt(startTime[1])
  }
  if (startTime[1] == 60 && Number(startTime[0]) < 24) {
    startTime[0] = Number(startTime[0]) + 1
    startTime[1] = '00'
  }
  if (startTime[0] == 24) {
    startTime = ['23', '59', '59']
  }

  startTime = today + ' ' + startTime.join(':')

  endTime = today + ' ' + endTime.join(':')
  var aa = new Date(startTime)
  var bb = new Date(endTime)

  if (!a) {
    var result = getDateArray()
  } else {
    // eslint-disable-next-line no-redeclare
    var result = getDateArray(aa, bb, 20)
  }
  let newdata = []
  for (var i = result.length - 1; i >= 0; i--) {
    newdata.push(timeFormat(result[i]))
  }

  return newdata
}

export function formatDateTwo(a, b, c) {
  let data = []
  let action = dayjs(a).hour()
  let end = dayjs(b).hour()
  let endminute = dayjs(b).minute()
  for (let i = action; i < end; i++) {
    data.push((i < 10 ? '0' + i : i) + ':00', (i < 10 ? '0' + i : i) + ':30')
  }
  data.push((end < 10 ? '0' + end : end) + ':' + (endminute == 0 ? '00' : endminute))
  return data
}

export function getBeforeDate(ns) {
  let timedata = []

  for (let i = 1; i <= ns; i++) {
    timedata.push(dayjs(new Date().getTime() + 86400000 * i).format('YYYY-MM-DD'))
  }

  return timedata
  // this.calendarDate = new Date(s)
}
