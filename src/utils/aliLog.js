/*
 * @Descripttion: 阿里监控
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2021-06-15 10:38:09
 * @LastEditors: 孙立政
 * @LastEditTime: 2021-07-05 17:26:57
 */
const BrowserLogger = require('alife-logger')
import { pids } from '@/config/settings.js'

const logger = (() => {
  try {
    return BrowserLogger.singleton({
      pid: pids,
      appType: 'web',
      imgUrl: 'https://arms-retcode.aliyuncs.com/r.png?',
      sendResource: true,
      enableLinkTrace: true,
      behavior: true,
      useFmp: true
    })
  } catch (e) {
    console.error(e)
  }
})()

logger.setConfig({
  setUsername: function() {
    return String(localStorage.getItem('userId'))
  }
})

export default logger
