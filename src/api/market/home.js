/*
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2022-06-16 14:28:08
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-06-23 21:12:30
 */
import request from '@/utils/request'

// 首页列表
export const mallGoodsShelvesShowList = data => {
  return request({
    url: `/app/mall/goods/mallGoodsShelvesShowList`,
    method: 'post',
    data: data
  })
}

// 首页拼团商品列表
export const mallPtGoodsShowList = data => {
  return request({
    url: `/app/campaign/index/${data.activityNo}/1/5`,
    method: 'post'
  })
}

// 单商品详情
export const goodsShelvesDetails = data => {
  return request({
    url: `/app/mall/goods/goodsShelvesDetails`,
    method: 'post',
    data: data
  })
}

// 首页拼团商品活动列表
export const pointActivityDisplay = data => {
  return request({
    url: `/app/campaign/pointActivityDisplay/${data.activityNo}?limitNum=5`,
    method: 'get'
  })
}

// 根据商品id查询商品上下架状态
export const goodsStatus = data => {
  return request({
    url: `/app/mall/goods/goodsStatus/${data}`,
    method: 'get',
    data: data
  })
}
