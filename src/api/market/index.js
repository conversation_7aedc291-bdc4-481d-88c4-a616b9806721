/*
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2022-05-05 20:10:09
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-08-08 18:38:58
 */
import request from '@/utils/request'

// 活动列表
export const getActivityList = data => {
  return request({
    url: `/app/campaign/index/${data.activityNo}/${data.pageNum}/${data.pageSize}`,
    method: 'post',
    data: data
  })
}

// 活动详情
export const getActivityDetail = data => {
  return request({
    url: `/app/campaign/detail/${data}`,
    method: 'post',
    data: data
  })
}

// 活动详情V2
export const getActivityDetailV2 = data => {
  return request({
    url: `/app/campaign/v2/detail`,
    method: 'post',
    data: data
  })
}

// 拼团详情
export const detail = data => {
  return request({
    url: `/app/campaign/detail/${data.activityJoinNo}`,
    method: 'post',
    data: data
  })
}

// 获取地址
export const listByGoods = data => {
  return request({
    url: `/v2/app/address/listByGoods`,
    method: 'get',
    params: data
  })
}

// 活动/商品详情--前两个可参与的拼团列表及商家活动状态(3秒刷新)
export const getTopTwoGroupWithActivityJoinStatus = data => {
  return request({
    url: `/app/campaign/getTopTwoGroupWithActivityJoinStatus/${data.activityJoinNo}`,
    method: 'post',
    data: data
  })
}
// 查看全部/可参与的拼团
export const moreGroup = data => {
  return request({
    url: `/app/campaign/moreGroup/${data.activityJoinNo}`,
    method: 'post',
    data: data
  })
}

// 拼团校验(获取当前在该商家参与活动下的待支付订单)
export const userJoinedOrderValid = data => {
  return request({
    url: `/app/campaign/userJoinedOrderValid/${data.activityJoinNo}`,
    method: 'post',
    data: data
  })
}

// 拼团校验-查询订单详情查询订单详情
export const findUnPayByActiveNo = data => {
  return request({
    url: `/app/mall/order/findUnPayByActiveNo/${data.activeNo}`,
    method: 'get',
    data: data
  })
}
// 根据团号判断该团是否已满(true为已满，false为有空)
export const judgeGroupIsFull = data => {
  return request({
    url: `/app/campaign/judgeGroupIsFull/${data.groupNo}`,
    method: 'post',
    data: data
  })
}

// 分页查看订单信息
export const getOrderList = data => {
  return request({
    url: `/app/mall/order/page`,
    method: 'post',
    data: data
  })
}
// 查询订单详情
export const findByOrderNo = data => {
  return request({
    url: `/app/mall/order/findByOrderNo/${data.orderNo}`,
    method: 'get',
    data: data
  })
}

// 拼团详情
export const cancelOrder = data => {
  return request({
    url: `/app/mall/order/cancelOrder/${data.orderNo}`,
    method: 'post',
    data: data
  })
}
// 订单取消
export const userJoinedGroupDetail = data => {
  return request({
    url: `/app/campaign/userJoinedGroupDetail`,
    method: 'post',
    data: data
  })
}
// 订单退款
export const refundApply = data => {
  return request({
    url: `/app/mall/order/refundApply`,
    method: 'post',
    data: data
  })
}

// 下单
export const createOrder = data => {
  return request({
    url: `/app/mall/order/createOrder`,
    method: 'post',
    data: data
  })
}

// 货架下单
export const createNormalOrder = data => {
  return request({
    url: `/app/mall/order/createNormalOrder`,
    method: 'post',
    data: data
  })
}

// 支付订单
export const payOrder = data => {
  return request({
    url: `/v4/app/mall/order/payOrder`,
    method: 'post',
    data: data
  })
}

// 支付方式获取
export const payStyle = data => {
  return request({
    url: `/v2/app/pay/payStyle/${data.configId}/${data.orderType}`,
    method: 'get'
  })
}

// 获取账户余额
export const mallOrderUserAccount = data => {
  return request({
    url: '/v2/app/capital/mallOrder/' + data,
    method: 'post'
  })
}

// 获取app首页菜单商城/发现开关
export const getAppMallMenu = data => {
  return request({
    url: `/v2/config/getAppMallMenu/${data.poolId}`,
    method: 'get',
    data: data
  })
}
// 获取app商城客服电话
export const getMallServicePhone = data => {
  return request({
    url: `/v2/config/getMallServicePhone/${data.poolId}`,
    method: 'post',
    data: data
  })
}

// 下单时自提门店查询设置自提时间
export const selectSelfFechTimeByLocationId = data => {
  return request({
    url: `/app/mall/order/selectSelfFechTimeByLocationId/${data}`,
    method: 'get'
  })
}

