/*
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2021-07-30 11:00:45
 * @LastEditors: 孙立政
 * @LastEditTime: 2023-05-20 16:29:47
 */
import request from '@/utils/request'
// import {
//   baseURLSchool
// } from '@/config/settings'

export const getBindByUserId = () => { // 获取绑定用户
  return request({
    url: '/v2/app/thirdUser/getBindByUserId',
    method: 'get'
  })
}

export const getBindByUserIdHik = () => { // 获取绑定用户---海康平台
  return request({
    url: '/v2/app/hik/getBindByUserId',
    method: 'get'
  })
}

// 人脸-ruku
export const uploads = data => {
  return request({
    url: '/v2/app/thirdUser/upload',
    method: 'post',
    data: data
  })
}

// 流水
export const getFlow = data => {
  return request({
    url: '/v2/app/thirdUser/getFlow',
    method: 'post',
    data: data
  })
}

// 判断是否存在绑定关系
export const getBindExist = data => {
  return request({
    url: '/v2/app/thirdUser/getBindExist',
    method: 'get',
    data: data
  })
}

// 充值记录查询
export const getRechargeRecord = data => {
  return request({
    url: '/v2/app/thirdUser/getRechargeRecord',
    method: 'post',
    data: data
  })
}

// 充值记录查询2
export const getAllRechargeRecord = data => {
  return request({
    url: '/v2/app/thirdUser/getAllRechargeRecord',
    method: 'post',
    data: data
  })
}

// 智慧校园线上充值
export const payBlance = data => {
  return request({
    url: '/v4/app/order/rechargeThird/pay',
    method: 'post',
    data: data
  })
}

// 获取考勤记录
export const selectAttendanceRecord = data => {
  return request({
    url: '/v2/app/thirdUser/getAccessCtrl',
    method: 'post',
    data
  })
}

// 退款记录
export const selectRefundByUserId = data => {
  return request({
    url: `/app/marketWalletTrans/selectRefundByUserId/${data.thirdUserNo}`,
    method: 'post',
    data
  })
}

// 海康-流水记录
export const getFlowHik = data => {
  return request({
    url: '/v2/app/hik/getFlow',
    method: 'post',
    data
  })
}

// 海康-重置记录
export const getRechargeRecordHik = data => {
  return request({
    url: '/v2/app/hik/getRechargeRecord',
    method: 'post',
    data
  })
}

/**
 * @description: 退款
 */

// 绑定银行卡
export const bindingBankCard = data => {
  return request({
    url: '/app/bank/binding',
    method: 'post',
    data
  })
}

// 绑定银行卡
export const updateBankCard = data => {
  return request({
    url: '/app/bank/update',
    method: 'post',
    data
  })
}

// 用户查询银行卡绑定信息
export const findByUserBankCard = data => {
  return request({
    url: `/app/bank/findByUser/${data}`,
    method: 'post',
    data
  })
}

// 海康用户学生余额退款
export const balanceRefund = data => {
  return request({
    url: `/v2/app/hik/balanceRefund/${data.personId}/${data.amount}`,
    method: 'post',
    data
  })
}

// 智慧校园普通用户余额退款
export const balanceRefundBase = data => {
  return request({
    url: `/v2/app/thirdUser/balanceRefund`,
    method: 'post',
    data
  })
}

// 海康用户学生余额退记录
export const refundList = data => {
  return request({
    url: `/v2/app/hik/refundList`,
    method: 'post',
    data
  })
}
