/*
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2022-09-22 09:51:13
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-09-22 21:41:01
 */
import request from '@/utils/request'

// 美食套餐团购订单-分页查看订单信息
export const orderList = data => {
  return request({
    url: '/app/fsm/order/page',
    method: 'post',
    data
  })
}

// 查询订单详情
export const findByOrderNo = data => {
  return request({
    url: `/app/fsm/order/findByOrderNo/${data}`,
    method: 'get',
    data
  })
}

// 订单下单
export const createOrder = data => {
  return request({
    url: `/app/fsm/order/createOrder`,
    method: 'post',
    data
  })
}

// 订单取消
export const cancelOrder = data => {
  return request({
    url: `/app/fsm/order/cancelOrder/${data}`,
    method: 'post',
    data
  })
}

// 获取店铺信息
export const findMarketDistance = data => {
  return request({
    url: `/app/fsm/order/findMarketDistance`,
    method: 'post',
    data
  })
}

// 获取订单进度
export const findOrderProgressListByOrderNo = data => {
  return request({
    url: `/app/fsm/order/findOrderProgressListByOrderNo/${data}`,
    method: 'post',
    data
  })
}

// 获取订单退款进度
export const findOrderRefundProgressListByOrderNo = data => {
  return request({
    url: `/app/fsm/order/findOrderRefundProgressListByOrderNo/${data}`,
    method: 'post',
    data
  })
}

// 获取订单退款单
export const getRefundByOrderNo = data => {
  return request({
    url: `/app/fsm/order/getRefundByOrderNo/${data}`,
    method: 'get',
    data
  })
}

// 订单退款
export const refundApply = data => {
  return request({
    url: `/app/fsm/order/refundApply`,
    method: 'post',
    data
  })
}

// 订单任务
export const orderJob = data => {
  return request({
    url: `/app/fsm/order/orderJob/${data}`,
    method: 'post',
    data
  })
}

// 支付订单
export const payOrder = data => {
  return request({
    url: `/v4/app/fsm/order/payOrder`,
    method: 'post',
    data
  })
}

// 查看券码
export const findVerificationCodeByOrderNo = data => {
  return request({
    url: `/app/fsm/order/findVerificationCodeByOrderNo/${data}`,
    method: 'get',
    data
  })
}

// 订单对应池资金账户
export const fsmOrderCapital = data => {
  return request({
    url: `/v2/app/capital/fsmOrder/${data}`,
    method: 'post',
    data
  })
}
