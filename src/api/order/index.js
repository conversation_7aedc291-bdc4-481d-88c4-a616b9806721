/*
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-05-31 16:05:42
 * @LastEditors: 孙立政
 * @LastEditTime: 2021-07-19 18:07:41
 */
import request from '@/utils/request'

// 获取订单列表
export const orderList = data => {
  return request({
    url: '/v2/app/order/pageV2',
    method: 'post',
    data
  })
}
// 取消订单
export const cancelOrder = data => {
  return request({
    url: '/v2/app/order/takeout/cancel/' + data,
    method: 'post',
    data
  })
}
// 处理退款
export const sureRefund = data => {
  return request({
    url: '/v2/app/order/takeout/refund',
    method: 'post',
    data
  })
}
// 获取订单详情
export const orderDetail = data => {
  return request({
    url: '/v2/app/order/takeout/detail/' + data,
    method: 'get',
    data
  })
}
// 修改地址获取
export const updateAddressList = data => {
  return request({
    url: '/v2/app/order/takeout/updateAddressList/' + data,
    method: 'get',
    data
  })
}

// 确认改地址
export const updateAddress = data => {
  return request({
    url: '/v2/app/order/takeout/address/update',
    method: 'post',
    data
  })
}
// 骑手位置
export const takeoutcouLoc = data => {
  return request({
    url: '/v2/app/order/takeout/cou_loc/' + data,
    method: 'get',
    data
  })
}
// 物流
export const getLogistics = data => {
  return request({
    url: `/v2/app/station/getLogistics/${data.agentLogisticsNo}/${data.phone}`,
    method: 'get',
    data
  })
}
// 平台订单
export const selfEmployed = data => {
  return request({
    url: `/v2/app/order/selfEmployed/detail/${data}`,
    method: 'get'
  })
}

// 自营商城取消订单
export const selfEmployedCancel = data => {
  return request({
    url: '/v2/app/order/selfEmployed/cancel/' + data,
    method: 'post',
    data
  })
}
// 确认收货
export const complete = data => {
  return request({
    url: '/v2/app/order/takeout/complete/' + data,
    method: 'post',
    data
  })
}
