/*
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-06-05 14:53:26
 * @LastEditors: zhaoyuxin
 * @LastEditTime: 2021-06-05 14:55:07
 */
import request from '@/utils/request'

export const getTbTripArticle = data => { // 获取列表
  return request({
    url: `/app/tbTripArticle/list/3/${data}/1`,
    method: 'get'
  })
}

export const getDetail = data => { // 获取详情
  return request({
    url: '/app/tbTripArticle/detail/' + data,
    method: 'post'
  })
}
