/*
 * @Descripttion:
 * @version: 1.0.0
 * @Author: zhaoyuxin
 * @Date: 2021-05-08 17:37:12
 * @LastEditors: zhaoyuxin
 * @LastEditTime: 2021-05-27 17:33:00
 */
import request from '@/utils/request'

// 发表评价
export const addEvaluate = data => {
  return request({
    url: '/v2/app/evaluation/evaluate',
    method: 'post',
    headers: {
      'content-type': 'application/json',
      'Token': localStorage.getItem('token')
    },
    data: data
  })
}
// 追加评价
export const appendComment = data => {
  return request({
    url: '/v2/app/evaluation/appendComment',
    method: 'post',
    headers: {
      'content-type': 'application/json',
      'Token': localStorage.getItem('token')
    },
    data: data
  })
}
// 删除评价
export const deleteComment = data => {
  return request({
    url: '/v2/app/evaluation/delete',
    method: 'post',
    headers: {
      'content-type': 'application/json',
      'Token': localStorage.getItem('token')
    },
    data: data
  })
}
// 根据店铺类型和订单类型获取评价类型
export const marketEvaluationType = data => {
  return request({
    url: `/v2/app/marketEvaluationType/find/${data.marketType}/${data.orderType}`,
    method: 'get',
    headers: {
      'content-type': 'application/json',
      'Token': localStorage.getItem('token')
    }
  })
}

// 我的评价
export const queryMyEva = data => {
  return request({
    url: '/v2/app/evaluation/market/queryMyEva',
    method: 'post',
    headers: {
      'content-type': 'application/json',
      'Token': localStorage.getItem('token')
    },
    data: data
  })
}

// 店铺评价
export const queryByType = data => {
  return request({
    url: '/v2/app/evaluation/market/queryByType',
    method: 'post',
    headers: {
      'content-type': 'application/json',
      'Token': localStorage.getItem('token')
    },
    data: data
  })
}
// 获取店铺总体评分
export const marketRate = data => {
  return request({
    url: '/v2/app/marketEvaluation/getTotal',
    method: 'post',
    headers: {
      'content-type': 'application/json',
      'Token': localStorage.getItem('token')
    },
    data: data
  })
}
