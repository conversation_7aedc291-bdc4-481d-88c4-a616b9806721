/*
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <PERSON>hao<PERSON><PERSON>
 * @Date: 2021-04-07 16:01:54
 * @LastEditors: 孙立政
 * @LastEditTime: 2021-12-29 20:22:01
 */
import request from '@/utils/request'
import {
  baseUrlWS
} from '@/config/settings'

export const postClean = data => { // 发布保洁需求
  return request({
    url: baseUrlWS + '/bee_8modules/app/pd/clean',
    method: 'post',
    data: data
  })
}
export const postRepair = data => { // 发布维修需求
  return request({
    url: baseUrlWS + '/bee_8modules/app/pd/maintain',
    method: 'post',
    data: data
  })
}
export const postRecru = data => { // 发布招聘需求
  return request({
    url: baseUrlWS + '/bee_8modules/app/pd/job',
    method: 'post',
    data: data
  })
}
export const getBanner = () => { // 首页banner
  return request({
    url: baseUrlWS + '/bee_8modules/app/activity/banner',
    method: 'get'
  })
}
export const getRecommend = () => { // 首页精选推荐
  return request({
    url: baseUrlWS + '/bee_8modules/app/activity/show',
    method: 'get'
  })
}
export const getCleanList = data => { // 服务项目列表-家庭保洁
  return request({
    url: baseUrlWS + '/bee_8modules/app/hc/item/' + data.typeName + '?limit=' + data.pageSize + '&page=' + data.pageNum,
    method: 'post'
  })
}
export const getRecruList = data => { // 服务项目列表-招聘信息
  return request({
    url: baseUrlWS + '/bee_8modules/app/hc/recruit/' + data.typeName + '?limit=' + data.pageSize + '&page=' + data.pageNum,
    method: 'post',
    data: data
  })
}
export const getRelatedList = data => { // 服务项目列表-招聘详情底下列表
  return request({
    url: baseUrlWS + '/bee_8modules/app/hc/recruit/' + data.id,
    method: 'get',
    data: data
  })
}
export const getRecruList1 = data => { // 服务项目列表-招聘信息搜索
  return request({
    url: baseUrlWS + '/bee_8modules/app/hc/recruit/' + data.typeName + '/' + '?limit=' + data.pageSize + '&page=' + data.pageNum + '&query=' + data.query,
    method: 'post',
    data: data
  })
}
export const getListDetail = data => { // 服务项目列表-维修-保洁项目详情
  return request({
    url: baseUrlWS + '/bee_8modules/app/hc/detail/' + data.id,
    method: 'get'
  })
}
export const getListDetailRT = data => { // 服务项目列表-招聘项目详情
  return request({
    url: baseUrlWS + '/bee_8modules/app/hc/rt/' + data.id,
    method: 'get'
  })
}
export const recruAppoint = data => { // 招聘预约
  return request({
    url: baseUrlWS + '/bee_8modules/app/order/job',
    method: 'post',
    data: data
  })
}
export const cleanAppoint = (data, type) => { // 保洁（/app/order/placeCleanOrder），维修预约（/app/order/placeMaintainOrder）
  return request({
    url: baseUrlWS + '/bee_8modules/app/order/' + type,
    method: 'post',
    data: data
  })
}
export const allAppointOrder = data => { // 我的预约全部订单
  return request({
    url: baseUrlWS + '/bee_8modules/app/order/apo/' + data.userId + '?limit=' + data.pageSize + '&page=' + data.pageNum,
    method: 'get'
  })
}
export const pendingOrder = data => { // 我的预约发布中订单
  return request({
    url: baseUrlWS + '/bee_8modules/app/order/pending/' + data.userId + '?limit=' + data.pageSize + '&page=' + data.pageNum,
    method: 'get'
  })
}
export const finishOrder = data => { // 我的预约已完成订单
  return request({
    url: baseUrlWS + '/bee_8modules/app/order/finish/' + data.userId + '?limit=' + data.pageSize + '&page=' + data.pageNum,
    method: 'get'
  })
}
export const cancelOrder = data => { // 我的预约取消订单
  return request({
    url: baseUrlWS + '/bee_8modules/app/order/cl/' + data.userId + '?limit=' + data.pageSize + '&page=' + data.pageNum,
    method: 'get'
  })
}
export const btnOrder = data => { // 我的预约取消/完成订单操作
  return request({
    url: baseUrlWS + '/bee_8modules/app/order/' + data.userId + '/' + data.type + '/' + data.orderId,
    method: 'post'
  })
}
export const orderDetail = data => { // 我的预约-订单详情
  return request({
    url: baseUrlWS + '/bee_8modules/app/order/od/' + data.orderId + '/' + data.type,
    method: 'post'
  })
}
export const secCate = data => { // 根据一级类目获取二级类目
  return request({
    url: baseUrlWS + '/bee_8modules/app/hc/type/' + data.categoryName,
    method: 'post'
  })
}
export const getUnit = data => { // 根据一级类目获取单位
  return request({
    url: baseUrlWS + '/bee_8modules/app/unit/get/' + data.categoryName,
    method: 'get'
  })
}
export const getPhone = data => { // 获取服务商手机号
  return request({
    url: baseUrlWS + '/bee_8modules/app/hc/tel/' + data.id,
    method: 'get'
  })
}
export const getCareerType = data => { // 获取职业类型
  return request({
    url: baseUrlWS + '/bee_8modules/app/hc/page',
    method: 'get'
  })
}
