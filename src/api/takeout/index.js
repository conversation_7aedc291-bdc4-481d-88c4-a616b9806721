/*
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2021-05-31 16:46:32
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-09-27 16:28:13
 */
import request from '@/utils/request'

export const getShopData = data => { // 获取店铺详情
  return request({
    url: '/v2/app/market/findById/' + data,
    method: 'get'
  })
}

export const getShopDataV2 = data => { // 获取店铺详情v2
  return request({
    url: '/v2/app/market/findByIdV2',
    method: 'post',
    data
  })
}

export const getList = data => { // 获取店铺商品列表
  return request({
    url: '/v2/app/goods/tag/marketV2/' + data,
    method: 'get'
  })
}

export const getAddressList = data => { // 获取地址列表
  return request({
    url: '/v2/app/address/list/takeout',
    method: 'get',
    params: data
  })
}

export const addressList = data => { // 获取地址列表
  return request({
    url: '/v2/app/address/list',
    method: 'get',
    params: data
  })
}

export const payOrder = data => { // 下单
  return request({
    url: '/v2/app/order/takeout/save',
    method: 'post',
    data
  })
}

export const getPay = data => { // 支付
  return request({
    url: '/v4/app/order/pay',
    method: 'post',
    data
  })
}

export const getUserAccount = data => { // 下单-获取账户余额
  return request({
    // url: "/v2/app/capital/1",
    url: '/v2/app/capital/market/' + data,
    method: 'post'
  })
}
export const orderUserAccount = data => { // 待支付-获取账户余额
  return request({
    // url: "/v2/app/capital/1",
    url: '/v2/app/capital/order/' + data,
    method: 'post'
  })
}

export const payLoading = data => { // 检查支付
  return request({
    url: '/v2/app/pay/' + data,
    method: 'get',
    data
  })
}

export const takeoutCate = data => { // 获取外卖分类
  return request({
    url: '/v2/app/market/getTakeawayCategory/' + data.regionId,
    method: 'get'
  })
}

export const getPostFree = data => { // 获取运费
  return request({
    url: '/v2/app/address/takeout/detail',
    method: 'post',
    data
  })
}

export const systemPayList = data => { // 获取系统支付配置
  return request({
    url: '/v2/config/pay',
    method: 'get'
  })
}

export const checkpaylist = data => { // 获取店铺支付配置
  return request({
    url: '/v2/app/market/pay_style/' + data,
    method: 'get'
  })
}

export const checkInitPwd = data => { // 检查是否是初始支付密码
  return request({
    url: '/app/user/checkInitPwd/' + data + '/2',
    method: 'get'
  })
}

export const getWeatherConfig = data => { // 查询天气
  return request({
    url: '/v2/config/getWeatherConfig/' + data,
    method: 'get'
  })
}

export const getStationForUserAddress = data => { // 查询地址周边所有站点并排序
  return request({
    url: '/v2/app/station/getStationForUserAddress',
    method: 'post',
    data
  })
}

// 查询当天用户符合的线上满赠活动
export const getOnlineActivityGift = data => { // 查询地址周边所有站点并排序
  return request({
    url: '/v2/app/activityGift/getActivityGiftForOrder',
    method: 'post',
    data
  })
}
// 查询店铺配置
export const getConfig = data => {
  return request({
    url: '/v2/app/market/config/findById/' + data,
    method: 'get'
  })
}

// 中秋活动
export const getZq = () => {
  return request({
    url: 'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/index/actieve/zq/zq.json?id=' + Math.ceil(Math.random() * 100),
    method: 'get'
  })
}

/**
 *
 * 套餐团购相关接口
 *
 */
export const fsmCreateOrder = data => {
  return request({
    url: '/app/fsm/order/createOrder',
    method: 'post',
    data
  })
}
