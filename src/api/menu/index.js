/*
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2021-05-31 14:36:44
 * @LastEditors: 孙立政
 * @LastEditTime: 2021-05-31 15:33:49
 */
import request from '@/utils/request'

// 获取菜谱分类
export const getMenuCate = () => {
  return request({
    url: 'app/recipeCategory/findAllNopage',
    method: 'get'
  })
}

// 获取菜谱数据
export const getMenuList = data => {
  return request({
    url: 'app/recipe/findAll/1/8',
    method: 'post',
    data: data
  })
}

// 获取某个菜谱详情
export const getMenuDetail = data => {
  return request({
    url: 'app/recipe/findDetail/' + data,
    method: 'get'
  })
}
