/*
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2021-06-03 10:38:58
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-11-03 09:56:30
 */
import request from '@/utils/request'

// 人脸-密码校验
export const checkPassword = data => {
  return request({
    url: '/app/user/checkPassword',
    method: 'post',
    data: data
  })
}
// 人脸-人脸校验
export const checkFace = data => {
  return request({
    url: '/v2/app/face/checkFace',
    method: 'post',
    data: data
  })
}
// 人脸-ruku
export const uploads = data => {
  return request({
    url: '/v2/app/face/upload',
    method: 'post',
    data: data
  })
}
// 获取人脸数据
export const getUserFace = data => {
  return request({
    url: '/v2/app/face/getUserFace',
    method: 'get'
  })
}

// 查询登录人人脸信息
export const faceInfoById = data => {
  return request({
    url: '/v2/app/face/faceInfoById',
    method: 'get'
  })
}
