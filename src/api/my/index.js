import request from '@/utils/request'
import store from '@/store/index'

// 我的模块

export const UserInfo = data => { // 获取用户信息
  return request({
    url: '/app/user/getUserInfo/' + data + '/' + store.getters.getRegionId,
    method: 'get',
    data: data
  })
}
export const UserBalance = data => { // 获取用户余额
  return request({
    url: '/app/capitalAccount/findByUserId/' + data,
    method: 'post',
    data: data
  })
}
export const findAllRegion = data => { // 查询所有大区
  return request({
    url: '/app/region/findAll',
    method: 'post'
  })
}
export const Allowance = (data) => { // 获取补贴
  return request({
    url: '/app/injectionRecord/findByCapitalAccountId/' + data.page + '/200/' + data.capitalAccountId,
    method: 'post',
    data: data

  })
}
export const otherMoney = data => { // 获取其他账单信息
  return request({
    url: '/app/CapitalFlowRecord/findByCapitalAccount',
    method: 'post',
    data: data
  })
}

export const billDetail = (data, geturl) => { // 获取账单详情
  return request({
    url: '/app/' + geturl + '/findById/' + data.id,
    method: 'post',
    data: data
  })
}

export const feedback = data => { // 意见反馈
  return request({
    url: '/v2/app/feedback/suggestion',
    method: 'post',
    data: data
  })
}

export const editAccountName = data => { // 修改用户名
  return request({
    url: '/app/user/editAccountName',
    method: 'post',
    data: data
  })
}

export const upload = data => { // 上传图片
  return request({
    url: '/app/upload/image',
    method: 'post',
    headers: {
      'content-type': 'multipart/form-data'
    },
    data: data
  })
}

export const version = isOS => { // 检查当前版本
  return request({
    url: '/app/version/checkAppVersion/1/' + isOS + '/' + 1,
    method: 'get'
  })
}
export const editPic = data => { // 修改头像
  return request({
    url: '/app/user/editPic',
    method: 'post',
    data: data
  })
}

export const ifPay = data => { // 获取充值方法
  return request({
    url: '/v2/config/pay',
    method: 'get',
    data: data
  })
}

export const payBlance = data => { // 确认充值
  return request({
    url: '/v2/app/order/2_1/inverst/pay',
    method: 'post',
    data: data
  })
}
export const myFoodList = () => { // 我的食堂
  return request({
    url: '/v2/app/market/myCanteen',
    method: 'get'
  })
}

// 获取钱包列表
export const capitalist = data => {
  return request({
    url: '/app/capitalAccount/findByUserId/' + data,
    method: 'post'
  })
}

// 获取大区客服电话
export const getCustomServicePhone = data => {
  return request({
    url: `/v2/config/getCustomServicePhone/${data}`,
    method: 'get'
  })
}
// 账号注销
export const userLogout = data => {
  return request({
    url: '/app/user/userLogout',
    method: 'post'
  })
}
