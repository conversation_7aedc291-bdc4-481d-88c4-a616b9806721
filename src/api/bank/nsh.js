/*
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2021-11-11 17:06:34
 * @LastEditors: 孙立政
 * @LastEditTime: 2023-08-22 21:47:00
 */
import request from '@/utils/request'

// 银联支付账户首页H5
export const rcbHomePage = data => {
  return request({
    url: `/v2/app/rcbUnionAccount/rcbHomePage/${data}`,
    method: 'post',
    data: data
  })
}

// 查询农商银联开户情况
export const findUnionRcbAccount = data => {
  return request({
    url: `/v2/app/rcbUnionAccount/findUnionRcbAccount`,
    method: 'post',
    data: data
  })
}

// 开户成功后调用服务
export const openAccountSmsSend = data => {
  return request({
    url: `/v2/app/ccbUnionPay/openAccountSmsSend`,
    method: 'post',
    data: data
  })
}

// .输入短信验证码，银联开户
export const unionPayCreateAcc = data => {
  return request({
    url: `/v2/app/ccbUnionPay/unionPayCreateAcc`,
    method: 'post',
    data: data
  })
}
