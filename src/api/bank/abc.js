/*
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2021-11-11 17:06:34
 * @LastEditors: 孙立政
 * @LastEditTime: 2023-08-22 21:47:00
 */
import request from '@/utils/request'

// 农行签约状态查询
export const fundAbcAccount = data => {
  return request({
    url: `/app/abcBusiness/fundAbcAccount`,
    method: 'post',
    data: data
  })
}

// 农行授权支付商户侧签约 - 申请
export const agentSignReq = data => {
  return request({
    url: `/app/abcBusiness/agentSignReq`,
    method: 'post',
    data: data
  })
}

// 农行授权支付商户侧签约 -验证码- 确认
export const agentSignSubmit = data => {
  return request({
    url: `/app/abcBusiness/agentSignSubmit/${data.capitalAccountId}/${data.orderNo}/${data.verifyCode}`,
    method: 'post',
    data: data
  })
}

// 农行授权支付商户侧签约 -验证码- 重发
export const agentSignResend = data => {
  return request({
    url: `/app/abcBusiness/agentSignResend/${data.capitalAccountId}/${data.orderNo}/${data.verifyCode}`,
    method: 'post',
    data: data
  })
}

// 农行授权支付商户侧签约 -验证码- 重发
export const agentUnSign = data => {
  return request({
    url: `/app/abcBusiness/agentUnSign/${data.capitalAccountId}`,
    method: 'post',
    data: data
  })
}

// 解约
export const agentUnSignReq = data => {
  return request({
    url: `/app/abcBusiness/accountUnBind`,
    method: 'post',
    data: data
  })
}
