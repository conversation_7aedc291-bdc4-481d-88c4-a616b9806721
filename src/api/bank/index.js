/*
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2021-10-20 16:28:41
 * @LastEditors: 孙立政
 * @LastEditTime: 2023-05-30 15:50:41
 */

import request from '@/utils/request'

// 查询开户情况
export const fundAccountQuery = data => {
  return request({
    url: '/v2/app/ccbUnionPay/fundAccountQuery',
    method: 'post',
    data: data
  })
}

// 查询建行账户
export const ccbAccQuery = () => {
  return request({
    url: '/v2/app/ccbUnionPay/ccbAccQuery',
    method: 'get'
  })
}

// 建行sdk开户成功后调用服务
export const ccbAccRecord = data => {
  return request({
    url: '/v2/app/ccbUnionPay/ccbAccRecord',
    method: 'post',
    data: data
  })
}

// 银联开户验证码发送
export const openAccountSmsSend = data => {
  return request({
    url: '/v2/app/ccbUnionPay/openAccountSmsSend',
    method: 'post',
    data: data
  })
}

// 输入短信验证码，银联开户
export const unionPayCreateAcc = data => {
  return request({
    url: '/v2/app/ccbUnionPay/unionPayCreateAcc',
    method: 'post',
    data: data
  })
}

// 农商行
export const rcbHomePage = data => {
  return request({
    url: `/v2/app/rcbUnionAccount/rcbHomePage/${data}`,
    method: 'post',
    data: data
  })
}

// 强校验
export const finishCheckUnionPay = data => {
  return request({
    url: `/open/finishCheckUnionPay`,
    method: 'post',
    data: data
  })
}

// 建行手机银行导流
export const ccbIncrTraffic = data => {
  return request({
    url: `/v2/app/ccbUnionPay/ccbIncrTraffic?deviceInfo=${data}`,
    method: 'get',
    data: data
  })
}

// 根据卡号查询卡信息
export const getBankcard = data => {
  return request({
    url: `https://fc.zjntwl.com/bankcard?no=` + data,
    method: 'get'
  })
}

// 上传图片
export function upload(data) {
  return request({
    url: `https://fc.zjntwl.com/upload?menu=ocr`,
    method: 'post',
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    data: data
  })
}
// 证件识别
export const getOcr = data => {
  return request({
    url: `https://fc.zjntwl.com/ocr`,
    method: 'post',
    data: data
  })
}

// 开户行网点查询
export const qrylhh = data => {
  return request({
    url: `https://fc.zjntwl.com/qrylhh`,
    method: 'get',
    params: data
  })
}

