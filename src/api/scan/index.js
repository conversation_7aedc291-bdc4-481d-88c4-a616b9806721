/*
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2021-05-31 16:46:50
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-09-02 17:34:10
 */
import request from '@/utils/request'

export const getScan = data => { // 获取店铺商品列表
  return request({
    url: '/app/market/scanCode?body=' + data.code,
    method: 'post'
  })
}

export const ScanCode = data => { // 扫码
  return request({
    url: '/app/market/scanCode?body=' + data,
    method: 'post',
    data: data
  })
}

export const ScanCodeLq = data => { // 龙泉扫码
  return request({
    url: '/app/market/scanCode?body=' + data + '&type=3',
    method: 'post',
    data: data
  })
}

export const Balance = data => { // 查询余额
  return request({
    url: '/app/capitalAccount/findByUserIdAndReceive/' + data.userId + '/' + data.receiveId + '/1',
    method: 'post',
    data: data
  })
}

export const checkInitPwd = data => { // 检查默认密码
  return request({
    url: '/app/user/checkInitPwd/' + data + '/2',
    method: 'get',
    data: data
  })
}

export const Pay = data => { // 扫码支付
  return request({
    url: '/v4/app/order/qrcodeOrderPay',
    method: 'post',
    data: data
  })
}

// 获取付款码
export const getPayment = data => {
  return request({
    url: '/v2/app/user/qrcode/payment',
    method: 'post',
    data: data
  })
}

// 人脸-密码校验
export const checkPassword = data => {
  return request({
    url: '/app/user/checkPassword',
    method: 'post',
    data: data
  })
}

// 查询付款码是否成功
export const checkOrderStatus = data => { // 检查默认密码
  return request({
    url: '/v2/app/order/checkOrderStatus',
    method: 'post',
    data: data
  })
}

// -------------------V2-------------------
// 创建扫码订单
export const saveQrcodeOrder = data => {
  return request({
    url: '/v2/app/order/saveQrcodeOrder',
    method: 'post',
    data: data
  })
}

// 获取APP支付加密信息
export const getEncryptData = data => {
  return request({
    url: '/app/rcbBusiness/getEncryptData',
    method: 'post',
    data: data
  })
}
