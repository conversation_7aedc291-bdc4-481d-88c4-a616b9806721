/*
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2021-05-31 16:46:32
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-09-22 17:08:49
 */
import request from '@/utils/request'

export const getShopData = data => { // 获取店铺详情
  return request({
    url: '/v2/app/market/findDelicacyInfoById',
    method: 'post',
    data
  })
}
export const getGoodsList = data => { // 获取商品
  return request({
    url: '/v2/app/goods/userFindPage',
    method: 'post',
    data
  })
}

export const queryByTypeV2 = data => { // 查看店铺评价
  return request({
    url: '/v2/app/evaluation/market/queryByTypeV2',
    method: 'post',
    data
  })
}
export const findRandomMarketByType = data => { // 获取推荐店铺
  return request({
    url: `/v2/app/market/findRandomMarketByType/${data.marketType}/${data.regionId}`,
    method: 'post',
    data
  })
}
export const getRecommand = data => { // 获取推荐商品
  return request({
    url: `/v2/app/goods/getRecommand/${data}`,
    method: 'get',
    data
  })
}

/**
 *
 * 套餐团购相关接口
 *
 */
// 店铺首页展示团购列表
export const setMealList = data => {
  return request({
    url: `/v2/app/setMeal/setMealList`,
    method: 'post',
    data
  })
}
// 根据id获取套餐详情
export const setMealInfo = data => {
  return request({
    url: `/v2/app/setMeal/setMealInfo/${data}`,
    method: 'get',
    data
  })
}

// 获取推荐店铺
export const findRandomMarketByTypev4 = data => {
  return request({
    url: `/v4/app/market/findRandomMarketByType/${data.marketType}/${data.regionId}`,
    method: 'post',
    data
  })
}

// 店铺收藏
export const marketCollect = data => {
  return request({
    url: `/app/collection/add`,
    method: 'post',
    data
  })
}

// 店铺取消收藏
export const marketUnCollect = data => {
  return request({
    url: `/app/collection/delete/${data.id}`,
    method: 'post',
    data
  })
}

// 店铺收藏列表
export const marketCollectList = data => {
  return request({
    url: `/app/collection/pageList/${data.pageNum}/${data.pageSize}`,
    method: 'post',
    data
  })
}
