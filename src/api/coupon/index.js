/*
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2022-08-06 10:05:40
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-10-09 14:15:51
 */
import request from '@/utils/request'

// 外卖店铺优惠列表
export const marketCouponList = data => {
  return request({
    url: '/app/coupon/marketCouponList',
    method: 'post',
    data: data
  })
}

// 根据当前用户获取外卖优惠券提示弹窗(首页优惠券列表)
export const couponNotice = data => {
  return request({
    url: '/app/coupon/notice?regionId=' + data,
    method: 'post',
    data: data
  })
}

// 优惠券领取
export const couponPick = data => {
  return request({
    url: `/app/coupon/pick?couponId=${data}`,
    method: 'post',
    data: data
  })
}

// 一键领取优惠券
export const couponPickAll = data => {
  return request({
    url: `/app/coupon/pickAll`,
    method: 'post',
    data: data
  })
}

// 提交订单前系统建议优惠券信息
export const recommendCoupon = data => {
  return request({
    url: `/app/coupon/recommendCoupon?marketId=${data.marketId}&orderMoneyAmount=${data.orderMoneyAmount}`,
    method: 'post',
    data: data
  })
}

// 用户自选优惠券列表
export const selectCoupon = data => {
  return request({
    url: `/app/coupon/selectCoupon?marketId=${data.marketId}&orderMoneyAmount=${data.orderMoneyAmount}&usedStyle=${data.usedStyle}&getChannel=${data.getChannel}`,
    method: 'post',
    data: data
  })
}

// 我的优惠券
export const myCoupon = data => {
  return request({
    url: `/app/coupon/myCoupon?regionId=${data.regionId}&couponStatus=${data.couponStatus}`,
    method: 'post',
    data: data
  })
}

// 店铺详情优惠券信息
export const marketCoupon = (data, usedStyle) => {
  return request({
    url: `/app/coupon/marketCoupon/${data}/${usedStyle}/1`, // 默认1领券  2抢券
    method: 'post',
    data: data
  })
}

// 根据优惠券id列表获取各优惠券针对本用户的领取情况列表(领取后列表刷新)
export const noticeRefresh = data => {
  return request({
    url: `/app/coupon/noticeRefresh`,
    method: 'post',
    data: data
  })
}

// 查询优惠券列表信息分页展示
export const historicalCouponList = data => {
  return request({
    url: `/app/coupon/historicalCouponList/${data.pageNum}/${data.pageSize}`,
    method: 'post',
    data: data
  })
}

// 我的优惠券列表V2（区分已领取和待领取）
export const myCouponV2 = data => {
  return request({
    url: `/app/coupon/myCouponV2?regionId=` + data.regionId,
    method: 'post',
    data: data
  })
}
// 我的优惠券数量
export const myCouponCount = data => {
  return request({
    url: `/app/coupon/myCouponCount?regionId=` + data.regionId,
    method: 'post',
    data: data
  })
}

// 优惠券领取V2
export const couponPickV2 = data => {
  return request({
    url: `/app/coupon/scramble`,
    method: 'post',
    data: data
  })
}
