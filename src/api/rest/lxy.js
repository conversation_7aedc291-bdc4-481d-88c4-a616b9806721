/*
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2021-07-09 10:17:00
 * @LastEditors: 孙立政
 * @LastEditTime: 2023-05-04 18:12:21
 */
import request from '@/utils/request'

// 获取推荐店铺
export const getRandomMarket = data => {
  return request({
    url: `/app/market/getRandomMarket/12/${data.regionId}/${data.size}`,
    method: 'get'
  })
}
// 获取推荐商品
export const getRandomGoods = data => {
  return request({
    url: `/v2/app/goods/getRandomGoods/${data.regionId}/${data.size}`,
    method: 'get'
  })
}

// 查询旅行社第一个商品
export const findFirstGoodsInMarketId = (data) => {
  return request({
    url: `/v2/app/goods/findFirstGoodsInMarketId`,
    method: 'post',
    data
  })
}

// 列表页
export const goodsPage = data => {
  return request({
    url: `/v2/app/goods/goods/page`,
    method: 'post',
    data
  })
}

// 获取店铺商品
export const marketGoods = data => {
  return request({
    url: `/v2/app/goods/marketGoods/page`,
    method: 'post',
    data
  })
}

// 商品详情页详情
export const goodsInfo = data => {
  return request({
    url: `/v2/app/goods/goodsInfo/${data}`,
    method: 'get'
  })
}

// 预约订单下单支付
export const bookOrderPay = data => {
  return request({
    url: `/app/bookOrder/pay`,
    method: 'post',
    data
  })
}

// 预约订单查询用户年款记录列表
export const moneyList = data => {
  return request({
    url: `/app/bookOrder/moneyList/${data.poolId}`,
    method: 'get',
    data
  })
}

// 获取用户预约订单列表
export const bookOrderList = data => {
  return request({
    url: '/app/bookOrder/bookOrderList',
    method: 'get',
    data: data
  })
}
