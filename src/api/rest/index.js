/*
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-06-05 14:20:14
 * @LastEditors: zhaoyuxin
 * @LastEditTime: 2021-06-05 14:21:10
 */
import request from '@/utils/request'
// 疗休养
export const findClassDetial = data => {
  return request({
    url: '/app/tbTripInformationClassification/findClassDetial/' + data,
    method: 'post'
  })
}
// 疗休养详情
export const tbTripArticle = data => {
  return request({
    url: '/app/tbTripArticle/detail/' + data,
    method: 'post'
  })
}
// 获取店铺列表
export const List = data => {
  return request({
    url: '/app/market/findAll/' + data.pages + '/10',
    method: 'post',
    data: data
  })
}
