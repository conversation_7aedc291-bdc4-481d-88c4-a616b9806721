/*
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2021-06-02 11:09:36
 * @LastEditors: 孙立政
 * @LastEditTime: 2021-06-02 11:10:09
 */
import request from '@/utils/request'

// 获取列表
export const getTbTripArticle = data => {
  return request({
    url: `/app/tbTripArticle/list/3/${data}/1`,
    method: 'get'
  })
}

// 获取详情
export const getDetail = data => {
  return request({
    url: '/app/tbTripArticle/detail/' + data,
    method: 'post'
  })
}
