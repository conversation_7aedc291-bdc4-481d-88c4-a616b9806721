/*
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-05-24 15:03:32
 * @LastEditors: 孙立政
 * @LastEditTime: 2023-04-03 14:03:12
 */
import request from '@/utils/request'
import store from '@/store/index'
import {
  baseUrlSearch
} from '@/config/settings'

// 获取分类二级类目
export const ClassList = data => {
  return request({
    url: '/v2/app/market/getCategory/' + store.getters.getRegionId + '/' + data.id,
    method: 'get',
    headers: {
      'content-type': 'application/json',
      'Token': localStorage.getItem('token')
    }
  })
}
// 获取外卖分类
export const takeoutCate = () => {
  return request({
    url: '/v2/app/market/getTakeawayCategory/' + store.getters.getRegionId,
    method: 'get',
    headers: {
      'content-type': 'application/json',
      'Token': localStorage.getItem('token')
    }
  })
}
// 获取美食店铺列表
export const MarketList = data => {
  return request({
    url: baseUrlSearch + '/db/searchByMarketCategory',
    method: 'post',
    data: data
  })
}
// // 获取酒店店铺列表
// export const HotelList = (data, pages) => {
//   return request({
//     url: '/app/market/findAllHotel/' + pages + '/1000',
//     method: 'post',
//     headers: {
//       'content-type': 'application/json',
//       'Token': localStorage.getItem('token')
//     },
//     data: data
//   })
// }

// // 获取菜市场店铺列表
// export const VegeList = data => {
//   return request({
//     url: baseUrlSearch + '/es/searchByMarketCategory',
//     method: 'post',
//     headers: {
//       'content-type': 'application/json',
//       'Token': localStorage.getItem('token')
//     },
//     data: data
//   })
// }
// // 点击二级类目搜索
// // 搜索店铺  searchByMarketCategory
// // 搜索商品  searchByGoodsName
export const searchList = (data, urls) => {
  return request({
    url: baseUrlSearch + '/db/' + urls,
    method: 'post',
    headers: {
      'content-type': 'application/json',
      'Token': localStorage.getItem('token')
    },
    data: data
  })
}

