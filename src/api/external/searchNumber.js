/*
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2021-11-17 17:18:58
 * @LastEditors: 孙立政
 * @LastEditTime: 2021-12-10 14:29:38
 */
import request from '@/utils/request'
import { baseUrlLocalservice } from '@/config/settings'

// 列表
export const findAll = data => {
  return request({
    url: baseUrlLocalservice + '/v2/app/searchNumber/findAll',
    method: 'post',
    data: data
  })
}
// 电话次数
export const updateCallsById = data => {
  return request({
    url: baseUrlLocalservice + '/v2/app/searchNumber/updateCallsById/' + data,
    method: 'post',
    data: data
  })
}
// 标记
export const updateNumberMarking = data => {
  return request({
    url: baseUrlLocalservice + '/v2/app/searchNumber/updateNumberMarking',
    method: 'post',
    data: data
  })
}
