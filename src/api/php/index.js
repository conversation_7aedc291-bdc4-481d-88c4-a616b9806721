/*
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-06-05 14:20:14
 * @LastEditors: 孙立政
 * @LastEditTime: 2023-02-03 20:25:26
 */
import request from '@/utils/request'
import {
  baseUrlPHP
} from '@/config/settings'
// 元宵节-题目列表
export const lists = data => {
  return request({
    url: baseUrlPHP + 'riddles/lists',
    method: 'get',
    params: data
  })
}
// 元宵节-答题
export const answer = data => {
  return request({
    url: baseUrlPHP + 'riddles/answer',
    method: 'post',
    data
  })
}

