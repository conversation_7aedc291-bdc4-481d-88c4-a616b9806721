/*
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2021-06-01 14:19:43
 * @LastEditors: 孙立政
 * @LastEditTime: 2023-05-23 16:50:19
 */
import request from '@/utils/request'
import { baseUrlSms } from '@/config/settings'

// 手机密码登录
export const loginByPwd = data => {
  return request({
    url: '/v3/app/user/login',
    method: 'post',
    data: data
  })
}

// 验证码登录
export const login = data => {
  return request({
    url: '/app/user/pn/login',
    method: 'post',
    data: data
  })
}

// 发送验证码
export const smsCode = data => {
  return request({
    url: baseUrlSms + '/sendCode?phone=' + data.tel + '&teleplateName=' + data.name,
    method: 'post',
    headers: {
      'remark': data.remark
    }
  })
}

export const pnCheck = data => { // 检查是否注册
  return request({
    url: '/app/user/pncheck/' + data,
    method: 'post',
    data: data
  })
}
export const register = (data, code) => { // 注册
  return request({
    url: '/app/user/registerByTel/' + code,
    method: 'post',
    data: data
  })
}
// 修改登录密码-验证码
export const resetLoginPwd = data => {
  return request({
    url: '/app/user/resetPwd/' + data.code,
    method: 'post',
    data: data
  })
}
// 修改登录密码-旧密码
export const editPwdLoginPwd = data => {
  return request({
    url: '/app/user/editPwd/' + data.oldPassword,
    method: 'post',
    data: data
  })
}
export const checkInitPwd = data => { // 检查默认密码
  return request({
    url: '/app/user/checkInitPwd/' + data.userId + '/1',
    method: 'get',
    data: data
  })
}
// // 修改支付密码
export const resetPayPwd = data => {
  return request({
    url: '/app/user/restPayPwdCode?payPassword=' + data.password + '&veryfyCode=' + data.code,
    method: 'post'
  })
}

// 微信登录--首次
export const storageThirdInfo = data => {
  return request({
    url: '/app/user/wechat/first/login',
    method: 'post',
    data: data
  })
}
// 微信登录
export const wechatLogin = data => {
  return request({
    url: '/app/user/wechatV2/login',
    method: 'post',
    data: data
  })
}

// 微信解绑
export const wechatUnbinding = data => {
  return request({
    url: '/app/user/wechatUnbinding?unionId=' + data,
    method: 'get'
  })
}

// 根据手机号查询微信绑定信息
export const getWechatUnbinding = data => {
  return request({
    url: '/app/user/bindingInfoByTelephone?telephone=' + data,
    method: 'get'
  })
}

// 退出
export const logout = data => {
  return request({
    url: '/app/user/logout',
    method: 'post',
    data: data
  })
}

// 三方账号绑定
export const accountSync = data => {
  return request({
    url: '/v4/open/platformPay/accountSync/3',
    method: 'post',
    data: data
  })
}
export const acctSyncCheck = data => {
  return request({
    url: '/v4/open/platformPay/acctSyncCheck/3',
    method: 'get',
    data: data
  })
}

// 初始化获取用户虚拟手机号
export const initGetVirtualPhone = data => {
  return request({
    url: `/v4/open/platformPay/initGetVirtualPhone/${data.platId}`,
    method: 'post',
    data: data
  })
}

// 获取图形验证码
export const getCaptcha = data => {
  return request({
    url: baseUrlSms + '/getCaptcha',
    method: 'get',
    responseType: 'blob'
  })
}
// 发送短信V2
export const sendCodeV2 = data => {
  return request({
    url: baseUrlSms + '/sendCodeV2',
    method: 'post',
    headers: {
      'captchakey': data.captchakey
    },
    data: data
  })
}
