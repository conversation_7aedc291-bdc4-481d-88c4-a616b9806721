/*
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-06-23 09:51:43
 * @LastEditors: 孙立政
 * @LastEditTime: 2021-06-26 14:17:56
 */
import request from '@/utils/request'

// 查询用户在店铺中符合领取条件的优惠券列表
export const marketCouList = data => {
  return request({
    url: '/v2/app/coupon/getCouponListForMarket',
    method: 'post',
    data: data
  })
}

// 下单查询本订单可用优惠券列表
export const orderCouList = data => {
  return request({
    url: '/v2/app/coupon/getCouponListForOrder',
    method: 'post',
    data: data
  })
}

// 我的-优惠券列表
export const myCouList = data => {
  return request({
    url: '/v2/app/coupon/getCouponListForUser',
    method: 'post',
    data: data
  })
}

// 领取优惠券操作
export const receiveCou = data => {
  return request({
    url: `/v2/app/coupon/receiveCoupon/${data.userId}/${data.regId}/${data.couponSn}`,
    method: 'post'
  })
}
