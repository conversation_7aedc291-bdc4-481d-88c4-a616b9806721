/*
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2021-05-31 15:51:32
 * @LastEditors: 孙立政
 * @LastEditTime: 2023-04-03 14:03:30
 */
import request from '@/utils/request'
import store from '@/store/index'
import { baseUrlSearch, baseUrlPHP } from '@/config/settings'

// 获取banner
export const getBannerList = data => {
  return request({
    url: '/app/banner/findAll/10/' + store.getters.getRegionId,
    method: 'get'
  })
}

// 获取分类
export const getCategory = data => {
  return request({
    url: '/v2/app/market/getCategory/' + store.getters.getRegionId + '/0',
    method: 'get',
    data
  })
}

// 获取天天特价
export const getTejiaList = data => {
  return request({
    url: '/v2/app/campaign/query/' + store.getters.getRegionId + '/4',
    method: 'post'
  })
}

// 获取优惠专区
export const getYouhuiList = data => {
  return request({
    url: '/v2/app/goods/seckill/page/1/2/' + store.getters.getRegionId,
    method: 'get'
  })
}

// 附近的店铺
export const getShopList = data => {
  return request({
    url: baseUrlSearch + '/db/searchByMarketCategory',
    method: 'post',
    data
  })
}
// 获取所有大区
export const regionList = data => {
  return request({
    url: '/app/region',
    method: 'get',
    data
  })
}

// 版本日志
export const addAppVersion = data => { // up
  return request({
    url: '/app/version/addAppVersion',
    method: 'post',
    headers: {
      'data': data
    },
    data
  })
}

// 日志记录用户异常登陆环境
export const environment = data => {
  return request({
    url: '/app/user/device/environment',
    method: 'post',
    data
  })
}

// 检查二类户开通弹出
export const isCcbUnionPay = data => {
  return request({
    url: '/v2/app/ccbUnionPay/userAccountCheck',
    method: 'get',
    data
  })
}

// 根据大区id和活动类型编号获取Banner设置详情
export const campaignBannerDetail = data => {
  return request({
    url: '/app/campaignBanner/detail',
    method: 'post',
    params: data
  })
}

// 查询正在使用中的首页皮肤
export const findTbSkinHomePage = data => {
  return request({
    url: '/app/skin/findTbSkinHomePage?regionId=1',
    method: 'get',
    data
  })
}

// 查询正在使用中的启动图皮肤
export const findTbSkinStartUp = data => {
  return request({
    url: '/app/skin/findTbSkinStartUp?regionId=' + data,
    method: 'get',
    data
  })
}

// php-相关配置
export const getPhpConfig = data => {
  return request({
    url: baseUrlPHP + 'LongQuanRobCoupon/coupon?aid=1',
    method: 'get',
    data
  })
}

export const getPhpConfig2 = data => {
  return request({
    url: baseUrlPHP + 'config/keys/all',
    method: 'get',
    data
  })
}
