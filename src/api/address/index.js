/*
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-05-24 15:33:07
 * @LastEditors: 孙立政
 * @LastEditTime: 2021-05-31 14:39:52
 */
import request from '@/utils/request'

// 新增 地址
export const newAddress = data => {
  return request({
    url: '/v2/app/address/add',
    method: 'post',
    data: data
  })
}

// 根据id获取地址
export const DetailAdr = data => {
  return request({
    url: '/v2/app/address/detail/' + data,
    method: 'get',
    headers: {
      'content-type': 'application/json',
      'Token': localStorage.getItem('token')
    }
  })
}
// 编辑地址保存
export const updateAddress = data => {
  return request({
    url: '/v2/app/address/update',
    method: 'post',
    headers: {
      'content-type': 'application/json',
      'Token': localStorage.getItem('token')
    },
    data: data
  })
}
// 删除收获地址
export const delAddress = data => {
  return request({
    url: '/v2/app/address/delete/' + data,
    method: 'delete',
    headers: {
      'content-type': 'application/json',
      'Token': localStorage.getItem('token')
    }
  })
}

export const AddressList = data => { // 获取地址列表
  return request({
    url: '/v2/app/address/list/takeout?marketSn=' + data,
    method: 'get',
    headers: {
      'content-type': 'application/json',
      'Token': localStorage.getItem('token')
    }
  })
}
