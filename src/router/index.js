/*
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2021-05-22 15:22:43
 * @LastEditors: 孙立政
 * @LastEditTime: 2023-07-28 10:21:01
 */
import Vue from 'vue'
import VueRouter from 'vue-router'
import classify from './modules/classify'
import scan from './modules/scan'
import shop from './modules/shop'
import search from './modules/search'
import takeout from './modules/takeout'
import address from './modules/address'
import order from './modules/order'
import login from './modules/login'
import my from './modules/my'
import rest from './modules/rest'
import notice from './modules/notice'
import evalute from './modules/evalute'
import work from './modules/work'
import workIndex from './modules/workIndex'
import region from './modules/region'
import shoppingMall from './modules/shoppingMall'
import advertisement from './modules/advertisement' // 广告
import school from './modules/school'
import external from './modules/external'
import market from './modules/market' // 商城
import activity from './modules/activity' // 活动

Vue.use(VueRouter)

const routes = [
  { path: '/', redirect: '/index' },
  {
    path: '/',
    name: 'Home',
    component: () => import('../views/Home.vue'),
    children: [
      {
        path: '/index',
        name: 'Index',
        meta: {
          keepAlive: false
        },
        component: () => import('../views/index')
      },
      {
        path: '/menu',
        name: 'Menu',
        meta: {
          keepAlive: false
        },
        component: () => import('../views/menu')
      },
      {
        path: '/order',
        name: 'Order',
        component: () => import('../views/order/index/index')
      },
      {
        path: '/my',
        name: 'My',
        component: () => import('../views/my/index/index')
      },
      {
        path: '/market/home',
        name: 'MarketHome',
        component: () => import('../views/market/home')
      }
    ]
  }, ...classify, ...shop, ...search, ...takeout, ...address, ...order, ...scan, ...login, ...my, ...rest, ...notice, ...evalute, ...work, ...region, ...shoppingMall,
  ...advertisement, ...school, ...external, ...workIndex, ...market, ...activity,
  {
    path: '/menuDetails',
    name: 'MenuDetails',
    component: () => import('../views/menu/details')
  }
]

const originalPush = VueRouter.prototype.push
VueRouter.prototype.push = function push(location) {
  return originalPush.call(this, location).catch(err => err)
}

const router = new VueRouter({
  routes
})

export default router
