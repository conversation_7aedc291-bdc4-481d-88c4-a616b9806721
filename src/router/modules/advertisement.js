/*
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2021-07-08 10:35:20
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-04-18 16:21:41
 */
export default [
  {
    path: '/lqNsBank',
    name: 'LqNsBank',
    meta: {
      title: ''
    },
    component: () => import('@/views/advertisement/lqNsBank.vue')
  },
  // 中秋活动
  {
    path: '/zq',
    name: 'Zq',
    meta: {
      title: '',
      keepAlive: true
    },
    component: () => import('@/views/advertisement/zq.vue')
  },
  // 房地产活动
  {
    path: '/fdc',
    name: 'Fdc',
    meta: {
      title: '',
      keepAlive: true
    },
    component: () => import('@/views/advertisement/fdc.vue')
  },
  // 房地产活动2
  {
    path: '/fdcTwo',
    name: 'FdcTwo',
    meta: {
      title: '',
      keepAlive: true
    },
    component: () => import('@/views/advertisement/fdcTwo.vue')
  },
  // 房地产活动3
  {
    path: '/fdc3',
    name: 'Fdc3',
    meta: {
      title: '',
      keepAlive: true
    },
    component: () => import('@/views/advertisement/fdc3.vue')
  },
  {
    path: '/fdc5',
    name: 'Fdc5',
    meta: {
      title: '',
      keepAlive: true
    },
    component: () => import('@/views/advertisement/fd5.vue')
  }
]
