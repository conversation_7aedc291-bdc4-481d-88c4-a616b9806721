/*
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2022-04-29 10:32:34
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-06-21 09:40:54
 */
export default [
  {
    path: '/market/pt',
    name: 'MarketPt',
    meta: {
      title: '点滴拼团'
    },
    component: () => import('@/views/market/pt')
  },
  {
    path: '/market/details',
    name: 'MarketDetails',
    meta: {
      title: '商品详情'
    },
    component: () => import('@/views/market/modules/Details')
  },
  {
    path: '/market/pt/details',
    name: 'MarketPtDetails',
    meta: {
      title: '团购详情'
    },
    component: () => import('@/views/market/modules/Details/pt')
  },
  {
    path: '/market/order',
    name: 'MarketOrder',
    meta: {
      title: '团购订单'
    },
    component: () => import('@/views/market/modules/Order')
  },
  {
    path: '/market/order/ptDetails',
    name: 'MarketOrderPtDetails',
    meta: {
      title: '团购详情'
    },
    component: () => import('@/views/market/modules/Order/ptDetails.vue')
  },
  {
    path: '/market/order/details',
    name: 'MarketOrderDetails',
    meta: {
      title: '订单详情'
    },
    component: () => import('@/views/market/modules/Order/details.vue')
  },
  {
    path: '/market/cart',
    name: 'MarketCart',
    meta: {
      title: '下单'
    },
    component: () => import('@/views/market/modules/Cart')
  },
  {
    path: '/market/pay',
    name: 'MarketPay',
    meta: {
      title: '支付'
    },
    component: () => import('@/views/market/modules/Cart/pay')
  }
]
