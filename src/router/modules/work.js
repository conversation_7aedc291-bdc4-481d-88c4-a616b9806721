/*
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-03-31 16:29:35
 * @LastEditors: zhaoyuxin
 * @LastEditTime: 2021-06-29 10:56:06
 */
export default [
  {
    // 本地服务首页
    path: '/work/home/<USER>',
    name: 'home',
    meta: {
      title: ''
    },
    component: () => import('@/views/work/home/<USER>')
  },
  {
    // 需求发布-保洁
    path: '/work/needs/clean',
    name: 'needsClean',
    meta: {
      title: '',
      keepAlive: false
    },
    component: () => import('@/views/work/needs/clean')
  },
  {
    // 需求发布-招人
    path: '/work/needs/recruitment',
    name: 'needsRecruitment',
    meta: {
      title: ''
    },
    component: () => import('@/views/work/needs/recruitment')
  },
  {
    // 服务项目列表-家庭保洁/维修
    path: '/work/list/listClean',
    name: 'listClean',
    meta: {
      title: ''
    },
    component: () => import('@/views/work/list/listClean')
  },
  {
    // 服务项目列表-招聘
    path: '/work/list/listRecru',
    name: 'listRecru',
    meta: {
      title: ''
    },
    component: () => import('@/views/work/list/listRecru')
  },
  {
    // 服务项目列表-家庭保洁/维修详情
    path: '/work/list/listCleanDetail',
    name: 'listCleanDetail',
    meta: {
      title: ''
    },
    component: () => import('@/views/work/list/listCleanDetail')
  },
  {
    // 服务项目列表-招聘详情
    path: '/work/list/listRecruDetail',
    name: 'listRecruDetail',
    meta: {
      title: ''
    },
    component: () => import('@/views/work/list/listRecruDetail')
  },
  {
    // 预约-预约下单
    path: '/work/appointment/anOrder',
    name: 'anOrder',
    meta: {
      title: ''
    },
    component: () => import('@/views/work/appointment/anOrder')
  },
  {
    // 预约-我的预约
    path: '/work/appointment/myOrder',
    name: 'myOrder',
    meta: {
      title: ''
    },
    component: () => import('@/views/work/appointment/myOrder')
  },
  {
    // 预约-我的预约维修保洁详情
    path: '/work/appointment/cleanOrderDetail',
    name: 'cleanOrderDetail',
    meta: {
      title: ''
    },
    component: () => import('@/views/work/appointment/cleanOrderDetail')
  },
  {
    // 预约-我的预约招聘详情
    path: '/work/appointment/recruOrderDetail',
    name: 'recruOrderDetail',
    meta: {
      title: ''
    },
    component: () => import('@/views/work/appointment/recruOrderDetail')
  }
]
