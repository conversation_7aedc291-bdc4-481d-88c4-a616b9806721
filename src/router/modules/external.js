/*
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2021-11-15 14:37:06
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-01-12 22:28:50
 */
export default [
  {
    path: '/external',
    name: 'External',
    meta: {
      title: '本地生活'
    },
    component: () => import('@/views/external/index')
  },
  {
    path: '/external/searchNumber',
    name: 'SearchNumber',
    meta: {
      title: '查号台'
    },
    component: () => import('@/views/external/searchNumber')
  },
  {
    path: '/external/webview',
    name: 'Webview',
    meta: {
      title: '点滴'
    },
    component: () => import('@/views/external/webview')
  },
  {
    path: '/external/order',
    name: 'ExternalOrder',
    meta: {
      title: '我的预约'
    },
    component: () => import('@/views/external/order')
  },
  {
    path: '/external/ccb',
    name: 'CcbIndex',
    meta: {
      title: '点滴 & 建行生活'
    },
    component: () => import('@/views/external/ccb')
  },
  {
    path: '/external/ccb/result',
    name: 'CcbResult',
    meta: {
      title: '点滴 & 建行生活'
    },
    component: () => import('@/views/external/ccb/result')
  }
]
