/*
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2021-04-26 10:33:43
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-09-20 14:05:43
 */
export default [{
  path: '/submitOrder',
  name: 'SubmitOrder',
  meta: {
    title: '提交订单'
  },
  component: () => import('@/views/takeout/submitOrder.vue')
},
{
  path: '/submitSetMeal', // 团购订单
  name: 'SubmitSetMeal',
  meta: {
    title: '提交订单'
  },
  component: () => import('@/views/takeout/submitSetMeal.vue')
},
{
  path: '/remarks',
  name: 'Remarks',
  meta: {
    title: '备注'
  },
  component: () => import('@/views/takeout/remarks.vue')
}
]
