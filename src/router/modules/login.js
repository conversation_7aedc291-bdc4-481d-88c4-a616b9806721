/*
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2021-06-01 10:32:26
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-02-18 17:33:33
 */
export default [
  // 验证码的登录-手机号输入页面
  {
    path: '/wxLogin2',
    name: 'wxLogin2',
    component: () => import('@/views/loginV2/wxLogin2')
  },
  // 验证码的登录-手机号输入页面
  {
    path: '/codeLogin',
    name: 'CodeLogin',
    component: () => import('@/views/loginV2/codeLogin')
  },
  // 验证码的登录-验证码输入页面
  {
    path: '/codeLogin/code',
    name: 'CodeLoginCode',
    component: () => import('@/views/loginV2/codeLogin/code')
  },
  // 密码登录
  {
    path: '/pwdLogin',
    name: 'PwdLogin',
    component: () => import('@/views/loginV2/pwdLogin')
  },
  // 找回密码-1
  {
    path: '/register',
    name: 'Register',
    component: () => import('@/views/loginV2/register')
  },
  // 找回密码-2
  {
    path: '/register/newPwd',
    name: 'RegisterNewPwd',
    component: () => import('@/views/loginV2/register/newPwd')
  },
  // 找回密码-3
  {
    path: '/register/newPwdCode',
    name: 'RegisterNewPwdCode',
    component: () => import('@/views/loginV2/register/code')
  },
  {
    path: '/privacy',
    name: 'Privacy',
    component: () => import('@/views/loginV2/bargain/privacy')
  },
  {
    path: '/useragt',
    name: 'Useragt',
    component: () => import('@/views/loginV2/bargain/useragt')
  },
  // 注销协议
  {
    path: '/cancellationMsg',
    name: 'CancellationMsg',
    component: () => import('@/views/loginV2/bargain/cancellationMsg')
  },
  // 微信登录-手机号输入页面
  {
    path: '/wxLogin',
    name: 'WxLogin',
    component: () => import('@/views/loginV2/wxLogin')
  },
  // 微信登录-验证码输入页面
  {
    path: '/wxLogin/code',
    name: 'WxLoginCode',
    component: () => import('@/views/loginV2/wxLogin/code')
  }
]

