/*
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2021-04-26 10:33:43
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-09-22 21:52:40
 */
export default [{
  path: '/payment',
  name: 'Payment',
  meta: {
    title: '支付订单'
  },
  component: () => import('@/views/order/orderPayment')
},
{
  path: '/orderDetail',
  name: 'OrderDetail',
  meta: {
    title: '订单详情'
  },
  component: () => import('@/views/order/orderDetail')
},
{
  path: '/setMealorderList',
  name: 'SetMealorderList',
  meta: {
    title: '我的套餐团购订单'
  },
  component: () => import('@/views/order/moudel/setMeal/list')
},
{
  path: '/setMealorderDetail',
  name: 'SetMealorderDetail',
  meta: {
    title: '订单详情'
  },
  component: () => import('@/views/order/moudel/setMeal/detail')
},
{
  path: '/setMealorderRefund',
  name: 'SetMealorderRefund',
  meta: {
    title: '退款详情'
  },
  component: () => import('@/views/order/moudel/setMeal/detail/refund.vue')
},
{
  path: '/applyRefund',
  name: 'ApplyRefund',
  meta: {
    title: '申请退款'
  },
  component: () => import('@/views/order/moudel/setMeal/detail/applyRefund.vue')
},
{
  path: '/lookCode',
  name: 'LookCode',
  meta: {
    title: '查看券码'
  },
  component: () => import('@/views/order/moudel/setMeal/detail/lookCode.vue')
},
{
  path: '/setMealOrderPayment',
  name: 'SetMealOrderPayment',
  meta: {
    title: '支付订单'
  },
  component: () => import('@/views/order/moudel/setMeal/orderPayment')
}
]
