/*
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2021-07-30 10:05:14
 * @LastEditors: 孙立政
 * @LastEditTime: 2023-05-18 13:59:52
 */
export default [
  {
    path: '/school/list',
    name: 'SchoolList',
    meta: {
      title: '智慧校园'
    },
    component: () => import('@/views/school/list.vue')
  },
  {
    path: '/school/info',
    name: 'SchoolInfo',
    meta: {
      title: '智慧校园'
    },
    component: () => import('@/views/school/info.vue')
  },
  {
    path: '/school/hikInfo',
    name: 'SchoolHikInfo',
    meta: {
      title: '智慧校园'
    },
    component: () => import('@/views/school/hikInfo.vue')
  },
  {
    path: '/school/face',
    name: 'SchoolFace',
    meta: {
      title: '智慧校园'
    },
    component: () => import('@/views/school/face')
  },
  {
    path: '/school/faceOk',
    name: 'SchoolFaceOk',
    meta: {
      title: '智慧校园'
    },
    component: () => import('@/views/school/face/faceOk')
  },
  {
    path: '/school/pay',
    name: 'SchoolPay',
    meta: {
      title: '智慧校园'
    },
    component: () => import('@/views/school/pay')
  },
  {
    path: '/school/checkLog',
    name: 'SchoolCheckLog',
    meta: {
      title: '考勤记录'
    },
    component: () => import('@/views/school/checkLog')
  },
  {
    path: '/school/refund',
    name: 'SchoolRefund',
    meta: {
      title: '退款'
    },
    component: () => import('@/views/school/refund')
  },
  {
    path: '/school/bindBankCard',
    name: 'BindBankCard',
    meta: {
      title: '退款绑卡'
    },
    component: () => import('@/views/school/bindBankCard')
  }
]
