/*
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2021-04-26 10:33:43
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-09-19 15:27:50
 */
export default [
  {
    path: '/shop',
    name: 'Shop',
    meta: {
      title: '外卖店铺'
    },
    component: () => import('@/views/shop/index')
  },
  {
    path: '/setMeal',
    name: 'SetMeal',
    meta: {
      title: '美食套餐'
    },
    component: () => import('@/views/shop/setMeal')
  },
  {
    path: '/groupList',
    name: 'GroupList',
    meta: {
      title: '美食套餐列表'
    },
    component: () => import('@/views/shop/setMeal/moudel/GroupList/index.vue')
  },
  {
    path: '/fineFoodShop',
    name: 'FineFoodShop',
    meta: {
      title: '美食店铺'
    },
    component: () => import('@/views/shop/fineFood/index.vue'),
    children: [
      {
        path: '/fineFoodShopIndex',
        name: 'FineFoodShopIndex',
        component: () => import('@/views/shop/fineFood/index/index.vue') // 详情首页
      },
      {
        path: '/fineFoodShopEvalute',
        name: 'FineFoodShopEvalute',
        component: () => import('@/views/shop/fineFood/evalute') // 评价
      },
      {
        path: '/fineFoodShopMenuDetail',
        name: 'FineFoodShopMenuDetail',
        component: () => import('@/views/shop/fineFood/menuDetail') // 商品列表
      },
      {
        path: '/fineFoodShopAlbum',
        name: 'FineFoodShopAlbum',
        component: () => import('@/views/shop/fineFood/album') // 店铺相册
      }
    ]
  },
  {
    path: '/hotelShop',
    name: 'HotelShop',
    meta: {
      title: '酒店店铺'
    },
    component: () => import('@/views/shop/hotel/index.vue'),
    children: [
      {
        path: '/hotelShopIndex',
        name: 'HotelShopIndex',
        component: () => import('@/views/shop/hotel/index/index.vue') // 详情首页
      },
      {
        path: '/fineFoodShopEvalute',
        name: 'FineFoodShopEvalute',
        component: () => import('@/views/shop/fineFood/evalute') // 评价
      },
      {
        path: '/fineFoodShopAlbum',
        name: 'FineFoodShopAlbum',
        component: () => import('@/views/shop/fineFood/album') // 店铺相册
      }
    ]
  }
]
