/*
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-06-05 14:17:11
 * @LastEditors: 孙立政
 * @LastEditTime: 2021-07-13 11:26:35
 */
export default [
  {
    path: '/rest/restDefault', // l疗休养展示页
    name: 'restDefault',
    meta: {
      title: ''
    },
    component: () => import('@/views/rest/restDefault')
  },
  {
    path: '/rest/travelDetails', // l疗休养详情
    name: 'travelDetails',
    meta: {
      title: ''
    },
    component: () => import('@/views/rest/travelDetails')
  },
  {
    path: '/rest/travelList', // 商户活动
    name: 'travelList',
    meta: {
      title: ''
    },
    component: () => import('@/views/rest/travelList')
  },
  {
    path: '/rest/tagly', // 美宿美景乡村乡食
    name: 'tagly',
    meta: {
      title: ''
    },
    component: () => import('@/views/rest/tagly')
  },
  {
    path: '/rest/longquan/index', // 龙泉疗休养
    name: 'AgencyIndex',
    meta: {
      title: '',
      keepAlive: true
    },
    component: () => import('@/views/rest/longquan/index')
  },
  {
    path: '/rest/longquan/agency', // 龙泉疗休养-旅行社店铺页
    name: 'Agency',
    meta: {
      title: ''
    },
    component: () => import('@/views/rest/longquan/agency/index')
  },
  {
    path: '/rest/longquan/agencyDetail', // 龙泉疗休养-旅行社详情页
    name: 'AgencyDetail',
    meta: {
      title: ''
    },
    component: () => import('@/views/rest/longquan/agencyDetail')
  },
  {
    path: '/rest/longquan/list', // 龙泉疗休养-旅行社列表页
    name: 'AgencyList',
    meta: {
      title: '',
      keepAlive: true
    },
    component: () => import('@/views/rest/longquan/list')
  },
  {
    path: '/rest/qingyuan/index', // 龙泉疗休养
    name: 'AgencyIndexQY',
    meta: {
      title: '',
      keepAlive: true
    },
    component: () => import('@/views/rest/qingyuan/index')
  },
  {
    path: '/rest/qingyuan/components/transfer', // 龙泉-中转
    name: 'AgencyTransferQY',
    meta: {
      title: ''
    },
    component: () => import('@/views/rest/qingyuan/components/transfer')
  },
  {
    path: '/rest/longquan/subscribe', // 龙泉疗休养-预约记录
    name: 'SubscribeIndex',
    meta: {
      title: '',
      keepAlive: true
    },
    component: () => import('@/views/rest/qingyuan/subscribeList')
  },
  {
    path: '/rest/qingyuan/agency', // 龙泉疗休养-旅行社店铺页
    name: 'AgencyQY',
    meta: {
      title: ''
    },
    component: () => import('@/views/rest/qingyuan/agency/index')
  },
  {
    path: '/rest/qingyuan/agencyDetail', // 龙泉疗休养-旅行社详情页
    name: 'AgencyDetailQY',
    meta: {
      title: ''
    },
    component: () => import('@/views/rest/qingyuan/agencyDetail')
  },
  {
    path: '/rest/qingyuan/list', // 龙泉疗休养-旅行社列表页
    name: 'AgencyListQY',
    meta: {
      title: '',
      keepAlive: true
    },
    component: () => import('@/views/rest/qingyuan/list')
  },
  {
    path: '/rest/qingyuan/index', // 龙泉疗休养
    name: 'AgencyIndexQY',
    meta: {
      title: '',
      keepAlive: true
    },
    component: () => import('@/views/rest/qingyuan/index')
  },
  {
    path: '/rest/qingyuan/components/transfer', // 龙泉-中转
    name: 'AgencyTransferQY',
    meta: {
      title: ''
    },
    component: () => import('@/views/rest/qingyuan/components/transfer')
  },
  {
    path: '/rest/qingyuan/subscribe', // 龙泉疗休养-预约记录
    name: 'SubscribeIndexQY',
    meta: {
      title: '',
      keepAlive: true
    },
    component: () => import('@/views/rest/qingyuan/subscribeList')
  }

]
