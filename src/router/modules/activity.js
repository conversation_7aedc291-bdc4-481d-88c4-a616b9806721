/*
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2022-12-08 10:35:17
 * @LastEditors: 孙立政
 * @LastEditTime: 2023-08-21 16:15:12
 */
export default [{
  path: '/activity-winter',
  name: 'ActivityWinter',
  meta: {
    title: '暖冬活动'
  },
  component: () => import('@/views/activity/winter/index.vue')
}, {
  path: '/activity-nhj',
  name: 'ActivityNhj',
  meta: {
    title: '点滴年货节'
  },
  component: () => import('@/views/advertisement/nhj.vue')
}, {
  path: '/activity-yxj',
  name: 'ActivityYxj',
  meta: {
    title: '元宵节'
  },
  component: () => import('@/views/activity/yxj/index.vue')
}, {
  path: '/activity-qrj',
  name: 'ActivityQrj',
  meta: {
    title: '情人节'
  },
  component: () => import('@/views/activity/qrj/index.vue')
}, {
  path: '/activity-38',
  name: 'Activity38',
  meta: {
    title: '女神节'
  },
  component: () => import('@/views/activity/a38/index.vue')
}, {
  path: '/activity-qmj',
  name: 'ActivityQMJ',
  meta: {
    title: '清明节'
  },
  component: () => import('@/views/activity/qmj/index.vue')
}, {
  path: '/activity-51',
  name: 'Activity51',
  meta: {
    title: '劳动节'
  },
  component: () => import('@/views/activity/a51/index.vue')
}, {
  path: '/activity-520',
  name: 'Activity520',
  meta: {
    title: '520'
  },
  component: () => import('@/views/activity/a520/index.vue')
}, {
  path: '/activity-61',
  name: 'Activity61',
  meta: {
    title: '儿童节'
  },
  component: () => import('@/views/activity/a61/index.vue')
}, {
  path: '/activity-dwj',
  name: 'ActivityDwj',
  meta: {
    title: '端午节'
  },
  component: () => import('@/views/activity/dwj/index.vue')
}, {
  path: '/activity-jyz',
  name: 'ActivityJyz',
  meta: {
    title: '加油站',
    keepAlive: true
  },
  component: () => import('@/views/activity/jyz/index.vue')
}, {
  path: '/activity-lq-nsh',
  name: 'ActivityLqNsh',
  meta: {
    title: '龙泉农商行',
    keepAlive: true
  },
  component: () => import('@/views/activity/lq-nsh/index.vue')
}, {
  path: '/activity-lq-pay',
  name: 'ActivityLqPay',
  meta: {
    title: '龙泉充值',
    keepAlive: true
  },
  component: () => import('@/views/activity/lq-pay/index.vue')
}, {
  path: '/activity-lq-lf',
  name: 'ActivityLqLf',
  meta: {
    title: '龙泉旅发',
    keepAlive: true
  },
  component: () => import('@/views/activity/lq-lf/index.vue')
}, {
  path: '/activity-qixi',
  name: 'ActivityQixi',
  meta: {
    title: '七夕',
    keepAlive: true
  },
  component: () => import('@/views/activity/qixi/index.vue')
}, {
  path: '/activity-zqj',
  name: 'ActivityZqj',
  meta: {
    title: '中秋',
    keepAlive: true
  },
  component: () => import('@/views/activity/zqj/index.vue')
}, {
  path: '/lq-20230928',
  name: 'ActivityLq20230928',
  meta: {
    title: '龙泉',
    keepAlive: true
  },
  component: () => import('@/views/activity/lq-20230928/index.vue')
}, {
  path: '/lq-coupon',
  name: 'ActivityLqCoupon',
  meta: {
    title: '龙泉-优惠券活动'
  },
  component: () => import('@/views/activity/lq-coupon/index.vue')
}, {
  path: '/activity-nhj-sc',
  name: 'ActivityNhjSC',
  meta: {
    title: '点滴遂昌年货节'
  },
  component: () => import('@/views/activity/nhj-sc/index.vue')
}, {
  path: '/lq-coupon-nhj',
  name: 'ActivityLqCouponNHJ',
  meta: {
    title: '龙泉-春节优惠券活动'
  },
  component: () => import('@/views/activity/lq-coupon-nhj/index.vue')
}, {
  path: '/lq-38',
  name: 'ActivityLq38',
  meta: {
    title: '龙泉-幸运女神节'
  },
  component: () => import('@/views/activity/lq-38/index.vue')
}, {
  path: '/sc-20240520',
  name: 'ActivitySc20240520',
  meta: {
    title: '遂昌-520'
  },
  component: () => import('@/views/activity/sc-20240520/index.vue')
}, {
  path: '/lq-20240918',
  name: 'ActivityLq20240918',
  meta: {
    title: '龙泉',
    keepAlive: true
  },
  component: () => import('@/views/activity/lq-20240918/index.vue')
}, {
  path: '/sc-20250211',
  name: 'ActivitySc20250211',
  meta: {
    title: '遂昌元宵节',
    keepAlive: true
  },
  component: () => import('@/views/activity/sc-20250211/index.vue')
}, {
  path: '/lq-20250408',
  name: 'ActivityLq20250408',
  meta: {
    title: '龙泉',
    keepAlive: true
  },
  component: () => import('@/views/activity/lq-20250408/index.vue')
}, {
  path: '/sc-20250620',
  name: 'ActivitySc20250620',
  meta: {
    title: '儿童剧场',
    keepAlive: true
  },
  component: () => import('@/views/activity/sc-20250620/index.vue')
}]
