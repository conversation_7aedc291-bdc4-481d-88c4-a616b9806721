/*
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2021-05-31 16:31:54
 * @LastEditors: 孙立政
 * @LastEditTime: 2021-05-31 16:52:09
 */
// 扫码支付
export default [{
  path: '/scan/index',
  name: 'scan',
  meta: {
    title: '扫码支付'
  },
  component: () => import('@/views/scan/index.vue')
},
// 付款码
{
  path: '/scan/qrCode',
  name: 'qrCode',
  meta: {
    title: '付款码'
  },
  component: () => import('@/views/scan/qrCode.vue')
},
// 扫码支付
{
  path: '/scan/success',
  name: 'success',
  meta: {
    title: ''
  },
  component: () => import('@/views/scan/success.vue')
}
]
