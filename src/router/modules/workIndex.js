/*
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-03-31 16:29:35
 * @LastEditors: 孙立政
 * @LastEditTime: 2021-12-29 20:03:14
 */
export default [
  {
    // 本地服务首页
    path: '/workIndex/home/<USER>',
    name: 'home-workIndex',
    meta: {
      title: ''
    },
    component: () => import('@/views/workIndex/home/<USER>')
  },
  {
    // 需求发布-保洁
    path: '/workIndex/needs/clean',
    name: 'needsClean-workIndex',
    meta: {
      title: '',
      keepAlive: false
    },
    component: () => import('@/views/workIndex/needs/clean')
  },
  {
    // 需求发布-招人
    path: '/workIndex/needs/recruitment',
    name: 'needsRecruitment-workIndex',
    meta: {
      title: ''
    },
    component: () => import('@/views/workIndex/needs/recruitment')
  },
  {
    // 服务项目列表-家庭保洁/维修
    path: '/workIndex/list/listClean',
    name: 'listClean-workIndex',
    meta: {
      title: ''
    },
    component: () => import('@/views/workIndex/list/listClean')
  },
  {
    // 服务项目列表-招聘
    path: '/workIndex/list/listRecru',
    name: 'listRecru-workIndex',
    meta: {
      title: ''
    },
    component: () => import('@/views/workIndex/list/listRecru')
  },
  {
    // 服务项目列表-家庭保洁/维修详情
    path: '/workIndex/list/listCleanDetail',
    name: 'listCleanDetail-workIndex',
    meta: {
      title: ''
    },
    component: () => import('@/views/workIndex/list/listCleanDetail')
  },
  {
    // 服务项目列表-招聘详情
    path: '/workIndex/list/listRecruDetail',
    name: 'listRecruDetail-workIndex',
    meta: {
      title: ''
    },
    component: () => import('@/views/workIndex/list/listRecruDetail')
  },
  {
    // 预约-预约下单
    path: '/workIndex/appointment/anOrder',
    name: 'anOrder-workIndex',
    meta: {
      title: ''
    },
    component: () => import('@/views/workIndex/appointment/anOrder')
  },
  {
    // 预约-我的预约
    path: '/workIndex/appointment/myOrder',
    name: 'myOrder-workIndex',
    meta: {
      title: ''
    },
    component: () => import('@/views/workIndex/appointment/myOrder')
  },
  {
    // 预约-我的预约维修保洁详情
    path: '/workIndex/appointment/cleanOrderDetail',
    name: 'cleanOrderDetail-workIndex',
    meta: {
      title: ''
    },
    component: () => import('@/views/workIndex/appointment/cleanOrderDetail')
  },
  {
    // 预约-我的预约招聘详情
    path: '/workIndex/appointment/recruOrderDetail',
    name: 'recruOrderDetail-workIndex',
    meta: {
      title: ''
    },
    component: () => import('@/views/workIndex/appointment/recruOrderDetail')
  }
]
