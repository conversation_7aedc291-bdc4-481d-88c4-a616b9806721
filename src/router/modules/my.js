/*
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2021-04-26 10:33:43
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-11-26 09:54:50
 */
export default [{
  path: '/editPayPwd',
  name: 'EditPayPwd',
  meta: {
    title: '修改支付密码'
  },
  component: () => import('@/views/my/editPwd/editPayPwd.vue')
},
{
  path: '/editLoginPwd',
  name: 'EditLoginPwd',
  meta: {
    title: '修改登录密码'
  },
  component: () => import('@/views/my/editPwd/editLoginPwd.vue')
},
{
  path: '/editLoginPwdToPwd',
  name: 'EditLoginPwdToPwd',
  meta: {
    title: '修改登录密码'
  },
  component: () => import('@/views/my/editPwd/editLoginPwdToPwd.vue')
},
{
  path: '/account',
  name: 'Account',
  meta: {
    title: '账号与安全'
  },
  component: () => import('@/views/my/editPwd')
},
{
  path: '/account/editLoginPwdList',
  name: 'EditLoginPwdList',
  meta: {
    title: '账号与安全'
  },
  component: () => import('@/views/my/editPwd/editLoginPwdList.vue')
},
{
  path: '/canteen',
  name: 'Canteen',
  meta: {
    title: '我的食堂'
  },
  component: () => import('@/views/my/canteen/index.vue')
},
{
  path: '/aboutUs',
  name: 'AboutUs',
  meta: {
    title: '关于我们'
  },
  component: () => import('@/views/my/aboutUs/index.vue')
},
{
  path: '/sdk',
  name: 'sdk',
  meta: {
    title: '第三方sdk名单'
  },
  component: () => import('@/views/my/aboutUs/sdk.vue')
},
{
  path: '/feedBack',
  name: 'FeedBack',
  meta: {
    title: '意见反馈'
  },
  component: () => import('@/views/my/feedBack/index.vue')
},
{
  path: '/wallet',
  name: 'Wallet',
  meta: {
    title: '我的钱包',
    level: 1
  },
  component: () => import('@/views/my/wallet/index.vue')
},
{
  path: '/recharge',
  name: 'Recharge',
  meta: {
    title: '充值'
  },
  component: () => import('@/views/my/recharge/index.vue')
},
{
  path: '/bill',
  name: 'Bill',
  meta: {
    title: '我的账单'
  },
  component: () => import('@/views/my/bill/index.vue')
},
{
  path: '/billDetail',
  name: 'BillDetail',
  meta: {
    title: '我的账单详情'
  },
  component: () => import('@/views/my/bill/billDetail.vue')
},
{
  path: '/personInfo',
  name: 'PersonInfo',
  meta: {
    title: '个人中心'
  },
  component: () => import('@/views/my/personInfo/index.vue')
},
{
  // 我的人脸-密码验证
  path: '/my/face',
  name: 'Face',
  meta: {
    title: ''
  },
  component: () => import('@/views/my/face/formPwd.vue')
},
{
  // 我的人脸-拍照
  path: '/my/facePhoto',
  name: 'FacePhoto',
  meta: {
    title: ''
  },
  component: () => import('@/views/my/face/index.vue')
},
{
  // 我的人脸-结果
  path: '/my/result',
  name: 'FaceResult',
  meta: {
    title: ''
  },
  component: () => import('@/views/my/face/result.vue')
},
{
  // 我的人脸-已上传展示
  path: '/my/faceOk',
  name: 'FaceOk',
  meta: {
    title: ''
  },
  component: () => import('@/views/my/face/faceOk.vue')
},
{
  // 优惠券
  path: '/my/couponHistory',
  name: 'CouponHistory',
  meta: {
    title: ''
  },
  component: () => import('@/views/my/coupon/history.vue')
},
{
  // 历史优惠券
  path: '/my/coupon',
  name: 'Coupon',
  meta: {
    title: ''
  },
  component: () => import('@/views/my/coupon')
},
{
  // 优惠券说明
  path: '/my/rules',
  name: 'Rules',
  meta: {
    title: ''
  },
  component: () => import('@/views/my/coupon/rules.vue')
},
{
  // 分享
  path: '/my/share',
  name: 'Share',
  meta: {
    title: ''
  },
  component: () => import('@/views/my/share/index.vue')
},
{
  // 设置
  path: '/my/edit',
  name: 'Edit',
  meta: {
    title: '',
    level: 1
  },
  component: () => import('@/views/my/edit/index.vue')
},
{
  // 注销账户
  path: '/my/cancellation',
  name: 'Cancellation',
  meta: {
    title: '',
    level: 1
  },
  component: () => import('@/views/my/editPwd/cancellation.vue')
},
{
  // 注销账户
  path: '/my/collection',
  name: 'Collection',
  meta: {
    title: '',
    level: 1
  },
  component: () => import('@/views/my/collection')
}
]
