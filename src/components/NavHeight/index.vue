<!--
 * @Descripttion: 状态栏
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2021-06-16 14:26:48
 * @LastEditors: 孙立政
 * @LastEditTime: 2021-06-25 09:48:38
-->
<template>
  <div class="navHeight">
    <div v-if="type === 0" id="navHeight" :style="styleVar" :class="bgc?'bgc':'bgi'" />

    <div id="ocuHeight" :style="styleVar" />
  </div>
</template>

<script>
export default {
  name: 'NavHeight',
  props: {
    type: {
      type: Number,
      default: 0
    },
    bgc: {
      type: String,
      default: ''
    },
    bgi: {
      type: String,
      default: ''
    }
  },
  data() {
    return {

    }
  },
  computed: {
    styleVar() {
      return {
        '---nav-height': this.$store.getters.getStatusHeight + 'px',
        '---background-color': this.bgc,
        '---background-image': 'url(' + this.bgi + ')'
      }
    }
  },
  created() {

  },
  methods: {

  }
}
</script>

<style scoped lang="scss">
    .navHeight {
        #navHeight {
            width: 100%;
            position: fixed;
            top: 0;
            width: 100%;
            z-index: 999;
            height:calc(var(---nav-height) + 2px);
        }
        .bgi {
          background-image: var(---background-image);
            background-size: 100% auto;
        }
        .bgc {
            background: var(---background-color);
        }
        #ocuHeight {
            height:calc(var(---nav-height) + 2px);
        }
    }
</style>
