<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2021-05-26 15:31:33
 * @LastEditors: 孙立政
 * @LastEditTime: 2023-04-17 09:30:37
-->
<template>
  <div class="contents">
    <van-popup v-model="type" :style="{ height: '100%',width: '100%' }">
      <div class="warp" @click="close">
        <img src="../../assets/loading.gif">
      </div>
    </van-popup>
  </div>
</template>

<script>
export default {
  components: {},
  props: {
    show: {
      type: [Boolean, String],
      default: false
    },
    closeType: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      type: this.show,
      times: 0
    }
  },
  watch: {
    show(n, o) {
      this.type = n
    }
  },
  mounted() {
    this.off()
  },
  methods: {
    // 等待超过15秒自动关闭
    off() {
      let self = this
      let myVar = setInterval(function() {
        self.times++
        if (self.times > 15) {
          self.type = false
          clearInterval(myVar)
        }
      }, 1000)
    },
    // 判断是否允许点击关闭
    close() {
      if (this.closeType == true) {
        this.type = false
      }
    }
  }
}
</script>

<style lang="scss" scoped>
	.contents {
		width: 100%;
    position: relative;
    z-index: 5020;
		.warp {
			width: 100%;
			display: flex;
			align-items: center;
			justify-content: center;
			height: 100%;
            img{
                width: 260px;
                height: 380px;
            }
		}
	}
</style>
