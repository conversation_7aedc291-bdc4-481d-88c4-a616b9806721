<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2022-02-11 18:07:32
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-03-03 16:12:31
-->
<template>
  <div class="dialog">
    <van-overlay :show="show" z-index="1000">
      <div class="wrapper" @click.stop>
        <div class="block">
          <div class="title">提示</div>
          <div class="content">{{ msg }}</div>
          <div class="btn">
            <div class="btn-close" @click="close">取消</div>
            <div class="btn-sure" @click="confirm">切换</div>
          </div>
        </div>
      </div>
    </van-overlay>
  </div>
</template>

<script>
export default {
  props: {
    show: {
      type: Boolean,
      default: function() {
        return false
      }
    },
    msg: {
      type: String,
      default: function() {
        return '发现您所在的城市发生变化，是否切换'
      }
    }
  },
  data() {
    return {
    }
  },
  mounted() {
  },
  methods: {
    confirm() {
      this.$emit('confirm')
    },
    close() {
      this.$emit('close')
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog {
    .wrapper {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 100%;
    }
    .block {
        width: 602px;
        height: 406px;
        background: #ffffff;
        border-radius: 36px;
        .title{
            text-align: center;
            height: 138px;
            line-height: 138px;
            font-size: 43px;
            font-family: PingFangSC-Medium;
            color: #222222;
        }
        .content{
            width: 514px;
            height: 100px;
            overflow-y: auto;
            font-size: 32px;
            color: #666666;
            margin: 0 auto;
        }
        .btn{
            width: 514px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            margin-top: 45px;
            div{
                width: 230px;
                height: 80px;
                text-align: center;
                line-height: 80px;
                border-radius: 42px;
                font-size: 37px;
                font-family: PingFangSC-Medium;
            }
            .btn-close{
                color: #666666;
                border: 2px solid #c2c2c2;
            }
            .btn-sure{
                background: #39cf3f;
                color: #ffffff;
            }
        }
    }
}
</style>
