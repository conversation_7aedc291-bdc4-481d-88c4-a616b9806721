<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2022-02-11 18:07:32
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-02-17 14:30:37
-->
<template>
  <div class="dialog">
    <van-overlay :show="show" z-index="1000">
      <div class="wrapper" @click.stop>
        <div class="block">
          <div class="title">提示</div>
          <div class="content">
            登录注册需您阅读并同意<span class="remind" @click="goUseragt">《用户服务协议》</span>和<span class="remind" @click="goPrivacy">《隐私协议》</span>，同意后可开始使用我们的服务
          </div>
          <div class="btn">
            <div class="btn-close" @click="close">不同意</div>
            <div class="btn-sure" @click="confirm">同意</div>
          </div>
        </div>
      </div>
    </van-overlay>
  </div>
</template>

<script>
export default {
  props: {
    show: {
      type: Boolean,
      default: function() {
        return false
      }
    }
  },
  data() {
    return {
    }
  },
  mounted() {
  },
  methods: {
    goUseragt() {
      this.$router.push('/useragt')
    },
    goPrivacy() {
      // 隐私协议
      this.$router.push('/privacy')
    },
    confirm() {
      this.$emit('confirm')
    },
    close() {
      this.$emit('close')
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog {
    .wrapper {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 100%;
    }
    .block {
        width: 602px;
        height: 450px;
        background: #ffffff;
        border-radius: 36px;
        .title{
            text-align: center;
            height: 138px;
            line-height: 138px;
            font-size: 43px;
            font-family: PingFangSC-Medium;
            color: #222222;
        }
        .content{
            width: 514px;
            overflow-y: auto;
            font-size: 32px;
            color: #666666;
            margin: 0 auto;
            .remind{
                color: #39CF3F;
            }
        }
        .btn{
            width: 514px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            margin-top: 45px;
            div{
                width: 230px;
                height: 80px;
                text-align: center;
                line-height: 80px;
                border-radius: 42px;
                font-size: 37px;
                font-family: PingFangSC-Medium;
            }
            .btn-close{
                color: #666666;
                border: 2px solid #c2c2c2;
            }
            .btn-sure{
                background: #222222;
                color: #ffffff;
            }
        }
    }
}
</style>
