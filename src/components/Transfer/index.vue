<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2021-07-10 15:53:01
 * @LastEditors: 孙立政
 * @LastEditTime: 2021-07-15 09:55:49
-->
<template>
  <div class="home" />
</template>

<script>
export default {
  data() {
    return {

    }
  },
  created() {

  },
  mounted() {
    this.$router.push({
      name: 'AgencyDetail',
      query: {
        id: this.$route.query.id,
        marketId: this.$route.query.marketId,
        from: 'trans'
      }
    })
  },
  methods: {

  }
}
</script>

<style scoped lang="scss">
</style>
