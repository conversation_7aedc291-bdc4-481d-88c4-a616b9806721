<!--
 * @Descripttion: 搜索页面列表卡片
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2021-05-22 16:45:57
 * @LastEditors: 孙立政
 * @LastEditTime: 2023-02-16 14:41:14
-->
<template>
  <div class="home">
    <div class="goodsCard" @click="goShop(list.id)">
      <div class="goodsCardBox">
        <div class="goodsCardLeft">
          <div class="goodsImg">
            <img :src="list.cover+'?x-oss-process=image/resize,w_700/format,jpg/quality,q_50'">
          </div>
        </div>
        <div class="goodsCardRight">
          <div class="goodsTitle">
            <div class="goodsLeft">
              {{ list.marketName | ellipsis(10) }}
            </div>
            <div v-if="list.status == true" class="goodsRightON">
              营业中
            </div>
            <div v-else class="goodsRightON">
              休息中
            </div>
            <!-- <div v-else class="goodsRightOff" /> -->
          </div>

          <div class="goodsCardBottom">
            <div class="goodsCardBottomCenter">
              <ul v-if="list.marketTags!=null" class="goodsTag">
                <li v-for="(tag, indexs) in list.marketTags.slice(0, 5)" :key="indexs">{{ tag.tagTitle | ellipsis(3) }}</li>
              </ul>
              <div class="goodsVolume">
                <span v-if="list.sales>0">已售{{ list.sales }}</span>

              </div>
            </div>
            <div class="goodsCardAddress">
              <div class="goodsCardAddressLeft">
                <div>起送￥{{ list.deliverLimitPrice }}</div>
                <div>配送￥{{ list.userPostFee }}</div>
              </div>
              <div>
                {{ list.distance.toFixed(2) }}km
              </div>
            </div>

          </div>

          <!-- <div v-if="list.goodsDTOList.length" class="goodsList">
            <div v-for="(goods, indexz) in list.goodsDTOList" :key="indexz" class="goodsListBox">
              <div class="goodsListImg">
                <img :src="goods.cover+'?x-oss-process=image/resize,w_700/format,jpg/quality,q_80'">
              </div>
              <div class="goodsListTitle">
                <span>{{ goods.goodsName | ellipsis(4) }}</span>
              </div>
              <div class="goodsListPrice">
                <div class="priceIcon">￥</div>
                <div class="prices">{{ goods.price }}</div>
                <div class="priceIconN">￥{{ goods.oriPrice }}</div>
              </div>
            </div>
          </div> -->
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    list: {
      type: Object,
      default: function() {
        return {}
      }
    }
  },
  methods: {
    // 跳转店铺
    goShop(row) {
      this.$store.state.cart.cartData[0].goodsList = []
      this.$store.state.market.marketData.remark = ''
      this.$router.push({
        name: 'Shop',
        query: {
          id: row
        }
      })
    },
    // 拨打电话
    getPhone(row) {
      AlipayJSBridge.call(
        'CallPhone',
        {
          phoneNum: row
        },
        function(result) {}
      )
    }
  }
}
</script>

<style lang="scss" scoped>
.home{
//   background: linear-gradient(180deg, #ffffff 1%, #f5f5f5);
  .goodsCard{
    width: 710px;
    // height: 352px;
    // max-height: 352px;
    min-height: 186px;
    background: #fff;
    border-radius: 16px;
    margin: 0 auto;
    margin-bottom: 20px;
    .goodsCardBox{
      width: 666px;
      // height: 162px;
      margin: 0 auto;
      padding-top: 22px;
      display: flex;
      .goodsImg{
        width: 124px;
        height: 124px;
        border-radius: 12px;
        img{
          width: 100%;
          height: 100%;
          border-radius: 12px;
        }
      }
      .goodsCardRight{
        width: 526px;
        margin-left: 22px;
        .goodsTitle{
          height: 45px;
          line-height: 45px;
          display: flex;
          justify-content: space-between;
          .goodsLeft{
            font-size: 32px;
            font-family: PingFangSC-Medium;
            color: #333333;
          }
          .goodsRightON{
            min-width: 74px;
            height: 32px;
            opacity: 1;
            border: 1px solid #0BB0FF;
            font-size: 20px;
            transform: scale(.9);
            text-align: center;
            line-height: 32px;
            color: #0BB0FF;
            margin-top: 5px;
          }
          .goodsRightOff{
            width: 91px;
            height: 38px;
            opacity: 1;
            background: #E5F6E5;
            border: 1px solid #169d1b;
            border-radius: 18px;
            font-size: 20px;
            text-align: center;
            line-height: 38px;
            color: #169D1B;
            transform: scale(.9);
          }
        }
        .goodsTag{
          height: 33px;
          line-height: 33px;
          font-size: 24px;
          color: #333333;
          li {
            display: inline-block;
            width: 90px;
            height: 35px;
            background: #FFF1E6;
            border-radius: 8px;
            text-align: center;
            line-height: 35px;
            color: #FF7406;
            font-size: 24px;
            transform: scale(.9);
            margin-right: 10px;
          }
        }
        .goodsCardBottom{
          font-size: 22px;
          color: #666666;
          margin-top: 6px;
          .goodsCardBottomCenter{
              display: flex;
              justify-content: space-between;
          }
          .goodsCardAddress{
            display: flex;
            justify-content: space-between;
            height: 33px;
            line-height: 33px;
            color: #666;
            font-size: 22px;
            margin-top: 13px;
            .goodsCardAddressLeft{
                display: flex;
                div{
                    margin-right: 20px;
                }
            }
          }
        }
        .goodsList{
          display: flex;
          white-space: nowrap;
          overflow-x: auto;
          -webkit-overflow-scrolling:touch;
          overflow-y: hidden;
          .goodsListBox{
            width: 158px;
            height: 183px;
            margin-top: 32px;
            margin-bottom: 26px;
            margin-right: 18px;
          }
          .goodsListImg{
            width: 158px;
            height: 120px;
            margin-bottom: 8px;

            img{
              width: 100%;
              height: 100%;
              border-radius: 12px;
              object-fit: cover;
            }
          }

          .goodsListTitle{
            width: 140px;
            height: 28px;
            font-size: 27px;
            color: #333333;
            line-height: 28px;
            margin-top: 2px;
          }
          .goodsListPrice{
              height: 40px;
              line-height: 40px;
              display: flex;
              color: #FF301E;
              margin-top: 3px;
              .priceIcon{
                font-size: 18px;
                line-height: 42px;
              }
              .priceIconN{
                font-size: 18px;
                color: #999999;
                margin-left: 7px;
                 line-height: 42px;
                text-decoration: line-through;
              }
              .prices{
                font-size: 27px;
              }
          }
        }
      }
    }
  }
}
</style>
