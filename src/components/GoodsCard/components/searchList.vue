<!--
 * @Descripttion: 搜索页面列表卡片
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2021-05-22 16:45:57
 * @LastEditors: 孙立政
 * @LastEditTime: 2023-04-20 11:32:46
-->
<template>
  <div class="home">
    <div class="goodsCard" @click="goShop(list)">
      <div class="goodsCardBox">
        <div class="goodsCardLeft">
          <div class="goodsImg">
            <img :src="list.cover+'?x-oss-process=image/resize,w_700/format,jpg/quality,q_50'">
          </div>
        </div>
        <div class="goodsCardRight">
          <div class="goodsTitle">
            <div class="goodsLeft">
              {{ list.marketName | ellipsis(10) }}
            </div>
            <div v-if="list.status == true" class="goodsRightON">
              营业中
            </div>
            <div v-else class="goodsRightOff">
              休息中
            </div>
          </div>

          <div class="goodsCardBottom">
            <div>
              <ul v-if="list.marketTags!=null" class="goodsTag">
                <li v-for="(tag, indexs) in list.marketTags.slice(0, 5)" :key="indexs">{{ tag.tagTitle | ellipsis(3) }}</li>
              </ul>
              <div class="goodsCardAddress">
                <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/components/GoodsCard/address.png">
                {{ list.address | ellipsis(15) }}
              </div>
            </div>
            <div class="goodsCardPhone" @click.stop="getPhone(list.mobilePhone)">
              <div>
                <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/components/GoodsCard/phone.png">
              </div>
              <div>
                电话
              </div>
            </div>
          </div>

          <!-- <div v-if="list.goodsDTOList.length" class="goodsList">
            <div v-for="(goods, indexz) in list.goodsDTOList" :key="indexz" class="goodsListBox">
              <div class="goodsListImg">
                <img :src="goods.cover+'?x-oss-process=image/resize,w_700/format,jpg/quality,q_80'">
              </div>
              <div class="goodsListTitle">
                <span>{{ goods.goodsName | ellipsis(4) }}</span>
              </div>
            </div>
          </div> -->
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    list: {
      type: Object,
      default: function() {
        return {}
      }
    }
  },
  methods: {
    // 跳转店铺
    goShop(row) {
      if (row.type == 15 && this.$store.getters.getRegionId != 1) {
        this.$router.push({
          name: 'FineFoodShopIndex',
          query: {
            id: row.id,
            type: this.type
          }
        })
      } else {
        this.$store.state.cart.cartData[0].goodsList = []
        this.$store.state.market.marketData.remark = ''
        this.$router.push({
          name: 'Shop',
          query: {
            id: row.id
          }
        })
      }
    },
    // 拨打电话
    getPhone(row) {
      AlipayJSBridge.call(
        'CallPhone',
        {
          phoneNum: row
        },
        function(result) {}
      )
    }
  }
}
</script>

<style lang="scss" scoped>
.home{
  background: linear-gradient(180deg, #ffffff 1%, #f5f5f5);
  .goodsCard{
    width: 710px;
    // height: 352px;
    // max-height: 352px;
    min-height: 186px;
    background: #fff;
    border-radius: 8px;
    margin: 0 auto 16px;
    .goodsCardBox{
      width: 666px;
      // height: 162px;
      margin: 0 auto;
      padding-top: 22px;
      display: flex;
      .goodsImg{
        width: 124px;
        height: 122px;
        border-radius: 12px;
        img{
          width: 100%;
          height: 100%;
          border-radius: 12px;
        }
      }
      .goodsCardRight{
        width: 526px;
        margin-left: 22px;
        .goodsTitle{
          height: 45px;
          line-height: 45px;
          display: flex;
          justify-content: space-between;
          .goodsLeft{
            font-size: 32px;
            font-weight: bold;
            color: #333333;
          }
          .goodsRightON{
            width: 91px;
            height: 38px;
            opacity: 1;
            background: #ffefe2;
            border: 1px solid #ff7807;
            border-radius: 18px;
            font-size: 20px;
            transform: scale(.9);
            text-align: center;
            line-height: 38px;
            color: #FF7807;
          }
          .goodsRightOff{
            width: 91px;
            height: 38px;
            opacity: 1;
            background: #E5F6E5;
            border: 1px solid #169d1b;
            border-radius: 18px;
            font-size: 20px;
            text-align: center;
            line-height: 38px;
            color: #169D1B;
            transform: scale(.9);
          }
        }
        .goodsTag{
          height: 33px;
          line-height: 33px;
          font-size: 24px;
          color: #333333;
          li {
            display: inline-block;
            width: 90px;
            height: 35px;
            background: #FFF0EF;
            border-radius: 8px;
            text-align: center;
            line-height: 35px;
            color: #FF4534;
            font-size: 24px;
            transform: scale(.9);
            margin-right: 10px;
          }
        }
        .goodsCardBottom{
          display: flex;
          justify-content: space-between;
          align-items: center;
          font-size: 22px;
          color: #666666;
          margin-top: 6px;
          .goodsCardAddress{
            display: inline-block;
            height: 33px;
            line-height: 33px;
            border: 1px solid #baa66e;
            border-radius: 9px;
            color: #B49F61;
            font-size: 22px;
            transform: scale(.9);
            margin-top: 12px;
            padding-right: 10px;
            margin-left: -3px;
            img {
              width: 18px;
              height: 20px;
              margin-left: 8px;
              position: relative;
              top: 2px;
            }
          }
          .goodsCardPhone{
            width: 70px;
            text-align: center;
            font-size: 18px;
            color: #999999;
            border-left: 1px solid #eee;

            img {
              width: 33px;
              height: 33px;
            }
          }
        }
        .goodsList{
          display: flex;
          white-space: nowrap;
          overflow-x: auto;
          -webkit-overflow-scrolling:touch;
          overflow-y: hidden;
          .goodsListBox{
            width: 160px;
            height: 183px;
            margin-top: 32px;
          }
          .goodsListImg{
            width: 120px;
            height: 118px;
            margin-bottom: 8px;
            img{
              width: 100%;
              height: 100%;
              border-radius: 12px;
              margin-right: 14px;
            }
          }

          .goodsListTitle{
            width: 140px;
            height: 28px;
            font-size: 20px;
            font-weight: 400;
            text-align: center;
            color: #333333;
            line-height: 28px;
            margin-top: 2px;
          }
        }
      }
    }
  }
}
</style>
