<!--
 * @Descripttion: 商品卡片
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2021-05-22 16:45:57
 * @LastEditors: 孙立政
 * @LastEditTime: 2023-06-30 17:55:36
-->
<template>
  <div class="home">
    <!-- 外卖列表 -->
    <template v-if="type == 1">
      <div v-show="!list.isShow" class="goodsCard" @click="goShop(list.id)">
        <div class="goodsCardBox">
          <div class="goodsCardLeft">
            <div class="goodsImg">
              <van-image
                width="81"
                radius="6"
                height="81"
                :src="list.cover + '?x-oss-process=image/resize,w_700/format,jpg/quality,q_80'"
              />
              <!-- <img :src="list.cover+'?x-oss-process=image/resize,w_700/format,jpg/quality,q_80'"> -->
              <div v-if="!list.status&&list.status!=null" class="status_tips">休息中</div>
            </div>
          </div>
          <div class="goodsCardRight">
            <div class="goodsTitle" style="display: flex;justify-content: space-between;">
              <div>
                {{ list.marketName | ellipsis(10) }}
              </div>

              <div v-if="list.isFeatured" class="goodsRightON">
                推荐
              </div>
            </div>
            <div class="goodsSales">
              <div style="display:flex">
                <!-- <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/shop/start.png" alt=""> -->
                <!-- <span class="goodsSalesF">4.8</span> -->
                <span v-if="this.$store.getters.getRegionId == 3" class="goodsSalesNum">交易量 {{ list.orderCount }}</span>
                <span v-else class="goodsSalesNum">已售{{ list.sales }}</span>
              </div>
              <div v-if="list.distance > 0.5" class="goodsSalesDistance">{{ list.distance.toFixed(2) }}km</div>
              <div v-else class="goodsSalesDistance">{{ list.distance.toFixed(2) * 1000 }}m</div>
            </div>
            <div v-if="'couponList' in list && list.couponList != null" class="goodsCouponTag">
              <div class="coupon_box">
                <div v-for="couponItem in list.couponList.slice(0, 3)" :key="couponItem.couponId">
                  <span>满{{ couponItem.useThreshold }}减{{ couponItem.preferentialAmount }}</span>
                </div>
              </div>

              <van-icon
                v-if="list.couponList != null && list.couponList.length > 3"
                name="arrow-down"
                class="goodsCouponTag_arrow"
              />
            </div>
            <div class="goodsCardBottom">
              <div v-if="this.$store.getters.getRegionId != 3">
                <span class="goodsPriceStart">起送￥{{ list.deliverLimitPrice }}</span>
                <span v-if="!list.isDistanceExceeded && list.userPostFee > 0" class="goodsPriceEnd">
                  配送￥{{ list.userPostFee }}
                  <span v-if="list.userPostFee != list.oriPostFee">￥{{ list.oriPostFee }}</span>
                </span>
                <span
                  v-else-if="!list.isDistanceExceeded && list.userPostFee == 0"
                  style="color:#1fc432"
                  class="goodsPriceEnd"
                >免配送费</span>
                <span v-if="list.isDistanceExceeded" style="color:#ff4534" class="goodsPriceEnd">超出配送范围</span>
              </div>
            </div>
            <div class="goodsTag">
              <div v-for="(tag, indexs) in list.marketTags.slice(0, 5)" :key="indexs">{{ tag.tagTitle | ellipsis(3) }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </template>
    <!-- 美食、酒店列表 -->
    <template v-if="type == 2 || type == 5">
      <div class="goodsCard">
        <div class="goodsCardBox">
          <div class="goodsCardLeft" @click="goShop(list.id)">
            <div class="goodsImg">
              <van-image
                width="81"
                radius="5"
                height="81"
                fit="cover"
                :src="list.cover + '?x-oss-process=image/resize,w_700/format,jpg/quality,q_80'"
              />
              <!-- <img :src="list.cover+'?x-oss-process=image/resize,w_700/format,jpg/quality,q_80'"> -->
            </div>
          </div>
          <div class="goodsCardRight" @click="goShop(list.id)">
            <div>
              <div class="goodsTitle">
                {{ list.marketName | ellipsis(10) }}
              </div>
              <div class="goods-tag">
                <div v-if="list.marketCategorys != null">
                  <span v-for="(item, index) in list.marketCategorys.slice(0, 4)" :key="index">{{ item.name }}</span>
                </div>
                <div>
                  <span v-if="list.mealSetSales > 0" class="sold">已售

                    <span style="font-size: 12.5px;margin-left: -2px;">{{ list.mealSetSales }}</span> </span>
                </div>

              </div>

              <div class="goodsCardAddress">
                <div>{{ list.address | ellipsis(13) }}</div>
                <div v-if="list.distance != null && list.distance > 0.5" class="goodsSalesDistance"><van-icon
                  class="location_o"
                  name="location-o"
                />{{ list.distance.toFixed(2) }}km</div>
                <div v-if="list.distance != null && list.distance < 0.5" class="goodsSalesDistance"><van-icon
                  class="location_o"
                  name="location-o"
                />{{ list.distance.toFixed(2) * 1000 }}m</div>

              </div>
            </div>
            <div class="divider" />

            <div
              v-if="list.marketDiscounts != null && list.marketDiscounts[0].discount != 1 && list.marketDiscounts[0].discount != 0"
              class="goodsCardCoupon delicious"
            >
              <div class="goodsCardCouponLeft">惠</div>
              <div class="goodsCardCouponRight">
                进店扫码享
                <span style="font-size: 12.5px;margin-right: -3px;margin-left: -3px;">{{
                  parseFloat((list.marketDiscounts[0].discount * 10).toFixed(1)) }}</span>
                折优惠
              </div>
            </div>
            <div v-else class="goodsCardCoupon">
              <div class="goodsCardCouponLeft">惠</div>
              <div class="goodsCardCouponRight">
                欢迎进店消费
              </div>
            </div>
            <div v-if="list.foodSetMeals">
              <div v-for="foods in list.foodSetMeals.slice(0, 3)" :key="foods.id" class="goodsCardGroup">
                <div class="goodsCardGroupLeft_tg_img">
                  <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/shop/fineFood/tgicon.png" alt="">
                </div>
                <div class="goodsCardGroupRight">
                  <span>￥{{ foods.specialPrice }}</span>
                  <span>￥{{ foods.originalPrice }}</span>
                  <span>{{ foods.setMealName }}</span>
                  <span v-if="foods.minNumberOfDiners != foods.maxNumberOfDiners" style="font-size: 12px;">{{
                    foods.minNumberOfDiners }}-{{ foods.maxNumberOfDiners }}人餐</span>
                  <span v-else style="font-size: 12px;">{{ foods.maxNumberOfDiners }}人餐</span>
                </div>
              </div>

            </div>

          </div>
        </div>
      </div>
    </template>

    <!-- 菜市场 -->
    <template v-if="type == 3">
      <SearchList :list="list" />
    </template>

    <!-- 生鲜 -->
    <template v-if="type == 4">
      <Fresh :list="list" />
    </template>
  </div>
</template>

<script>
import SearchList from './components/searchList'
import Fresh from './components/fresh'
export default {
  components: { SearchList, Fresh },
  props: {
    type: {
      type: [Number, String],
      default: 1
    },
    list: {
      type: Object,
      default: function() {
        return {}
      }
    }
  },
  created() {
  },
  methods: {
    // 跳转店铺
    goShop(row) {
      if (this.$route.query.id == 154) {
        this.$router.push({
          name: 'AgencyQY',
          query: {
            id: row,
            marketId: row,
            from: this.$router.history.current.name,
            cateId: 154
          }
        })
        return
      }

      this.$store.state.cart.cartData[0].goodsList = []
      this.$store.state.market.marketData.remark = ''

      if (this.type == 2 && this.$store.getters.getRegionId != 1 && this.$store.getters.getRegionId != 3) {
        this.$router.push({
          name: 'FineFoodShopIndex',
          query: {
            id: row,
            type: this.type
          }
        })
        return
      } if (this.type == 2 && (this.$store.getters.getRegionId == 1 || this.$store.getters.getRegionId == 3)) {
        this.$router.push({
          name: 'SetMeal',
          query: {
            id: row,
            type: this.type
          }
        })
      } else if (this.type == 5) {
        this.$router.push({
          name: 'HotelShopIndex',
          query: {
            id: row,
            type: this.type
          }
        })
      } else {
        this.$router.push({
          name: 'Shop',
          query: {
            id: row
          }
        })
      }
    },
    // 拨打电话
    getPhone(row) {
      AlipayJSBridge.call(
        'CallPhone',
        {
          phoneNum: row
        },
        function(result) { }
      )
    }
  }
}
</script>

<style lang="scss" scoped>
.home {
  .goodsCard {
    width: 710px;
    min-height: 206px;
    background: #fff;
    border-radius: 8px;
    margin: 0 auto 16px;
    overflow: hidden;

    .goodsCardBox {
      width: 666px;
      min-height: 162px;
      margin-top: 22px;
      margin-left: 22px;
      margin-bottom: 22px;
      display: flex;

      .goodsImg {
        width: 162px;
        height: 162px;
        border-radius: 12px;
        position: relative;
        overflow: hidden;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
          border-radius: 12px;
        }

        .status_tips {
          position: absolute;
          bottom: 0;
          width: 160px;
          height: 40px;
          opacity: 0.8;
          background: #000000;
          color: #fff;
          font-size: 24px;
          text-align: center;
          line-height: 40px;
          border-bottom-right-radius: 12px;
          border-bottom-left-radius: 12px;
        }
      }

      .goodsCardRight {
        line-height: 0;
        width: 480px;
        margin-left: 22px;
        //推荐标签
        .goodsRightON{
          min-width: 74px;
          height: 32px;
          opacity: 1;
          background: linear-gradient(to right, #FFD700, #FFC107);
          font-size: 20px;
          text-align: center;
          line-height: 32px;
          color: #8B4513;
          margin-top: 5px;
          border-radius: 16px;
          padding: 0 10px;
          box-shadow: 0 2px 4px rgba(255, 215, 0, 0.5);
          position: relative;
          overflow: hidden;
          font-weight: 600;
          letter-spacing: 1px;
        }
        .goodsRightON::before {
          content: '';
          position: absolute;
          top: 0;
          left: -100%;
          width: 100%;
          height: 100%;
          background: linear-gradient(to right, transparent, rgba(255, 255, 255, 0.5), transparent);
          animation: shine 2s infinite;
        }
        @keyframes shine {
          to {
            left: 100%;
          }
        }

        .goodsTitle {
          height: 45px;
          line-height: 45px;
          font-size: 32px;
          font-weight: 500;
          color: #333333;
          margin-top: 5px;
          font-family: PingFangSC-Medium;
        }

        .goods-tag {
          color: #666666;
          font-size: 25px;
          font-weight: 500;
          font-family: PingFangSC-Medium;
          margin-top: 20px;
          display: flex;
          justify-content: space-between;

          .sold {
            font-weight: 400;
            font-family: PingFangSC;
          }

        }

        .goodsTag {
          height: 33px;
          line-height: 33px;
          font-size: 24px;
          color: #333333;
          font-weight: 400;
          margin-top: 11px;
          display: flex;

          div {
            min-width: 64px;
            height: 32px;
            background: #FFF5EC;
            border-radius: 8px;
            font-family: PingFangSC-Medium;
            font-size: 20px;
            color: #FF7807;
            text-align: center;
            line-height: 32px;
            margin-right: 10px;
          }

        }

        .goodsCouponTag {
          height: 33px;
          font-size: 24px;
          color: #333333;
          font-weight: 400;
          margin-top: 10px;
          display: flex;
          justify-content: space-between;

          .coupon_box {
            height: 33px;
            display: flex;
            border-radius: 8px;
            line-height: 23px;
            border: 1px solid #ffd0d3;

            div {
              min-width: 100px;
              height: 21px;
              margin-top: 5px;
              font-family: PingFangSC-Medium;
              font-size: 20px;
              color: #FF6248;
              text-align: center;
              letter-spacing: 1px;
              border-right: 1px solid #ffd0d3;

              span {
                margin-left: 5px;
                margin-right: 5px;
              }
            }
          }

          .goodsCouponTag_arrow {
            color: #999;
            margin-left: 20px;
            font-size: 17px;
            position: relative;
            top: 8px;
            opacity: .5;
          }
        }

        .goodsSales {
          display: flex;
          justify-content: space-between;
          height: 33px;
          line-height: 33px;
          // margin-top: 8px;
          font-size: 24px;

          img {
            width: 23px;
            height: 23px;
            position: relative;
            top: 2px;
          }

          .goodsSalesF {
            font-family: PingFangSC;
            font-weight: 600;
            color: #ff7807;
          }

          .goodsSalesNum {
            font-size: 22px;
            color: #666666;
            font-family: PingFangSC;
          }

          .goodsSalesDistance {
            font-size: 22px;
            color: #666666;
            font-family: PingFangSC;
          }
        }

        .goodsCardBottom {
          height: 30px;
          line-height: 30px;
          display: flex;
          justify-content: space-between;
          font-size: 22px;
          color: #666666;
          font-family: PingFangSC;
          font-weight: 400;
          margin-top: 3px;

          .goodsPriceEnd {
            margin-left: 40px;

            span {
              margin-left: 2px;
              text-decoration: line-through;
            }
          }
        }

        .goodsCardAddress {
          display: flex;
          justify-content: space-between;
          height: 31px;
          line-height: 31px;
          border-radius: 9px;
          color: #787878;
          font-size: 22px;
          margin-top: 25px;
          margin-left: 2px;

          div:nth-child(1) {
            font-size: 24px;
          }

          img {
            width: 18px;
            height: 20px;
            margin-left: 8px;
            position: relative;
            top: 2px;
          }
        }

        .location_o {
          position: relative;
          top: 3px;
        }

        .divider {
          width: 100%;
          height: 1px;
          background: #f5f5ff;
          margin-top: 20px;
        }
      }

      .goodsCardGroup {
        display: flex;
        height: 33px;
        line-height: 33px;
        margin-top: 12px;

        .goodsCardGroupLeft {
          width: 32px;
          height: 32px;
          opacity: 1;
          background: #ff6a32;
          border-radius: 6px;
          text-align: center;
          line-height: 32px;
          color: #fff;
          font-size: 20px;
          transform: scale(.9);

        }

        .goodsCardGroupLeft_tg_img {
          img {
            width: 30px;
            height: 30px;
            float: left;
          }
        }

        .goodsCardGroupRight {
          font-size: 25px;
          color: #333333;
          margin-left: 6px;
          font-weight: 400;
          overflow: hidden;

          span:nth-child(1) {
            margin-right: 8px;
          }

          span:nth-child(2) {
            color: #999;
            margin-right: 8px;
            text-decoration: line-through;
          }

          span:nth-child(3) {
            margin-right: 8px;
          }
        }
      }

      .goodsCardCoupon {
        display: flex;
        height: 33px;
        line-height: 33px;
        margin-top: 20px;

        .goodsCardCouponLeft {
          width: 32px;
          height: 32px;
          opacity: 1;
          background: #ff6a32;
          border-radius: 6px;
          text-align: center;
          line-height: 32px;
          color: #fff;
          font-size: 20px;
          transform: scale(.9);
        }

        .goodsCardCouponRight {
          font-size: 26px;
          color: #333333;
          margin-left: 6px;
        }
      }

      .goodsCardSeparate {
        width: 2px;
        height: 43px;
        background-color: #f4f4f4;
        margin-top: 41px;
      }

    }
  }
}</style>
