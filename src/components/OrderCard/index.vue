<!--
 * @Descripttion: 订单卡片
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2021-05-25 16:30:47
 * @LastEditors: 孙立政
 * @LastEditTime: 2021-12-29 10:57:12
-->
<template>
  <div class="">
    <TakeoutOrder v-if="list.paymentChannel == 12||list.paymentChannel == 7" :list="format()" @clickHandler1="clickHandler2" />
    <NotTakeoutOrder v-if="list.paymentChannel == 2|| list.paymentChannel == 3" :list="format()" @clickHandler1="clickHandler2" />
  </div>
</template>

<script>
import TakeoutOrder from './components/takeoutOrder'
import NotTakeoutOrder from './components/notTakeoutOrder'
export default {
  components: {
    TakeoutOrder,
    NotTakeoutOrder
  },
  props: {
    list: {
      type: Object,
      default: function() {
        return {}
      }
    }
  },
  mounted() {},
  methods: {
    format() {
      if (this.list.orderDeliver == null) {
        this.list.orderDeliver = {}
        this.list.orderDeliver.deliverPart = ''
      }
      return this.list
    },
    clickHandler2(type, data) {
      this.$emit('clickHandler3', type, data)
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
