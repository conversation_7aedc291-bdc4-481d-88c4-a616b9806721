<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2021-05-25 16:42:47
 * @LastEditors: 孙立政
 * @LastEditTime: 2021-12-30 21:27:04
-->
<template>
  <div class="home">
    <div class="coderCard">
      <div class="coderCardTop" @click="goShop(list)">
        <div class="coderCardTopLeft">
          <img
            v-if="list.paymentChannel == 12"
            src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/shoppingMall/logo.png"
            class="coderCardTopLeftTypeImg"
          >
          <img
            v-else
            src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/order/takeout.png"
            class="coderCardTopLeftTypeImg"
          >
          <div class="coderCardTopLeftTypeTitle">{{ list.marketName | ellipsis(12) }}</div>
          <img
            class="coderCardTopLeftRightIcon"
            src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/order/right.png"
          >
        </div>
        <div v-if="list.paymentChannel != 12" class="coderCardTopRight">
          <span v-if="list.doRefund == true && list.orderRefund.refundStatus < 3">退款中</span>
          <span v-else-if="list.status == 0">待付款</span>
          <span v-else-if="list.status == 1">待接单</span>
          <span v-else-if="list.status == 2&&list.deliveryType!=2">待配送</span>
          <span v-else-if="list.status == 3">配送中</span>
          <span v-else-if="list.status == 4">已完成</span>
          <span v-else-if="list.status == 41">已完成</span>
          <span v-else-if="list.status == 5">已取消</span>
          <span v-else-if="list.status == 6">退款中</span>
          <span v-else-if="list.status == 7">已退款</span>
          <span v-if="list.status == 2&&list.deliveryType==2">待自取</span>
        </div>
      </div>
      <div class="separate" />
      <div class="coderCardGoods" @click="goOrderDetail(list)">
        <div class="coderCardGoodsList">
          <div v-for="(item, index) in list.orderGoodsList" :key="index" class="coderCardGoodsListFor">
            <div class="coderCardGoodsListImg">
              <img :src="item.skuImage + '?x-oss-process=image/resize,w_700/format,jpg/quality,q_35' " :onerror="errorGoodsImg" alt="">
            </div>
            <div class="coderCardGoodsListTitle">{{ item.goodsName|ellipsis(4) }}</div>
          </div>
        </div>
        <div class="coderCardGoodsRight">
          <div class="coderCardGoodsRightPrice">￥{{ list.actualPay }}</div>
          <div v-if="list.orderGoodsList!=null" class="coderCardGoodsRightNum">共{{ list.orderGoodsList.length }}件</div>
        </div>
      </div>
      <div class="coderCardTime">下单时间：{{ list.createTime }}</div>
      <div class="coderCardBtn">
        <div v-if="list.status == 0" @click="payOrder(list)">立即支付</div>
        <div v-if="list.evaStatus==1&&list.status==4&&list.paymentChannel != 12" @click="evaluate(list)">我要评价</div>
        <div v-if="list.paymentChannel == 12&&list.status == 4" @click="goSysShop(list)">再来一单</div>

        <div v-if="(list.status == 1||list.status == 2) && list.doRefund != true&&list.paymentChannel != 12&&list.deliveryType!=2&&list.orderDeliver.deliverPart==1" @click="refund (list)">我要退款</div>
        <div v-else-if="(list.status == 1||list.status == 2) &&(list.orderDeliver.deliverPart==1||list.orderDeliver.deliverPart==3)&&list.paymentChannel != 12" @click="refund (list)">我要退款</div>
        <div v-else-if="(list.status == 1||list.status == 2) && list.orderDeliver.deliverPart==2&&list.paymentChannel != 12" @click="refund (list)">我要退款</div>
        <div v-else-if="(list.status == 1||list.status == 2) && list.orderDeliver.deliverPart==0&&list.deliveryType!=2&&list.paymentChannel != 12" @click="refund (list)">我要退款</div>

        <div v-if="list.status == 0" class="btn" @click="cancelOrder(list)">取消订单</div>
        <div v-if="list.status == 2&&list.deliveryType==2&&list.paymentChannel != 12" class="btnGoods" @click="complete(list)">确认收货</div>
      </div>
    </div>
  </div>
</template>

<script>
import {
  addData
} from '@/utils/upLog.js'
export default {
  props: {
    list: {
      type: Object,
      default: function() {
        return {}
      }
    }
  },
  data() {
    return {
      errorGoodsImg: "this.src='https://diandi-app.oss-cn-hangzhou.aliyuncs.com/logo/errorimg.png'"
    }
  },
  created() {
  },
  methods: {
    // 再来一单
    goSysShop(row) {
      this.$router.push({
        name: 'ShoppingMallIndex',
        query: {
          id: row.marketId
        }
      })
    },
    // 订单详情
    goOrderDetail(list) {
      this.$router.push({
        name: 'OrderDetail',
        query: {
          orderNo: list.orderNo,
          type: list.paymentChannel
        }
      })
    },
    goShop(list) {
      // 订单列表轨迹
      addData(31)
      this.$store.state.cart.cartData[0].goodsList = []
      if (list.marketId == '1138') {
        this.$router.push({
          name: 'ShoppingMallIndex',
          query: {
          //     id: this.orderData.marketId
            id: 1138
          }
        })
      } else {
        this.$router.push({
          name: 'Shop',
          query: {
            id: list.marketId
          }
        })
      }
    },
    payOrder(list) {
      this.$emit('clickHandler1', 1, list)
    },
    evaluate(list) {
      this.$emit('clickHandler1', 2, list)
    },
    refund(list) {
      this.$emit('clickHandler1', 3, list)
    },
    cancelOrder(list) {
      this.$emit('clickHandler1', 4, list)
    },
    complete(list) {
      this.$emit('clickHandler1', 5, list)
    }
  }
}
</script>

<style lang="scss" scoped>
.home {
  .coderCard {
    width: 710px;
    // height: 440px;
    background: #fff;
    border-radius: 16px;
    margin: 0 auto;
    margin-bottom: 20px;
    padding-bottom: 24px;
    .coderCardTop {
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 90px;
      .coderCardTopLeft {
        width: 85%;
        display: flex;
        align-items: center;
        .coderCardTopLeftTypeImg {
          width: 46px;
          height: 46px;
          margin-left: 22px;
          vertical-align: middle;
        }
        .coderCardTopLeftTypeTitle {
          font-size: 30px;
          color: #333333;
          margin-left: 8px;
          font-family:PingFangSC-Medium;
          font-weight: 500;
          line-height: 90px;
        }
        .coderCardTopLeftRightIcon {
          margin-left: 4px;
          width: 22px;
          height: 22px;
          vertical-align: middle;
        }
      }
      .coderCardTopRight {
        width: 15%;
        text-align: center;
        font-size: 26px;
        color: #999999;
        margin-right: 10 px;
        font-family: PingFangSC;
      }
    }
    .separate {
      width: 690px;
      height: 1px;
      margin: 0 auto;
      border: 1px solid #f4f4f4;
    }
    .coderCardGoods {
      display: flex;
      margin-top: 22px;
      .coderCardGoodsList {
        width: 488px;
        display: flex;
        margin-left: 75px;
        overflow-x: auto;
        .coderCardGoodsListFor {
          flex-shrink: 0;
          width: 124px;
          height: 162px;
          font-size: 22px;
          margin-right: 12px;
          overflow: hidden;
          border-radius: 12px;
          img {
            width: 124px;
            height: 124px;
            border-radius: 12px;
          }
          .coderCardGoodsListTitle {
            color: #333;
            font-size: 22px;
            font-family: PingFangSC;
            font-weight: 400;
          }
        }
      }
      .coderCardGoodsRight {
        width: 150px;
        height: 162px;
        text-align: center;
        margin-right: 20px;
        .coderCardGoodsRightPrice {
          height: 40px;
          font-size: 28px;
          font-family:PingFangSC-Medium;
          font-weight: 500;
          text-align: right;
          color: #333333;
          line-height: 40px;
          margin-top: 25px;
        }
        .coderCardGoodsRightNum {
          height: 33px;
          font-size: 24px;
          font-family: PingFangSC;
          font-weight: 400;
          text-align: right;
          color: #999999;
          line-height: 33px;
        }
      }
    }
    .coderCardTime {
      height: 32px;
      font-size: 24px;
      font-family: PingFangSC;
      font-weight: 400;
      text-align: left;
      color: #999999;
      line-height: 33px;
      margin-left: 76px;
      margin-top: 32px;
    }
    .coderCardBtn {
      display: flex;
      justify-content: flex-end;
      width: 95%;
      margin:0 auto;
      color: #333;
      div {
        width: 124px;
        height: 54px;
        border: 1px solid #dddddd;
        border-radius: 9px;
        font-size: 24px;
        text-align: center;
        line-height: 54px;
        margin-top: 24px;
        margin-left: 20px;
      }
      .btnGoods{
        width: 124px;
        height: 54px;
        line-height: 54px;
        background: linear-gradient(90deg,#ff1e29, #ff5a25);
        border-radius: 8px;
        font-size: 24px;
        text-align: center;
        color: #fff;
      }
    }
  }
  .coderCard:first-child {
    margin-top: 20px;
  }
}
</style>
