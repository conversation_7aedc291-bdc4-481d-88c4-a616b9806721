<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2021-05-25 16:42:47
 * @LastEditors: 孙立政
 * @LastEditTime: 2021-12-03 11:32:51
-->
<template>
  <div class="home">
    <div class="coderCard">
      <div class="coderCardTop">
        <div class="coderCardTopLeft">
          <img
            v-if="list.paymentChannel==3"
            class="coderCardTopLeftTypeImg"
            src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/order/scan.png"
          >
          <img
            v-if="list.paymentChannel==2"
            class="coderCardTopLeftTypeImg"
            src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/order/card.png"
          >
          <div class="coderCardTopLeftTypeTitle">{{ list.marketName | ellipsis(9) }}</div>
          <!-- <img
            class="coderCardTopLeftRightIcon"
            src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/order/right.png"
          > -->
        </div>
        <div v-if="list.status == 0&&list.payAsync == 1" class="coderCardTopRight">
          <span class="payError">支付失败，待支付</span>
        </div>
        <div v-else class="coderCardTopRight">
          <span v-if="list.status != 0">已完成</span>
          <span v-else>已取消</span>
        </div>
      </div>
      <div class="separate" />
      <div class="coderCardGoods">
        <div class="coderCardGoodsList">
          <div class="coderCardGoodsListFor">
            <div class="coderCardGoodsListImg">
              <img v-if="list.paymentChannel==2" src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/order/carding.png" alt="">
              <img v-if="list.paymentChannel==3" src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/order/scaning.png" alt="">
            </div>
            <div class="coderCardGoodsListTips">
              <span v-if="list.paymentChannel==2">刷卡支付</span>
              <span v-if="list.paymentChannel==3&&list.poolId!=13">扫码支付</span>
              <span v-if="list.paymentChannel==3&&list.poolId==13">疗养预约</span>
            </div>
          </div>
        </div>
        <div class="coderCardGoodsRight">
          <div class="coderCardGoodsRightPrice">￥{{ list.actualPay }}</div>
          <div class="coderCardGoodsRightNum">总价</div>
        </div>
      </div>
      <div class="coderCardTime">下单时间：{{ list.createTime }}</div>
      <div v-if="list.status == 0&&list.payAsync == 1" class="coderCardBtn">
        <div @click="payOrder(list)">立即支付</div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    list: {
      type: Object,
      default: function() {
        return {}
      }
    }
  },
  methods: {
    // 订单详情
    goOrderDetail(list) {
      this.$router.push({
        name: 'OrderDetail',
        query: {
          orderNo: list.orderNo,
          type: 50 // 写死
        }
      })
    },
    payOrder(list) {
      this.$emit('clickHandler1', 1, list)
    }
  }
}
</script>

<style lang="scss" scoped>
.home {
  .coderCard {
    width: 710px;
    min-height: 296px;
    background: #fff;
    border-radius: 16px;
    margin: 0 auto;
    margin-bottom: 20px;
    .coderCardTop {
      display: flex;
      justify-content: space-between;
      height: 90px;
      align-items: center;
      .coderCardTopLeft {
        max-width: 85%;
        display: flex;
        align-items: center;
        .coderCardTopLeftTypeImg {
          width: 46px;
          height: 46px;
          margin-left: 22px;
          vertical-align: middle;
        }
        .coderCardTopLeftTypeTitle {
          font-weight: bold;
          font-size: 30px;
          color: #333333;
          margin-left: 8px;
          font-family:PingFangSC-Medium;
          font-weight: 500;
          line-height: 90px;
        }
        .coderCardTopLeftRightIcon {
          margin-left: 4px;
          width: 22px;
          height: 22px;
          vertical-align: middle;
        }
      }
      .coderCardTopRight {
        min-width: 15%;
        text-align: center;
        font-size: 26px;
        color: #999999;
        margin-right: 10 px;
        font-family: PingFangSC;
        .payError{
          color: red;
          margin-right: 15px;
        }
      }
    }
    .separate {
      width: 690px;
      height: 1px;
      margin: 0 auto;
      border: 1px solid #f4f4f4;
    }
    .coderCardGoods {
      display: flex;
      margin-top: 22px;
      .coderCardGoodsList {
        width: 488px;
        margin-left: 75px;
        .coderCardGoodsListFor {
          display: flex;
          align-items: center;
          height: 96px;
          font-size: 22px;
          margin-right: 12px;
          img {
            width: 96px;
            height: 96px;
            border-radius: 16px;
          }
        }
        .coderCardGoodsListTips {
          font-size: 26px;
          font-family:PingFangSC-Medium;
          font-weight: 500;
          text-align: left;
          color: #333333;
          margin-left: 16px;
        }
      }
      .coderCardGoodsRight {
        width: 150px;
        // height: 162px;
        text-align: center;
        margin-right: 20px;
        .coderCardGoodsRightPrice {
          height: 40px;
          font-size: 28px;
          font-family: PingFangSC, PingFangSC-Medium;
          font-weight: 500;
          text-align: right;
          color: #333333;
          line-height: 40px;
          margin-top: 25px;
        }
        .coderCardGoodsRightNum {
          height: 33px;
          font-size: 24px;
          font-family: PingFangSC;
          font-weight: 400;
          text-align: right;
          color: #999999;
          line-height: 33px;
        }
      }
    }
    .coderCardTime {
      height: 32px;
      font-size: 24px;
      font-family: PingFangSC;
      font-weight: 400;
      text-align: left;
      color: #999999;
      margin-left: 76px;
      margin-top: 30px;
    }
    .coderCardBtn {
      display: flex;
      justify-content: flex-end;
      width: 95%;
      height: 100px;
      margin:0 auto;
      color: #333;
      div {
        width: 124px;
        height: 54px;
        border: 1px solid #dddddd;
        border-radius: 9px;
        font-size: 24px;
        text-align: center;
        line-height: 54px;
        margin-top: 24px;
        margin-left: 20px;
      }
      .btnGoods{
        width: 124px;
        height: 54px;
        line-height: 54px;
        background: linear-gradient(90deg,#ff1e29, #ff5a25);
        border-radius: 8px;
        font-size: 24px;
        text-align: center;
        color: #fff;
      }
    }
  }
  .coderCard:first-child {
    margin-top: 20px;
  }
}
</style>
