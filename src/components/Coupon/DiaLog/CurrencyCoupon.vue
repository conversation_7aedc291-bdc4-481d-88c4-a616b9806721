<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2021-06-21 15:19:36
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-08-16 22:07:17
-->
<template>
  <div class="home">
    <div class="xr_coupon">
      <div class="xr_coupon_top" />
      <div class="xr_coupon_info">
        <div v-for="(item,index) in list" :key="item.id" :class="isPickStatus(item.pickStatus)" @click="pickCoupon(item,index,0)">
          <div class="coupon_num">
            <div class="coupon_num_price">
              <span>￥</span>
              <span>{{ item.preferentialAmount }}</span>
            </div>
            <div class="coupon_condition">满{{ item.useThreshold }}可用</div>
          </div>
          <div class="coupon_msg">
            <div>{{ item.couponName }}</div>
            <div>{{ item.applyMarket===0?'全部外卖店铺可用':'部分店铺可用' }}</div>
          </div>
        </div>
      </div>
      <div class="xr_coupon_bottom">
        <div class="coupon_tips">
          点击领取外卖限时优惠券
        </div>
        <img v-if="list.length === 1" class="coupon_btn" src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/coupon/dialog/btn1.png" alt="" @click="pickCoupon(list[0],0,0)">
        <img v-else class="coupon_btn" src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/coupon/dialog/btn2.png" alt="" @click="pickCoupon(list[0],0,1)">
      </div>
      <div class="close" @click="close">
        <van-icon class="close_icon" name="close" />
      </div>
    </div>

  </div>
</template>

<script>
import MixinItem from './mixin'
export default {
  mixins: [MixinItem],
  props: {
    list: {
      type: Array,
      default: () => []
    }
  },
  methods: {
    pickCoupon(item, index, type) {
      if (type === 0) {
        this.$emit('pick', item, index)
      } else {
        this.$emit('pickAll')
      }
    },
    isPickStatus(val) {
      if (val === 0) {
        return 'coupon'
      } else if (val === 1) {
        return 'die_coupon'
      } else if (val === 2) {
        return 'die_coupon1'
      }
    },
    close() {
      this.$emit('offCoupon')
    }
  }
}
</script>
<style scoped lang="scss">
    .home {
        width: 100%;
        height: 100vh;
        background-color: rgba(0,0,0,.8);
        position: fixed;
        top: 0;
        z-index: 1002;
        display: flex;
        .xr_coupon{
            width: 650px;
            margin: 0 auto;
            align-self: center;
            .coupon{
                width: 590px;
                height: 157px;
                background-image: url(https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/coupon/dialog/coupon.png);
                background-size: 100% 100%;
                margin: 0 auto;
                margin-top: 10px;
                display: flex;
            }
            .die_coupon{
                width: 590px;
                height: 157px;
                background-image: url(https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/coupon/dialog/coupon2.png);
                background-size: 100% 100%;
                margin: 0 auto;
                margin-top: 10px;
                display: flex;
            }
            .die_coupon1{
              width: 590px;
                min-height: 157px;
                background-image: url(https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/coupon/dialog/coupon3.png);
                background-size: 100% 100%;
                margin: 0 auto;
                margin-top: 10px;
                display: flex;
            }
            .coupon_num{
                width: 190px;
                text-align: center;
                .coupon_num_price{
                    width: 198px;
                    height: 83px;
                    color: #ff1929;
                    margin-top: 14px;
                    span:nth-child(1) {
                        font-size: 30px;
                    }
                    span:nth-child(2) {
                        font-size: 67px;
                        font-weight: bold;
                    }
                }
                .coupon_condition{
                    height: 22px;
                    // margin-top: 2px;
                    font-size: 22px;
                    color: #666666;
                }
            }
            .coupon_msg{
                width: 330px;
                margin-left: 22px;
                font-family: PingFangSC-Medium;
                padding-top: 39px;
                overflow: hidden;
                div:nth-child(1) {
                  min-height: 45px;
                  line-height: 45px;
                  font-size: 29px;
                  color: #222222;
                }
                div:nth-child(2) {
                    font-size: 22px;
                    color: #ff1929;
                    margin-top: 4px;
                    margin-bottom: 10px;
                }
            }
            .coupon_tips{
                font-size: 35px;
                color: #FFECB9;
                margin-bottom: 5px;
                margin-top: 10px;
            }
            .coupon_btn{
                width: 513px;
                height: 96px;
            }
            .xr_coupon_top{
                width: 643px;
                height: 283px;
                background-image: url(https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/coupon/dialog/currency/1.png);
                background-size: 100% 100%;
            }
            .xr_coupon_info{
                width: 643px;
                min-height: 196px;
                max-height: 520px;
                background-image: url(https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/coupon/dialog/currency/2.png);
                background-size: 100% 100%;
                overflow: auto;
                margin-top: -1px;
            }
            .xr_coupon_bottom{
                width: 643px;
                height: 171px;
                background-image: url(https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/coupon/dialog/currency/3.png);
                background-size: 100% 100%;
                text-align: center;
                overflow: hidden;
            }

        }
        .close{
            width: 100%;
            text-align: center;
            .close_icon{
              margin-top: 50px;
              color: rgba($color: #fff, $alpha: 0.3);
            }
        }
    }
</style>
