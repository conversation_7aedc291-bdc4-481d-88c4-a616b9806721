<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2022-08-04 11:21:35
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-08-16 20:52:47
-->
<template>
  <div />
</template>

<script>
import { couponPick, couponPickAll } from '@/api/coupon'
export default {
  data() {
    return {
    }
  },
  created() {
  },
  methods: {
    pick(item, index) {
      this.test()
      if (this.list[index].status == 1) {
        return
      }
      // let data = {
      //   couponId: item.id,
      //   regionId: this.$store.getters.getRegionId
      // }
      couponPick(item.id).then(res => {
        if (res.status === 200) {
          this.$toast('成功领取1张')
          // this.list[index].status = 1
          this.test()
          if (this.list.length === 1) {
            this.$router.push({
              path: '/Classify',
              query: { name: '外卖', cateId: 0, isTakeaway: true }
            })
          }
        }
      })
    },
    pickAll() {
      // let data = {
      //   regionId: this.$store.getters.getRegionId
      // }
      couponPickAll({ couponIdList: this.couponIdList }).then(res => {
        if (res.status === 200) {
          this.$toast('领取成功')
          this.$emit('limitObject')
          this.$router.push({
            path: '/Classify',
            query: { name: '外卖', cateId: 0, isTakeaway: true }
          })
        }
      })
    }
  }
}
</script>

<style scoped>
</style>
