<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2022-08-06 14:36:57
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-08-16 21:08:01
-->
<template>
  <div class="home">
    <XrCoupon v-if="type === 1" :list="list" @offCoupon="offCoupon" @pick="pick" @pickAll="pickAll" />
    <LyhCoupon v-if="type === 2" :list="list" @offCoupon="offCoupon" @pick="pick" @pickAll="pickAll" />
    <CallBack v-if="type === 3" :list="list" @offCoupon="offCoupon" @pick="pick" @pickAll="pickAll" />
    <AllCoupon v-if="type === 0" :list="list" @offCoupon="offCoupon" @pick="pick" @pickAll="pickAll" />
    <CurrencyCoupon v-if="type === 4" :list="list" @offCoupon="offCoupon" @pick="pick" @pickAll="pickAll" />
  </div>
</template>

<script>
// import MixinItem from './mixin'
import { couponPick } from '@/api/coupon'
import { XrCoupon, LyhCoupon, CallBack, AllCoupon, CurrencyCoupon } from './index'
export default {
  components: {
    XrCoupon,
    LyhCoupon,
    CallBack,
    AllCoupon,
    CurrencyCoupon
  },
  // mixins: [MixinItem],
  props: {
    type: {
      type: Number,
      default: 0
    },
    list: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {

    }
  },
  methods: {
    pick(item, index) {
      if (this.list[index].status == 1) {
        return
      }

      couponPick(item.id).then(res => {
        if (res.status === 200) {
          this.$toast('成功领取1张')
          // this.list[index].status = 1
          this.$emit('noticeRefresh')
          if (this.list.length === 1) {
            this.$router.push({
              path: '/Classify',
              query: { name: '外卖', cateId: 0, isTakeaway: true }
            })
          }
        }
      })
    },
    pickAll() {
      this.$emit('couponPickAll')
    },
    offCoupon() {
      this.$emit('offCoupon')
    }
  }
}
</script>

<style scoped lang="scss">
    .home {}
</style>
