<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2022-01-07 10:24:57
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-03-14 15:20:27
-->
<template>
  <div class="keyboard">
    <!-- 点击键盘以外的区域隐藏键盘 -->
    <div v-if="showKeyboard" class="bg" @click.stop="hide" />

    <div v-if="showKeyboard" ref="cusBoard" class="cus-board">
      <div class="kTitle">
        <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/keyboard/aq.png" alt="">
        点滴安全键盘
      </div>
      <!-- 字母数字键盘区 -->
      <div v-for="(line, index) in keys" v-show="keyboardType === 0" :key="'line' + index" class="letter-line">
        <!-- 收起键盘 -->
        <div v-if="index === keys.length - 1" class="action" @click.stop="hide">
          <img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAABaElEQVRYR+2XPS8FQRSGn/tP1DQS7U3Q+yiEUkWpohVES0NJdUuiwO3xAyQaav+EvHFOMhnr3pm1s+smptmvs+d95syZs2d7fI17YMnO2zpIc6UHnAE7balGOucC+LCbj8At8FIYZhZYBRakEwKcAnuFxd39CbAbAywCikIbQ7N/GAVwGFHoOrwXX8s81Ua2SQAHBnFkzj1XHFyCdWwmByBchdTwpixTUgRKJ+J/Dvz9CHSeA50DeIFpEkTfHd+mWTnQFIRX1KQ6INL5ppTNz1NOBBrW/uaucgm2gcvSyuZ/C7iIP8fqBZ6BYWEI9Z5zYUd0BywXFv3J/VBbow8cO1GLIIr4vgA6HRMFMGUt2M2IRFWCrQEqOu8poc2JQNgDbgDXkcA6cBX1kWMZcgCmTWDGvIYQofgroGdvY9XtxyTFzm2qIPTMZ54l7oUoB0C2MYS/ny1eF6AKopb4bwAcYmDT30xd8zjcnxdMko9bH1MNAAAAAElFTkSuQmCC">
        </div>
        <!-- 大小写切换 -->
        <div v-if="index === keys.length - 2&&symbolType === 0" class="action" @click.stop="switchs">
          <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/keyboard/a-b.png">
        </div>
        <div v-for="key in line" :key="key" :data-text="key" class="item" @touchstart="touchStart" @touchend="touchEnd">{{ key }}</div>
        <!-- 删除 -->
        <div v-if="index === keys.length - 1" class="action" @click.stop="handleDel">
          <img src="data:image/png;base64,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">
        </div>
        <!-- 英文符号 -->
        <div v-if="index === keys.length - 2" class="action" @click.stop="symbol">
          符
        </div>
      </div>

      <!-- 数字键盘 -->
      <div v-for="(line, index) in keysNumber" v-show="keyboardType === 1" :key="'lineN' + index" class="letter-line">
        <!-- 收起键盘 -->
        <div v-if="index === keys.length - 1" class="actionNumber" @click.stop="hide">
          <img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAABaElEQVRYR+2XPS8FQRSGn/tP1DQS7U3Q+yiEUkWpohVES0NJdUuiwO3xAyQaav+EvHFOMhnr3pm1s+smptmvs+d95syZs2d7fI17YMnO2zpIc6UHnAE7balGOucC+LCbj8At8FIYZhZYBRakEwKcAnuFxd39CbAbAywCikIbQ7N/GAVwGFHoOrwXX8s81Ua2SQAHBnFkzj1XHFyCdWwmByBchdTwpixTUgRKJ+J/Dvz9CHSeA50DeIFpEkTfHd+mWTnQFIRX1KQ6INL5ppTNz1NOBBrW/uaucgm2gcvSyuZ/C7iIP8fqBZ6BYWEI9Z5zYUd0BywXFv3J/VBbow8cO1GLIIr4vgA6HRMFMGUt2M2IRFWCrQEqOu8poc2JQNgDbgDXkcA6cBX1kWMZcgCmTWDGvIYQofgroGdvY9XtxyTFzm2qIPTMZ54l7oUoB0C2MYS/ny1eF6AKopb4bwAcYmDT30xd8zjcnxdMko9bH1MNAAAAAElFTkSuQmCC">
        </div>
        <div v-for="key in line" :key="key" :data-text="key" class="itemNumber" @touchstart="touchStart" @touchend="touchEnd">{{ key }}</div>
        <!-- 删除 -->
        <div v-if="index === keys.length - 1" class="actionNumber" @click.stop="handleDel">
          <img src="data:image/png;base64,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">
        </div>
      </div>

    </div>
  </div>
</template>
<script>
export default {
  name: 'Keyboard',
  props: {
    // 文本数据源
    text: {
      type: String,
      default: ''
    },
    // 固定开头
    defaultVal: {
      type: String,
      default: ''
    },
    // 输入框数量
    length: {
      type: Number,
      default: 1000
    },
    // 键盘类型
    keyboardType: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      keys: [
        [1, 2, 3, 4, 5, 6, 7, 8, 9, 0],
        ['q', 'w', 'e', 'r', 't', 'y', 'u', 'i', 'o', 'p'],
        ['a', 's', 'd', 'f', 'g', 'h', 'j', 'k', 'l'],
        ['z', 'x', 'c', 'v', 'b', 'n', 'm']
      ], // 字母键盘
      keysNumber: [
        [1, 2, 3],
        [4, 5, 6],
        [7, 8, 9],
        [0]
      ], // 数字键盘
      showKeyboard: false, // 控制键盘显隐
      isFocus: false, // 是否聚焦
      switchsType: 0, // 大小写
      symbolType: 0// 符号选择
    }
  },
  computed: {
    /**
     * 获取当前输入框下标
     */
    currentInput: function() {
      // 返回当前文本的长度
      const length = this.text.length || 0
      return length
    }
  },
  mounted() {
    // 更新固定开头值到文本数据源上
    this.$emit('update:text', this.defaultVal)
  },
  methods: {
    /**
     * 显示键盘
     */
    show() {
      this.isFocus = true
      this.showKeyboard = true
      // 需定时器执行 否则会找不到dom
      setTimeout(() => {
        // 升起键盘
        this.$refs.cusBoard.style.transform = `translateY(0)`
      }, 20)
    },
    /**
     * 隐藏键盘
     */
    hide() {
      // 失去焦点
      this.isFocus = false
      // 降下键盘
      this.$refs.cusBoard.style.transform = `translateY(100%)`
      // 需定时器执行 否则会没有动画过度
      setTimeout(() => {
        this.showKeyboard = false
      }, 500)
    },
    /**
     * 按下
     */
    touchStart(el) {
      let self = this
      // 点击目标
      const { target } = el

      let text = this.text
      // 文本达到上限 不做处理 返回
      if (text.length >= this.length) {
        this.$toast('...')
        return
      }
      //   this.$throttle(() => {
      // 拼接点击的 值
      const content = target.innerText
      text += content
      // 更新文本数据源
      self.$emit('update:text', text)

      // 背景色改变
      target.style.background = 'rgb(228, 229, 228)'
      // 添加激活className 显示反馈
      target.classList.add('active')
    //   }, 1000)
    },
    touchEnd(el) {
      // 点击目标
      const { target } = el
      // 通过定时器实现过渡效果
      setTimeout(() => {
        // 背景色改变
        target.style.background = '#fff'
        // 移除className
        target.classList.remove('active')
      }, 100)
    },
    /**
     * 点击删除键
     */
    handleDel() {
      if (this.defaultVal && this.text.length === this.defaultVal.length && this.text.indexOf(this.defaultVal) === 0) {
        // 有默认开头 如果文本只有固定开头 没有任何输入 点击删除不做任何操作
        return
      }
      // 从后面开始 删除一个文本
      let text = this.text
      text = text.slice(0, text.length - 1)
      this.$emit('update:text', text)
    },
    /**
     * 切换大小写
     */
    switchs() {
      if (this.switchsType === 1) {
        this.switchsType = 0
        this.keys = [
          [1, 2, 3, 4, 5, 6, 7, 8, 9, 0],
          ['q', 'w', 'e', 'r', 't', 'y', 'u', 'i', 'o', 'p'],
          ['a', 's', 'd', 'f', 'g', 'h', 'j', 'k', 'l'],
          ['z', 'x', 'c', 'v', 'b', 'n', 'm']
        ]
      } else {
        this.switchsType = 1
        this.keys = [
          [1, 2, 3, 4, 5, 6, 7, 8, 9, 0],
          ['Q', 'W', 'E', 'R', 'T', 'Y', 'U', 'I', 'O', 'P'],
          ['A', 'S', 'D', 'F', 'G', 'H', 'J', 'K', 'L'],
          ['Z', 'X', 'C', 'V', 'B', 'N', 'M']
        ]
      }
    },
    /**
     * 切换符号
     */
    symbol() {
      if (this.symbolType === 0) {
        this.symbolType = 1
        this.keys = [
          [',', '.', ';', ':', '!', '@', '#', '$', '%', '^'],
          ['&', '*', '(', ')', '_', '-', '+', '=', '|'],
          ['/', '[', ']', '{', '}']
        ]
      } else {
        this.symbolType = 0
        this.keys = [
          [1, 2, 3, 4, 5, 6, 7, 8, 9, 0],
          ['q', 'w', 'e', 'r', 't', 'y', 'u', 'i', 'o', 'p'],
          ['a', 's', 'd', 'f', 'g', 'h', 'j', 'k', 'l'],
          ['z', 'x', 'c', 'v', 'b', 'n', 'm']
        ]
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.keyboard {
  user-select: none;
}
.bg {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1;
  background: rgba(255, 255, 255, 0);
}
.cus-board {
  font-size: 30px;
  width: 750px;
  background: rgb(246, 247, 246);
  padding: 10px 10px 60px 10px;
  position: fixed;
  z-index: 9999;
  bottom: 0;
  left: 0;
  right: 0;
  transform: translateY(100%);
  transition: all 0.5s;
  .active {
    &::after {
      position: absolute;
      top: -80px;
      left: 0;
      width: 62px;
      height: 80px;
      background-color: #ffffff;
      content: attr(data-text);
      animation: itemActive 0.5s infinite;
    }
  }
  .kTitle{
    height: 50px;
    line-height: 50px;
    font-size: 32px;
    text-align: center;
    overflow: hidden;
    img {
        display: inline-block;
        width: 32px;
        height: 32px;
        position: relative;
        top: 3.5px;
    }
  }
}
.item,
.action {
  width: 62px;
  height: 80px;
  border-radius: 10px;
  background-color: white;
  line-height: 80px;
  text-align: center;
  position: relative;
  img {
    display: inline-block;
    width: 32px;
    height: 32px;
  }
}
.itemNumber,
.actionNumber {
  width: 180px;
  height: 90px;
  border-radius: 10px;
  background-color: white;
  line-height: 90px;
  text-align: center;
  position: relative;
  img {
    display: inline-block;
    width: 32px;
    height: 32px;
  }
}
@keyframes itemActive {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(2);
  }
  100% {
    transform: scale(1);
  }
}
.line {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 30px;
}
.letter-line {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  .item,
  .action {
    margin: 6px;
  }
  .itemNumber,
  .actionNumber {
    margin: 10px;
  }
}
</style>
