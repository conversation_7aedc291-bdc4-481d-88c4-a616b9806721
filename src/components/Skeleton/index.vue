<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2021-10-29 09:46:56
 * @LastEditors: 孙立政
 * @LastEditTime: 2021-11-09 13:57:36
-->
<template>
  <div class="home">
    <div v-if="type == 'shop'" class="shop">
      <van-skeleton v-for="item in 6" :key="item" avatar :row="3" avatar-shape="square" />
    </div>
    <div v-if="type == 'order'" class="order">
      <van-skeleton v-for="item in 8" :key="item" avatar :row="3" avatar-shape="square" />
    </div>

  </div>
</template>

<script>
export default {
  props: {
    type: {
      type: String,
      default: function() {
        return 'shop'
      }
    }
  },
  data() {
    return {

    }
  },
  created() {

  },
  mounted() {

  },
  methods: {

  }
}
</script>

<style scoped lang="scss">
.home {
    .shop{
        .van-skeleton{
            margin-top: 20px;
        }
        .van-skeleton__avatar{
            width: 150px;
            height: 150px;
            margin-top: 13px;
        }
    }
}
</style>
