<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2022-04-26 11:00:48
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-05-27 10:01:01
-->
<template>
  <div class="homep">
    <van-overlay :show="show" z-index="1000">
      <div class="wrapper" @click.stop>
        <div v-if="boxStatus" class="block">
          <div class="title">公告</div>
          <div class="content">
            <div>
              <span class="b">致尊敬的点滴用户：</span>
            </div>
            <div>
              根据人民银行整改时限要求，鉴于二类账户开通已达90%以上，点滴平台将于2022年5月31日24时关闭老交易通道。请尚未开通点滴二类账户的用户尽快办理开通手续，在办理过程遇到问题的请联系工作人员590571，15306780331。
            </div>
            <div class="content_bottom">
              遂昌点滴网络科技有限公司
            </div>
            <div class="content_bottom">
              2022年4月27日
            </div>
          </div>
          <div class="btn">
            <div class="btn-sure" @click="confirm">确认</div>
          </div>
        </div>
      </div>
    </van-overlay>
  </div>
</template>

<script>
export default {
  data() {
    return {
      show: true,
      boxStatus: true
    }
  },
  mounted() {},
  methods: {
    // 同意协议
    confirm() {
      this.show = false
      sessionStorage.setItem('userAccountCheck', 1)
    }
  }
}
</script>

<style scoped lang="scss">
.homep {
  .xy{
      color: #39CF3F;
  }
  .wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
  }
  .block {
    width: 602px;
    height: 758px;
    background: #ffffff;
    border-radius: 36px;
    .title{
      text-align: center;
      height: 128px;
      line-height: 128px;
      font-size: 43px;
      font-family: PingFangSC-Medium;
      color: #222222;
    }
    .content{
      width: 514px;
      height: 366px;
      font-size: 32px;
      color: #666666;
      margin: 0 auto;
      .b{
        color: #222222;
        font-family: PingFangSC-Medium;
      }
      .content_bottom{
        text-align: right;
        margin-top: 10px;
      }
    }
    .btn{
      width: 514px;
      margin: 0 auto;
      margin-top: 110px;
      div{
          text-align: center;
          margin: 0 auto;
      }
      .btn-close{
        width: 330px;
        height: 60px;
        line-height: 60px;
        font-size: 30px;
        color: #909090;
        border-bottom: 1px solid #cfcfcf;
      }
      .btn-sure{
        width: 442px;
        height: 88px;
        line-height: 88px;
        border-radius: 42px;
        background: #39cf3f;
        color: #ffffff;
        font-family: PingFangSC-Medium;
        font-size: 37px;
      }
    }
  }
}
</style>
