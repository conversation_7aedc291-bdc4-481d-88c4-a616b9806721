<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2021-07-29 16:20:15
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-07-19 15:50:17
-->
<template>
  <div class="homep">
    <van-overlay :show="show" z-index="1003">
      <div class="wrapper" @click.stop>
        <div v-if="boxStatus" class="block">
          <div class="title">服务协议和隐私协议</div>
          <div class="content">
            <div>
              <span class="b">请你务必审慎阅读、充分理解</span>“服务协议”和“隐私政策”各条款，包括但不限于：为了更好的向你提供服务，我们需要收集你的定位信息、设备标识、操作日志等信息用于分析、优化应用性能。
            </div>
            <div>
              你可阅读<span class="xy" @click="goUseragt">《服务协议》</span>和<span class="xy" @click="goPrivacy">《隐私政策》</span>了解详细信息。如果你同意，请点击下面按钮开始接受我们的服务。
            </div>
          </div>
          <div class="btn">
            <div class="btn-close" @click="cancel">不同意，退出应用</div>
            <div class="btn-sure" @click="confirm">同意并继续</div>
          </div>
        </div>
      </div>
    </van-overlay>
  </div>
</template>

<script>
export default {
  data() {
    return {
      show: true,
      boxStatus: true
    }
  },
  mounted() {
    this.getResutl()
  },
  methods: {
    // 同意协议
    confirm() {
      // 记录缓存 1-同意  0-未同意
      localStorage.setItem('privacyOff', 1)
      var u = navigator.userAgent
      var isiOS = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/) // ios终端
      if (isiOS) {
        this.getLocationMsg()
      }
      this.boxStatus = false
      // 请求权限
      AlipayJSBridge.call('GetJurisdiction', function(result) {
        console.log(result)
      })
    },
    // 关闭
    cancel() {
      AlipayJSBridge.call('ExitApp', function(result) {
        console.log(result)
      })
    },
    // 回调
    onBeforeClose(action, done) {
      if (action === 'confirm') {
        return done(false)
      } else {
        return done(true)
      }
    },
    // 权限通知监听
    getResutl() {
      let self = this
      document.addEventListener('getJurisResult', function(e) {
        console.log('----权限------')
        console.log(e)
        if (e.data.success == true) {
          self.getLocationMsg()
        } else {
          // 拒绝跳转大区选择
          self.$router.push({
            name: 'RegionV2'
          })
        }
        console.log('----权限end------')
      }, false)
    },
    // 获取定位
    getLocationMsg() {
      let self = this
      const timer = window.setTimeout(() => {
        self.$router.push({
          name: 'RegionV2'
        })
      }, 3000)
      AlipayJSBridge.call('LocationMsg', {}, function(result) {
        let locationdata = JSON.parse(result.locationMsg)
        window.clearTimeout(timer)
        // 定位失败跳转大区选择
        if (locationdata.latitude == 0.0 || locationdata == 0) {
          self.$router.push({
            name: 'RegionV2'
          })
        } else {
          let locationdata = JSON.parse(result.locationMsg)
          self.$store.state.location.latitude = locationdata.latitude
          self.$store.state.location.longitude = locationdata.longitude
          self.$store.state.location.address = locationdata.address
          self.show = false
        }
      })
    },
    // 隐私协议
    goUseragt() {
      this.$router.push('/useragt')
    },
    goPrivacy() {
      this.$router.push('/privacy')
    }
  }
}
</script>

<style scoped lang="scss">
.homep {
  .xy{
      color: #39CF3F;
  }
  ::v-deep .van-overlay{
    background-color: rgba(88, 86, 86, 1);
  }
  .wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
  }
  .block {
    width: 602px;
    height: 788px;
    background: #ffffff;
    border-radius: 36px;
    .title{
      text-align: center;
      height: 128px;
      line-height: 128px;
      font-size: 43px;
      font-family: PingFangSC-Medium;
      color: #222222;
    }
    .content{
      width: 514px;
      height: 366px;
      font-size: 32px;
      color: #666666;
      margin: 0 auto;
      .b{
        color: #222222;
        font-family: PingFangSC-Medium;
      }
    }
    .btn{
      width: 514px;
      margin: 0 auto;
      margin-top: 45px;
      div{
          text-align: center;
          margin: 0 auto;
      }
      .btn-close{
        width: 330px;
        height: 60px;
        line-height: 60px;
        font-size: 30px;
        color: #909090;
        border-bottom: 1px solid #cfcfcf;
      }
      .btn-sure{
        width: 442px;
        height: 88px;
        line-height: 88px;
        border-radius: 42px;
        background: #39cf3f;
        color: #ffffff;
        margin-top: 39px;
        font-family: PingFangSC-Medium;
        font-size: 37px;
      }
    }
  }
}
</style>
