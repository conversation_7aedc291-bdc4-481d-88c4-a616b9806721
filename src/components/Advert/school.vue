<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2021-06-21 15:19:36
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-07-14 14:54:36
-->
<template>
  <div class="home" />
</template>

<script>
import { activityNo } from '@/config/die'
export default {
  data() {
    return {

    }
  },
  created() {

  },
  mounted() {},
  methods: {
    goActive() {
      this.close()
      this.$router.push({
        name: 'MarketPt',
        query: {
          activityNo: activityNo
        }
      })
    },
    close() {
      let dayjs = require('dayjs')
      let data = dayjs(new Date()).format('DD')
      localStorage.setItem('advertopen2', data)
      this.$emit('funIfOpen')
    }
  }
}
</script>
<style scoped lang="scss">
    .home {
        width: 100%;
        height: 100vh;
        background-color: rgba(0,0,0,.8);
        position: fixed;
        top: 0px;
        z-index: 1000;
    }
</style>
