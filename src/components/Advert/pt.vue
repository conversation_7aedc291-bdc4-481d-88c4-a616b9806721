<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2021-06-21 15:19:36
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-06-06 09:55:01
-->
<template>
  <div class="home">
    <div class="close" @click="close">
      <van-icon v-if="false" class="close_icon" name="close" color="#fff" />
    </div>
    <div class="adImgs" @click="close">
      <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/components/Advert/ptys.png" alt="">
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {

    }
  },
  created() {

  },
  mounted() {},
  methods: {
    goActive() {
      this.close()
      this.$router.push({
        name: 'Market'
      })
    },
    close() {
      // let dayjs = require('dayjs')
      // let data = dayjs(new Date()).format('DD')
      localStorage.setItem('ifPtAd', true)
      this.$emit('funifPtAd')
    }
  }
}
</script>
<style scoped lang="scss">
    .home {
        width: 100%;
        height: 100vh;
        background-color: rgba(0,0,0,.8);
        position: fixed;
        top: 0;
        z-index: 1000;
        .adImgs{
            width: 676px;
            margin: 0 auto;
            margin-top: 80px;
            img{
              width: 676px;
            }
        }
        .close{
            width: 100%;
            text-align: right;
            margin-top: 62%;

            .close_icon{
              margin-right: 85px;
            }
        }
    }
</style>
