<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2021-06-21 15:19:36
 * @LastEditors: 孙立政
 * @LastEditTime: 2023-08-11 17:39:50
-->
<template>
  <div class="home">

    <div class="adImgs" @click="goActive">
      <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/home/<USER>/activity/20240918/dialog.png?a=1" alt="">
    </div>

    <div class="close" @click="close">
      <van-icon class="close_icon" name="close" size="25" color="#fff" />
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {

    }
  },
  created() {

  },
  mounted() {},
  methods: {
    goActive() {
      this.close()
      if (this.$store.getters.getUserId == null) {
        this.$router.push({ name: 'wxLogin2' })
        return
      }
      this.$router.push({ name: 'ActivityLq20240918' })
    },
    close() {
      this.$emit('funIfOpen')
    }
  }
}
</script>
<style scoped lang="scss">
    .home {
        width: 100%;
        height: 100vh;
        background-color: rgba(0,0,0,.8);
        position: fixed;
        top: 0;
        z-index: 1002;
        .adImgs{
            width: 620px;
            margin: 0 auto;
            margin-top: 48%;
            img{
              width: 620px;
            }
        }
        .close{
            width: 100%;
            text-align: center;
            margin-top: 20px;

            .close_icon{
              // margin-right: 85px;
            }
        }
    }
</style>
