<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2021-06-17 14:21:06
 * @LastEditors: zhaoyuxin
 * @LastEditTime: 2021-07-02 15:18:23
-->
<template>
  <div class="home">
    <div class="navbar" :style="styleVar">
      <div class="left" @click="goBack">
        <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/components/back.png" alt="">
      </div>
      <div class="center">{{ title }}</div>
      <div class="right" @click="rightClick">
        {{ rightName }}
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    title: {
      type: String,
      default: function() {
        return '点滴'
      }
    },
    rightName: {
      type: String,
      default: function() {
        return ''
      }
    }
  },
  computed: {
    styleVar() {
      return {
        '---nav-height': this.$store.getters.getStatusHeight + 'px'
      }
    }
  },
  methods: {
    goBack() {
      this.$router.go(-1)
    },
    rightClick() {
      this.$emit('rightClick')
    }
  }
}
</script>

<style scoped lang="scss">
    .navbar {
        width: 100%;
        height: 80px;
        line-height: 80px;
        background-color: #fff;
        display: flex;
        position: fixed;
        top: var(---nav-height);
        z-index: 995;
        .left {
            width: 20%;
            img {
                width: 40px;
                height: 40px;
                margin-left: 34px;
            }
        }
        .center {
            width: 60%;
            font-size: 36px;
            color: #222222;
            font-weight: 600;
            text-align: center;
        }
        .right {
            width: 20%;
            font-size: 28px;
            font-weight: 400;
            color: #333333;
        }

    }
</style>
