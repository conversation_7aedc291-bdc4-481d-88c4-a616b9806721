<!--
 * @Descripttion: 底部导航栏
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2021-05-22 15:22:43
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-10-27 14:48:52
-->
<template>
  <div class="tabbar">
    <div class="footer" :style="styleVar">
      <!-- <div v-if="loginStatus" class="loginInfo" @click="goLogin">
        <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV2/tabBar/loginInfo.png" alt="">
      </div> -->
      <div class="list">
        <div v-for="(item,index) of items" :key="index" :class="[item.cls,{on:index === idx}]" @click="change(item,index)">
          <img :src="index===idx?item.srcSelect:item.src" :class="[item.cls,{'animate__animated animate__pulse':index === idx}]">
          <span :class="['colorChange',{on:index===idx}]">{{ item.name }}</span>
        </div>
      </div>

    </div>
  </div>

</template>

<script type="text/javascript">
import {
  nmyUrl
} from '@/config/die'
import { initGetVirtualPhone } from '@/api/login'
export default {
  props: {
    idx: {
      type: Number,
      default: 0
    },
    items: {
      type: Array,
      default: function() {
        return []
      }
    }
  },
  data() {
    return {
      loginStatus: false
    }
  },
  computed: {
    styleVar() {
      return {
        '---nav-height': this.$store.getters.getNavigationBarHeight + 'px'
      }
    }
  },
  created() {
    if (localStorage.getItem('token')) {
      this.loginStatus = false
    } else {
      this.loginStatus = true
    }
  },
  methods: {
    goLogin() {
      this.$router.push({
        path: '/codeLogin'
      })
    },
    async change(item, index) {
      console.log(item)

      if (index === 1 && this.$store.getters.getRegionId === 7) {
        this.$toast('即将上线，敬请期待')
        return
      }
      if (item.cls === 'market') {
        const { status, message } = await initGetVirtualPhone({
          platId: 5
        })
        if (status === 401) {
          this.$toast('请先登录')
          return
        }
        if (status !== 200) {
          this.$toast('获取号码失败，请稍后再试')
          return
        }

        let userNo = this.$store.getters.getUserNo

        let statusHeight = this.$store.getters.getStatusHeight
        let url = nmyUrl + '&statusHeight=' + statusHeight + '&navigationBarHeight=' + this.$store.getters.getNavigationBarHeight + '&userNo=' + this.signNmy(userNo) + '&phone=' + this.signNmy(message)

        AlipayJSBridge.call('pushWindow', {
          url: url,
          param: {
            readTitle: true,
            showOptionMenu: false,
            transparentTitle: 'always'
          },
          passData: {}
        })
        return
      }

      this.$router.push(item.push)
      this.$emit('change', index)
    },
    // 加密
    signNmy(val) {
      const crypto = require('crypto')

      // 加密秘钥和iv，可以自行更改
      const secretKey = '92f33d7ce63483f5'
      const iv = '572019c19836877d'

      const cipher = crypto.createCipheriv('aes-128-cbc', secretKey, iv)

      let encrypted = cipher.update(val, 'utf8', 'hex')
      encrypted += cipher.final('hex')

      return encrypted
    }
  }
}
</script>

<style lang="scss" scoped>
.footer{

	position: fixed;
	left: 0;
	bottom: 0;
	box-sizing: border-box;
	background: #fff;
	width: 100%;
  height: calc(120px + var(---nav-height));
  box-shadow: -4px -4px 27px 0px rgba(220,220,220,0.50);
  .list{
    width: 100%;
    display: flex;
    justify-content: space-between;
  }
  .colorChange{
    color: #4f4f4f;
  }

  div {
      width: 25%;
      text-align: center;
      font-size: 22px;
  }
  div img{
      width: 50px;
      height: 50px;
      margin-top: 16px;
  }
  div span{
      display: block;
      color: #4F4F4F;
      font-family: PingFangSC;
      font-weight: 400;
  }
  .on{
      color: #005539;
  }
  .loginInfo{
    position: fixed;
    left: 0;
    bottom: calc(130px + var(---nav-height));
    width: 100%;
    height: 94px;
    img{
      width: 100%;
      height: 100%;
    }
  }
}

</style>

