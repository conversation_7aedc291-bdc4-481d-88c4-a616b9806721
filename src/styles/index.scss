body {
    height: 100%;
    background-color: rgb(242, 242, 242);
    -webkit-text-size-adjust:none;
}

html {
    height: 100%;
    box-sizing: border-box;
}

// @font-face {
// 	font-family: PingFangSC;
// 	src: url('./PingFangSC-Regular.woff2');
// }
// @font-face {
// 	font-family: PingFangSC-Medium;
// 	src: url('./PingFangSC-Semibold.woff2');
// }
  
body,dl,dd,ul,ol,h1,h2,h3,h4,h5,h6,pre,form,input,textarea,p,hr,thead,tbody,tfoot,th,td{
    margin:0;
    padding:0;
}

#app {
    height: 100%;
    font-family: PingFangSC;
}

a {
    text-decoration:none;
    color: #000000;
}

div {
    box-sizing: border-box;
}

::-webkit-scrollbar {
    display: none; /* Chrome Safari */
}
@import url(./market.scss);
