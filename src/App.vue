<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2021-05-22 15:21:14
 * @LastEditors: 孙立政
 * @LastEditTime: 2023-05-08 10:14:26
-->
<template>
  <div id="app">
    <keep-alive>
      <router-view v-if="$route.meta.keepAlive" />
    </keep-alive>
    <router-view v-if="!$route.meta.keepAlive" />

    <Loading :show="loadingShow" />
  </div>
</template>

<script>
import { addData } from '@/utils/upLog.js'
import Loading from '@/components/Loading/index'
import { environment } from '@/api/index'
import logger from './utils/aliLog'
export default {
  components: {
    Loading
  },
  data() {
    return {
      loadingShow: false
    }
  },
  watch: {
    $route: {
      handler: function(val, oldVal) {
        if (val.path != '/index' && val.path != '/menu' && val.path != '/order' && val.path != '/my') {
          this.loadingShow = true
        }
        this.$nextTick(function() { // 页面加载完成后执行
          this.loadingShow = false
        })
      },
      // 深度观察监听
      deep: true
    }
  },
  created() {
    let self = this

    // 获取状态栏和底部安全区域高度
    this.$store.commit('setStatusHeight')

    // 获取定位
    this.$store.commit('setLocation')

    localStorage.setItem('location', JSON.stringify({
      latitude: 28.592388,
      longitude: 119.275865,
      address: {
        poiName: '遂昌县'
      }
    }))

    var u = navigator.userAgent
    var isiOS = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/) // ios终端
    if (!isiOS) {
      this.$store.commit('setNavigationBarHeight')

      localStorage.setItem('privacyOff', 1)
    }

    // 监听返回事件
    document.addEventListener('goBack', function(e) {
      setTimeout(function() {
        if (
          self.$route.path == '/index' ||
						self.$route.path == '/menu' ||
						self.$route.path == '/market/home' ||
						self.$route.path == '/order' ||
						self.$route.path == '/my'
        ) {
          AlipayJSBridge.call('BackPressed', {}, function(result) {})
        } else if (self.$route.path == '/pwdLogin' || self.$route.path == '/codeLogin') {
          self.$router.push('/index')
        } else if (self.$route.path == '/orderDetail') {
          self.$router.push('/order')
        } else if (self.$route.path == '/market/order' && self.$route.query.from == 1) {
          self.$router.push('/market/home')
        } else if (self.$route.path == '/market/order/ptDetails' && self.$route.query.from == 1) {
          self.$router.push({
            path: '/market/home'
          })
        } else if (self.$route.path == '/market/order/details' && self.$route.query.from == 1) {
          self.$router.push({
            path: '/market/order',
            query: {
              from: 1
            }
          })
        } else if (self.$route.path == '/setMealorderList' && self.$route.query.from == 3) {
          self.$router.push('/index')
        } else if (self.$route.path == '/setMealorderRefund' && self.$route.query.from == 1) {
          self.$router.push(
            {
              path: '/setMealorderList',
              query: {
                from: 3
              }
            })
        } else if (self.$route.path == '/classify') {
          self.$router.push('/index')
        } else if (self.$route.query.from == 2) {
          self.$router.push({
            path: '/market/order/details',
            query: {
              from: 1,
              orderNo: self.$route.query.orderNo
            }
          })
        } else {
          self.$router.go(-1)
        }
      }, 10)
    })

    // 监听通知栏
    document.addEventListener('messageNotice', function(e) {
      if (e.data.type == '1') {
        self.$router.push({
          path: '/notice/detile',
          query: {
            id: e.data.id
          }
        })
      }
      if (e.data.type == '2') {
        self.$router.push({
          name: 'Shop',
          query: {
            id: e.data.id,
            st: 'index'
          }
        })
      }
      if (e.data.type == '3') {
        self.$router.push({
          name: 'MarketHome'
        })
      }
    }, false)

    // AlipayJSBridge.call('GetJurisdiction', function(result) {
    //   console.log(result)
    // })

    // 报活
    addData(0)
  },
  mounted() {
    logger.sum('遂昌')

    this.getScreenshotStatus()
    this.getSchame()
    this.listenersSchame()
    this.isChangeEvent()
    this.getEnvironment()
  },
  methods: {
    LocationMsg() {
      let self = this
      AlipayJSBridge.call('LocationMsg', {}, function(result) {
        console.log('------定位--------')
        console.log(result)
        console.log('------定位end--------')

        let locationdata = JSON.parse(result.locationMsg)
        self.$store.state.location.latitude = locationdata.latitude
        self.$store.state.location.longitude = locationdata.longitude
        self.$store.state.location.address = locationdata.address
      })

      // self.$store.state.location.latitude = 28.592388
      // self.$store.state.location.longitude = 119.275865
      // self.$store.state.location.address = {
      //   poiName: '遂昌县'
      // }
    },
    // 检查启动schame参数
    getSchame() {
      let self = this
      AlipayJSBridge.call('GetUrl', {}, function(result) {
        if (result.urls != '' && result.urls != null) {
          self.$router.push({
            name: 'Shop',
            query: {
              id: result.urls,
              st: 'index'
            }
          })
        }
      })
    },
    // 监听schame参数
    listenersSchame() {
      let self = this
      document.addEventListener('postSharea', function(e) {
        if (e.data.code) {
          self.$router.push({
            name: 'Shop',
            query: {
              id: e.data.code,
              st: 'index'
            }
          })
        }
      })
    },
    // 判断页面状态
    isChangeEvent() {
      let self = this
      // 不同浏览器 hidden 名称
      let hiddenProperty = 'hidden' in document ? 'hidden'
        : 'webkitHidden' in document ? 'webkitHidden'
          : 'mozHidden' in document ? 'mozHidden'
            : null
      // 不同浏览器的事件名
      let visibilityChangeEvent = hiddenProperty.replace(/hidden/i, 'visibilitychange')
      let onVisibilityChange = function() {
        if (document[hiddenProperty]) {
          console.log('页面激活')
          self.getSchame()
          self.LocationMsg()
        }
      }
      document.addEventListener(visibilityChangeEvent, onVisibilityChange)
    },
    // root检查异常信息上报
    getEnvironment() {
      let self = this
      let data = { 'userId': this.$store.getters.getUserId, 'note': '当前运行环境已root' }

      var u = navigator.userAgent
      var isiOS = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/) // ios终端
      if (!isiOS) {
        // Android
        AlipayJSBridge.call('Environment', {}, function(result) {
          if (result.success == true) {
            self.$toast('当前运行环境已root')
            environment(data).then((res) => {})
          }
        })
      } else {
        // ios
        AlipayJSBridge.call('getRootStatus', {}, function(result) {
          if (result.data.status == '1') {
            self.$toast('当前运行环境已root')
            environment(data).then((res) => {})
          }
        })
      }
    },
    // 截屏操作检查
    getScreenshotStatus() {
      let self = this
      document.addEventListener('getScreenshotStatus', function(e) {
        if (e) {
          if (self.$route.path == '/scan/index' || self.$route.path == '/submitOrder' || self.$route.path == '/scan/qrCode' ||
          self.$route.path == '/pwdLogin' || self.$route.path == '/market/pay' || self.$route.path == '/editPayPwd' || self.$route.path == '/editLoginPwd'
          ) {
            self.$toast('当前页面截屏信息请勿分享，避免造成损失')
          }
        }
      }, false)
    }
  }
}
</script>
<style>
</style>
