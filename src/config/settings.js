/*
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-03-08 19:30:32
 * @LastEditors: 孙立政
 * @LastEditTime: 2023-08-22 09:44:02
 */
const setting = {
  baseURL: process.env.VUE_APP_BASE_URL,
  baseUrlSms: process.env.VUE_APP_BASE_URL_SMS,
  baseUrlSearch: process.env.VUE_APP_BASE_URL_SEARCH,
  baseUrlUpLog: process.env.VUE_APP_BASE_URL_UPLOG,
  baseUrlWS: process.env.VUE_APP_BASE_URL_WS,
  baseUrlOpen: process.env.VUE_APP_BASE_URL_OPEN,
  baseUrlLocalservice: process.env.VUE_APP_BASE_URL_LOCALSERVICE,
  baseUrlPHP: process.env.VUE_APP_BASE_URL_PHP,

  // 阿里监控
  // 测试环境
  // pids: 'bo093fstp4@a014d310a1486a1',
  // 生产环境
  pids: 'bo093fstp4@68b882428fe6802',
  rel: '2.10.31', // 前端版本
  version: '*******',
  requestTimeout: 60000, // 最长请求时间
  successCode: [200, 502], // 操作正常code
  invalidCode: 402, // 登录失效code
  noPermissionCode: 401 // 无权限code
}
module.exports = setting
