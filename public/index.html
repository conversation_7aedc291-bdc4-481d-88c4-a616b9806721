<!DOCTYPE html>
<html lang="">

<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="format-detection" content="telephone=yes" />
  <meta name="viewport"
    content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, viewport-fit=cover,user-scalable=no" />
  <title>点滴</title>

  <script type="text/javascript">
    window._AMapSecurityConfig = {
      securityJsCode: '2cf9bbd6a9e812aa365c543e043cc2a7',
    }
  </script>
  <script src="https://pv.sohu.com/cityjson?ie=utf-8"></script>
  <script type="text/javascript"
    src='//webapi.amap.com/maps?v=1.4.15&key=0e83907db061f77e7fec06442b042fdb&plugin=AMap.Autocomplete,AMap.Geocoder'></script>
  <!-- <script src="https://cdn.bootcss.com/vConsole/3.3.4/vconsole.min.js"></script>
		<script>
			// 初始化
			var vConsole = new VConsole();
    </script> -->
  <!-- <script charset='UTF-8' src="./js/sensorsdata.min.js "></script> -->

  <!-- PageSpy SDK -->
  <script crossorigin="anonymous" src="https://logup.zjntwl.com/page-spy/index.min.js"></script>

  <!-- 插件（非必须，但建议使用） -->
  <script crossorigin="anonymous" src="https://logup.zjntwl.com/plugin/data-harbor/index.min.js"></script>
  <script crossorigin="anonymous" src="https://logup.zjntwl.com/plugin/rrweb/index.min.js"></script>
  <script>
    window.$harbor = new DataHarborPlugin();
    window.$rrweb = new RRWebPlugin();

    [window.$harbor, window.$rrweb].forEach(p => {
      PageSpy.registerPlugin(p)
    })

    window.$pageSpy = new PageSpy({
      autoRender: false,
    });
    window.$pageSpy.updateRoomInfo({ project: '点滴用户端', title: localStorage.getItem("phone") || '点滴用户端' });
  </script>
</head>

<body>
  <noscript>
    <strong>您的手机不支持JS脚本执行.</strong>
  </noscript>

  <!-- <script>
      var sensors = window['sensorsDataAnalytic201505'];
      sensors.init({
          server_url: 'https://logup.diandiandidi.top/setdata',
          is_track_single_page:true, // 单页面配置，默认开启，若页面中有锚点设计，需要将该配置删除，否则触发锚点会多触发 $pageview 事件
          use_client_time:true, 
          send_type:'beacon',
          show_log: false,
          heatmap: {
            //是否开启点击图，default 表示开启，自动采集 $WebClick 事件，可以设置 'not_collect' 表示关闭。
            clickmap:'default',
            //是否开启触达图，not_collect 表示关闭，不会自动采集 $WebStay 事件，可以设置 'default' 表示开启。
            scroll_notice_map:'default',
            collect_tags: {
              div: true,
              li: true,
              img: true,
            }
          } 
      });
      sensors.quick('autoTrack');
      sensors.login(localStorage.getItem("userId")?localStorage.getItem("userId"):'/')
    </script> -->
  <div id="app"></div>
  <!-- built files will be auto injected -->
</body>

</html>